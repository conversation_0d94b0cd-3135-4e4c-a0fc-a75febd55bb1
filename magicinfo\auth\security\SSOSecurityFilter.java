package com.samsung.magicinfo.auth.security;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.mvc.security.ExtendedWebAuthenticationDetailsSource;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.sun.jna.platform.win32.Sspi.SecBufferDesc;
import java.io.IOException;
import java.security.Principal;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import javax.security.auth.Subject;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.GenericFilterBean;
import waffle.servlet.AutoDisposableWindowsPrincipal;
import waffle.servlet.NegotiateRequestWrapper;
import waffle.servlet.WindowsPrincipal;
import waffle.servlet.spi.SecurityFilterProviderCollection;
import waffle.util.AuthorizationHeader;
import waffle.windows.auth.IWindowsAuthProvider;
import waffle.windows.auth.IWindowsCredentialsHandle;
import waffle.windows.auth.IWindowsIdentity;
import waffle.windows.auth.IWindowsImpersonationContext;
import waffle.windows.auth.PrincipalFormat;
import waffle.windows.auth.impl.WindowsAccountImpl;
import waffle.windows.auth.impl.WindowsAuthProviderImpl;
import waffle.windows.auth.impl.WindowsCredentialsHandleImpl;
import waffle.windows.auth.impl.WindowsSecurityContextImpl;

public class SSOSecurityFilter extends GenericFilterBean {
   protected Logger logger = LoggingManagerV2.getLogger(SSOSecurityFilter.class);
   @Autowired
   @Qualifier("extendedDbAuthenticationManager")
   private AuthenticationManager authenticationManager;
   private static final String PRINCIPALSESSIONKEY = SSOSecurityFilter.class.getName() + ".PRINCIPAL";
   private SecurityFilterProviderCollection providers;
   private boolean impersonate;
   private IWindowsAuthProvider auth;
   boolean isUseSSO = false;
   private PrincipalFormat principalFormat;
   private PrincipalFormat roleFormat;
   private final List exceptUrlList;

   public SSOSecurityFilter() {
      super();
      this.principalFormat = PrincipalFormat.FQN;
      this.roleFormat = PrincipalFormat.FQN;
      this.exceptUrlList = Arrays.asList("/openapi", "/restapi");
      this.logger.info("[MagicInfo_SSOFilter] loaded.");
   }

   public byte[] getServiceTicketSSPI(String serviceName) {
      String securityPackage = "Kerberos";
      IWindowsCredentialsHandle clientCredentials = null;
      WindowsSecurityContextImpl clientContext = null;
      String currentUser = WindowsAccountImpl.getCurrentUsername();

      try {
         clientCredentials = WindowsCredentialsHandleImpl.getCurrent("Kerberos");
         clientCredentials.initialize();
         clientContext = new WindowsSecurityContextImpl();
         clientContext.setPrincipalName(currentUser);
         clientContext.setCredentialsHandle(clientCredentials);
         clientContext.setSecurityPackage("Kerberos");
         Object continueToken = null;

         do {
            clientContext.initialize(clientContext.getHandle(), (SecBufferDesc)continueToken, serviceName);
         } while(clientContext.isContinue());

         byte[] var7 = clientContext.getToken();
         return var7;
      } finally {
         if (clientContext != null) {
            clientContext.dispose();
         }

         if (clientCredentials != null) {
            clientCredentials.dispose();
         }

      }
   }

   public void afterPropertiesSet() throws ServletException {
      if (this.auth == null) {
         this.auth = new WindowsAuthProviderImpl();
      }

      if (this.providers == null) {
         this.providers = new SecurityFilterProviderCollection(this.auth);
      }

      try {
         this.isUseSSO = false;
         if (CommonConfig.get("sso.auto.login") != null && CommonConfig.get("sso.auto.login").equalsIgnoreCase("true")) {
            this.isUseSSO = true;
         }
      } catch (ConfigException var2) {
         this.isUseSSO = false;
      }

      if (this.isUseSSO) {
         this.logger.warn("[MagicInfo_SSOSecurityFilter] SSO auto login on");
      } else {
         this.logger.warn("[MagicInfo_SSOSecurityFilter] SSO auto login off");
      }

   }

   public void doFilter(ServletRequest sreq, ServletResponse sres, FilterChain chain) throws IOException, ServletException {
      HttpServletRequest request = (HttpServletRequest)sreq;
      HttpServletResponse response = (HttpServletResponse)sres;
      if (this.isUseSSO) {
         Iterator var6 = this.exceptUrlList.iterator();

         while(var6.hasNext()) {
            String requestUrl = (String)var6.next();
            if (request.getServletPath().startsWith(requestUrl)) {
               chain.doFilter(request, response);
            }
         }

         if (this.doFilterPrincipal(request, response, chain)) {
            return;
         }

         AuthorizationHeader authorizationHeader = new AuthorizationHeader(request);
         if (!authorizationHeader.isNull()) {
            IWindowsIdentity windowsIdentity;
            try {
               windowsIdentity = this.providers.doFilter(request, response);
               if (windowsIdentity == null) {
                  return;
               }
            } catch (IOException var28) {
               this.logger.error("error logging in user: " + var28.getMessage());
               this.logger.trace(var28);
               this.sendUnauthorized(response, true);
               return;
            }

            IWindowsImpersonationContext ctx = null;

            try {
               if (!windowsIdentity.isGuest()) {
                  this.logger.info("[MagicInfo_SSOSecurityFilter] logged in user: " + windowsIdentity.getFqn() + " sid : " + windowsIdentity.getSidString());
                  HttpSession session = request.getSession(true);
                  if (session == null) {
                     throw new ServletException("Expected HttpSession");
                  }

                  Subject subject = (Subject)session.getAttribute("javax.security.auth.subject");
                  if (subject == null) {
                     subject = new Subject();
                  }

                  Object windowsPrincipal;
                  if (this.impersonate) {
                     windowsPrincipal = new AutoDisposableWindowsPrincipal(windowsIdentity, this.principalFormat, this.roleFormat);
                  } else {
                     windowsPrincipal = new WindowsPrincipal(windowsIdentity, this.principalFormat, this.roleFormat);
                  }

                  this.logger.info("[MagicInfo_SSOSecurityFilter] sid : " + ((WindowsPrincipal)windowsPrincipal).getSidString());
                  this.logger.info("[MagicInfo_SSOSecurityFilter] name : " + ((WindowsPrincipal)windowsPrincipal).getName());
                  this.logger.info("[MagicInfo_SSOSecurityFilter] roles : " + ((WindowsPrincipal)windowsPrincipal).getRolesString());
                  String windowsPrincipaName = ((WindowsPrincipal)windowsPrincipal).getName();
                  String userName = null;
                  if (windowsPrincipaName != null && windowsPrincipaName.indexOf("\\") > 0) {
                     String[] userStr = windowsPrincipaName.split("\\\\");
                     userName = userStr[1];
                  }

                  subject.getPrincipals().add(windowsPrincipal);
                  request.getSession(false).setAttribute("javax.security.auth.subject", subject);
                  this.logger.info("[MagicInfo_SSOSecurityFilter] successfully logged in user: " + windowsIdentity.getFqn());
                  request.getSession(false).setAttribute(PRINCIPALSESSIONKEY, windowsPrincipal);
                  NegotiateRequestWrapper requestWrapper = new NegotiateRequestWrapper(request, (WindowsPrincipal)windowsPrincipal);
                  if (this.impersonate) {
                     this.logger.info("impersonating user");
                     ctx = windowsIdentity.impersonate();
                  }

                  boolean loginSession = true;
                  UserContainer userContainer = SecurityUtils.getUserContainer();
                  if (userContainer != null && !userContainer.getUser().getUser_id().equals(userName)) {
                     loginSession = false;
                  }

                  if (loginSession && userName != null) {
                     UserInfoImpl userDao = UserInfoImpl.getInstance();

                     try {
                        User user = userDao.getUserByUserId(userName);
                        UsernamePasswordAuthenticationToken loginToken = new UsernamePasswordAuthenticationToken(user.getUser_id(), user.getPassword());
                        if (loginToken != null) {
                           ExtendedWebAuthenticationDetailsSource authenticationDetailsSource = new ExtendedWebAuthenticationDetailsSource();
                           loginToken.setDetails(authenticationDetailsSource.buildDetails(request));
                           Authentication authentication = this.authenticationManager.authenticate(loginToken);
                           SecurityContextHolder.getContext().setAuthentication(authentication);
                           this.logger.info("[MagicInfo_SSOSecurityFilter] SSO Login id : " + userName);
                        }
                     } catch (Exception var26) {
                        this.logger.info("[MagicInfo_SSOSecurityFilter] SSO Login fail e: " + var26.getMessage());
                     }
                  }

                  chain.doFilter(requestWrapper, response);
                  return;
               }

               this.logger.info("guest login disabled: " + windowsIdentity.getFqn());
               this.sendUnauthorized(response, true);
            } finally {
               if (this.impersonate && ctx != null) {
                  this.logger.info("terminating impersonation");
                  ctx.revertToSelf();
               } else {
                  windowsIdentity.dispose();
               }

            }

            return;
         }

         this.sendUnauthorized(response, false);
      } else {
         chain.doFilter(request, response);
      }

   }

   public void destroy() {
   }

   private boolean doFilterPrincipal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
      Principal principal = request.getUserPrincipal();
      if (principal == null) {
         HttpSession session = request.getSession(false);
         if (session != null) {
            principal = (Principal)session.getAttribute(PRINCIPALSESSIONKEY);
         }
      }

      if (principal == null) {
         return false;
      } else if (this.providers.isPrincipalException(request)) {
         return false;
      } else {
         if (principal instanceof WindowsPrincipal) {
            WindowsPrincipal windowsPrincipal = (WindowsPrincipal)principal;
            if (this.impersonate && windowsPrincipal.getIdentity() == null) {
               return false;
            }

            NegotiateRequestWrapper requestWrapper = new NegotiateRequestWrapper(request, windowsPrincipal);
            IWindowsImpersonationContext ctx = null;
            if (this.impersonate) {
               this.logger.info("re-impersonating user");
               ctx = windowsPrincipal.getIdentity().impersonate();
            }

            try {
               chain.doFilter(requestWrapper, response);
            } finally {
               if (this.impersonate && ctx != null) {
                  this.logger.info("terminating impersonation");
                  ctx.revertToSelf();
               }

            }
         } else {
            this.logger.info("previously authenticated user: " + principal.getName());
            chain.doFilter(request, response);
         }

         return true;
      }
   }

   private void sendUnauthorized(HttpServletResponse response, boolean close) {
      try {
         this.providers.sendUnauthorized(response);
         if (close) {
            response.setHeader("Connection", "close");
         } else {
            response.setHeader("Connection", "keep-alive");
         }

         response.sendError(401);
         response.flushBuffer();
      } catch (IOException var4) {
         throw new RuntimeException(var4);
      }
   }
}
