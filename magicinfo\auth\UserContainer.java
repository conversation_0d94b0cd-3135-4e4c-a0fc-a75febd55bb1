package com.samsung.magicinfo.auth;

import com.samsung.magicinfo.framework.user.entity.User;
import java.io.Serializable;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import javax.servlet.http.HttpSessionBindingEvent;
import javax.servlet.http.HttpSessionBindingListener;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

public class UserContainer implements HttpSessionBindingListener, Serializable, UserDetails {
   private static final long serialVersionUID = 964355978994173389L;
   private User user = null;
   private long loginTime;
   private Timestamp last_login_date;
   private String directUrl = null;
   private Locale locale;
   private String scope;
   private List authorities;
   private String mngOrgId;

   public String getScope() {
      return this.scope;
   }

   public void setScope(String scope) {
      this.scope = scope;
   }

   public boolean checkAuthority(String check_name) {
      if (this.getAuthorities().isEmpty()) {
         return false;
      } else {
         check_name = check_name + " Authority";
         return this.getAuthorities().contains(new SimpleGrantedAuthority(check_name));
      }
   }

   public UserContainer() {
      super();
      this.initialize();
   }

   public Locale getLocale() {
      return this.locale;
   }

   public void setLocale(Locale locale) {
      this.locale = locale;
   }

   public User getUser() {
      return this.user;
   }

   public String getDirectUrl() {
      return this.directUrl;
   }

   public void setUser(User user) {
      this.user = user;
   }

   public String getLoginTime() {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
      Date date = new Date(this.loginTime);
      String formatTime = sdf.format(date);
      return formatTime;
   }

   public void setLoginTime(long currentTime) {
      this.loginTime = currentTime;
   }

   public Timestamp getLast_Login_Date() {
      return this.last_login_date;
   }

   public void setLast_Login_date(Timestamp last_login_date) {
      this.last_login_date = last_login_date;
   }

   public void setDirectUrl(String directUrl) {
      this.directUrl = directUrl;
   }

   public void valueBound(HttpSessionBindingEvent httpSessionBindingEvent) {
   }

   public void valueUnbound(HttpSessionBindingEvent httpSessionBindingEvent) {
      this.cleanUp();
   }

   private void initialize() {
   }

   private void cleanUp() {
   }

   public void setAuthorities(List authorities) {
      this.authorities = authorities;
   }

   public List getAuthorities() {
      return this.authorities != null ? this.authorities : Collections.emptyList();
   }

   public String getPassword() {
      return this.user != null ? this.user.getPassword() : null;
   }

   public String getUsername() {
      return this.user != null ? this.user.getUser_name() : null;
   }

   public boolean isAccountNonExpired() {
      return true;
   }

   public boolean isAccountNonLocked() {
      return true;
   }

   public boolean isCredentialsNonExpired() {
      return true;
   }

   public boolean isEnabled() {
      return true;
   }
}
