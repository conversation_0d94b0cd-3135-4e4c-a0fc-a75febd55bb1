package com.samsung.license;

import java.util.HashMap;
import java.util.Random;

public class Mklicense {
   private static int SN_LEN = 18;
   private static int ENCODE_SN_LEN = 24;
   private static int INT_ENSN_LEN = 20;
   private static int INT_MC_LEN = 12;
   private static int INT_LI_LEN = 32;
   private static int INT_SNKE_LEN = 4;
   private static int INT_SEDV_LEN = 5;
   private static int INT_MCKE_LEN = 2;
   private static int INT_MCDV_LEN = 6;
   private static int INT_KE_LEN = 8;
   private static int INT_DV_LEN = 4;
   private static int FORM_PRODUCT_KIND_LEN = 2;
   private static int FORM_SW_EDITION_LEN = 1;
   private static int FORM_SW_KIND_LEN = 1;
   private static int FORM_SW_CHG_LEN = 2;
   private static int FORM_SW_OPTION_LEN = 1;
   private static int FORM_SW_COST_LEN = 1;
   private static int FORM_CONNECTION_LEN = 4;
   private static int FORM_SW_DATE_LEN = 3;
   private static int FORM_SW_SEQ_LEN = 3;
   private static String[][] arList = new String[][]{{"H", "Z", "1", "G", "6", "M", "0", "L", "N", "I", "B", "7", "Y", "W", "K", "3", "X", "A", "5", "F", "V", "O", "J", "T", "E", "2", "U", "9", "P", "S", "D", "4", "R", "8", "C", "Q"}, {"P", "O", "1", "5", "Q", "N", "W", "V", "D", "R", "U", "2", "T", "M", "S", "A", "X", "9", "E", "L", "C", "Y", "0", "7", "K", "B", "Z", "F", "8", "4", "6", "G", "I", "3", "H", "J"}, {"A", "6", "1", "B", "5", "3", "Z", "D", "Y", "2", "I", "Q", "X", "H", "R", "W", "0", "C", "S", "P", "V", "T", "J", "O", "U", "G", "4", "F", "N", "K", "8", "M", "E", "L", "7", "9"}, {"B", "X", "W", "F", "V", "8", "Y", "D", "U", "4", "E", "Z", "G", "R", "6", "T", "Q", "S", "O", "C", "5", "1", "P", "H", "N", "2", "M", "A", "3", "L", "I", "0", "K", "9", "7", "J"}, {"C", "W", "D", "V", "B", "9", "X", "5", "U", "N", "T", "Y", "E", "M", "S", "1", "L", "R", "O", "3", "Q", "F", "P", "Z", "2", "K", "4", "A", "J", "8", "G", "I", "7", "6", "H", "0"}, {"T", "0", "7", "S", "U", "F", "R", "Q", "8", "A", "V", "E", "G", "H", "W", "P", "9", "X", "1", "2", "J", "O", "Y", "I", "B", "D", "Z", "3", "6", "K", "C", "N", "5", "M", "4", "L"}, {"3", "A", "5", "J", "0", "I", "K", "1", "H", "6", "L", "B", "O", "2", "N", "P", "G", "M", "Z", "E", "Q", "4", "X", "F", "R", "8", "D", "S", "C", "Y", "9", "W", "T", "V", "7", "U"}, {"E", "Z", "G", "8", "3", "F", "Q", "D", "1", "P", "Y", "H", "2", "4", "R", "A", "C", "O", "6", "S", "X", "I", "5", "L", "N", "T", "B", "W", "0", "V", "K", "M", "U", "9", "J", "7"}, {"I", "0", "4", "J", "H", "8", "A", "O", "L", "Z", "6", "N", "P", "G", "2", "Y", "M", "K", "Q", "F", "R", "3", "X", "B", "1", "5", "W", "U", "C", "S", "V", "9", "E", "T", "7", "D"}, {"8", "J", "D", "I", "5", "Y", "2", "H", "K", "X", "A", "Z", "N", "9", "M", "L", "C", "W", "6", "T", "B", "G", "O", "V", "S", "3", "U", "0", "R", "F", "7", "P", "Q", "4", "1", "E"}, {"0", "A", "G", "5", "Z", "9", "Y", "F", "N", "C", "M", "X", "1", "H", "W", "E", "6", "O", "L", "V", "I", "U", "2", "T", "P", "7", "J", "S", "K", "8", "B", "R", "D", "4", "Q", "3"}, {"C", "1", "W", "J", "V", "9", "X", "I", "U", "K", "T", "Y", "B", "5", "6", "Z", "S", "H", "L", "8", "G", "R", "D", "O", "M", "Q", "4", "3", "N", "P", "0", "F", "7", "E", "A", "2"}, {"E", "D", "7", "F", "C", "K", "6", "U", "T", "1", "9", "S", "V", "L", "J", "R", "W", "B", "I", "4", "X", "M", "O", "Y", "Q", "G", "Z", "3", "P", "8", "N", "A", "0", "H", "2", "5"}, {"I", "6", "H", "J", "C", "1", "G", "3", "B", "E", "4", "K", "F", "S", "R", "9", "Q", "T", "D", "L", "Z", "U", "2", "7", "8", "Y", "V", "P", "X", "M", "W", "N", "0", "A", "O", "5"}, {"I", "9", "H", "6", "J", "E", "G", "K", "7", "4", "L", "F", "D", "3", "U", "T", "1", "S", "R", "V", "A", "M", "Q", "W", "2", "C", "X", "N", "P", "Y", "0", "B", "O", "Z", "5", "8"}, {"M", "9", "L", "N", "I", "1", "Z", "3", "J", "K", "E", "O", "H", "Y", "D", "7", "X", "P", "2", "W", "F", "V", "C", "U", "8", "T", "Q", "6", "G", "R", "S", "4", "B", "0", "5", "A"}, {"E", "9", "Z", "D", "0", "A", "F", "Y", "C", "7", "3", "X", "S", "B", "2", "W", "R", "T", "G", "1", "V", "Q", "U", "L", "6", "P", "N", "K", "5", "M", "J", "O", "H", "4", "8", "I"}, {"E", "0", "B", "D", "S", "F", "6", "1", "U", "C", "T", "G", "3", "R", "4", "V", "N", "M", "O", "Q", "W", "9", "A", "P", "X", "L", "H", "Y", "8", "K", "2", "Z", "J", "I", "5", "7"}, {"9", "A", "I", "1", "Z", "G", "3", "X", "4", "W", "H", "Y", "J", "F", "0", "V", "K", "U", "B", "T", "E", "6", "S", "D", "2", "8", "L", "R", "C", "N", "7", "O", "Q", "M", "5", "P"}, {"I", "6", "H", "J", "4", "A", "G", "K", "X", "W", "N", "Y", "3", "F", "L", "V", "1", "Z", "9", "M", "U", "O", "T", "E", "7", "S", "P", "B", "D", "R", "0", "5", "C", "Q", "2", "8"}, {"G", "A", "Y", "F", "9", "H", "0", "X", "1", "E", "Z", "C", "O", "W", "6", "V", "Q", "2", "U", "N", "P", "I", "R", "B", "T", "3", "S", "5", "M", "D", "8", "J", "4", "L", "7", "K"}, {"B", "2", "9", "3", "K", "Q", "C", "7", "J", "R", "L", "P", "S", "6", "O", "X", "W", "T", "V", "Y", "I", "5", "U", "H", "N", "A", "M", "D", "8", "Z", "0", "G", "4", "E", "F", "1"}, {"6", "C", "4", "D", "3", "Z", "K", "B", "7", "Y", "L", "J", "X", "9", "E", "W", "1", "O", "P", "M", "V", "I", "F", "U", "N", "T", "Q", "5", "S", "0", "R", "H", "A", "2", "8", "G"}, {"G", "E", "I", "D", "H", "C", "F", "J", "8", "0", "B", "K", "9", "V", "3", "X", "Z", "7", "W", "A", "L", "Y", "U", "1", "S", "6", "T", "2", "O", "R", "M", "Q", "4", "N", "5", "P"}, {"G", "3", "W", "D", "X", "H", "Z", "V", "F", "I", "2", "Y", "E", "C", "1", "U", "9", "B", "4", "J", "T", "6", "5", "P", "O", "7", "S", "N", "0", "R", "K", "M", "Q", "A", "L", "8"}, {"L", "3", "4", "M", "G", "K", "1", "N", "F", "J", "A", "H", "I", "8", "X", "O", "Y", "2", "B", "W", "E", "P", "Z", "0", "V", "6", "D", "U", "R", "7", "T", "C", "9", "Q", "5", "S"}, {"U", "I", "H", "B", "A", "T", "V", "0", "J", "G", "S", "W", "2", "K", "5", "X", "C", "F", "R", "9", "4", "Y", "Q", "E", "D", "3", "P", "1", "L", "O", "Z", "6", "8", "M", "7", "N"}, {"E", "8", "4", "F", "D", "6", "G", "R", "Q", "9", "1", "S", "H", "P", "O", "T", "C", "5", "B", "3", "U", "N", "I", "M", "V", "A", "0", "W", "J", "2", "Z", "L", "X", "K", "7", "Y"}, {"Z", "1", "E", "D", "O", "5", "N", "P", "F", "3", "Y", "S", "X", "C", "M", "W", "T", "Q", "R", "V", "U", "4", "2", "G", "B", "L", "9", "7", "K", "H", "6", "J", "A", "I", "8", "0"}, {"R", "2", "B", "Q", "S", "K", "M", "6", "T", "N", "L", "P", "3", "U", "O", "A", "5", "V", "J", "I", "W", "D", "1", "Y", "X", "C", "8", "F", "9", "0", "E", "H", "4", "Z", "G", "7"}, {"9", "G", "5", "A", "H", "6", "F", "I", "Q", "2", "Z", "E", "R", "P", "Y", "3", "0", "S", "X", "O", "J", "W", "8", "V", "T", "B", "K", "U", "N", "M", "4", "L", "D", "1", "C", "7"}, {"5", "L", "X", "B", "W", "3", "M", "A", "Y", "V", "K", "1", "4", "U", "Z", "C", "7", "T", "N", "J", "0", "S", "H", "E", "9", "R", "O", "2", "I", "Q", "D", "G", "8", "P", "6", "F"}, {"L", "6", "G", "1", "I", "K", "M", "X", "H", "J", "W", "4", "Y", "3", "V", "N", "F", "D", "U", "9", "Z", "T", "E", "O", "0", "2", "S", "P", "C", "7", "R", "8", "A", "5", "Q", "B"}, {"Q", "O", "N", "I", "R", "S", "9", "P", "D", "M", "8", "T", "0", "A", "7", "L", "1", "U", "6", "H", "Z", "5", "2", "G", "K", "4", "Y", "3", "V", "J", "F", "X", "C", "W", "E", "B"}, {"G", "A", "Y", "3", "0", "X", "B", "H", "Z", "F", "S", "W", "9", "6", "T", "V", "E", "2", "R", "U", "I", "Q", "C", "8", "P", "J", "O", "D", "1", "7", "K", "5", "N", "L", "4", "M"}, {"6", "B", "T", "5", "S", "U", "A", "J", "R", "Z", "V", "1", "I", "Y", "W", "K", "9", "X", "P", "C", "H", "O", "8", "4", "L", "0", "G", "Q", "M", "D", "3", "N", "2", "F", "E", "7"}};

   public Mklicense() {
      super();
   }

   public static String getEncodeSn(String sn) {
      String snVal = "";
      String snTVal = "";
      String strVal = "";
      String splitVal = "";
      int chgVal = false;
      Random rand = new Random(System.currentTimeMillis());
      long rChkVal = (long)Math.abs(rand.nextInt(35) + 1);
      long rOptVal = (long)Math.abs(rand.nextInt(35) + 1);
      String chkVal = "0";
      String optVal = "A";
      if (rChkVal > 9L) {
         chkVal = getInt2Alphabet((int)rChkVal);
      } else {
         chkVal = Long.toString(rChkVal);
      }

      if (rOptVal > 9L) {
         optVal = getInt2Alphabet((int)rOptVal);
      } else {
         optVal = Long.toString(rOptVal);
      }

      String form_snVal = "";
      if (!nvl(sn).equals("") && SN_LEN == sn.length()) {
         int la;
         int chgVal;
         for(la = 0; la < sn.length(); ++la) {
            splitVal = sn.substring(la, la + 1);
            if (isNumber(splitVal)) {
               strVal = strVal + arList[(int)rChkVal][Integer.parseInt(splitVal)];
            } else {
               chgVal = getAlphabet2Int(splitVal);
               strVal = strVal + arList[(int)rChkVal][chgVal];
            }
         }

         snVal = strVal + optVal + chkVal;

         for(la = 0; la < snVal.length() - 1; ++la) {
            String snstr = snVal.substring(la, la + 1);
            if (isNumber(snstr)) {
               snTVal = snTVal + arList[la][Integer.parseInt(snstr)];
            } else {
               chgVal = getAlphabet2Int(snstr);
               snTVal = snTVal + arList[la][chgVal];
            }
         }

         snTVal = snTVal + chkVal;

         for(la = 0; la < INT_SEDV_LEN; ++la) {
            int laPos = la * INT_SNKE_LEN;
            if (la == 0) {
               form_snVal = form_snVal + snTVal.substring(laPos, laPos + INT_SNKE_LEN);
            } else {
               form_snVal = form_snVal + "-" + snTVal.substring(laPos, laPos + INT_SNKE_LEN);
            }
         }
      }

      return form_snVal;
   }

   public static String getDecodeSn(String sn) {
      String de_snVal = "";
      String en_snVal = "";
      String chkVal = "";
      String tmpVal = "";
      String pstr = "";
      String strVal = "";
      en_snVal = nvl(sn).replaceAll("-", "");
      if (!en_snVal.equals("") && en_snVal.length() == INT_ENSN_LEN) {
         chkVal = en_snVal.substring(19, 20);
         tmpVal = en_snVal.substring(0, SN_LEN + 1);
         int nChkVal = false;
         int nChkVal;
         if (isNumber(chkVal)) {
            nChkVal = Integer.parseInt(chkVal);
         } else {
            nChkVal = getAlphabet2Int(chkVal);
         }

         int g;
         String[] tmpList;
         int f;
         for(g = 0; g < tmpVal.length(); ++g) {
            pstr = tmpVal.substring(g, g + 1);
            tmpList = arList[g];

            for(f = 0; f < tmpList.length; ++f) {
               if (pstr.equals(tmpList[f])) {
                  if (f > 9) {
                     strVal = strVal + getInt2Alphabet(f);
                  } else {
                     strVal = strVal + Integer.toString(f);
                  }
                  break;
               }
            }
         }

         for(g = 0; g < strVal.length(); ++g) {
            pstr = strVal.substring(g, g + 1);
            tmpList = arList[nChkVal];

            for(f = 0; f < tmpList.length; ++f) {
               if (pstr.equals(tmpList[f])) {
                  if (f > 9) {
                     de_snVal = de_snVal + getInt2Alphabet(f);
                  } else {
                     de_snVal = de_snVal + Integer.toString(f);
                  }
                  break;
               }
            }
         }

         de_snVal = de_snVal + chkVal;
      }

      return de_snVal;
   }

   public static String getEncodeLic(String sn, String mac) {
      String strLic = "";
      String fstrLic = "";
      String strVal = "";
      String splitVal = "";
      int chgVal = false;
      long rChkVal = 0L;
      String chkVal = "0";
      String form_fstrLic = "";
      mac = nvl(mac).replaceAll("-", "");
      if (!mac.equals("") && mac.length() >= INT_MC_LEN) {
         sn = sn.replaceAll("-", "");
         if (!nvl(sn).equals("") && INT_ENSN_LEN == sn.length()) {
            String snVal = sn.replaceAll("-", "");
            chkVal = snVal.substring(snVal.length() - 1, snVal.length());
            if (isNumber(chkVal)) {
               rChkVal = (long)Integer.parseInt(chkVal);
            } else {
               rChkVal = (long)getAlphabet2Int(chkVal);
            }

            snVal = mac + snVal;

            int la;
            String pstr;
            int chgVal;
            for(la = 0; la < snVal.length(); ++la) {
               pstr = snVal.substring(la, la + 1);
               if (la == snVal.length() - 1) {
                  strLic = strLic + chkVal;
               } else if (isNumber(pstr)) {
                  strLic = strLic + arList[(int)rChkVal][Integer.parseInt(pstr)];
               } else {
                  chgVal = getAlphabet2Int(pstr);
                  strLic = strLic + arList[(int)rChkVal][chgVal];
               }
            }

            for(la = 0; la < strLic.length(); ++la) {
               pstr = strLic.substring(la, la + 1);
               if (isNumber(pstr)) {
                  fstrLic = fstrLic + arList[la][Integer.parseInt(pstr)];
               } else {
                  chgVal = getAlphabet2Int(pstr);
                  fstrLic = fstrLic + arList[la][chgVal];
               }
            }

            for(la = 0; la < INT_DV_LEN; ++la) {
               int laPos = la * INT_KE_LEN;
               if (la == 0) {
                  form_fstrLic = form_fstrLic + fstrLic.substring(laPos, laPos + INT_KE_LEN);
               } else {
                  form_fstrLic = form_fstrLic + "-" + fstrLic.substring(laPos, laPos + INT_KE_LEN);
               }
            }
         } else {
            form_fstrLic = "Invalid SerialNumber.";
         }

         return form_fstrLic;
      } else {
         form_fstrLic = "Invalid MacAddress.";
         return form_fstrLic;
      }
   }

   public static HashMap getLicInfo(String lic) {
      HashMap rtnMap = new HashMap();
      String strTmp = "";
      String tmpSn = "";
      String tmpMac = "";
      lic = lic.replaceAll("-", "");
      if (lic != null && !lic.equals("") && lic.length() == INT_LI_LEN) {
         HashMap tmpMap = getDecodeLic(lic);
         if (tmpMap != null) {
            tmpSn = (String)tmpMap.get("SN");
            tmpMac = ((String)tmpMap.get("MAC")).replaceAll("-", "");
            if (tmpSn.length() == INT_ENSN_LEN) {
               rtnMap.put("PRODUCT_KIND", tmpSn.substring(0, FORM_PRODUCT_KIND_LEN));
               tmpSn = tmpSn.substring(FORM_PRODUCT_KIND_LEN, tmpSn.length());
               rtnMap.put("SW_EDITION", tmpSn.substring(0, FORM_SW_EDITION_LEN));
               tmpSn = tmpSn.substring(FORM_SW_EDITION_LEN, tmpSn.length());
               rtnMap.put("SW_KIND", tmpSn.substring(0, FORM_SW_KIND_LEN));
               tmpSn = tmpSn.substring(FORM_SW_KIND_LEN, tmpSn.length());
               rtnMap.put("SW_CHG", tmpSn.substring(0, FORM_SW_CHG_LEN));
               tmpSn = tmpSn.substring(FORM_SW_CHG_LEN, tmpSn.length());
               rtnMap.put("SW_OPTION", tmpSn.substring(0, FORM_SW_OPTION_LEN));
               tmpSn = tmpSn.substring(FORM_SW_OPTION_LEN, tmpSn.length());
               String costType = tmpSn.substring(0, FORM_SW_COST_LEN);
               System.out.println("[Mklicense] costType : " + costType);
               rtnMap.put("SW_COST", costType);
               tmpSn = tmpSn.substring(FORM_SW_COST_LEN, tmpSn.length());
               String tmpCnt = tmpSn.substring(0, FORM_CONNECTION_LEN);
               int nTotVal = false;
               int nTotVal = Integer.parseInt(tmpCnt);
               rtnMap.put("CONNECTION", Integer.toString(nTotVal));
               tmpSn = tmpSn.substring(FORM_CONNECTION_LEN, tmpSn.length());
               String tmpDate = tmpSn.substring(0, FORM_SW_DATE_LEN);
               String tmpYY = tmpDate.substring(0, 2);
               String tmpMM = tmpDate.substring(2, 3);
               if (isNumber(tmpMM)) {
                  tmpMM = getLeftFilledString(tmpMM, "0", 2);
               } else {
                  tmpMM = Integer.toString(getAlphabet2Int(tmpMM));
                  tmpMM = getLeftFilledString(tmpMM, "0", 2);
               }

               tmpDate = "20" + tmpYY + "-" + tmpMM;
               rtnMap.put("SW_DATE", tmpDate);
               String valMac;
               String tmpSeqYY;
               String seq2;
               boolean tempYYMM;
               boolean tempYY;
               boolean tempMM;
               int nTotSeq;
               if (!costType.equalsIgnoreCase("D")) {
                  tmpSn = tmpSn.substring(FORM_SW_DATE_LEN, tmpSn.length());
                  valMac = tmpSn.substring(0, FORM_SW_SEQ_LEN);
                  tmpSeqYY = valMac.substring(0, 1);
                  seq2 = valMac.substring(1, 2);
                  String seq3 = valMac.substring(2, 3);
                  tempYYMM = false;
                  tempYY = false;
                  tempMM = false;
                  int nTotSeq = false;
                  int nSeq1;
                  if (isNumber(tmpSeqYY)) {
                     nSeq1 = Integer.parseInt(tmpSeqYY) * 16 * 16;
                  } else {
                     nSeq1 = getAlphabet2Int(tmpSeqYY) * 16 * 16;
                  }

                  int nSeq2;
                  if (isNumber(seq2)) {
                     nSeq2 = Integer.parseInt(seq2) * 16;
                  } else {
                     nSeq2 = getAlphabet2Int(seq2) * 16;
                  }

                  int nSeq3;
                  if (isNumber(seq3)) {
                     nSeq3 = Integer.parseInt(seq3);
                  } else {
                     nSeq3 = getAlphabet2Int(seq3);
                  }

                  nTotSeq = nSeq1 + nSeq2 + nSeq3;
                  System.out.println("[Mklicense] sw_seq " + Integer.toString(nTotSeq));
                  rtnMap.put("SW_SEQ", Integer.toString(nTotSeq));
               } else {
                  tmpSn = tmpSn.substring(FORM_SW_DATE_LEN, tmpSn.length());
                  valMac = tmpSn.substring(0, FORM_SW_SEQ_LEN);
                  System.out.println("[Mklicense] sw_seq demo 1 : " + valMac);
                  tmpSeqYY = "";
                  seq2 = "";
                  String[] swSequence = new String[4];
                  tempYYMM = false;
                  tempYY = false;
                  tempMM = false;
                  nTotSeq = 0;

                  for(int i = 0; i < valMac.length(); ++i) {
                     swSequence[i] = valMac.substring(i, i + 1);
                     int tmpChar = getAlphabet2Int(swSequence[i]);
                     nTotSeq = tmpChar;
                     System.out.println("swSequence[i] to digiChar " + swSequence[i] + " to " + tmpChar);
                  }

                  tmpSeqYY = "20" + swSequence[0] + swSequence[1];
                  seq2 = String.valueOf(nTotSeq);
                  valMac = tmpSeqYY + "-" + seq2;
                  System.out.println("[Mklicense] sw_seq demo 2 : " + valMac);
                  rtnMap.put("SW_SEQ", valMac);
               }

               valMac = "";

               for(int ma = 0; ma < 6; ++ma) {
                  int mpos = ma * 2;
                  if (ma == 0) {
                     valMac = valMac + tmpMac.substring(mpos, mpos + 2);
                  } else {
                     valMac = valMac + "-" + tmpMac.substring(mpos, mpos + 2);
                  }
               }

               rtnMap.put("MAC", valMac);
            }
         }
      }

      return rtnMap;
   }

   public static HashMap getDecodeLic(String lic) {
      String pstr = "";
      String mac = "";
      String sn = "";
      String strVal = "";
      String fstrVal = "";
      String chkVal = "";
      String srcVal = "";
      String form_snVal = "";
      String form_valMac = "";
      HashMap deVal = new HashMap();
      lic = lic.replaceAll("-", "");
      if (!nvl(lic).equals("") && INT_LI_LEN == lic.length()) {
         int nChkVal;
         int mpos;
         for(nChkVal = 0; nChkVal < lic.length(); ++nChkVal) {
            pstr = lic.substring(nChkVal, nChkVal + 1);
            String[] tmpList = arList[nChkVal];

            for(mpos = 0; mpos < tmpList.length; ++mpos) {
               if (pstr.equals(tmpList[mpos])) {
                  if (mpos > 9) {
                     strVal = strVal + getInt2Alphabet(mpos);
                  } else {
                     strVal = strVal + Integer.toString(mpos);
                  }
                  break;
               }
            }
         }

         chkVal = strVal.substring(strVal.length() - 1, strVal.length());
         int nChkVal = false;
         if (isNumber(chkVal)) {
            nChkVal = Integer.parseInt(chkVal);
         } else {
            nChkVal = getAlphabet2Int(chkVal);
         }

         int ma;
         for(ma = 0; ma < strVal.length(); ++ma) {
            pstr = strVal.substring(ma, ma + 1);
            String[] tmpList = arList[nChkVal];
            if (ma == strVal.length() - 1) {
               fstrVal = fstrVal + chkVal;
            } else {
               for(int f = 0; f < tmpList.length; ++f) {
                  if (pstr.equals(tmpList[f])) {
                     if (f > 9) {
                        fstrVal = fstrVal + getInt2Alphabet(f);
                     } else {
                        fstrVal = fstrVal + Integer.toString(f);
                     }
                     break;
                  }
               }
            }
         }

         mac = fstrVal.substring(0, INT_MC_LEN);
         srcVal = fstrVal.substring(INT_MC_LEN, strVal.length());
         sn = getDecodeSn(srcVal);

         for(ma = 0; ma < INT_MCDV_LEN; ++ma) {
            mpos = ma * INT_MCKE_LEN;
            if (ma == 0) {
               form_valMac = form_valMac + mac.substring(mpos, mpos + INT_MCKE_LEN);
            } else {
               form_valMac = form_valMac + "-" + mac.substring(mpos, mpos + INT_MCKE_LEN);
            }
         }

         deVal.put("SN", sn);
         deVal.put("MAC", form_valMac);
      }

      return deVal;
   }

   public static String getInt2Alphabet(int val) {
      String rtnStr = "";
      String[] arAlpha = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};
      if (val > 9) {
         val -= 10;
         rtnStr = arAlpha[val];
      } else {
         rtnStr = Integer.toString(val);
      }

      return rtnStr;
   }

   public static int getAlphabet2Int(String val) {
      int rtn = 0;
      String[] arAlpha = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

      for(int i = 0; i < arAlpha.length; ++i) {
         if (arAlpha[i].equals(val.toUpperCase())) {
            rtn = i + 10;
         }
      }

      if (rtn == 0) {
         rtn = Integer.parseInt(val);
      }

      return rtn;
   }

   public static String nvl(String str) {
      return str == null ? "" : str;
   }

   public static boolean isNumber(String n) {
      boolean isNum = false;
      String[] numberList = new String[]{"1", "2", "3", "4", "5", "6", "7", "8", "9", "0"};

      for(int i = 0; i < numberList.length; ++i) {
         if (n.equals(numberList[i])) {
            isNum = true;
         }
      }

      return isNum;
   }

   public static String getLeftFilledString(String str, String fill, int len) {
      StringBuffer sb = new StringBuffer();

      for(int i = 0; i < len; ++i) {
         sb.append(fill);
      }

      sb.append(str);
      return sb.substring(sb.length() - len);
   }
}
