## generateDiagnosticUploaderJnlp
##
<?xml version='1.0' encoding='UTF-8' ?>
<jnlp spec='1.0'
	codebase='${serverUrl}/servlet/'
	href='${JNLP_SERVLET}${jnlpFileName}'>
	<information>
		<title>MIDiagUploader</title>
		<vendor>MagicInfo</vendor>
	</information>
	<security>
		<all-permissions/>
	</security>
	<resources os="Windows" arch="amd64">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
		<jar href='${serverUrl}/uploader/${urlToken}/previewer.jar' main='true' />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/jnlp.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/transform.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-httpclient-3.1.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-codec-1.3.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-logging-1.1.1.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-net-3.3.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/swt64.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-ssl.jar' download="eager" />
	</resources>
	<resources os="Windows" arch="x86_64">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
		<jar href='${serverUrl}/uploader/${urlToken}/previewer.jar' main='true' />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/jnlp.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/transform.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-httpclient-3.1.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-codec-1.3.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-logging-1.1.1.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-net-3.3.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/swt64.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-ssl.jar' download="eager" />
	</resources>
	<resources os="Windows" arch="x86">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
		<jar href='${serverUrl}/uploader/${urlToken}/previewer.jar' main='true' />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/jnlp.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/transform.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-httpclient-3.1.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-codec-1.3.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-logging-1.1.1.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-net-3.3.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/swt.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-ssl.jar' download="eager" />
	</resources>
	<resources os="Windows" arch="i386">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
		<jar href='${serverUrl}/uploader/${urlToken}/previewer.jar' main='true' />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/jnlp.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/transform.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-httpclient-3.1.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-codec-1.3.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-logging-1.1.1.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-net-3.3.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/swt.jar' download="eager" />
		<jar href='${serverUrl}/uploader/${urlToken}/lib/commons-ssl.jar' download="eager" />
	</resources>
	<application-desc main-class='com.samsung.magicinfo.previewer.DownloaderApplication'>
		<argument>${requestLocale}</argument>
		<argument>${ftpPort}</argument>
		<argument>${loginUserId}</argument>
		<argument>${issuedToken}</argument>
		<argument>${jnlpFileName}</argument> 
		<argument>${srpList}</argument> 
		<argument>${download_method}</argument> 
		<argument>${MIIP}</argument>
		<argument>${MIPort}</argument>
		<argument>${DstIP}</argument>
		<argument>${DstPort}</argument>
	</application-desc>
</jnlp>
