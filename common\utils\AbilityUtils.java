package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.role.dao.AbilityDao;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.Logger;

public class AbilityUtils {
   private static final String ANONYMOUS_USER = "anonymousUser";
   static Logger logger = LoggingManagerV2.getLogger(AbilityUtils.class);
   private UserContainer userContainer = null;

   public AbilityUtils() {
      super();
   }

   public boolean isAdmin() {
      if (!this.userIsLogged()) {
         return false;
      } else {
         return this.userContainer.getUser().getRoot_group_id() == 0L;
      }
   }

   public boolean isContentApprovalAuthority() throws ConfigException {
      return this.checkAuthority("Content Manage");
   }

   public boolean isContentApprovalAuthority(String userId) {
      AbilityDao abilityDao = new AbilityDao();

      try {
         List abilityList = abilityDao.getAllAbilityListByUserId(userId);

         for(int i = 0; i < abilityList.size(); ++i) {
            String abilityName = (String)((Map)abilityList.get(i)).get("ABILITY_NAME");
            if (abilityName.equals("Content Manage Authority")) {
               return true;
            }
         }
      } catch (SQLException var6) {
         logger.error("", var6);
      }

      return false;
   }

   public boolean checkAuthority(String checkName) {
      return !this.userIsLogged() ? false : this.userContainer.checkAuthority(checkName);
   }

   private boolean userIsLogged() {
      UserContainer uc = SecurityUtils.getUserContainer();
      if (uc != null && uc.getUser() != null && uc.getUser().getUser_id() != null && !uc.getUser().getUser_id().equals("anonymousUser")) {
         this.userContainer = uc;
      }

      return this.userContainer != null;
   }

   public static boolean isVisibleEditButton(String menuType, String group_type, String group_id, String loginUserId) {
      boolean result = false;
      if ("GROUPED".equalsIgnoreCase(group_type)) {
         try {
            result = isLoginUserGroup(menuType, group_id, loginUserId);
         } catch (NumberFormatException var6) {
            logger.error("", var6);
         } catch (SQLException var7) {
            logger.error("", var7);
         }
      } else if ("ALL".equalsIgnoreCase(group_type)) {
         result = true;
      }

      return result;
   }

   public static boolean isLoginUserGroup(String menuType, String group_id, String loginUserId) throws NumberFormatException, SQLException {
      boolean result = false;
      Group group = null;
      if (menuType.equalsIgnoreCase("CONTENT")) {
         ContentInfo cInfo = ContentInfoImpl.getInstance();
         group = cInfo.getGroupInfo(new Long(group_id));
      } else if (menuType.equalsIgnoreCase("PLAYLIST")) {
         PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
         group = pInfo.getGroupInfo(new Long(group_id));
      }

      if (group != null && loginUserId.equalsIgnoreCase(group.getCreator_id())) {
         result = true;
      } else {
         result = false;
      }

      return result;
   }
}
