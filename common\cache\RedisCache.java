package com.samsung.common.cache;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.protocol.queue.RequestContext;
import io.netty.util.concurrent.FastThreadLocal;
import io.netty.util.concurrent.GlobalEventExecutor;
import io.netty.util.internal.InternalThreadLocalMap;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;
import org.apache.logging.log4j.Logger;
import org.redisson.Redisson;
import org.redisson.api.RBucket;
import org.redisson.api.RKeys;
import org.redisson.api.RMap;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisException;
import org.redisson.client.codec.Codec;
import org.redisson.codec.SerializationCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;

public class RedisCache implements BasicCache, ServletContextListener {
   private static final String NAMESPACE = "MAGICNFO:5d41402abc4b2a76b9719d91101";
   static Logger logger = LoggingManagerV2.getLogger(RedisCache.class);
   private static RedisCache instance = null;
   private static RedissonClient redisson;
   private static int threadPool = 20;
   private static Codec codec = new SerializationCodec();

   private RedisCache() {
      super();
      Config config = new Config();
      String redis_url = null;
      String redisConfigPath = null;
      int timeout = 3000;
      int connectionTimeout = 3000;
      String cluster = "N";

      String strConnectionTimeout;
      try {
         strConnectionTimeout = CommonConfig.get("redis.pool.count");
         String strTimeout = CommonConfig.get("redis.timeout");
         String strConnectionTimeout = CommonConfig.get("redis.connectionTimeout");
         cluster = CommonConfig.get("redis.cluster");
         if (strConnectionTimeout != null && !strConnectionTimeout.equals("")) {
            threadPool = Integer.valueOf(strConnectionTimeout);
            if (threadPool < 10) {
               threadPool = 10;
            }
         }

         if (strTimeout != null && !strTimeout.equals("")) {
            timeout = Integer.valueOf(strTimeout);
         }

         if (strConnectionTimeout != null && !strConnectionTimeout.equals("")) {
            connectionTimeout = Integer.valueOf(strConnectionTimeout);
         }

         redis_url = CommonConfig.get("redis.url");
         if (CommonConfig.get("redis.config.path") != null && !CommonConfig.get("redis.config.path").equals("")) {
            redisConfigPath = CommonConfig.get("redis.config.path");
         }
      } catch (Exception var15) {
         logger.error("redis.url is empty!!", var15);
      }

      if (redisConfigPath != null) {
         try {
            config = Config.fromJSON(new File(redisConfigPath));
            redisson = Redisson.create(config);
         } catch (IOException var14) {
            logger.error("[MagicInfo_RedisCache] fail init REDIS e : " + var14.getMessage());
            logger.error("[MagicInfo_RedisCache] " + var14);
         }
      } else if (redis_url != null) {
         if (cluster != null && cluster.equalsIgnoreCase("Y")) {
            strConnectionTimeout = "2000";

            try {
               if (CommonConfig.get("redis.scanInterval") != null && !CommonConfig.get("redis.scanInterval").equals("")) {
                  strConnectionTimeout = CommonConfig.get("redis.scanInterval");
               }
            } catch (Exception var13) {
               strConnectionTimeout = "2000";
            }

            String[] redisUrl = redis_url.split(" ");
            ((ClusterServersConfig)((ClusterServersConfig)config.useClusterServers().setScanInterval(Integer.valueOf(strConnectionTimeout)).setConnectTimeout(connectionTimeout)).setTimeout(timeout)).setMasterConnectionPoolSize(threadPool);
            String[] var17 = redisUrl;
            int var10 = redisUrl.length;

            for(int var11 = 0; var11 < var10; ++var11) {
               String url = var17[var11];
               config.useClusterServers().addNodeAddress(new String[]{"redis://" + url});
            }

            redisson = Redisson.create(config);
            logger.error("[MagicInfo_RedisCache] CluterServer url : " + redis_url);
         } else {
            config.setCodec(new SerializationCodec());
            config.setThreads(0);
            config.setNettyThreads(0);
            ((SingleServerConfig)((SingleServerConfig)((SingleServerConfig)((SingleServerConfig)config.useSingleServer().setConnectTimeout(connectionTimeout)).setTimeout(timeout)).setRetryAttempts(10)).setRetryInterval(1000)).setConnectionPoolSize(threadPool).setConnectionMinimumIdleSize(10).setAddress("redis://" + redis_url);
            logger.error("[MagicInfo_RedisCache] SingleServer url : " + redis_url);
            redisson = Redisson.create(config);
         }
      }

      if (redisson != null) {
         logger.info("Redis is ready with PoolSize = " + threadPool + ", timeout = " + timeout + ", connectionTimeout = " + connectionTimeout);
      } else {
         logger.info("fail to load Redis ");
      }

   }

   public static RedisCache getInstance() {
      if (instance == null) {
         Class var0 = RedisCache.class;
         synchronized(RedisCache.class) {
            logger.info("Creating a new Redis Client instance");
            instance = new RedisCache();
         }
      }

      return instance;
   }

   public void set(String key, Object o) throws Exception {
      RBucket bucket = redisson.getBucket("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);

      try {
         bucket.set(o);
      } catch (Exception var5) {
         logger.error("Redis error: ", var5);
      }

   }

   public void set(String key, int timeToLive, Object o) throws Exception {
      RBucket bucket = redisson.getBucket("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);

      try {
         bucket.set(o, (long)timeToLive, TimeUnit.SECONDS);
      } catch (Exception var6) {
         logger.error("Redis error: ", var6);
      }

   }

   public CASValue gets(String key, Object autoInitObj) throws Exception {
      CASValue casValue = null;
      RBucket bucket = redisson.getBucket("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);

      try {
         if (!bucket.isExists()) {
            logger.warn("[MagicInfo_Cached] Memory init key :" + key);
            if (autoInitObj == null) {
               return casValue;
            }

            bucket.trySet(autoInitObj);
         }

         casValue = new CASValue(1L, bucket.get());
      } catch (Exception var6) {
         logger.error("Redis error: ", var6);
      }

      return casValue;
   }

   public Object get(String key) throws Exception {
      RBucket bucket = redisson.getBucket("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);
      Object object = null;

      try {
         object = bucket.get();
      } catch (Exception var5) {
         logger.error("Redis error: ", var5);
      }

      return object;
   }

   public void clean() throws Exception {
      RKeys keys = redisson.getKeys();
      keys.flushall();
   }

   public void delete(String key) throws Exception {
      RBucket bucket = redisson.getBucket("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);

      try {
         bucket.delete();
      } catch (Exception var4) {
         logger.error("Redis error: ", var4);
      }

   }

   public CASResponse cas(String key, long casId, Object o) throws Exception {
      CASResponse casResponse = CASResponse.OBSERVE_MODIFIED;
      RBucket bucket = redisson.getBucket("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);

      try {
         bucket.set(o);
         casResponse = CASResponse.OK;
      } catch (Exception var8) {
         logger.error("Redis error: ", var8);
         casResponse = CASResponse.OBSERVE_TIMEOUT;
      }

      return casResponse;
   }

   public Object cas(String key, Object item, MutatorOperation operation) {
      RBucket bucket = redisson.getBucket("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);

      try {
         for(int i = 0; i < 10; ++i) {
            if (operation instanceof FullMapMerger || operation instanceof MapItemMerger) {
               Map currentMap = (Map)bucket.get();
               if (currentMap == null) {
                  currentMap = new HashMap();
               }

               if (item != null) {
                  Entry newItem = (Entry)item;
                  ((Map)currentMap).put(newItem.getKey(), newItem.getValue());
                  return bucket.getAndSet(currentMap);
               }

               return currentMap;
            }

            if (!(operation instanceof MapItemRemover)) {
               break;
            }

            Map currentMap = (Map)bucket.get();
            Map resultMap = new HashMap();
            if (currentMap != null) {
               resultMap.putAll(currentMap);
            } else if (item == null) {
               break;
            }

            if (item == null) {
               return currentMap;
            }

            resultMap.remove((String)item);
            bucket.getAndSet(currentMap);
         }
      } catch (Exception var8) {
         logger.error("Redis error: ", var8);
      }

      logger.error("Redis error: Couldn't get a CAS");
      return null;
   }

   public boolean isEmpty(String key) throws Exception {
      Boolean result = true;
      RBucket bucket = redisson.getBucket("MAGICNFO:5d41402abc4b2a76b9719d91101" + key);

      try {
         result = !bucket.isExists();
      } catch (Exception var5) {
         logger.error("Redis error: ", var5);
      }

      return result;
   }

   public void contextInitialized(ServletContextEvent servletContextEvent) {
   }

   public void contextDestroyed(ServletContextEvent servletContextEvent) {
      if (instance != null) {
         redisson.shutdown();
         FastThreadLocal.removeAll();
         FastThreadLocal.destroy();
         InternalThreadLocalMap.remove();
         InternalThreadLocalMap.destroy();
         GlobalEventExecutor.INSTANCE.shutdownGracefully().awaitUninterruptibly();

         try {
            System.err.println("Sleep for a bit so that we don't get any errors about Redisson threads not being shut down yet.");
            Thread.sleep(15000L);
         } catch (Exception var3) {
            logger.error("", var3);
         }
      }

   }

   public Object getMapCache(String cacheKey, String hashKey, int command) {
      RMap map = null;
      Object o = null;

      try {
         map = redisson.getMap("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         if (command > 0) {
            o = map.remove(hashKey);
            logger.trace("[MagicInfo_RedisCache] getMapCache remove key : " + cacheKey + " hashKey : " + hashKey);
         } else {
            o = map.get(hashKey);
            logger.trace("[MagicInfo_RedisCache] getMapCache key : " + cacheKey + " hashKey : " + hashKey);
         }
      } catch (Exception var7) {
         logger.error("[MagicInfo_RedisCache] error key : " + cacheKey + " error : " + var7.getMessage());
      }

      return o;
   }

   public boolean putMapCache(String cacheKey, String hashKey, Object o) {
      boolean checked = false;
      RMap map = null;

      try {
         map = redisson.getMap("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         if (map != null) {
            map.put(hashKey, o);
            checked = true;
            logger.trace("[MagicInfo_RedisCache] putMapCache key : " + cacheKey + " hashKey : " + hashKey);
         } else {
            checked = false;
         }

         return checked;
      } catch (Exception var7) {
         logger.error("[MagicInfo_RedisCache] error putMapCache key : " + cacheKey + " hashKey : " + hashKey + " error : " + var7.getMessage());
         throw var7;
      }
   }

   public void removeMapCache(String cacheKey, String hashKey) {
      RMap map = null;

      try {
         map = redisson.getMap("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         if (map != null) {
            map.remove(hashKey);
            logger.trace("[MagicInfo_RedisCache] removeMapCache key : " + cacheKey + " hashKey : " + hashKey);
         }
      } catch (Exception var5) {
         logger.error("[MagicInfo_RedisCache] error removeMapCache key : " + cacheKey + " hashKey : " + hashKey + " error : " + var5.getMessage(), var5);
      }

   }

   public List readAllMap(String cacheKey) {
      List list = null;
      RMap map = null;

      try {
         map = redisson.getMap("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         if (map != null) {
            Collection colls = map.readAllValues();
            if (colls != null && colls.size() > 0) {
               list = new ArrayList(colls);
            }
         }
      } catch (Exception var5) {
         logger.error("[MagicInfo_RedisCache] error getListMapCache key : " + cacheKey + " error : " + var5.getMessage(), var5);
      }

      return list;
   }

   public int getSizeMap(String cacheKey) {
      RMap map = null;
      int size = 0;

      try {
         map = redisson.getMap("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         if (map != null) {
            size = map.size();
         }
      } catch (Exception var5) {
         logger.error("[MagicInfo_RedisCache] error getListMapCache key : " + cacheKey + " error : " + var5.getMessage(), var5);
      }

      return size;
   }

   public boolean enQueue(String cacheKey, Object o) {
      RQueue queue = null;
      boolean chk = false;

      try {
         queue = redisson.getQueue("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         if (o != null) {
            chk = queue.add(o);
         }
      } catch (RedisException var7) {
         if (var7.getMessage().contains("WRONGTYPE")) {
            logger.error("[MagicInfo_RedisCache] error enQueue key WRONGTYPE : " + cacheKey + " error : " + var7.getMessage());
            RBucket bucket = redisson.getBucket("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
            bucket.delete();
            queue = redisson.getQueue("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
            if (o != null) {
               chk = queue.add(o);
            }
         }
      } catch (Exception var8) {
         logger.error("[MagicInfo_RedisCache] error enQueue key : " + cacheKey + " error : " + var8.getMessage());
      }

      return chk;
   }

   public Object deQueue(String cacheKey) {
      RQueue queue = null;

      try {
         queue = redisson.getQueue("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         Object o = queue.poll();
         if (o != null) {
            return o;
         }
      } catch (Exception var4) {
         logger.error("[MagicInfo_RedisCache] error deQueue key : " + cacheKey + " error : " + var4.getMessage());
      }

      return null;
   }

   public List readAllQueue(String cacheKey) {
      RQueue queue = null;
      List list = null;

      try {
         queue = redisson.getQueue("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         list = queue.readAll();
      } catch (Exception var5) {
         logger.error("[MagicInfo_RedisCache] error deQueue key : " + cacheKey + " error : " + var5.getMessage());
      }

      return list;
   }

   public Object getQueue(String cacheKey) {
      RQueue queue = null;

      try {
         queue = redisson.getQueue("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         Object o = queue.peek();
         if (o != null) {
            return o;
         }
      } catch (Exception var4) {
         logger.error("[MagicInfo_RedisCache] error getQueue key : " + cacheKey + " error : " + var4.getMessage());
      }

      return null;
   }

   public boolean isExistServiceInQueue(String cacheKey, String service) throws Exception {
      try {
         RQueue queue = redisson.getQueue("MAGICNFO:5d41402abc4b2a76b9719d91101" + cacheKey);
         Iterator iterator = queue.iterator();

         RequestContext request;
         do {
            if (!iterator.hasNext()) {
               return false;
            }

            request = (RequestContext)iterator.next();
         } while(request == null || !request.getWebServiceContext().getServiceID().equals(service));

         return true;
      } catch (Exception var6) {
         logger.error("[RedisCache] fail isExistServiceInQueue e : " + var6.getMessage());
         throw var6;
      }
   }
}
