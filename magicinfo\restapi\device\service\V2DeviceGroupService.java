package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2ListQueryFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceAvailableMenuResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceBackupModeResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceBackupPlayInfoReq;
import com.samsung.magicinfo.restapi.device.model.V2DeviceBackupPlayInfoRes;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupCreation;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupDeletion;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupRename;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupSearch;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSetSboxLayoutResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceVwlPreviewResorce;
import com.samsung.magicinfo.restapi.device.model.V2VwlLayoutManagerResource;
import com.samsung.magicinfo.rms.model.DeviceGroupResource;
import java.sql.SQLException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

public interface V2DeviceGroupService {
   ResponseBody listGroupInfo(Long var1) throws Exception;

   ResponseBody getDeviceGroupList(V2ListQueryFilter var1) throws Exception;

   List listGroups(String var1) throws Exception;

   ResponseBody listGroupsInfo(String[] var1);

   V2DeviceGroupCreation createGroup(DeviceGroupResource var1) throws Exception;

   V2DeviceGroupDeletion deleteGroup(DeviceGroupResource var1) throws Exception;

   V2DeviceGroupRename renameGroup(String var1, V2DeviceGroupRename var2) throws Exception;

   List getDeviceGroupsWithChildren(V2DeviceGroupSearch var1, HttpServletRequest var2) throws Exception;

   V2CommonGroupResource moveGroup(Long var1, Long var2, Long var3) throws Exception;

   V2CommonBulkResultResource moveDevices(List var1, String var2) throws Exception;

   V2CommonBulkResultResource backupTagChecking(String var1) throws Exception;

   V2CommonBulkResultResource backupClear(String var1, HttpServletRequest var2) throws Exception;

   V2DeviceSetSboxLayoutResource connectionCheckByGroupId(Long var1) throws Exception;

   V2VwlLayoutManagerResource vwlLayoutManager(Long var1) throws Exception;

   V2DeviceBackupModeResource backupModeSelect(String var1, V2CommonIds var2) throws Exception;

   V2DeviceBackupPlayInfoRes saveBackupPlayConfiguration(long var1, V2DeviceBackupPlayInfoReq var3) throws Exception;

   V2DeviceAvailableMenuResource availableMenu(String var1) throws Exception;

   V2DeviceVwlPreviewResorce vwlPreview(String var1) throws Exception;

   String cancelVwl(String var1) throws Exception;

   List getDeviceTagsByGroupIds(V2CommonGroupIds var1) throws SQLException;

   V2CommonGroupIds analysticEnable(Long var1) throws Exception;

   V2CommonGroupIds analysticCancel(Long var1) throws Exception;

   Boolean checkAllDevicesTagged(Long var1) throws Exception;
}
