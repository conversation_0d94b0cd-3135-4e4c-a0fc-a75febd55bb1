package com.samsung.magicinfo.dms.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.dms.service.MessageScheduleGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "Basic Schedules Management System Rest api",
   description = "Operations pertaining to schedules in Basic Schedules Management System",
   tags = {"Schedule API Group"}
)
@RestController
@RequestMapping({"/restapi/v1.0/dms/schedule/messages/groups"})
public class MessageScheduleGroupController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private MessageScheduleGroupService MessageScheduleGroupService;

   public MessageScheduleGroupController() {
      super();
   }

   @ApiOperation(
      value = "get message default group",
      notes = "get message default group.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getRootGroups() {
      this.logger.info("[REST][MESSAGE SCHEDULE GROUP][getRootGroups] get message default group");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.MessageScheduleGroupService.getRootGroups();
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][MESSAGE SCHEDULE GROUP][getRootGroups] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][MESSAGE SCHEDULE GROUP][getRootGroups] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         this.logger.error("[REST][MESSAGE SCHEDULE GROUP][getRootGroups] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var3.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][MESSAGE SCHEDULE GROUP][getRootGroups] Exception is occured. " + var4.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get message group information by groupId",
      notes = "get message group information by groupId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{groupId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listMessageScheduleGroupInfo(@PathVariable Long groupId) throws Exception {
      this.logger.info("[REST][MESSAGE SCHEDULE GROUP][listMessageScheduleGroupInfo] get group information by groupId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.MessageScheduleGroupService.listGroupInfo(groupId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][MESSAGE SCHEDULE GROUP][listMessageScheduleGroupInfo][" + groupId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][MESSAGE SCHEDULE GROUP][listMessageScheduleGroupInfo][" + groupId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][MESSAGE SCHEDULE GROUP][listMessageScheduleGroupInfo][" + groupId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][MESSAGE SCHEDULE GROUP][listMessageScheduleGroupInfo][" + groupId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get group information by groupId",
      notes = "get group information by groupId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{groupId}/child"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getChildGroups(@PathVariable("groupId") String groupId) {
      this.logger.info("[REST][MESSAGE SCHEDULE GROUP][getChildGroups] get group information by groupId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.MessageScheduleGroupService.getChildGroups(groupId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][MESSAGE SCHEDULE GROUP][getChildGroups][" + groupId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][MESSAGE SCHEDULE GROUP][getChildGroups][" + groupId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][MESSAGE SCHEDULE GROUP][getChildGroups][" + groupId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][MESSAGE SCHEDULE GROUP][getChildGroups][" + groupId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }
}
