package com.samsung.common.exception;

public class ExceptionCode {
   public static final String[] HTTP200 = new String[]{"200", "HTTP", "OK"};
   public static final String[] HTTP202 = new String[]{"202", "HTTP", "Accepted"};
   public static final String[] HTTP204 = new String[]{"204", "HTTP", "No Content"};
   public static final String[] HTTP400 = new String[]{"400", "HTTP", "Bad Request"};
   public static final String[] HTTP403 = new String[]{"403", "HTTP", "Forbidden"};
   public static final String[] HTTP404 = new String[]{"404", "HTTP", "Not Found"};
   public static final String[] HTTP405 = new String[]{"405", "HTTP", "Method Not Allowed"};
   public static final String[] HTTP500 = new String[]{"500", "HTTP", "Internal Server Error"};
   public static final String[] HTTP501 = new String[]{"501", "HTTP", "Not Implemented"};
   public static final String[] HTTP503 = new String[]{"503", "HTTP", "Service Unavailable"};
   public static final String[] HTTP601 = new String[]{"601", "HTTP", "Key Exchange Error"};
   public static final String[] HTTP602 = new String[]{"602", "HTTP", "Trigger Error"};
   public static final String[] SWS501 = new String[]{"501", "SWS", "SOAP 1.2 Syntax  Error"};
   public static final String[] SWS502 = new String[]{"502", "SWS", "SOAP Version Mismatch"};
   public static final String[] SWS503 = new String[]{"503", "SWS", "Cannot process Mandatory Element"};
   public static final String[] SWS504 = new String[]{"504", "SWS", "Bad Encoding Error"};
   public static final String[] SWS505 = new String[]{"505", "SWS", "WS-Addressing Error"};
   public static final String[] SWS506 = new String[]{"506", "SWS", "WS-Eventing"};
   public static final String[] SWS507 = new String[]{"507", "SWS", "WS-Security Auth Error"};
   public static final String[] SWS508 = new String[]{"508", "SWS", "WS-Security Encryption Error"};
   public static final String[] SWS509 = new String[]{"509", "SWS", "WS-Security Digital Signature Error"};
   public static final String[] SWS510 = new String[]{"510", "SWS", "Attachment Error"};
   public static final String[] SWS512 = new String[]{"512", "SWS", "Invalid Action"};
   public static final String[] SWS513 = new String[]{"513", "SWS", "\"setTriggerCycle\" method is not supported in normal binding mode."};
   public static final String[] SWS514 = new String[]{"514", "SWS", "Unsupported Service Name"};
   public static final String[] SWS515 = new String[]{"515", "SWS", "Not valid XML"};
   public static final String[] SWS516 = new String[]{"516", "SWS", "Not supported operation"};
   public static final String[] SWS517 = new String[]{"517", "SWS", "Schema Validation Error"};
   public static final String[] SWS518 = new String[]{"518", "SWS", "DeviceSN is incorrect"};
   public static final String[] SVC519 = new String[]{"519", "SWS", "Message from not approved device"};
   public static final String[] SWS599 = new String[]{"599", "SWS", "Unknown exception from user defined web service."};
   public static final String[] SRM600 = new String[]{"600", "SRM", "Syntax Error"};
   public static final String[] SRM601 = new String[]{"601", "SRM", "Invalid MO Path"};
   public static final String[] SRM602 = new String[]{"602", "SRM", "Invalid MO Value"};
   public static final String[] SRM603 = new String[]{"603", "SRM", "Access Violation"};
   public static final String[] SRM604 = new String[]{"604", "SRM", "Invalid input argument"};
   public static final String[] SRM605 = new String[]{"605", "SRM", "Invalid output argument"};
   public static final String[] SRM606 = new String[]{"606", "SRM", "Invalid Index"};
   public static final String[] SRM607 = new String[]{"607", "SRM", "No MO tree is loaded for this device model"};
   public static final String[] APP700 = new String[]{"700", "Application", "Not supported REPORT_INDICATED In COMMAND"};
   public static final String[] APP701 = new String[]{"701", "Application", "Transaction Failed"};
   public static final String[] APP702 = new String[]{"702", "Application", "Database Failed"};
   public static final String[] APP703 = new String[]{"703", "Application", "Invalid Configuration Error"};
   public static final String[] APP704 = new String[]{"704", "Application", "File System Failed"};
   public static final String[] APP705 = new String[]{"705", "Application", "Scheduler Failed"};
   public static final String[] APP706 = new String[]{"706", "Application", "etc"};
   public static final String[] APP712 = new String[]{"712", "Application", "Interval value has to be more than 0"};
   public static final String[] APP713 = new String[]{"713", "Application", "There has no service to be matched."};
   public static final String[] APP714 = new String[]{"714", "Application", "Can't find ActivityContext for message-id."};
   public static final String[] APP715 = new String[]{"715", "Application", "DeviceInfo is required to send the request message."};
   public static final String[] APP716 = new String[]{"716", "Application", "File not found"};
   public static final String[] APP717 = new String[]{"717", "Application", "Can't find the service data session."};
   public static final String[] APP718 = new String[]{"718", "Application", "A Message is cannot Interfacing"};
   public static final String[] APP719 = new String[]{"719", "Application", "Invalid Address Information Message"};
   public static final String[] APP720 = new String[]{"720", "Application", "Can't find Indirect Addressing Context"};
   public static final String[] SVC800 = new String[]{"800", "Service", "General Service Activity Exception"};
   public static final String[] SVC801 = new String[]{"801", "Service", "RM device is not ready for download"};
   public static final String[] SVC802 = new String[]{"802", "Service", "RM device failed to donwload contents from URL"};
   public static final String[] SVC803 = new String[]{"803", "Service", "RM device failed to install Contents"};
   public static final String[] SVC805 = new String[]{"805", "Service", "RM device failed to install Rules"};
   public static final String[] SVC807 = new String[]{"807", "Service", "Report for COMMAND is not arrived from the device"};
   public static final String[] SVC808 = new String[]{"808", "Service", "RM device failed to donwload the software from URL"};
   public static final String[] SVC809 = new String[]{"809", "Service", "Requested contents not available"};
   public static final String[] SVC810 = new String[]{"810", "Service", "Avaliable memory size is smaller than the content size."};
   public static final String[] SVC811 = new String[]{"811", "Service", "Failed to send the request message"};
   public static final String[] SVC812 = new String[]{"812", "Service", "Failed to send the response message for the device request."};
   public static final String[] SVC813 = new String[]{"813", "Service", "RM device sent an invalid reponse message."};
   public static final String[] SVC814 = new String[]{"814", "Service", "RM device sned an invalid alarm."};
   public static final String[] SVC815 = new String[]{"815", "Service", "RM device send an invalid report."};
   public static final String[] SVC816 = new String[]{"816", "Service", "RM device failed to download contents."};
   public static final String[] SVC817 = new String[]{"817", "Service", "RM device failed to download the rule file."};
   public static final String[] SVC818 = new String[]{"818", "Service", "Failed to subscribe RM device event."};
   public static final String[] SVC819 = new String[]{"819", "Service", "RM device failed to download the rule file from URL"};
   public static final String[] SVC820 = new String[]{"820", "Service", "RM device failed to download the software."};
   public static final String[] SVC821 = new String[]{"821", "Service", "RM device failed to install the software"};
   public static final String[] SVC822 = new String[]{"822", "Service", "Not Supported Version"};
   public static final String[] SVC823 = new String[]{"823", "Service", "Service response time out"};
   public static final String[] SVC824 = new String[]{"824", "Service", "Device message time out"};
   public static final String[] SVC825 = new String[]{"825", "Service", "Too many requests"};
   public static final String[] RES900 = new String[]{"900", "Responsive", " Connection refused"};
   public static final String[] RES902 = new String[]{"902", "Responsive", "Cannot delete.(used in playlist or schedule)"};
   public static final String[] RES903 = new String[]{"903", "Responsive", "This ID is already in use."};
   public static final String[] RES904 = new String[]{"904", "Responsive", "The name is duplicated."};
   public static final String[] RES905 = new String[]{"905", "Responsive", "This ID does not exist"};
   public static final String[] RES906 = new String[]{"906", "Responsive", "The content does not exist"};
   public static final String[] RES907 = new String[]{"907", "Responsive", "The unapproved content does not exist"};
   public static final String[] RES908 = new String[]{"908", "Responsive", "This playlist does not exist."};
   public static final String[] RES909 = new String[]{"909", "Responsive", "Failed to update data."};
   public static final String[] RES910 = new String[]{"910", "Responsive", "Device response timeout exception."};
   public static final String[] RES911 = new String[]{"911", "Responsive", "Content's priority is bigger than playlist's."};
   public static final String[] RES912 = new String[]{"912", "Responsive", "Failed to create a playlist"};
   public static final String[] RES913 = new String[]{"913", "Responsive", "Not supported device type and version for syncplaylist"};
   public static final String[] RES914 = new String[]{"914", "Responsive", "The number of contents by group does not match."};
   public static final String[] RES915 = new String[]{"915", "Responsive", "Playback time cannot be zero."};
   public static final String[] RES916 = new String[]{"916", "Responsive", "Content group not entered."};
   public static final String[] RES917 = new String[]{"917", "Responsive", "Invalid content ID."};
   public static final String[] RES918 = new String[]{"918", "Responsive", "Duplicated content order."};
   public static final String[] REST1000 = new String[]{"1000", "REST2.0", "Unknown error occurred."};
   public static final String[] REST1001 = new String[]{"1001", "REST2.0", "Access denied."};
   public static final String[] REST1002 = new String[]{"1002", "REST2.0", "Invalid input parameter."};
   public static final String[] REST1003 = new String[]{"1003", "REST2.0", "Not enough disk space."};
   public static final String[] NOT_AUTHORIZED_USER = new String[]{"401000", "Manager", "The user is not authorized."};
   public static final String[] EMPTY_USER_ID = new String[]{"400700", "Manager", "The user Id is empty."};
   public static final String[] PASSWORD_VALIDATION_1 = new String[]{"400701", "Manager", "The password is empty."};
   public static final String[] PASSWORD_VALIDATION_2 = new String[]{"400704", "Manager", "The password cannot include user id."};
   public static final String[] PASSWORD_VALIDATION_3 = new String[]{"400705", "Manager", "Cannot use a three digit serial number or repeat the same character three or more times."};
   public static final String[] PASSWORD_VALIDATION_4 = new String[]{"400709", "Manager", "Cannot use blank."};
   public static final String[] PASSWORD_VALIDATION_5 = new String[]{"400723", "Manager", "The length of password is invalid."};
   public static final String[] PASSWORD_VALIDATION_6 = new String[]{"400726", "Manager", "The combination of password is invalid."};
   public static final String[] PASSWORD_VALIDATION_7 = new String[]{"400728", "Manager", "The combination of password is valid, but length is invalid."};
   public static final String[] PASSWORD_VALIDATION_8 = new String[]{"400722", "Manager", "Confirm password is empty."};
   public static final String[] PASSWORD_VALIDATION_9 = new String[]{"400724", "Manager", "Confirm password does not match password."};
   public static final String[] PASSWORD_VALIDATION_10 = new String[]{"400707", "Manager", "Password cannot include phone number."};
   public static final String[] PASSWORD_VALIDATION_11 = new String[]{"400708", "Manager", "Password that be used recently cannot be used again."};
   public static final String[] PASSWORD_VALIDATION_12 = new String[]{"400706", "Manager", "New password cannot be the same as the old password."};
   public static final String[] ADMIN_WITHDRAW_NOT_ALLOWED = new String[]{"400812", "Manager", "Admin cannot withdraw."};
   public static final String[] FAIL_OPEN_FILE = new String[]{"3000", "Manager", "fail to I/O a file."};

   public ExceptionCode() {
      super();
   }
}
