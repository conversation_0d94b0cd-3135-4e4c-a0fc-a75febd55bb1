package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.logger.LoggingManagerV2;
import java.sql.Connection;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class AreaCompareListDao {
   Logger logger = LoggingManagerV2.getLogger(AreaCompareListDao.class);

   public AreaCompareListDao() {
      super();
   }

   public AreaCompareListDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public List getMyAreaCompareList(String user_id) {
      throw new UnsupportedOperationException("The AreaCompareListDao#getMyAreaCompareList method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getCompareAreaList(String user_id, int compare_id) {
      throw new UnsupportedOperationException("The AreaCompareListDao#getMyAreaCompareList method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public int getExistingAreaNumber(String user_id) {
      throw new UnsupportedOperationException("The AreaCompareListDao#getExistingAreaNumber method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean checkWhetherAreaAlreadyExist(String user_id, String area_id) {
      throw new UnsupportedOperationException("The AreaCompareListDao#checkWhetherAreaAlreadyExist method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public int insertMyCompareAreaList(String user_id, String area_id) {
      throw new UnsupportedOperationException("The AreaCompareListDao#insertMyCompareAreaList method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getUnSavedAreaCompareList(String user_id) {
      throw new UnsupportedOperationException("The AreaCompareListDao#getUnSavedAreaCompareList method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public int deleteCompareArea(String user_id, String area_id) {
      throw new UnsupportedOperationException("The AreaCompareListDao#deleteCompareArea method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public int deleteCompareAreaList(String user_id, int compare_id) {
      throw new UnsupportedOperationException("The AreaCompareListDao#deleteCompareAreaList method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }
}
