package com.samsung.magicinfo.cms.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.cms.model.ContentGroupResource;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("ContentGroupService")
@Transactional
public class ContentGroupServiceImpl implements ContentGroupService {
   protected final Log logger = LogFactory.getLog(this.getClass());
   ContentInfo cInfo = ContentInfoImpl.getInstance();
   ContentInfo contentGroupDao = ContentInfoImpl.getInstance();
   ContentDao cmsDao = new ContentDao();
   ContentDao contentTreeDao = new ContentDao();
   LeftMenuGroupTreeDao treeDao = new LeftMenuGroupTreeDao();

   public ContentGroupServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody listDefaultGroup() throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         new ArrayList();
         User user = SecurityUtils.getLoginUser();
         String userId = null;
         if (user == null) {
            throw new AccessDeniedException("no userId");
         } else {
            userId = user.getUser_id();
            ArrayList lists = new ArrayList();
            List tmplists = this.contentGroupDao.getGroupList(userId);
            if (tmplists != null) {
               for(int i = 0; i < tmplists.size(); ++i) {
                  Group group = (Group)tmplists.get(i);
                  if (group.getP_group_id() <= 0L) {
                     ContentGroupResource resource = new ContentGroupResource();
                     resource.setCreateDate(group.getCreate_date());
                     resource.setCreatorId(group.getCreator_id());
                     resource.setGroupDepth(group.getGroup_depth());
                     resource.setGroupId(group.getGroup_id());
                     resource.setGroupName(group.getGroup_name());
                     resource.setIndex(group.getIndex());
                     resource.setOrganizationId(group.getOrganization_id());
                     resource.setParentGroupId(group.getP_group_id());
                     lists.add(resource);
                  }
               }
            }

            responsebody.setItems(lists);
            responsebody.setStatus("Success");
            return responsebody;
         }
      } catch (Exception var10) {
         this.logger.error("", var10);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var10.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody getGroup(String groupId) throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         ContentGroupResource group = null;
         long id = Long.valueOf(groupId);
         Group tmpGroup = this.contentGroupDao.getGroupInfo(id);
         if (tmpGroup != null) {
            group = new ContentGroupResource();
            group.setCreateDate(tmpGroup.getCreate_date());
            group.setCreatorId(tmpGroup.getCreator_id());
            group.setGroupDepth(tmpGroup.getGroup_depth());
            group.setGroupId(tmpGroup.getGroup_id());
            group.setGroupName(tmpGroup.getGroup_name());
            group.setIndex(tmpGroup.getIndex());
            group.setOrganizationId(tmpGroup.getOrganization_id());
            group.setParentGroupId(tmpGroup.getP_group_id());
         }

         responsebody.setItems(group);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (SQLException var7) {
         this.logger.error("", var7);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var7.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody listChildGroup(String groupId) throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         List lists = new ArrayList();
         User user = SecurityUtils.getLoginUser();
         String userId = null;
         if (user == null) {
            throw new AccessDeniedException("no userId");
         } else {
            userId = user.getUser_id();
            if (groupId != null && !groupId.equals("0") && !groupId.equals("")) {
               List tmp = this.contentGroupDao.getChildGroupList(Long.valueOf(groupId), false, userId);
               if (tmp != null) {
                  for(int i = 0; i < tmp.size(); ++i) {
                     Group tmpGroup = (Group)tmp.get(i);
                     ContentGroupResource resource = new ContentGroupResource();
                     resource.setCreateDate(tmpGroup.getCreate_date());
                     resource.setCreatorId(tmpGroup.getCreator_id());
                     resource.setGroupDepth(tmpGroup.getGroup_depth());
                     resource.setGroupId(tmpGroup.getGroup_id());
                     resource.setGroupName(tmpGroup.getGroup_name());
                     resource.setIndex(tmpGroup.getIndex());
                     resource.setOrganizationId(tmpGroup.getOrganization_id());
                     resource.setParentGroupId(tmpGroup.getP_group_id());
                     lists.add(resource);
                  }
               }
            }

            responsebody.setItems(lists);
            responsebody.setStatus("Success");
            return responsebody;
         }
      } catch (Exception var10) {
         this.logger.error(var10.getMessage(), var10);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var10.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public Map createGroup(Group group) throws Exception {
      Map resultMap = new HashMap();
      String parentGroupId = group.getP_group_id().toString();
      String newName = group.getGroup_name();
      String table = "MI_CMS_INFO_CONTENT_GROUP";
      String menu = "MyContent";
      Long root_group_id = null;

      try {
         User user = SecurityUtils.getLoginUser();
         String userId = null;
         if (user != null) {
            userId = user.getUser_id();
            long depth = this.treeDao.get_GroupDepth(parentGroupId, table);
            root_group_id = this.contentTreeDao.getRoot_GroupId(parentGroupId);
            boolean var12 = this.treeDao.checkNodeName(newName, "", Integer.parseInt(parentGroupId), table, "ContentPlaylist", userId);
            if (var12) {
               resultMap.put("HttpStatus", HttpStatus.CONFLICT);
               return resultMap;
            } else {
               int group_id = this.treeDao.setGroupTreeCreate(menu, table, parentGroupId, newName, depth + "", root_group_id, userId);
               resultMap.put("HttpStatus", HttpStatus.CREATED);
               resultMap.put("group_id", group_id);
               return resultMap;
            }
         } else {
            throw new AccessDeniedException("no userId");
         }
      } catch (SQLException var14) {
         this.logger.error("", var14);
         resultMap.put("HttpStatus", HttpStatus.INTERNAL_SERVER_ERROR);
         return resultMap;
      }
   }
}
