package com.samsung.magicinfo.dms.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.dms.service.ContentScheduleGroupService;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "Basic Schedules Management System Rest api",
   description = "Operations pertaining to schedules in Basic Schedules Management System",
   tags = {"Schedule API Group"}
)
@RestController
@RequestMapping({"/restapi/v1.0/dms/schedule/contents/groups"})
public class ContentScheduleGroupController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private ContentScheduleGroupService ContentScheduleGroupService;

   public ContentScheduleGroupController() {
      super();
   }

   @ApiOperation(
      value = "ProgramGroup list",
      notes = "get all content schedule group organization.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getRootGroups() {
      this.logger.info("[REST][CONTENT SCHEDULE GROUP][getRootGroups] get content default group");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.ContentScheduleGroupService.getRootGroups();
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][CONTENT SCHEDULE GROUP][getRootGroups] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT SCHEDULE GROUP][getRootGroups] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         this.logger.error("[REST][CONTENT SCHEDULE GROUP][getRootGroups] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var3.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][CONTENT SCHEDULE GROUP][getRootGroups] Exception is occured. " + var4.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get contents group information by groupId",
      notes = "get contents group information by groupId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{groupId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listContentScheduleGroupInfo(@PathVariable Long groupId) throws Exception {
      this.logger.info("[REST][CONTENT SCHEDULE GROUP][listContentScheduleGroupInfo] get group information by groupId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.ContentScheduleGroupService.listGroupInfo(groupId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][CONTENT SCHEDULE GROUP][listContentScheduleGroupInfo][" + groupId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT SCHEDULE GROUP][listContentScheduleGroupInfo][" + groupId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][CONTENT SCHEDULE GROUP][listContentScheduleGroupInfo][" + groupId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][CONTENT SCHEDULE GROUP][listContentScheduleGroupInfo][" + groupId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "Program list",
      notes = "get child content schedule group lists by group id.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{groupId}/child"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getChildGroups(@PathVariable("groupId") String groupId) {
      this.logger.info("[REST][CONTENT SCHEDULE GROUP][getChildGroups] get group information by groupId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.ContentScheduleGroupService.getChildGroups(groupId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][CONTENT SCHEDULE GROUP][getChildGroups][" + groupId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT SCHEDULE GROUP][getChildGroups][" + groupId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][CONTENT SCHEDULE GROUP][getChildGroups][" + groupId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][CONTENT SCHEDULE GROUP][getChildGroups][" + groupId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get group information by groupId",
      notes = "get group information by groupId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{groupId}"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity createContentScheduleGroupInfo(@PathVariable Long groupId, @RequestParam("name") String name) throws Exception {
      this.logger.info("[REST][CONTENT SCHEDULE GROUP][createContentScheduleGroupInfo] get group information by groupId");
      ResponseBody responsebody = new ResponseBody();

      try {
         ProgramGroup group = new ProgramGroup();
         group.setP_group_id(groupId);
         group.setGroup_name(name);
         responsebody = this.ContentScheduleGroupService.createGroup(group);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][CONTENT SCHEDULE GROUP][createContentScheduleGroupInfo][" + groupId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT SCHEDULE GROUP][createContentScheduleGroupInfo][" + groupId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var5) {
         this.logger.error("[REST][CONTENT SCHEDULE GROUP][createContentScheduleGroupInfo][" + groupId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][CONTENT SCHEDULE GROUP][createContentScheduleGroupInfo][" + groupId + "] Exception is occured. " + var6.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }
}
