package com.samsung.magicinfo.cms.model;

import java.util.ArrayList;
import java.util.List;

public class ContentDeleteResource {
   private List deletedContentIds = new ArrayList();
   private String status = "";
   private String undeletableContentIds = "";
   private List contentNameList = new ArrayList();
   private List refPlaylists = new ArrayList();
   private List refSchedules = new ArrayList();
   private List refEvents = new ArrayList();

   public ContentDeleteResource() {
      super();
   }

   public List getDeletedContentIds() {
      return this.deletedContentIds;
   }

   public void setDeletedContentIds(List deletedContentIds) {
      this.deletedContentIds = deletedContentIds;
   }

   public String getStatus() {
      return this.status;
   }

   public void setStatus(String status) {
      this.status = status;
   }

   public String getUndeletableContentIds() {
      return this.undeletableContentIds;
   }

   public void setUndeletableContentIds(String undeletableContentIds) {
      this.undeletableContentIds = undeletableContentIds;
   }

   public List getContentNameList() {
      return this.contentNameList;
   }

   public void setContentNameList(List contentNameList) {
      this.contentNameList = contentNameList;
   }

   public List getRefPlaylists() {
      return this.refPlaylists;
   }

   public void setRefPlaylists(List refPlaylists) {
      this.refPlaylists = refPlaylists;
   }

   public List getRefSchedules() {
      return this.refSchedules;
   }

   public void setRefSchedules(List refSchedules) {
      this.refSchedules = refSchedules;
   }

   public List getRefEvents() {
      return this.refEvents;
   }

   public void setRefEvents(List refEvents) {
      this.refEvents = refEvents;
   }
}
