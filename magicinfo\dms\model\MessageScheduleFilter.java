package com.samsung.magicinfo.dms.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Max;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class MessageScheduleFilter {
   @ApiModelProperty(
      example = "modify_date"
   )
   @Pattern(
      regexp = "modify_date",
      message = "[MessageScheduleFilter][sortColumn] Only modify_date are available."
   )
   private String sortColumn = null;
   @ApiModelProperty(
      example = "desc"
   )
   @Pattern(
      regexp = "desc|asc",
      message = "[MessageScheduleFilter][sortOrder] Only asc, desc are available."
   )
   private String sortOrder = null;
   @ApiModelProperty(
      example = "message"
   )
   @Size(
      max = 20,
      message = "[MessageScheduleFilter][searchText] max size is 20."
   )
   private String searchText = null;
   @ApiModelProperty(
      example = "0"
   )
   private int startIndex;
   @ApiModelProperty(
      example = "10"
   )
   @Max(50L)
   private int pageSize;
   @ApiModelProperty(
      example = "ALL"
   )
   private String groupType = "ALL";
   @JsonIgnore
   private String messageId = null;
   @ApiModelProperty(
      example = "0"
   )
   private String groupId = null;
   @JsonIgnore
   private String groupName = null;
   @ApiModelProperty(
      example = "ALL"
   )
   private String status;
   @ApiModelProperty(
      example = "SPLAYER"
   )
   private String deviceType = null;
   @ApiModelProperty(
      example = "2.0"
   )
   private Float deviceTypeVersion = null;

   public MessageScheduleFilter() {
      super();
      this.searchText = "";
      this.sortOrder = "desc";
      this.pageSize = 10;
      this.sortColumn = "modify_date";
      this.startIndex = 0;
      this.status = "ALL";
      this.messageId = null;
   }

   public String getSortColumn() {
      return this.sortColumn;
   }

   public void setSortColumn(String sortColumn) {
      this.sortColumn = sortColumn;
   }

   public String getSortOrder() {
      return this.sortOrder;
   }

   public void setSortOrder(String sortOrder) {
      this.sortOrder = sortOrder;
   }

   public String getSearchText() {
      return this.searchText;
   }

   public void setSearchText(String searchText) {
      this.searchText = searchText;
   }

   public int getStartIndex() {
      return this.startIndex;
   }

   public void setStartIndex(int startIndex) {
      this.startIndex = startIndex;
   }

   public int getPageSize() {
      return this.pageSize;
   }

   public void setPageSize(int pageSize) {
      this.pageSize = pageSize;
   }

   public String getGroupType() {
      return this.groupType;
   }

   public void setGroupType(String groupType) {
      this.groupType = groupType;
   }

   public String getMessageId() {
      return this.messageId;
   }

   public void setMessageId(String messageId) {
      this.messageId = messageId;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public String getGroupName() {
      return this.groupName;
   }

   public void setGroupName(String groupName) {
      this.groupName = groupName;
   }

   public String getStatus() {
      return this.status;
   }

   public void setStatus(String status) {
      this.status = status;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public Float getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(Float deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }
}
