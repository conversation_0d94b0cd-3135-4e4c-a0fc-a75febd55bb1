package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.setup.manager.ConfigEncryptionManagement;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Paths;
import org.apache.logging.log4j.Logger;
import org.ini4j.Wini;

public class Command {
   private static final int EXIT_CODE_SUCCESS = 0;
   private static final int EXIT_CODE_NO_ARGS = 1;
   private static final int EXIT_CODE_NOT_ENOUGH_ARGS = 2;
   private static final int EXIT_CODE_ERROR = 3;
   static Logger logger = LoggingManagerV2.getLogger(Command.class);

   public Command() {
      super();
   }

   public static void main(String[] args) {
      if (args.length == 0) {
         System.exit(1);
      }

      String cmd = args[0];
      byte var3 = -1;
      switch(cmd.hashCode()) {
      case -1607257499:
         if (cmd.equals("encrypt")) {
            var3 = 1;
         }
         break;
      case -1512664257:
         if (cmd.equals("encryptINI")) {
            var3 = 2;
         }
         break;
      case 886710442:
         if (cmd.equals("generateKey")) {
            var3 = 0;
         }
      }

      switch(var3) {
      case 0:
         String key = ConfigEncryptionManagement.getRandomKey(32);
         if (args.length == 2) {
            try {
               Files.write(Paths.get(args[1]), key.getBytes(), new OpenOption[0]);
            } catch (IOException var11) {
               logger.info(var11.getMessage());
               System.exit(3);
            }
         }
         break;
      case 1:
         if (args.length < 3) {
            System.err.println("arguments error. not enough arguments.");
            System.exit(2);
         }

         try {
            System.out.println(getEncryptString(args[1], args[2]));
         } catch (Exception var10) {
            logger.info(var10.getMessage());
            System.exit(3);
         }
         break;
      case 2:
         if (args.length != 5) {
            System.err.println("arguments error.");
            System.exit(2);
         }

         try {
            String encUserName = getEncryptString(args[1], args[3]);
            String encPassword = getEncryptString(args[2], args[3]);
            File file = new File(args[4]);
            if (!file.exists()) {
               file.createNewFile();
            }

            Wini iniFile = new Wini(file);
            iniFile.put("encrypted database", "username", encUserName);
            iniFile.put("encrypted database", "password", encPassword);
            iniFile.store();
         } catch (Exception var9) {
            logger.info(var9.getMessage());
         }
      }

      System.exit(0);
   }

   private static String getEncryptString(String str, String key) throws Exception {
      return ConfigEncryptionManagement.getEncryption(str, key);
   }
}
