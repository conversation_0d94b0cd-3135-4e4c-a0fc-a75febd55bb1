package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMemo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.restapi.device.model.V2DeviceMemoResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2DeviceMemoService")
@Transactional
public class V2DeviceMemoServiceImpl implements V2DeviceMemoService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceMemoServiceImpl.class);

   public V2DeviceMemoServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceMemoResource getDeviceMemo(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      String userId = SecurityUtils.getUserContainer().getUser().getUser_id();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceMemo memo = null;
      V2DeviceMemoResource resource = new V2DeviceMemoResource();

      try {
         memo = deviceInfo.getDeviceMemo(deviceId);
         if (memo != null) {
            resource.setUserId(userId);
            resource.setMemo(memo.getMemo());
            resource.setRegDate(memo.getReg_date());
         } else {
            resource.setUserId("");
            resource.setMemo("");
            resource.setRegDate((Timestamp)null);
         }
      } catch (Exception var7) {
         this.logger.error("", var7);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceMemoResource editDeviceMemo(String deviceId, String param_memo) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      AbilityUtils ability = new AbilityUtils();
      boolean checkDeviceWrite = ability.checkAuthority("Device Write");
      String userId = SecurityUtils.getUserContainer().getUser().getUser_id();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceMemo memo = null;
      V2DeviceMemoResource resource = new V2DeviceMemoResource();
      if (checkDeviceWrite) {
         memo = new DeviceMemo();
         memo.setMemo(param_memo);
         memo.setUserId(userId);
         memo.setDeviceId(deviceId);
         Date date = Calendar.getInstance().getTime();
         memo.setReg_date(new Timestamp(date.getTime()));
         boolean result = deviceInfo.setDeviceMemo("MODIFY", memo);
         if (result) {
            resource.setUserId(memo.getUserId());
            resource.setMemo(memo.getMemo());
            resource.setRegDate(memo.getReg_date());
            return resource;
         } else {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SAVE_FAIL, new String[]{"memo"});
         }
      } else {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceMemoResource delDeviceMemo(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      AbilityUtils ability = new AbilityUtils();
      boolean checkDeviceWrite = ability.checkAuthority("Device Write");
      String userId = SecurityUtils.getUserContainer().getUser().getUser_id();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceMemo memo = null;
      V2DeviceMemoResource resource = new V2DeviceMemoResource();
      if (checkDeviceWrite) {
         memo = new DeviceMemo();
         memo.setDeviceId(deviceId);
         memo.setUserId(userId);
         Date date = Calendar.getInstance().getTime();
         memo.setReg_date(new Timestamp(date.getTime()));

         try {
            deviceInfo.setDeviceMemo("DELETE", memo);
         } catch (SQLException var10) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL, new String[]{"memo"});
         }

         resource.setUserId(memo.getUserId());
         resource.setMemo(memo.getMemo());
         resource.setRegDate(memo.getReg_date());
         return resource;
      } else {
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
      }
   }
}
