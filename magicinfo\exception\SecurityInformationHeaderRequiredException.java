package com.samsung.magicinfo.exception;

import com.samsung.common.exception.BasicException;

public class SecurityInformationHeaderRequiredException extends BasicException {
   private static final long serialVersionUID = 4838480978318060707L;

   public SecurityInformationHeaderRequiredException(String reason) {
      super(reason);
   }

   public SecurityInformationHeaderRequiredException(String code, String reason) {
      super(code, reason);
   }

   public SecurityInformationHeaderRequiredException(String code, String subCode, String reason) {
      super(code, subCode, reason);
   }
}
