package com.samsung.common.utils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;
import java.util.StringTokenizer;
import java.util.TimeZone;

public class SimpleCalendar extends GregorianCalendar {
   public static final long serialVersionUID = 2168115492755451610L;
   public static final long ONE_SECOND = 1000L;
   public static final long ONE_MINUTE = 60000L;
   public static final long ONE_HOUR = 3600000L;
   public static final long ONE_DAY = 86400000L;
   public static final long ONE_MONTH = 2592000000L;
   public static final long ONE_YEAR = 31536000000L;

   public SimpleCalendar(int year, int month, int date, int hour, int minute, int second) {
      super(year, month, date, hour, minute, second);
   }

   public SimpleCalendar(int year, int month, int date, int hour, int minute) {
      super(year, month, date, hour, minute);
   }

   public SimpleCalendar(int year, int month, int date) {
      super(year, month, date);
   }

   public SimpleCalendar(String pattern) {
      this();
      this.makeDate(pattern);
   }

   public SimpleCalendar(long millis) {
      this();
      this.setTimeInMillis(millis);
   }

   public SimpleCalendar(Date date) {
      this();
      this.setTime(date);
   }

   public SimpleCalendar(TimeZone zone, Locale aLocale) {
      super(zone, aLocale);
   }

   public SimpleCalendar(Locale aLocale) {
      super(aLocale);
   }

   public SimpleCalendar(TimeZone zone) {
      super(zone);
   }

   public SimpleCalendar() {
      super();
   }

   public long getTimeInMillis() {
      return super.getTimeInMillis();
   }

   public void setTimeInMillis(long millis) {
      super.setTimeInMillis(millis);
   }

   private void makeDate(String pattern) {
      try {
         StringTokenizer stDate;
         int month;
         int date;
         if (pattern.length() != 19 && pattern.length() != 21) {
            if (pattern.length() == 10 && pattern.length() < 19) {
               stDate = new StringTokenizer(pattern, "- :");
               int year = Integer.parseInt(stDate.nextToken());
               month = Integer.parseInt(stDate.nextToken()) - 1;
               date = Integer.parseInt(stDate.nextToken());
               this.set(1, year);
               this.set(2, month);
               this.set(5, date);
               this.set(11, 0);
               this.set(12, 0);
               this.set(13, 0);
               this.set(14, 0);
            }
         } else {
            stDate = new StringTokenizer(pattern.substring(0, pattern.indexOf(" ")), "-");
            StringTokenizer stTime = new StringTokenizer(pattern.substring(pattern.indexOf(" ") + 1, pattern.length()), ":");
            month = Integer.parseInt(stDate.nextToken());
            date = Integer.parseInt(stDate.nextToken()) - 1;
            int date = Integer.parseInt(stDate.nextToken());
            int hour = Integer.parseInt(stTime.nextToken());
            int minute = Integer.parseInt(stTime.nextToken());
            int second = Integer.parseInt(stTime.nextToken().substring(0, 2));
            this.set(0, 1);
            this.set(1, month);
            this.set(2, date);
            this.set(5, date);
            this.set(11, hour);
            this.set(12, minute);
            this.set(13, second);
            this.set(14, 0);
         }
      } catch (Exception var10) {
      }

   }

   public String getYear() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getMonth() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("MM");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getDay() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("dd");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getDayOfWeek() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("EEE");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getHour() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("hh");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getHour24() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("HH");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getMinute() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("mm");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getSecond() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("ss");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getDate() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getTimes() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getDateTime() {
      SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public String getDateTime(String pattern) {
      SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
      return dateFormat.format(new Date(this.getTimeInMillis())).toString();
   }

   public Date convertToDate() {
      return new Date(this.getTimeInMillis());
   }

   public Timestamp convertToTimestamp() {
      return new Timestamp(this.getTimeInMillis());
   }

   public String toString() {
      return this.getDateTime();
   }
}
