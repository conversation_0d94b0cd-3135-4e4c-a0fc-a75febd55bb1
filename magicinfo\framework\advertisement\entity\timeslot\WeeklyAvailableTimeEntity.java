package com.samsung.magicinfo.framework.advertisement.entity.timeslot;

public class WeeklyAvailableTimeEntity {
   private int time = -1;
   private int sunday_type_a = 0;
   private int monday_type_a = 0;
   private int tuesday_type_a = 0;
   private int wednesday_type_a = 0;
   private int thursday_type_a = 0;
   private int friday_type_a = 0;
   private int saturday_type_a = 0;
   private int sunday_type_b = 0;
   private int monday_type_b = 0;
   private int tuesday_type_b = 0;
   private int wednesday_type_b = 0;
   private int thursday_type_b = 0;
   private int friday_type_b = 0;
   private int saturday_type_b = 0;
   private int sunday_type_c = 0;
   private int monday_type_c = 0;
   private int tuesday_type_c = 0;
   private int wednesday_type_c = 0;
   private int thursday_type_c = 0;
   private int friday_type_c = 0;
   private int saturday_type_c = 0;

   public WeeklyAvailableTimeEntity() {
      super();
   }

   public int getTime() {
      return this.time;
   }

   public void setTime(int time) {
      this.time = time;
   }

   public int getSunday_type_a() {
      return this.sunday_type_a;
   }

   public void setSunday_type_a(int sunday_type_a) {
      this.sunday_type_a = sunday_type_a;
   }

   public int getMonday_type_a() {
      return this.monday_type_a;
   }

   public void setMonday_type_a(int monday_type_a) {
      this.monday_type_a = monday_type_a;
   }

   public int getTuesday_type_a() {
      return this.tuesday_type_a;
   }

   public void setTuesday_type_a(int tuesday_type_a) {
      this.tuesday_type_a = tuesday_type_a;
   }

   public int getWednesday_type_a() {
      return this.wednesday_type_a;
   }

   public void setWednesday_type_a(int wednesday_type_a) {
      this.wednesday_type_a = wednesday_type_a;
   }

   public int getThursday_type_a() {
      return this.thursday_type_a;
   }

   public void setThursday_type_a(int thursday_type_a) {
      this.thursday_type_a = thursday_type_a;
   }

   public int getFriday_type_a() {
      return this.friday_type_a;
   }

   public void setFriday_type_a(int friday_type_a) {
      this.friday_type_a = friday_type_a;
   }

   public int getSaturday_type_a() {
      return this.saturday_type_a;
   }

   public void setSaturday_type_a(int saturday_type_a) {
      this.saturday_type_a = saturday_type_a;
   }

   public int getSunday_type_b() {
      return this.sunday_type_b;
   }

   public void setSunday_type_b(int sunday_type_b) {
      this.sunday_type_b = sunday_type_b;
   }

   public int getMonday_type_b() {
      return this.monday_type_b;
   }

   public void setMonday_type_b(int monday_type_b) {
      this.monday_type_b = monday_type_b;
   }

   public int getTuesday_type_b() {
      return this.tuesday_type_b;
   }

   public void setTuesday_type_b(int tuesday_type_b) {
      this.tuesday_type_b = tuesday_type_b;
   }

   public int getWednesday_type_b() {
      return this.wednesday_type_b;
   }

   public void setWednesday_type_b(int wednesday_type_b) {
      this.wednesday_type_b = wednesday_type_b;
   }

   public int getThursday_type_b() {
      return this.thursday_type_b;
   }

   public void setThursday_type_b(int thursday_type_b) {
      this.thursday_type_b = thursday_type_b;
   }

   public int getFriday_type_b() {
      return this.friday_type_b;
   }

   public void setFriday_type_b(int friday_type_b) {
      this.friday_type_b = friday_type_b;
   }

   public int getSaturday_type_b() {
      return this.saturday_type_b;
   }

   public void setSaturday_type_b(int saturday_type_b) {
      this.saturday_type_b = saturday_type_b;
   }

   public int getSunday_type_c() {
      return this.sunday_type_c;
   }

   public void setSunday_type_c(int sunday_type_c) {
      this.sunday_type_c = sunday_type_c;
   }

   public int getMonday_type_c() {
      return this.monday_type_c;
   }

   public void setMonday_type_c(int monday_type_c) {
      this.monday_type_c = monday_type_c;
   }

   public int getTuesday_type_c() {
      return this.tuesday_type_c;
   }

   public void setTuesday_type_c(int tuesday_type_c) {
      this.tuesday_type_c = tuesday_type_c;
   }

   public int getWednesday_type_c() {
      return this.wednesday_type_c;
   }

   public void setWednesday_type_c(int wednesday_type_c) {
      this.wednesday_type_c = wednesday_type_c;
   }

   public int getThursday_type_c() {
      return this.thursday_type_c;
   }

   public void setThursday_type_c(int thursday_type_c) {
      this.thursday_type_c = thursday_type_c;
   }

   public int getFriday_type_c() {
      return this.friday_type_c;
   }

   public void setFriday_type_c(int friday_type_c) {
      this.friday_type_c = friday_type_c;
   }

   public int getSaturday_type_c() {
      return this.saturday_type_c;
   }

   public void setSaturday_type_c(int saturday_type_c) {
      this.saturday_type_c = saturday_type_c;
   }
}
