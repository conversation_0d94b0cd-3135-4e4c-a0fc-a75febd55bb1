package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.FaceInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.FaceKey;
import com.samsung.magicinfo.framework.advertisement.entity.FaceRawEntity;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.apache.logging.log4j.Logger;

public class FaceContentBasedDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(FaceContentBasedDao.class);
   int DOW = 0;
   int QUARTER = 0;
   Timestamp DAY = null;
   Timestamp MONTH = null;
   Timestamp YEAR = null;

   public FaceContentBasedDao() {
      super();
   }

   private void setDayItems(String logDate) {
      try {
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         Date date = sdf.parse(logDate);
         Calendar cal = Calendar.getInstance();
         cal.setTime(date);
         this.DOW = cal.get(7) - 1;
         this.QUARTER = cal.get(2) / 3 + 1;
      } catch (ParseException var5) {
         this.logger.error("", var5);
      }

      this.DAY = Timestamp.valueOf(logDate + " 00:00:00");
      this.MONTH = Timestamp.valueOf(logDate.substring(0, 7) + "-01 00:00:00");
      this.YEAR = Timestamp.valueOf(logDate.substring(0, 4) + "-01-01 00:00:00");
   }

   public boolean insertFaceContentBasedHistroy(List play_history) throws SQLException {
      int totalRows = play_history.size();
      String[] common_info = (String[])play_history.get(0);
      String device_id = common_info[0];
      String log_date = common_info[1];
      boolean isDuplicated = false;
      this.setDayItems(log_date);
      HashMap mapRawByFaceKey = this.splitContentBasedHistoryByFaceKey(play_history, totalRows);
      if (mapRawByFaceKey.size() == 0) {
         this.logger.error("[STATISTICS] splitted map size is zero");
         return false;
      } else {
         HashMap mapDailyInfo = new HashMap();
         HashMap mapNewDailyInfoToMonth = new HashMap();
         HashMap mapNewDailyInfoToYear = new HashMap();
         Set keySet = mapRawByFaceKey.keySet();

         Iterator iterator;
         FaceKey faceKey;
         FaceInfoEntity originalYearPopInfo;
         for(iterator = keySet.iterator(); iterator.hasNext(); mapDailyInfo.put(faceKey, originalYearPopInfo)) {
            faceKey = (FaceKey)iterator.next();
            ArrayList faceRawList = (ArrayList)mapRawByFaceKey.get(faceKey);
            originalYearPopInfo = new FaceInfoEntity();

            try {
               this.addPopHourInfoList(device_id, faceKey, faceRawList, originalYearPopInfo);
            } catch (Exception var19) {
               isDuplicated = true;
               this.logger.error("[STATISTICS] - Blocking Hour-info-DAO insert by using duplicate key value violates - (device_id: " + device_id + " content_id: " + faceKey + ")");
            }
         }

         try {
            this.addPopDayInfoList(device_id, mapDailyInfo);
         } catch (Exception var18) {
            isDuplicated = true;
            this.logger.error("[STATISTICS] - Blocking Day-info-DAO insert by using duplicate key value violates - (device_id: " + device_id + " log_date: " + log_date + ")");
         }

         if (isDuplicated) {
            return false;
         } else {
            keySet = mapDailyInfo.keySet();
            iterator = keySet.iterator();

            while(iterator.hasNext()) {
               FaceKey faceKey = (FaceKey)iterator.next();
               FaceInfoEntity tmpPopInfoToAdd = (FaceInfoEntity)mapDailyInfo.get(faceKey);
               FaceInfoEntity originalMonthPopInfo = this.getPopMonthInfo(faceKey, device_id);
               if (originalMonthPopInfo != null) {
                  originalMonthPopInfo.addDuration(tmpPopInfoToAdd.getDuration());
                  originalMonthPopInfo.addViewCount(tmpPopInfoToAdd.getView_count());
                  this.setPopMonthInfo(faceKey, device_id, originalMonthPopInfo.getDuration(), originalMonthPopInfo.getView_count());
               } else {
                  mapNewDailyInfoToMonth.put(faceKey, tmpPopInfoToAdd);
               }

               originalYearPopInfo = this.getPopYearInfo(faceKey, device_id);
               if (originalYearPopInfo != null) {
                  originalYearPopInfo.addDuration(tmpPopInfoToAdd.getDuration());
                  originalYearPopInfo.addViewCount(tmpPopInfoToAdd.getView_count());
                  this.setPopYearInfo(faceKey, device_id, originalYearPopInfo.getDuration(), originalYearPopInfo.getView_count());
               } else {
                  mapNewDailyInfoToYear.put(faceKey, tmpPopInfoToAdd);
               }
            }

            if (mapNewDailyInfoToMonth.size() != 0) {
               this.addPopMonthInfoList(device_id, mapNewDailyInfoToMonth);
            }

            if (mapNewDailyInfoToYear.size() != 0) {
               this.addPopYearInfoList(device_id, log_date, mapNewDailyInfoToYear);
            }

            return true;
         }
      }
   }

   private FaceInfoEntity getPopMonthInfo(FaceKey faceKey, String device_id) throws SQLException {
      return ((FaceContentBasedDaoMapper)this.getMapper()).getPopMonthInfo(device_id, faceKey, this.MONTH);
   }

   private FaceInfoEntity getPopYearInfo(FaceKey faceKey, String device_id) throws SQLException {
      return ((FaceContentBasedDaoMapper)this.getMapper()).getPopYearInfo(device_id, faceKey, this.YEAR);
   }

   private void addPopDayInfoList(String deviceId, HashMap dailyPlayInfo) throws SQLException {
      ((FaceContentBasedDaoMapper)this.getMapper()).addPopDayInfoList(deviceId, dailyPlayInfo, this.DAY, this.DOW);
   }

   private HashMap splitContentBasedHistoryByFaceKey(List play_history, int totalRows) {
      HashMap mapRaw = new HashMap();

      for(int i = 1; i < totalRows; ++i) {
         String[] play_info = (String[])play_history.get(i);

         Timestamp start_time;
         try {
            start_time = Timestamp.valueOf(play_info[0]);
         } catch (IllegalArgumentException var11) {
            this.logger.error("[STATISTICS] IllegalArgumentException has been occured for " + play_info[0] + " index : " + i);
            continue;
         }

         FaceRawEntity cbse = new FaceRawEntity(start_time, Integer.valueOf(play_info[3]), Integer.valueOf(play_info[4]));
         FaceKey faceKey = new FaceKey(play_info[5], play_info[1], play_info[2]);
         ArrayList arrCbse;
         if ((arrCbse = (ArrayList)mapRaw.get(faceKey)) == null) {
            ArrayList newArrCbse = new ArrayList();
            newArrCbse.add(cbse);
            mapRaw.put(faceKey, newArrCbse);
         } else {
            arrCbse.add(cbse);
            mapRaw.put(faceKey, arrCbse);
         }
      }

      return mapRaw;
   }

   private void addPopHourInfoList(String deviceId, FaceKey faceKey, ArrayList faceRawEntity, FaceInfoEntity dailyPlayInfo) throws SQLException {
      Iterator var5 = faceRawEntity.iterator();

      while(var5.hasNext()) {
         FaceRawEntity rawEntity = (FaceRawEntity)var5.next();
         dailyPlayInfo.addViewCount(rawEntity.getCount());
         dailyPlayInfo.addDuration(rawEntity.getDuration());
      }

      ((FaceContentBasedDaoMapper)this.getMapper()).addPopHourInfoList(deviceId, faceKey, faceRawEntity);
   }

   private void addPopMonthInfoList(String deviceId, HashMap mapNewDailyInfo) throws SQLException {
      ((FaceContentBasedDaoMapper)this.getMapper()).addPopMonthInfoList(deviceId, mapNewDailyInfo, this.MONTH, this.QUARTER);
   }

   private void addPopYearInfoList(String deviceId, String logDate, HashMap mapNewDailyInfo) throws SQLException {
      Timestamp startTime = Timestamp.valueOf(logDate.substring(0, 4) + "-01-01 00:00:00");
      ((FaceContentBasedDaoMapper)this.getMapper()).addPopYearInfoList(deviceId, mapNewDailyInfo, startTime);
   }

   private void setPopMonthInfo(FaceKey faceKey, String device_id, int duration, int view_count) throws SQLException {
      ((FaceContentBasedDaoMapper)this.getMapper()).setPopMonthInfo(view_count, duration, this.MONTH, faceKey, device_id);
   }

   private void setPopYearInfo(FaceKey faceKey, String device_id, int duration, int view_count) throws SQLException {
      ((FaceContentBasedDaoMapper)this.getMapper()).setPopYearInfo(view_count, duration, this.YEAR, faceKey, device_id);
   }

   public boolean checkDuplicationByDB(String log_date, String device_id) throws SQLException {
      Long count = ((FaceContentBasedDaoMapper)this.getMapper()).checkDuplicationByDB(Timestamp.valueOf(log_date + " 00:00:00"), device_id);
      return count < 1L;
   }
}
