package com.samsung.common.utils.page;

import com.samsung.common.logger.LoggingManagerV2;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import org.apache.logging.log4j.Logger;

public class PageManager {
   static Logger logger = LoggingManagerV2.getLogger(PageManager.class);
   private String currentURL;
   private String contextPath;
   private int currentPage;
   private int totalRowCount;
   private int pageSize;
   private Hashtable queryString;
   private String type;
   private ListInfo info;

   public PageManager() {
      this("default");
   }

   public PageManager(String type) {
      this(type, 1);
   }

   public PageManager(String type, int totalCount) {
      this(type, totalCount, 1);
   }

   public PageManager(String type, int totalCount, int currentPage) {
      this(type, totalCount, currentPage, (String)null);
   }

   public PageManager(String type, int totalCount, int currentPage, String url) {
      super();
      this.queryString = new Hashtable();
      this.type = "default";
      this.info = null;
      this.setType(type);
      this.setCurrentPage(currentPage);
      this.setTotalRowCount(totalCount);
      this.setCurrentURL(url);
      this.setPageSize(this.info.getPageSize());
   }

   private void setType(String _type) {
      this.type = _type;
      this.info = ListInfo.getListInfo(this.type);
   }

   public void setCurrentPage(int i) {
      this.currentPage = i;
   }

   public void setTotalRowCount(int cnt) {
      this.totalRowCount = cnt;
   }

   public void setPageSize(int cnt) {
      this.pageSize = cnt;
   }

   public void setCurrentURL(String url) {
      this.currentURL = url;
   }

   public void setContextPath(String path) {
      this.contextPath = path;
   }

   public int getTotalRowCount() {
      return this.totalRowCount;
   }

   public int getPageSize() {
      return this.pageSize;
   }

   public int getCurrentPage() {
      if (this.getTotalRowCount() == 0) {
         this.currentPage = 0;
      }

      return this.currentPage;
   }

   public String getCurrnetURL() {
      return this.currentURL;
   }

   public int getTotalPageCount() throws Exception {
      try {
         int totalPageCount = 0;
         if (this.totalRowCount > 0) {
            totalPageCount = this.totalRowCount / this.pageSize;
            if (totalPageCount * this.pageSize - this.totalRowCount != 0) {
               ++totalPageCount;
            }
         }

         return totalPageCount;
      } catch (ArithmeticException var6) {
         throw new Exception("Doesn't read " + this.type + ".pagesize from list.properties");
      } finally {
         ;
      }
   }

   public String getFirstLinkedPage() {
      int totPgCnt = 0;

      try {
         totPgCnt = this.getTotalPageCount();
      } catch (Exception var6) {
         logger.error("", var6);
      } finally {
         ;
      }

      if (this.totalRowCount != 0 && totPgCnt != 1 && this.currentPage != 1) {
         return this.currentPage == 1 ? this.makePageLinkWithURL(String.valueOf(this.currentPage), this.info.getFirstSymbol()) : this.makePageLinkWithURL(String.valueOf(1), this.info.getFirstSymbol());
      } else {
         return this.makePageLinkWithoutURL(String.valueOf(this.currentPage), this.info.getFirstSymbol());
      }
   }

   public String getPreviousLinkedPage() {
      int totPgCnt = 0;

      try {
         totPgCnt = this.getTotalPageCount();
      } catch (Exception var6) {
         logger.error("", var6);
      } finally {
         ;
      }

      if (this.totalRowCount != 0 && totPgCnt != 1 && this.currentPage != 1) {
         return this.currentPage == 1 ? this.makePageLinkWithURL(String.valueOf(this.currentPage), this.info.getPrevSymbol()) : this.makePageLinkWithURL(String.valueOf(this.currentPage - 1), this.info.getPrevSymbol());
      } else {
         return this.makePageLinkWithoutURL(String.valueOf(this.currentPage), this.info.getPrevSymbol());
      }
   }

   public String getNextLinkedPage() {
      int totPgCnt = 0;

      try {
         totPgCnt = this.getTotalPageCount();
      } catch (Exception var6) {
         logger.error("", var6);
      } finally {
         ;
      }

      if (this.totalRowCount != 0 && totPgCnt != 1 && this.currentPage != totPgCnt) {
         return this.currentPage >= totPgCnt ? this.makePageLinkWithURL(String.valueOf(this.currentPage), this.info.getNextSymbol()) : this.makePageLinkWithURL(String.valueOf(this.currentPage + 1), this.info.getNextSymbol());
      } else {
         return this.makePageLinkWithoutURL(String.valueOf(this.currentPage), this.info.getNextSymbol());
      }
   }

   public String getLastLinkedPage() {
      int totPgCnt = 0;

      try {
         totPgCnt = this.getTotalPageCount();
      } catch (Exception var6) {
         logger.error("", var6);
      } finally {
         ;
      }

      if (this.totalRowCount != 0 && totPgCnt != 1 && this.currentPage != totPgCnt) {
         return this.currentPage >= totPgCnt ? this.makePageLinkWithURL(String.valueOf(this.currentPage), this.info.getLastSymbol()) : this.makePageLinkWithURL(String.valueOf(totPgCnt), this.info.getLastSymbol());
      } else {
         return this.makePageLinkWithoutURL(String.valueOf(this.currentPage), this.info.getLastSymbol());
      }
   }

   public String getTotalLinkedPage() throws Exception {
      if (this.totalRowCount == 0) {
         this.setCurrentPage(0);
      }

      StringBuffer sb = new StringBuffer();
      String strHtml = "&nbsp;&nbsp;&nbsp;<input type='text' id='curPage' name='curPage' value='" + this.currentPage + "' style='width:25px;height:20px;text-align:center;'   onkeypress='if(event.keyCode==13) {goPage(this.value, " + this.getTotalPageCount() + ");event.keyCode=0;}'>&nbsp;/&nbsp;" + this.getTotalPageCount() + "&nbsp;&nbsp;";
      sb.append(strHtml);
      return sb.toString();
   }

   private int getStartPage() {
      int spos = true;
      int spos = (this.getCurrentPage() - 1) / this.info.getListSize() * this.info.getListSize() + 1;
      return spos;
   }

   private int getEndPage() {
      boolean var1 = false;

      try {
         int epos = this.getStartPage() + this.info.getListSize() - 1;
         if (epos > this.getTotalPageCount()) {
            epos = this.getTotalPageCount();
         }

         return epos;
      } catch (Exception var3) {
         logger.error("", var3);
         return 0;
      }
   }

   private String makePageLink(int i) {
      StringBuffer html = new StringBuffer();
      if (this.currentPage != i) {
         html.append(this.makePageLinkWithURL(String.valueOf(i), String.valueOf(i)));
      } else {
         if (i > 1) {
            html.append("/");
         }

         html.append(" <span style=\"").append("font-weight:bold").append("\">").append(i).append("</span>");
      }

      return html.toString();
   }

   private String makePageLinkWithURL(String linkedPageNum, String symbol) {
      StringBuffer html = new StringBuffer();
      String contextImgPath = "";
      String sepStr = "&nbsp;";
      if (symbol.toLowerCase().startsWith("<img")) {
         int idx = symbol.indexOf("\"");
         if (idx == -1) {
            contextImgPath = "";
         } else {
            contextImgPath = symbol.substring(0, idx + 1) + this.contextPath + symbol.substring(10, symbol.length());
         }
      } else {
         if (!symbol.equals("1")) {
            sepStr = " / ";
         }

         contextImgPath = symbol;
      }

      html.append(sepStr).append("<a href=\"").append(this.currentURL).append("?page=").append(linkedPageNum).append(this.getQueryString()).append("\">").append(contextImgPath).append("</A> ");
      return html.toString();
   }

   private String makePageLinkWithoutURL(String linkedPageNum, String symbol) {
      StringBuffer html = new StringBuffer();
      String contextImgPath = "";
      String sepStr = "&nbsp;";
      if (symbol.toLowerCase().startsWith("<img")) {
         int idx = symbol.indexOf("\"");
         if (idx == -1) {
            contextImgPath = "";
         } else {
            contextImgPath = symbol.substring(0, idx + 1) + this.contextPath + symbol.substring(10, symbol.length());
         }
      } else {
         if (!symbol.equals("1")) {
            sepStr = " / ";
         }

         contextImgPath = symbol;
      }

      html.append(sepStr).append(contextImgPath);
      return html.toString();
   }

   public void setQueryString(String key, String value) {
      if (key != null && value != null && key.trim().length() != 0) {
         if (!key.trim().equalsIgnoreCase("page")) {
            if (!key.trim().equalsIgnoreCase("type")) {
               try {
                  this.queryString.put(URLEncoder.encode(key.trim(), "UTF-8"), URLEncoder.encode(value.trim(), "UTF-8"));
               } catch (UnsupportedEncodingException var4) {
                  logger.error("", var4);
               }

            }
         }
      }
   }

   public void setQueryString(String queryString) {
      if (queryString != null && queryString.length() > 0) {
         String[] qss = queryString.split("&");

         for(int i = 0; i < qss.length; ++i) {
            int idx = qss[i].indexOf(61);
            if (idx >= 0) {
               String key = qss[i].substring(0, idx);
               String val = qss[i].substring(idx + 1);
               this.setQueryString(key, val);
            }
         }

      }
   }

   public void setQueryString(Map map) {
      Iterator it = map.entrySet().iterator();

      while(it.hasNext()) {
         Entry entry = (Entry)it.next();
         this.setQueryString(entry.getKey().toString(), entry.getValue().toString());
      }

   }

   public String getQueryString() {
      StringBuffer sb = new StringBuffer();
      Iterator it = this.queryString.entrySet().iterator();

      while(it.hasNext()) {
         Entry entry = (Entry)it.next();
         sb.append("&").append(entry.getKey().toString()).append("=").append(entry.getValue().toString());
      }

      return sb.toString();
   }

   public String getSWFirstLinkedPage() {
      if (this.totalRowCount == 0) {
         return "";
      } else {
         return this.currentPage == 1 ? this.makeSWPageLinkWithURL(String.valueOf(this.currentPage), this.info.getFirstSymbol()) : this.makeSWPageLinkWithURL(String.valueOf(1), this.info.getFirstSymbol());
      }
   }

   public String getSWPreviousLinkedPage() {
      if (this.totalRowCount == 0) {
         return "";
      } else {
         return this.currentPage == 1 ? this.makeSWPageLinkWithURL(String.valueOf(this.currentPage), this.info.getPrevSymbol()) : this.makeSWPageLinkWithURL(String.valueOf(this.currentPage - 1), this.info.getPrevSymbol());
      }
   }

   public String getSWNextLinkedPage() {
      int totPgCnt = 0;

      try {
         totPgCnt = this.getTotalPageCount();
      } catch (Exception var3) {
         logger.error("", var3);
      }

      if (this.totalRowCount == 0) {
         return "";
      } else {
         return this.currentPage >= totPgCnt ? this.makeSWPageLinkWithURL(String.valueOf(this.currentPage), this.info.getNextSymbol()) : this.makeSWPageLinkWithURL(String.valueOf(this.currentPage + 1), this.info.getNextSymbol());
      }
   }

   public String getSWLastLinkedPage() {
      int totPgCnt = 0;

      try {
         totPgCnt = this.getTotalPageCount();
      } catch (Exception var3) {
         logger.error("", var3);
      }

      if (this.totalRowCount == 0) {
         return "";
      } else {
         return this.currentPage >= totPgCnt ? this.makeSWPageLinkWithURL(String.valueOf(this.currentPage), this.info.getLastSymbol()) : this.makeSWPageLinkWithURL(String.valueOf(totPgCnt), this.info.getLastSymbol());
      }
   }

   public String getSWTotalLinkedPage() {
      int spos = this.getStartPage();
      int epos = this.getEndPage();
      if (spos > epos) {
         return "";
      } else {
         StringBuffer sb = new StringBuffer();

         for(int i = spos; i <= epos; ++i) {
            sb.append(this.makeSWPageLink(i));
         }

         return sb.toString();
      }
   }

   private String makeSWPageLinkWithURL(String linkedPageNum, String symbol) {
      StringBuffer html = new StringBuffer();
      String contextImgPath = "";
      String sepStr = "&nbsp;";
      if (symbol.toLowerCase().startsWith("<img")) {
         int idx = symbol.indexOf("\"");
         if (idx == -1) {
            contextImgPath = "";
         } else {
            contextImgPath = symbol.substring(0, idx + 1) + this.contextPath + symbol.substring(10, symbol.length());
         }
      } else {
         if (!symbol.equals("1")) {
            sepStr = " / ";
         }

         contextImgPath = symbol;
      }

      html.append(sepStr).append("<a href=\"#").append(linkedPageNum).append("\" onclick=\"javascript:swPg('").append(linkedPageNum).append("');\">").append(contextImgPath).append("</A> ");
      return html.toString();
   }

   private String makeSWPageLink(int i) {
      StringBuffer html = new StringBuffer();
      if (this.currentPage != i) {
         html.append(this.makeSWPageLinkWithURL(String.valueOf(i), String.valueOf(i)));
      } else {
         if (i > 1) {
            html.append("/");
         }

         html.append("<span style=\"").append("font-weight:bold").append("\">").append(i).append("</span>");
      }

      return html.toString();
   }

   public ListInfo getInfo() {
      return this.info;
   }

   public void setInfo(ListInfo info) {
      this.info = info;
   }
}
