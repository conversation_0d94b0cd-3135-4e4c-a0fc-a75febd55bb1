package com.samsung.magicinfo.ems.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.CommonUtils;
import com.samsung.magicinfo.ems.model.EditTagInfoFilter;
import com.samsung.magicinfo.ems.model.EnvironmentNoticeFilter;
import com.samsung.magicinfo.ems.model.EnvironmentPriorityFilter;
import com.samsung.magicinfo.ems.model.TagResource;
import com.samsung.magicinfo.ems.service.EnvironmentService;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.Authorization;
import java.util.List;
import javax.validation.Valid;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "Setting Management System",
   description = "Operations pertaining to Setting in Setting Management System",
   tags = {"Setting API Group"}
)
@RestController
@RequestMapping({"/restapi/v1.0/ems"})
public class EnvironmentController {
   @Autowired
   private EnvironmentService environmentService;
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());

   public EnvironmentController() {
      super();
   }

   @ApiOperation(
      value = "get dashboard list",
      notes = "get dashboard list(dashboardName, dashboardId).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Get the current using dashboard information"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity listDashboardInfo() throws Exception {
      this.logger.info("[REST][EMS][listDashboardInfo] get dashboard information.");
      ResponseBody dashboardInfo = this.environmentService.listDashboardInfo();

      try {
         if (dashboardInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][listDashboardInfo] finish successfully.");
            return new ResponseEntity(dashboardInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][listDashboardInfo] Internal Error is occured. " + dashboardInfo.getErrorMessage());
            return new ResponseEntity(dashboardInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         dashboardInfo.setStatus("Fail");
         dashboardInfo.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][listDashboardInfo] access denied.");
         return new ResponseEntity(dashboardInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][EMS][listDashboardInfo] Exception is occured. " + var4.toString());
         dashboardInfo.setStatus("Fail");
         dashboardInfo.setErrorMessage(var4.toString());
         return new ResponseEntity(dashboardInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "create dashboard menu",
      notes = "create dashboard menu(dashboardName, priority).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard/create"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 201,
   message = "Get the current using dashboard information"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity createDashboard(@RequestParam(value = "dashboardName",required = true) String dashboardName) throws Exception {
      this.logger.info("[REST][EMS][createDashboard] get updated time result after sending MO");
      ResponseBody createDashboard = this.environmentService.createDashboard(dashboardName);

      try {
         if (createDashboard.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][createDashboard] finish successfully.");
            return new ResponseEntity(createDashboard, HttpStatus.CREATED);
         } else {
            this.logger.error("[REST][EMS][createDashboard] Internal Error is occured. " + createDashboard.getErrorMessage());
            return new ResponseEntity(createDashboard, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         createDashboard.setStatus("Fail");
         createDashboard.setErrorMessage(var4.getMessage());
         this.logger.error("[REST][EMS][createDashboard] access denied.");
         return new ResponseEntity(createDashboard, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][EMS][createDashboard] Exception is occured. " + var5.toString());
         createDashboard.setStatus("Fail");
         createDashboard.setErrorMessage(var5.getMessage());
         return new ResponseEntity(createDashboard, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "delete dashboard menu",
      notes = "delete dashboard menu(dashboardName).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard"},
      method = {RequestMethod.DELETE},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Selected dashboard item was deleted"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity deleteDashboard(@RequestParam(value = "dashboardName",required = true) String dashboardName) {
      this.logger.info("[REST][EMS][deleteDashboard] get updated time result after sending MO");
      ResponseBody deleteDashboard = this.environmentService.deleteDashboard(dashboardName);

      try {
         if (deleteDashboard.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][deleteDashboard] finish successfully.");
            return new ResponseEntity(deleteDashboard, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][deleteDashboard] Internal Error is occured. " + deleteDashboard.getErrorMessage());
            return new ResponseEntity(deleteDashboard, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         deleteDashboard.setStatus("Fail");
         deleteDashboard.setErrorMessage(var4.getMessage());
         this.logger.error("[REST][EMS][deleteDashboard] access denied.");
         return new ResponseEntity(deleteDashboard, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][EMS][deleteDashboard] Exception is occured. " + var5.toString());
         deleteDashboard.setStatus("Fail");
         deleteDashboard.setErrorMessage(var5.getMessage());
         return new ResponseEntity(deleteDashboard, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "update priority dashboard menu(move)",
      notes = "priority list(dashboard name and dashboard priority number).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Selected dashboard item was updated"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity updateDashboardPriority(@Valid @RequestBody EnvironmentPriorityFilter filter, BindingResult result) {
      this.logger.info("[REST][EMS][updateDashboardPriority] update dashboard priority.");
      ResponseBody responseBody = new ResponseBody();

      try {
         if (result.hasErrors()) {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(result.getFieldError().getDefaultMessage());
            return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
         } else {
            responseBody = this.environmentService.updateDashboardPriority(filter);
            if (responseBody.getStatus().equals("Success")) {
               this.logger.info("[REST][EMS][updateDashboardPriority] finish successfully.");
               return new ResponseEntity(responseBody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][EMS][updateDashboardPriority] Internal Error is occured. " + responseBody.getErrorMessage());
               return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         }
      } catch (AccessDeniedException var5) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var5.getMessage());
         this.logger.error("[REST][EMS][updateDashboardPriority] access denied.");
         return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][EMS][updateDashboardPriority] Exception is occured. " + var6.toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var6.getMessage());
         return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get storage information summary",
      notes = "get storage information summary(rootPath, freeSize, usedSize, totalSize, percent).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard/storage"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Get the storage information"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity listDashboardStorageInfo() throws Exception {
      this.logger.info("[REST][EMS][listDashboardStorageInfo] get storage information for dashboard.");
      ResponseBody storageInfo = this.environmentService.listDashboardStorageInfo();

      try {
         if (storageInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][listDashboardStorageInfo] finish successfully.");
            return new ResponseEntity(storageInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][listDashboardStorageInfo] Internal Error is occured. " + storageInfo.getErrorMessage());
            return new ResponseEntity(storageInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         storageInfo.setStatus("Fail");
         storageInfo.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][listDashboardStorageInfo] access denied.");
         return new ResponseEntity(storageInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][EMS][listDashboardStorageInfo] Exception is occured. " + var4.toString());
         storageInfo.setStatus("Fail");
         storageInfo.setErrorMessage(var4.getMessage());
         return new ResponseEntity(storageInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get notice information summary",
      notes = "get notice information summary(noticeTitle, noticeId, noticeUserId, noticeWriteDate, noticeImportant).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard/notice"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Get the notice information"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity listDashboardNoticeInfo() throws Exception {
      this.logger.info("[REST][EMS][listDashboardNoticeInfo] get updated time result after sending MO");
      ResponseBody noticeInfo = this.environmentService.listDashboardNoticeInfo();

      try {
         if (noticeInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][listDashboardNoticeInfo] finish successfully.");
            return new ResponseEntity(noticeInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][listDashboardNoticeInfo] Internal Error is occured. " + noticeInfo.getErrorMessage());
            return new ResponseEntity(noticeInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         noticeInfo.setStatus("Fail");
         noticeInfo.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][listDashboardNoticeInfo] access denied.");
         return new ResponseEntity(noticeInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][EMS][listDashboardNoticeInfo] Exception is occured. " + var4.toString());
         noticeInfo.setStatus("Fail");
         noticeInfo.setErrorMessage(var4.toString());
         return new ResponseEntity(noticeInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get notice information by selected notice id(click notice)",
      notes = "get notice information by selected notice id(click notice)(noticeId, title, notice, important, startDate, endDate, mode).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard/notice/edit"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Get the notice detail view information"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity listEditNoticeInfo(@RequestParam(value = "noticeId",required = true) String noticeId) throws Exception {
      this.logger.info("[REST][EMS][listEditNoticeInfo] get editable notice list.");
      ResponseBody noticeViewInfo = this.environmentService.listEditNoticeInfo(noticeId);

      try {
         if (noticeViewInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][listEditNoticeInfo] finish successfully.");
            return new ResponseEntity(noticeViewInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][listEditNoticeInfo] Internal Error is occured. " + noticeViewInfo.getErrorMessage());
            return new ResponseEntity(noticeViewInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         noticeViewInfo.setStatus("Fail");
         noticeViewInfo.setErrorMessage(var4.getMessage());
         this.logger.error("[REST][EMS][listEditNoticeInfo] access denied.");
         return new ResponseEntity(noticeViewInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][EMS][listEditNoticeInfo] Exception is occured. " + var5.toString());
         noticeViewInfo.setStatus("Fail");
         noticeViewInfo.setErrorMessage(var5.toString());
         return new ResponseEntity(noticeViewInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "update notice information",
      notes = "update notice information(noticeId, title, notice, important, startDate, endDate).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard/notice/edit"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Selected notice information was updated"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity updateNoticeInfo(@Valid @RequestBody EnvironmentNoticeFilter filter, BindingResult result) throws Exception {
      this.logger.info("[REST][EMS][updateNoticeInfo] update notice.");
      ResponseBody responseBody = new ResponseBody();

      try {
         if (result.hasErrors()) {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(result.getFieldError().getDefaultMessage());
            return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
         } else {
            boolean create = false;
            responseBody = this.environmentService.createNoticeInfo(filter, create);
            if (responseBody.getStatus().equals("Success")) {
               this.logger.info("[REST][EMS][updateNoticeInfo] finish successfully.");
               return new ResponseEntity(responseBody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][EMS][updateNoticeInfo] Internal Error is occured. " + responseBody.getErrorMessage());
               return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         }
      } catch (AccessDeniedException var5) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var5.getMessage());
         this.logger.error("[REST][EMS][updateNoticeInfo] access denied.");
         return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][EMS][updateNoticeInfo] Exception is occured. " + var6.toString());
         responseBody.setErrorMessage(var6.toString());
         responseBody.setStatus("Fail");
         return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "create notice information",
      notes = "create notice information(noticeId, title, notice, important, startDate, endDate).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard/notice/edit"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 201,
   message = "Selected notice information was created"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity createNoticeInfo(@Valid @RequestBody EnvironmentNoticeFilter filter, BindingResult result) throws Exception {
      this.logger.info("[REST][EMS][createNoticeInfo] create new notice.");
      ResponseBody responseBody = new ResponseBody();

      try {
         if (result.hasErrors()) {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(result.getFieldError().getDefaultMessage());
            return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
         } else {
            boolean create = true;
            responseBody = this.environmentService.createNoticeInfo(filter, create);
            if (responseBody.getStatus().equals("Success")) {
               this.logger.info("[REST][EMS][createNoticeInfo] finish successfully.");
               return new ResponseEntity(responseBody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][EMS][createNoticeInfo] Internal Error is occured. " + responseBody.getErrorMessage());
               return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         }
      } catch (AccessDeniedException var5) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var5.getMessage());
         this.logger.error("[REST][EMS][createNoticeInfo] access denied.");
         return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][EMS][createNoticeInfo] Exception is occured. " + var6.toString());
         responseBody.setErrorMessage(var6.toString());
         responseBody.setStatus("Fail");
         return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "delete notice information",
      notes = "delete notice information(noticeId, title, notice, important, startDate, endDate).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard/notice/edit"},
      method = {RequestMethod.DELETE},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Selected notice information was deleted"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity deleteNoticeInfo(@RequestParam(value = "noticeId",required = true) String noticeId) throws Exception {
      this.logger.info("[REST][EMS][deleteNoticeInfo] delete notice.");
      ResponseBody deleteNoticeInfo = this.environmentService.deleteNoticeInfo(noticeId);

      try {
         if (deleteNoticeInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][deleteNoticeInfo] finish successfully.");
            return new ResponseEntity(deleteNoticeInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][deleteNoticeInfo] Internal Error is occured. " + deleteNoticeInfo.getErrorMessage());
            return new ResponseEntity(deleteNoticeInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][EMS][deleteNoticeInfo] access denied.");
         deleteNoticeInfo.setStatus("Fail");
         deleteNoticeInfo.setErrorMessage(var4.getMessage());
         return new ResponseEntity(deleteNoticeInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][EMS][deleteNoticeInfo] Exception is occured. " + var5.toString());
         deleteNoticeInfo.setErrorMessage(var5.toString());
         deleteNoticeInfo.setStatus("Fail");
         return new ResponseEntity(deleteNoticeInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get login information summary",
      notes = "get login information summary(userId, userName, role, lastSignIn).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard/login"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Get the login information"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity listDashboardLoginInfo() throws Exception {
      this.logger.info("[REST][EMS][listDashboardLoginInfo] get login information for dashboard.");
      ResponseBody loginInfo = this.environmentService.listDashboardLoginInfo();

      try {
         if (loginInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][listDashboardLoginInfo] finish successfully.");
            return new ResponseEntity(loginInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][listDashboardLoginInfo] Internal Error is occured. " + loginInfo.getErrorMessage());
            return new ResponseEntity(loginInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         loginInfo.setStatus("Fail");
         loginInfo.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][listDashboardLoginInfo] access denied.");
         return new ResponseEntity(loginInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][EMS][listDashboardLoginInfo] Exception is occured. " + var4.toString());
         loginInfo.setStatus("Fail");
         loginInfo.setErrorMessage(var4.toString());
         return new ResponseEntity(loginInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @KPI
   @ApiOperation(
      value = "get license list",
      notes = "get license list(license_key, product_code, product_name, license_type, max_clients, start_date, end_date, reg_date).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/license"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Get the license information"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity listLicense() throws Exception {
      this.logger.info("[REST][EMS][listLicense] get license list.");
      ResponseBody licenseInfo = this.environmentService.listLicense();

      try {
         if (licenseInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][listLicense] finish successfully.");
            return new ResponseEntity(licenseInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][listLicense] Internal Error is occured. " + licenseInfo.getErrorMessage());
            return new ResponseEntity(licenseInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         licenseInfo.setStatus("Fail");
         licenseInfo.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][listLicense] access denied.");
         return new ResponseEntity(licenseInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][EMS][listLicense] Exception is occured. " + var4.toString());
         licenseInfo.setStatus("Fail");
         licenseInfo.setErrorMessage(var4.toString());
         return new ResponseEntity(licenseInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get menu list",
      notes = "get menu list.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/menu"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Get the available menu"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity listMenu() throws Exception {
      this.logger.info("[REST][EMS][listMenu] Get the available menu.");
      ResponseBody menuInfo = this.environmentService.listMenu();

      try {
         if (menuInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][listMenu] finish successfully.");
            return new ResponseEntity(menuInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][listMenu] Internal Error is occured. " + menuInfo.getErrorMessage());
            return new ResponseEntity(menuInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         menuInfo.setStatus("Fail");
         menuInfo.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][listMenu] access denied.");
         return new ResponseEntity(menuInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][EMS][listMenu] Exception is occured. " + var4.toString());
         menuInfo.setStatus("Fail");
         menuInfo.setErrorMessage(var4.toString());
         return new ResponseEntity(menuInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get organization list and tag list",
      notes = "get organization list and tag list.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/tag"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity getTagListInfo(@RequestBody TagResource params, BindingResult result) throws Exception {
      this.logger.info("[REST][EMS][getTagListInfo] get tag list.");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.environmentService.getTagListInfo(params);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][EMS][getTagListInfo] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][getTagListInfo] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var5) {
         this.logger.error("[REST][DEVICE][getTagListInfo] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.toString());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][EMS][getTagListInfo] Exception is occured. " + var6.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.toString());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "create Tag",
      notes = "create Tag.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/tag/create"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity createTag(@RequestBody List params, BindingResult result) throws Exception {
      this.logger.info("[REST][EMS][createTag] create Tag.");
      ResponseBody responsebody = new ResponseBody();

      try {
         if (!CommonUtils.checkAvailable("playlist")) {
            return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
         } else {
            responsebody = this.environmentService.createTag(params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][EMS][createTag] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][EMS][createTag] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         }
      } catch (AccessDeniedException var5) {
         this.logger.error("[REST][EMS][createTag] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.toString());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][EMS][createTag] Exception is occured. " + var6.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.toString());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "Search detail tag  ",
      notes = "Inquire detailed information about tag. ",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/tag/{tagId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "tagId",
   value = "tagId",
   required = false,
   dataType = "String"
)})
   public ResponseEntity getTagInfo(@PathVariable("tagId") String tagId) throws Exception {
      this.logger.info("[REST][SETTING][getTagInfo] Detail Tag Inforamtion Search ");
      ResponseBody responseBody = new ResponseBody();

      try {
         if (!CommonUtils.checkAvailable("playlist")) {
            return new ResponseEntity(responseBody, HttpStatus.METHOD_NOT_ALLOWED);
         } else {
            responseBody = this.environmentService.getTagInfo(tagId);
            responseBody.setApiVersion("1.0");
            if (responseBody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][EMS][createTag] finish successfully.");
               return new ResponseEntity(responseBody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][EMS][createTag] Internal Error is occured. " + responseBody.getErrorMessage());
               return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][EMS][createTag] access denied.");
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var4.toString());
         return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][EMS][createTag] Exception is occured. " + var5.toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var5.toString());
         return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "Edit tag",
      notes = "Called when specific tag name is clicked and clicked save button.",
      authorizations = {@Authorization("api_key")}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "tagId",
   value = "tagId",
   required = false,
   dataType = "String",
   example = "2"
)})
   @RequestMapping(
      value = {"/tag/{tagId}"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   public ResponseEntity editTagInfo(@PathVariable("tagId") String tagId, @Valid @RequestBody EditTagInfoFilter filter) throws Exception {
      this.logger.info("[REST][SETTING][editTagInfo] Edit Tag Infomation");
      ResponseBody responseBody = new ResponseBody();

      try {
         if (!CommonUtils.checkAvailable("playlist")) {
            return new ResponseEntity(responseBody, HttpStatus.METHOD_NOT_ALLOWED);
         } else {
            responseBody = this.environmentService.editTagInfo(tagId, filter);
            responseBody.setApiVersion("1.0");
            if (responseBody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][EMS][createTag] finish successfully.");
               return new ResponseEntity(responseBody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][EMS][createTag] Internal Error is occured. " + responseBody.getErrorMessage());
               return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         }
      } catch (AccessDeniedException var5) {
         this.logger.error("[REST][EMS][createTag] access denied.");
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var5.toString());
         return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][EMS][createTag] Exception is occured. " + var6.toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var6.toString());
         return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "Delete tag",
      notes = "Select list to view details and delete tags.",
      authorizations = {@Authorization("api_key")}
   )
   @DeleteMapping(
      value = {"/tag/{tagId}"},
      produces = {"application/json"}
   )
   @ApiImplicitParams({@ApiImplicitParam(
   name = "tagId",
   value = "tagId",
   required = false,
   dataType = "String"
)})
   public ResponseEntity deleteTagInfo(@PathVariable("tagId") String tagId) throws Exception {
      this.logger.info("[REST][SETTING][deleteTagInfo] Delete Tag Inforamtion");
      ResponseBody responseBody = new ResponseBody();

      try {
         if (!CommonUtils.checkAvailable("playlist")) {
            return new ResponseEntity(responseBody, HttpStatus.METHOD_NOT_ALLOWED);
         } else {
            responseBody = this.environmentService.deleteTagInfo(tagId);
            responseBody.setApiVersion("1.0");
            if (responseBody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][EMS][createTag] finish successfully.");
               return new ResponseEntity(responseBody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][EMS][createTag] Internal Error is occured. " + responseBody.getErrorMessage());
               return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][EMS][createTag] access denied.");
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var4.toString());
         return new ResponseEntity(responseBody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][EMS][createTag] Exception is occured. " + var5.toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var5.toString());
         return new ResponseEntity(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get category list",
      notes = "get category list.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/category/{categoryId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getCategoryInfo(@PathVariable String categoryId) throws Exception {
      this.logger.info("[REST][EMS][getCategoryInfo] get category list.");
      ResponseBody responsebody = new ResponseBody();

      try {
         if (!CommonUtils.checkAvailable("playlist")) {
            return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
         } else {
            responsebody = this.environmentService.getCategoryInfo(categoryId);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][EMS][getCategoryInfo] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][EMS][getCategoryInfo] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][DEVICE][getCategoryInfo] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][EMS][getCategoryInfo] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "check license and device count",
      notes = "check license and device count(license_key, product_code, product_name, license_type, max_clients, start_date, end_date, reg_date).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/license/check"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Get License Status"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity checkLicenseStatus() throws Exception {
      this.logger.info("[REST][EMS][checkLicenseStatus] get updated time result after sending MO");
      ResponseBody licenseInfo = this.environmentService.checkLicenseStatus();

      try {
         if (licenseInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][checkLicenseStatus] finish successfully.");
            return new ResponseEntity(licenseInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][checkLicenseStatus] Internal Error is occured. " + licenseInfo.getErrorMessage());
            return new ResponseEntity(licenseInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         licenseInfo.setStatus("Fail");
         licenseInfo.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][checkLicenseStatus] access denied.");
         return new ResponseEntity(licenseInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][EMS][checkLicenseStatus] Exception is occured. " + var4.toString());
         licenseInfo.setStatus("Fail");
         licenseInfo.setErrorMessage(var4.getMessage());
         return new ResponseEntity(licenseInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "check license and device count",
      notes = "check license and device count(license_key, product_code, product_name, license_type, max_clients, start_date, end_date, reg_date).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/license/check/detail"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "Get License Status and Device Count"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity getLicenseCheckDetail() throws Exception {
      this.logger.info("[REST][EMS][getLicenseCheckDetail] check whether there exist excess devices over the count of license.");
      ResponseBody licenseInfo = this.environmentService.getLicenseCheckDetail();

      try {
         if (licenseInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][getLicenseCheckDetail] finish successfully.");
            return new ResponseEntity(licenseInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][getLicenseCheckDetail] Internal Error is occured. " + licenseInfo.getErrorMessage());
            return new ResponseEntity(licenseInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         licenseInfo.setStatus("Fail");
         licenseInfo.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][getLicenseCheckDetail] access denied.");
         return new ResponseEntity(licenseInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         licenseInfo.setStatus("Fail");
         licenseInfo.setErrorMessage(var4.toString());
         this.logger.error("[REST][EMS][getLicenseCheckDetail] Exception is occured. " + var4.toString());
         return new ResponseEntity(licenseInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get server setup info",
      notes = "get server setup info.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/setup"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "get server setup info"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity getServerSetupInfo() throws Exception {
      this.logger.info("[REST][EMS][getServerSetupInfo] get server setup information.");
      ResponseBody licenseInfo = this.environmentService.getServerSetupInfo();

      try {
         if (licenseInfo.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][getUpdatedTimeInfoResult] finish successfully.");
            return new ResponseEntity(licenseInfo, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][getUpdatedTimeInfoResult] Internal Error is occured. " + licenseInfo.getErrorMessage());
            return new ResponseEntity(licenseInfo, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         licenseInfo.setStatus("Fail");
         licenseInfo.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][getUpdatedTimeInfoResult] access denied.");
         return new ResponseEntity(licenseInfo, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         licenseInfo.setStatus("Fail");
         licenseInfo.setErrorMessage(var4.getMessage());
         this.logger.error("[REST][EMS][getUpdatedTimeInfoResult] Exception is occured. " + var4.toString());
         return new ResponseEntity(licenseInfo, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get device status info",
      notes = "get device status info.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/device/check"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "get server setup info"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity getDeviceStatusInfo() throws Exception {
      this.logger.info("[REST][EMS][getServerSetupInfo] get device status information.");
      ResponseBody deviceStatus = this.environmentService.getUsedDeviceTypeInfoByLicense();

      try {
         if (deviceStatus.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][getDeviceStatusResult] finish successfully.");
            return new ResponseEntity(deviceStatus, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][getDeviceStatusResult] Internal Error is occured. " + deviceStatus.getErrorMessage());
            return new ResponseEntity(deviceStatus, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         deviceStatus.setStatus("Fail");
         deviceStatus.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][getDeviceStatusResult] access denied.");
         return new ResponseEntity(deviceStatus, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         deviceStatus.setStatus("Fail");
         deviceStatus.setErrorMessage(var4.getMessage());
         this.logger.error("[REST][EMS][getDeviceStatusResult] Exception is occured. " + var4.toString());
         return new ResponseEntity(deviceStatus, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get menu item count",
      notes = "get menu item count.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/menu/count"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   @ApiResponses({@ApiResponse(
   code = 200,
   message = "get server setup info"
), @ApiResponse(
   code = 500,
   message = "Internal server error due to incorrect dashboard information"
)})
   public ResponseEntity getMenuItemCount() throws Exception {
      this.logger.info("[REST][EMS][getMenuItemCount] get device status information.");
      ResponseBody responsebody = this.environmentService.getMenuItemCount();

      try {
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][EMS][getMenuItemCount] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][EMS][getMenuItemCount] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][EMS][getMenuItemCount] access denied.");
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         this.logger.error("[REST][EMS][getMenuItemCount] Exception is occured. " + var4.toString());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }
}
