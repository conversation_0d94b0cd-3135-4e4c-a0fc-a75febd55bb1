package com.samsung.common.utils;

import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

public class SemaphoreThrottler implements Throttler {
   private Semaphore semaphore;

   public SemaphoreThrottler(int permits) {
      super();
      this.semaphore = new Semaphore(permits);
   }

   public boolean tryAcquire() {
      return this.semaphore.tryAcquire();
   }

   public boolean tryAcquire(long timeout, TimeUnit timeUnit) {
      try {
         return this.semaphore.tryAcquire(timeout, timeUnit);
      } catch (InterruptedException var5) {
         return false;
      }
   }

   public void release() {
      this.semaphore.release();
   }
}
