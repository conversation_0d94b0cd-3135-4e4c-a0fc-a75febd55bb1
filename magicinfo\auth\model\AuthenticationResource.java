package com.samsung.magicinfo.auth.model;

import com.samsung.magicinfo.auth.security.otp.UserAuthDevice;

public abstract class AuthenticationResource {
   private String username;
   private String password;
   private String totp;
   private String hotp;
   private UserAuthDevice userAuthDevice;

   public AuthenticationResource() {
      super();
   }

   public String getUsername() {
      return this.username;
   }

   public void setUsername(String username) {
      this.username = username;
   }

   public String getPassword() {
      return this.password;
   }

   public void setPassword(String password) {
      this.password = password;
   }

   public String getTotp() {
      return this.totp;
   }

   public void setTotp(String totp) {
      this.totp = totp;
   }

   public String getHotp() {
      return this.hotp;
   }

   public void setHotp(String hotp) {
      this.hotp = hotp;
   }

   public UserAuthDevice getUserAuthDevice() {
      return this.userAuthDevice;
   }

   public void setUserAuthDevice(UserAuthDevice userAuthDevice) {
      this.userAuthDevice = userAuthDevice;
   }
}
