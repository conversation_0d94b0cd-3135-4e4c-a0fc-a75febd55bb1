package com.samsung.magicinfo.auth.model.json.request;

import com.samsung.magicinfo.auth.model.AuthenticationResource;
import com.samsung.magicinfo.auth.security.otp.UserAuthDevice;
import com.samsung.magicinfo.framework.kpi.annotation.LogProperty;
import java.io.Serializable;

public class AuthenticationRequest extends AuthenticationResource implements Serializable {
   private static final long serialVersionUID = 6624726180748515507L;
   @LogProperty(
      valueType = "NAME"
   )
   private String username;
   private String password;
   private String totp;
   private String hotp;
   private UserAuthDevice userAuthDevice;

   public AuthenticationRequest() {
      super();
   }

   public AuthenticationRequest(String username, String password) {
      super();
      this.setUsername(username);
      this.setPassword(password);
   }

   public String getUsername() {
      return this.username;
   }

   public void setUsername(String username) {
      this.username = username;
   }

   public String getPassword() {
      return this.password;
   }

   public void setPassword(String password) {
      this.password = password;
   }

   public String getTotp() {
      return this.totp;
   }

   public void setTotp(String totp) {
      this.totp = totp;
   }

   public String getHotp() {
      return this.hotp;
   }

   public void setHotp(String hotp) {
      this.hotp = hotp;
   }

   public UserAuthDevice getUserAuthDevice() {
      return this.userAuthDevice;
   }

   public void setUserAuthDevice(UserAuthDevice userAuthDevice) {
      this.userAuthDevice = userAuthDevice;
   }
}
