package com.samsung.magicinfo.ems.model;

import io.swagger.annotations.ApiModelProperty;

public class EdgeServerResource {
   @ApiModelProperty(
      example = "hostName"
   )
   public String hostName;
   @ApiModelProperty(
      example = "***********"
   )
   public String ipAddress;
   @ApiModelProperty(
      example = "7001"
   )
   public String webPort;
   @ApiModelProperty(
      example = "21"
   )
   public String ftpPort;
   @ApiModelProperty(
      example = "true"
   )
   public boolean privateMode;
   @ApiModelProperty(
      example = "***********"
   )
   public String setPrivateIpAddress;
   @ApiModelProperty(
      example = "7001"
   )
   public Long privateWebPort;
   @ApiModelProperty(
      example = "21"
   )
   public Long privateFtpPort;
   public String status;

   public EdgeServerResource() {
      super();
   }

   public String getHostName() {
      return this.hostName;
   }

   public void setHostName(String hostName) {
      this.hostName = hostName;
   }

   public String getIpAddress() {
      return this.ipAddress;
   }

   public void setIpAddress(String ipAddress) {
      this.ipAddress = ipAddress;
   }

   public String getWebPort() {
      return this.webPort;
   }

   public void setWebPort(String webPort) {
      this.webPort = webPort;
   }

   public String getFtpPort() {
      return this.ftpPort;
   }

   public void setFtpPort(String ftpPort) {
      this.ftpPort = ftpPort;
   }

   public boolean getPrivateMode() {
      return this.privateMode;
   }

   public void setPrivateMode(boolean privateMode) {
      this.privateMode = privateMode;
   }

   public String getPrivateIpAddress() {
      return this.setPrivateIpAddress;
   }

   public void setPrivateIpAddress(String setPrivateIpAddress) {
      this.setPrivateIpAddress = setPrivateIpAddress;
   }

   public Long getPrivateWebPort() {
      return this.privateWebPort;
   }

   public void setPrivateWebPort(Long privateWebPort) {
      this.privateWebPort = privateWebPort;
   }

   public Long getPrivateFtpPort() {
      return this.privateFtpPort;
   }

   public void setPrivateFtpPort(Long privateFtpPort) {
      this.privateFtpPort = privateFtpPort;
   }

   public boolean isPrivateMode() {
      return this.privateMode;
   }

   public String getSetPrivateIpAddress() {
      return this.setPrivateIpAddress;
   }

   public void setSetPrivateIpAddress(String setPrivateIpAddress) {
      this.setPrivateIpAddress = setPrivateIpAddress;
   }

   public String getStatus() {
      return this.status;
   }

   public void setStatus(String status) {
      this.status = status;
   }
}
