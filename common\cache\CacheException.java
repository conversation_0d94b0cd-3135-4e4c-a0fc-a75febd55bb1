package com.samsung.common.cache;

public class <PERSON>acheException extends RuntimeException {
   private static final long serialVersionUID = -5879308340862213496L;

   public CacheException() {
      super();
   }

   public CacheException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
      super(message, cause, enableSuppression, writableStackTrace);
   }

   public CacheException(String message, Throwable cause) {
      super(message, cause);
   }

   public CacheException(String message) {
      super(message);
   }

   public CacheException(Throwable cause) {
      super(cause);
   }
}
