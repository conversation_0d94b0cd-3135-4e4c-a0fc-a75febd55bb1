package com.samsung.common.cache;

import java.util.HashMap;
import java.util.Map;

public class MapItemRemover implements MutatorOperation {
   public MapItemRemover() {
      super();
   }

   public Map mutate(Map currentItem, String newItem) {
      if (newItem != null) {
         currentItem.remove(newItem);
      }

      return currentItem;
   }

   public Map initialValue(String item) {
      return new HashMap();
   }
}
