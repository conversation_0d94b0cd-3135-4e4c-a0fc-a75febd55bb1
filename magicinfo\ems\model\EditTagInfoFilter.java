package com.samsung.magicinfo.ems.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(
   value = "EditTagInfoFilter",
   description = "Setting Filter Model"
)
public class EditTagInfoFilter {
   @ApiModelProperty(
      example = "Tag2",
      required = false
   )
   private String tagName;
   @ApiModelProperty(
      example = "",
      required = false
   )
   private String tagCondition;
   @ApiModelProperty(
      example = "Tag Wirte Test 1234!",
      required = false
   )
   private String tagDescription;
   @ApiModelProperty(
      example = "0",
      required = false
   )
   private String tagType;

   public EditTagInfoFilter() {
      super();
   }

   public String getTagName() {
      return this.tagName;
   }

   public void setTagName(String tagName) {
      this.tagName = tagName;
   }

   public String getTagCondition() {
      return this.tagCondition;
   }

   public void setTagCondition(String tagCondition) {
      this.tagCondition = tagCondition;
   }

   public String getTagDescription() {
      return this.tagDescription;
   }

   public void setTagDescription(String tagDescription) {
      this.tagDescription = tagDescription;
   }

   public String getTagType() {
      return this.tagType;
   }

   public void setTagType(String tagType) {
      this.tagType = tagType;
   }
}
