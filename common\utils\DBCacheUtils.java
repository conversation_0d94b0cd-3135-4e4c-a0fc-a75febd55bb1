package com.samsung.common.utils;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDBCacheEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceNetworkConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemInfoConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.protocol.rmql.RMQL;
import java.io.IOException;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;
import org.apache.logging.log4j.Logger;

public class DBCacheUtils {
   static Logger logger = LoggingManagerV2.getLogger(DBCacheUtils.class);

   public DBCacheUtils() {
      super();
   }

   public static Device getDevice(String deviceId) throws Exception {
      try {
         DeviceDBCacheEntity deviceDBCacheEntity = getDeviceDBCacheEntityFromCache(deviceId);
         if (deviceDBCacheEntity == null) {
            throw new NullPointerException();
         } else if (!deviceDBCacheEntity.initializedDevice) {
            logger.error("DBCacheUtils [Miss:No value in Device Memory](not initialized) deviceId=" + deviceId);
            throw new NullPointerException();
         } else {
            Device retDevice = deviceDBCacheEntity.getDevice();
            logger.debug("DBCacheUtils [Hit:get value from Device Memory]  - success to get device info from cache " + deviceId);
            return retDevice;
         }
      } catch (NullPointerException var3) {
         logger.debug("DBCacheUtils [init:No exist] deviceId = " + deviceId);
         throw var3;
      } catch (Exception var4) {
         logger.error("DBCacheUtils Exception deviceId = " + deviceId, var4);
         throw var4;
      }
   }

   public static void setDevice(Device device, String deviceId) {
      if (device != null) {
         try {
            CASResponse casResponse = null;
            int var7 = 0;

            do {
               CASValue casObj = CacheFactory.getCache().gets("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, new DeviceDBCacheEntity());
               DeviceDBCacheEntity targetEntity = (DeviceDBCacheEntity)casObj.getValue();
               targetEntity.setDevice(device);
               targetEntity.initializedDevice = true;
               logger.debug("[save: DB value to Device Memory] deviceId=" + deviceId);
               casResponse = CacheFactory.getCache().cas("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, casObj.getCas(), targetEntity);
            } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var7++));
         } catch (Exception var6) {
            logger.debug("[DBCache] Error to save device info to cache. deviceId " + deviceId);
            StringWriter stack = new StringWriter();
            logger.fatal("[DBCache] " + stack.toString());
         }

      }
   }

   public static DeviceDisplayConf getDeviceDisplayConf(String deviceId) throws Exception {
      try {
         DeviceDBCacheEntity deviceDBCacheEntity = getDeviceDBCacheEntityFromCache(deviceId);
         if (deviceDBCacheEntity == null) {
            throw new NullPointerException();
         } else if (!deviceDBCacheEntity.initializedDeviceDisplayConf) {
            logger.debug("[Miss:No value in DeviceDisplayConf Memory](not initialized) deviceId=" + deviceId);
            throw new NullPointerException();
         } else {
            DeviceDisplayConf retDeviceDisplayConf = deviceDBCacheEntity.getDeviceDisplayConf();
            logger.debug("[Hit:get value from DeviceDisplayConf Memory]  - success to get deviceDisplayConf info from cache " + deviceId);
            return retDeviceDisplayConf;
         }
      } catch (NullPointerException var3) {
         logger.debug("[init:No device Memory] deviceId=" + deviceId);
         throw var3;
      } catch (Exception var4) {
         logger.error("DBCacheUtils Exception deviceId = " + deviceId);
         throw var4;
      }
   }

   public static void setDeviceDisplayConf(DeviceDisplayConf deviceDisplayConf, String deviceId) {
      if (deviceDisplayConf != null) {
         try {
            CASResponse casResponse = null;
            int var3 = 0;

            do {
               CASValue casObj = CacheFactory.getCache().gets("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, new DeviceDBCacheEntity());
               DeviceDBCacheEntity targetEntity = (DeviceDBCacheEntity)casObj.getValue();
               targetEntity.setDeviceDisplayConf(deviceDisplayConf);
               if (deviceDisplayConf.getBasic_panel_status() != null) {
                  targetEntity.initializedDeviceDisplayConf = true;
               }

               logger.debug("[save: DB value to DeviceDisplayConf Memory] deviceId=" + deviceId);
               casResponse = CacheFactory.getCache().cas("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, casObj.getCas(), targetEntity);
            } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var3++));
         } catch (Exception var6) {
            logger.debug("[DBCache] Error to save setDeviceDisplayConf info to cache. deviceId " + deviceId);
         }

      }
   }

   public static DeviceSecurityConf getDeviceSecurityConf(String deviceId) throws Exception {
      try {
         DeviceDBCacheEntity deviceDBCacheEntity = getDeviceDBCacheEntityFromCache(deviceId);
         if (deviceDBCacheEntity == null) {
            throw new NullPointerException();
         } else if (!deviceDBCacheEntity.initializedDeviceSecurityConf) {
            logger.debug("[Miss:No value in DeviceSecurityConf Memory](not initialized) deviceId=" + deviceId);
            throw new NullPointerException();
         } else {
            DeviceSecurityConf retDeviceSecurityConf = deviceDBCacheEntity.getDeviceSecurityConf();
            logger.debug("[Hit:get value from DeviceSecurityConf Memory]  - success to get deviceSecurityConf info from cache " + deviceId);
            return retDeviceSecurityConf;
         }
      } catch (NullPointerException var3) {
         logger.debug("[init:No device Memory] deviceId=" + deviceId);
         throw var3;
      } catch (Exception var4) {
         logger.error("DBCacheUtils Exception deviceId = " + deviceId);
         throw var4;
      }
   }

   public static void setDeviceSecurityConf(DeviceSecurityConf deviceSecurityConf, String deviceId) {
      if (deviceSecurityConf != null) {
         try {
            CASResponse casResponse = null;
            int var3 = 0;

            do {
               CASValue casObj = CacheFactory.getCache().gets("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, new DeviceDBCacheEntity());
               DeviceDBCacheEntity targetEntity = (DeviceDBCacheEntity)casObj.getValue();
               targetEntity.setDeviceSecurityConf(deviceSecurityConf);
               targetEntity.initializedDeviceSecurityConf = true;
               logger.debug("[save: DB value to DeviceSecurityConf Memory] deviceId=" + deviceId);
               casResponse = CacheFactory.getCache().cas("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, casObj.getCas(), targetEntity);
            } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var3++));
         } catch (Exception var6) {
            logger.debug("[DBCache] Error to save setDeviceSecurityConf info to cache. deviceId " + deviceId);
         }

      }
   }

   public static DeviceGeneralConf getDeviceGeneralConf(String deviceId) throws Exception {
      try {
         DeviceDBCacheEntity deviceDBCacheEntity = getDeviceDBCacheEntityFromCache(deviceId);
         if (deviceDBCacheEntity == null) {
            throw new NullPointerException();
         } else if (!deviceDBCacheEntity.initializedDeviceGeneralConf) {
            logger.debug("[Miss:No value in DeviceGeneralConf Memory] deviceId=" + deviceId);
            throw new NullPointerException();
         } else {
            DeviceGeneralConf retDeviceGeneralConf = deviceDBCacheEntity.getDeviceGeneralConf();
            logger.debug("[Hit:get value from DeviceGeneralConf Memory]  - success to get deviceDisplayConf info from cache " + deviceId);
            return retDeviceGeneralConf;
         }
      } catch (NullPointerException var3) {
         logger.debug("[init:No device Memory] deviceId=" + deviceId);
         throw var3;
      } catch (Exception var4) {
         logger.error("DBCacheUtils Exception deviceID = " + deviceId);
         throw var4;
      }
   }

   public static void setDeviceGeneralConf(DeviceGeneralConf deviceGeneralConf, String deviceId) {
      if (deviceGeneralConf != null) {
         try {
            CASResponse casResponse = null;
            int var3 = 0;

            do {
               CASValue casObj = CacheFactory.getCache().gets("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, new DeviceDBCacheEntity());
               DeviceDBCacheEntity targetEntity = (DeviceDBCacheEntity)casObj.getValue();
               targetEntity.setDeviceGeneralConf(deviceGeneralConf);
               targetEntity.initializedDeviceGeneralConf = true;
               logger.debug("[save: DB value to DeviceGeneralConf Memory] deviceId=" + deviceId);
               casResponse = CacheFactory.getCache().cas("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, casObj.getCas(), targetEntity);
            } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var3++));
         } catch (Exception var6) {
            logger.debug("[DBCache] Error to save setDeviceGeneralConf info to cache. deviceId " + deviceId);
         }

      }
   }

   public static DeviceNetworkConf getDeviceNetworkConf(String deviceId) throws Exception {
      try {
         DeviceDBCacheEntity deviceDBCacheEntity = getDeviceDBCacheEntityFromCache(deviceId);
         if (deviceDBCacheEntity == null) {
            throw new NullPointerException();
         } else if (!deviceDBCacheEntity.initializedDeviceNetworkConf) {
            logger.debug("[Miss:No value in DeviceNetworkConf Memory] deviceId=" + deviceId);
            throw new NullPointerException();
         } else {
            DeviceNetworkConf retDeviceNetworkConf = deviceDBCacheEntity.getDeviceNetworkConf();
            logger.debug("[Hit:get value from DeviceNetworkConf Memory]  - success to get deviceDisplayConf info from cache " + deviceId);
            return retDeviceNetworkConf;
         }
      } catch (NullPointerException var3) {
         logger.debug("[init:No device Memory] deviceId=" + deviceId);
         throw var3;
      } catch (Exception var4) {
         logger.error("DBCacheUtils Exception deviceID = " + deviceId);
         throw var4;
      }
   }

   public static void setDeviceNetworkConf(DeviceNetworkConf deviceNetworkConf, String deviceId) {
      if (deviceNetworkConf != null) {
         try {
            CASResponse casResponse = null;
            int var3 = 0;

            do {
               CASValue casObj = CacheFactory.getCache().gets("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, new DeviceDBCacheEntity());
               DeviceDBCacheEntity targetEntity = (DeviceDBCacheEntity)casObj.getValue();
               targetEntity.setDeviceNetworkConf(deviceNetworkConf);
               targetEntity.initializedDeviceNetworkConf = true;
               logger.debug("[save: DB value to DeviceNetworkConf Memory] deviceId=" + deviceId);
               casResponse = CacheFactory.getCache().cas("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, casObj.getCas(), targetEntity);
            } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var3++));
         } catch (Exception var6) {
            logger.debug("[DBCache] Error to save setDeviceNetworkConf info to cache. deviceId " + deviceId);
         }

      }
   }

   public static DeviceSystemInfoConf getDeviceSystemInfoConf(String deviceId) throws Exception {
      try {
         DeviceDBCacheEntity deviceDBCacheEntity = getDeviceDBCacheEntityFromCache(deviceId);
         if (deviceDBCacheEntity == null) {
            throw new NullPointerException();
         } else if (!deviceDBCacheEntity.initializedDeviceSystemInfoConf) {
            logger.debug("[Miss:No value in DeviceSystemInfoConf Memory] deviceId=" + deviceId);
            throw new NullPointerException();
         } else {
            DeviceSystemInfoConf retDeviceSystemInfoConf = deviceDBCacheEntity.getDeviceSystemInfoConf();
            logger.debug("[Hit:get value from DeviceSystemInfoConf Memory]  - success to get deviceDisplayConf info from cache " + deviceId);
            return retDeviceSystemInfoConf;
         }
      } catch (NullPointerException var3) {
         logger.debug("[init:No device Memory] deviceId=" + deviceId);
         throw var3;
      } catch (Exception var4) {
         logger.error("DBCacheUtils Exception deviceID = " + deviceId);
         throw var4;
      }
   }

   public static void setDeviceSystemInfoConf(DeviceSystemInfoConf deviceSystemInfoConf, String deviceId) {
      if (deviceSystemInfoConf != null) {
         try {
            CASResponse casResponse = null;
            int var3 = 0;

            do {
               CASValue casObj = CacheFactory.getCache().gets("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, new DeviceDBCacheEntity());
               DeviceDBCacheEntity targetEntity = (DeviceDBCacheEntity)casObj.getValue();
               targetEntity.setDeviceSystemInfoConf(deviceSystemInfoConf);
               targetEntity.initializedDeviceSystemInfoConf = true;
               logger.debug("[save: DB value to DeviceSystemInfoConf Memory] deviceId=" + deviceId);
               casResponse = CacheFactory.getCache().cas("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, casObj.getCas(), targetEntity);
            } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var3++));
         } catch (Exception var6) {
            logger.debug("[DBCache] Error to save setDeviceSystemInfoConf info to cache. deviceId " + deviceId);
         }

      }
   }

   public static DeviceSystemSetupConf getDeviceSystemSetupConf(String deviceId) throws Exception {
      try {
         DeviceDBCacheEntity deviceDBCacheEntity = getDeviceDBCacheEntityFromCache(deviceId);
         if (deviceDBCacheEntity == null) {
            throw new NullPointerException();
         } else if (!deviceDBCacheEntity.initializedDeviceSystemSetupConf) {
            logger.debug("[Miss:No value in DeviceSystemSetupConf Memory] deviceId=" + deviceId);
            throw new NullPointerException();
         } else {
            DeviceSystemSetupConf retDeviceSystemSetupConf = deviceDBCacheEntity.getDeviceSystemSetupConf();
            logger.debug("[Hit:get value from DeviceSystemSetupConf Memory]  - success to get deviceDisplayConf info from cache " + deviceId);
            return retDeviceSystemSetupConf;
         }
      } catch (NullPointerException var3) {
         logger.debug("[init:No device Memory] deviceId=" + deviceId);
         throw var3;
      } catch (Exception var4) {
         logger.error("DBCacheUtils Exception deviceId = " + deviceId);
         throw var4;
      }
   }

   public static void setDeviceSystemSetupConf(DeviceSystemSetupConf deviceSystemSetupConf, String deviceId) {
      if (deviceSystemSetupConf != null) {
         try {
            CASResponse casResponse = null;
            int var3 = 0;

            do {
               CASValue casObj = CacheFactory.getCache().gets("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, new DeviceDBCacheEntity());
               DeviceDBCacheEntity targetEntity = (DeviceDBCacheEntity)casObj.getValue();
               targetEntity.setDeviceSystemSetupConf(deviceSystemSetupConf);
               targetEntity.initializedDeviceSystemSetupConf = true;
               logger.debug("[save: DB value to DeviceSystemSetupConf Memory] deviceId=" + deviceId);
               casResponse = CacheFactory.getCache().cas("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId, casObj.getCas(), targetEntity);
            } while(!MemcacheFailoverUtils.isOKCASResponse(casResponse, var3++));
         } catch (Exception var6) {
            logger.debug("[DBCache] Error to save setDeviceSystemSetupConf info to cache. deviceId " + deviceId);
         }

      }
   }

   private static DeviceDBCacheEntity getDeviceDBCacheEntityFromCache(String deviceId) throws ClassNotFoundException, IOException, Exception {
      try {
         DeviceDBCacheEntity deviceDBCacheEntity = (DeviceDBCacheEntity)CacheFactory.getCache().get("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId);
         return CacheFactory.getCacheType().equals("jndi") && deviceDBCacheEntity != null ? (DeviceDBCacheEntity)deviceDBCacheEntity.getClone() : deviceDBCacheEntity;
      } catch (Exception var3) {
         throw var3;
      }
   }

   public static void runDBQueryThread(Object mapper, Object device) {
      StackTraceElement[] stackTraceElementArray = Thread.currentThread().getStackTrace();
      logger.debug("[runDBQueryThread:Device Memory to DB] executeQueryThread - method name : " + stackTraceElementArray[2].getClassName() + "." + stackTraceElementArray[2].getMethodName() + "() line:" + stackTraceElementArray[2].getLineNumber());
      DBQueryRunnerThread dBQueryRunnerThread = new DBQueryRunnerThread(mapper, stackTraceElementArray[2].getMethodName(), device);
      dBQueryRunnerThread.start();
   }

   public static void deleteDeviceEntities(String deviceId) {
      try {
         if (CacheFactory.getCache().get("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId) != null) {
            CacheFactory.getCache().delete("DB_CACHE_DEVICE_MEMORY_MAP" + deviceId);
         }

         if (CacheFactory.getCache().get("WAITING_REQUEST_QUEUE" + deviceId) != null) {
            CacheFactory.getCache().delete("WAITING_REQUEST_QUEUE" + deviceId);
         }

         if (CacheFactory.getCache().get("REQUESTED_MSG_MAP" + deviceId) != null) {
            CacheFactory.getCache().delete("REQUESTED_MSG_MAP" + deviceId);
         }

         if (CacheFactory.getCache().get("CONTEXT_MEMORY_MAP" + deviceId) != null) {
            CacheFactory.getCache().delete("CONTEXT_MEMORY_MAP" + deviceId);
         }
      } catch (Exception var2) {
         logger.error("DBCacheUtils Exception deviceId = " + deviceId);
      }

   }

   public static void setPreAssignedGroup(String deviceId, DeviceGroup deviceGroup) {
      Object preAssignedGroupMap = null;

      try {
         preAssignedGroupMap = (Map)CacheFactory.getCache().get("PRE_ASSIGNED_GROUP_MAP");
         if (preAssignedGroupMap == null) {
            preAssignedGroupMap = new HashMap();
         }

         ((Map)preAssignedGroupMap).put(deviceId, deviceGroup);
         CacheFactory.getCache().set("PRE_ASSIGNED_GROUP_MAP", preAssignedGroupMap);
      } catch (Exception var4) {
         logger.error("DBCacheUtils setPreAssignedGroup", var4);
      }

   }

   public static void deletePreAssignedGroup(String deviceId) {
      Object preAssignedGroupMap = null;

      try {
         preAssignedGroupMap = (Map)CacheFactory.getCache().get("PRE_ASSIGNED_GROUP_MAP");
         if (preAssignedGroupMap == null) {
            preAssignedGroupMap = new HashMap();
         }

         ((Map)preAssignedGroupMap).remove(deviceId);
         CacheFactory.getCache().set("PRE_ASSIGNED_GROUP_MAP", preAssignedGroupMap);
      } catch (Exception var3) {
         logger.error("DBCacheUtils setPreAssignedGroup", var3);
      }

   }

   public static DeviceGroup getPreAssignedGroup(String deviceId) {
      Map preAssignedGroupMap = null;

      try {
         preAssignedGroupMap = (Map)CacheFactory.getCache().get("PRE_ASSIGNED_GROUP_MAP");
      } catch (Exception var3) {
         logger.error("DBCacheUtils getPreAssignedGroup", var3);
      }

      return (DeviceGroup)preAssignedGroupMap.get(deviceId);
   }

   public static Map getPreAssignedGroupMap() {
      Object preAssignedGroupMap = null;

      try {
         preAssignedGroupMap = (Map)CacheFactory.getCache().get("PRE_ASSIGNED_GROUP_MAP");
         if (preAssignedGroupMap == null) {
            preAssignedGroupMap = new HashMap();
         }
      } catch (Exception var2) {
         logger.error("DBCacheUtils getPreAssignedGroup", var2);
      }

      return (Map)preAssignedGroupMap;
   }

   public static void setRmqlExpireTime(RMQL rmql, long seconds) {
      String messageId = UUID.randomUUID().toString();
      rmql.addParamObject("messageId", messageId);

      try {
         CacheFactory.getCache(CacheFactory.CACHE_TYPE_LOCAL).set("MO_EXPIRE_TIME_" + messageId, seconds * 1000L);
      } catch (Exception var5) {
         logger.error("DBCacheUtils setRmqlExpireTime", var5);
      }

   }
}
