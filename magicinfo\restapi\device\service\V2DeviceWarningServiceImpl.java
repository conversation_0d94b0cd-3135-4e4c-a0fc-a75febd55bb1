package com.samsung.magicinfo.restapi.device.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.Menu;
import com.samsung.magicinfo.framework.common.MenuEntity;
import com.samsung.magicinfo.framework.common.MessageSourceManager;
import com.samsung.magicinfo.framework.common.MessageSourceManagerImpl;
import com.samsung.magicinfo.framework.device.constants.DeviceConstants;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManager;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManagerImpl;
import com.samsung.magicinfo.framework.device.warningRule.entity.DeviceThreshold;
import com.samsung.magicinfo.framework.device.warningRule.entity.DeviceWarningRule;
import com.samsung.magicinfo.framework.device.warningRule.manager.DeviceWarningRuleInfo;
import com.samsung.magicinfo.framework.device.warningRule.manager.DeviceWarningRuleInfoImpl;
import com.samsung.magicinfo.framework.monitoring.entity.ClientFaultEntity;
import com.samsung.magicinfo.framework.monitoring.entity.DeviceErrorWarningEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ErrorWarning;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteFail;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.device.model.V2ClientFaultResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceErrorWarning;
import com.samsung.magicinfo.restapi.device.model.V2DeviceErrorWarningResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGetWarningResources;
import com.samsung.magicinfo.restapi.device.model.V2DeviceThreshold;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningGroupConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningRule;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningRuleElement;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningRuleElementResoruce;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.NotFoundException;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.ModelAndView;

@Service("V2DeviceWarningService")
@Transactional
public class V2DeviceWarningServiceImpl implements V2DeviceWarningService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceWarningServiceImpl.class);
   private DeviceStatisticsDownloadService downloadService = null;

   public V2DeviceWarningServiceImpl() {
      super();
   }

   public void setDownloadService(DeviceStatisticsDownloadService downloadService) {
      this.downloadService = downloadService;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceGetWarningResources getWarningRuleElement(String ruleId) throws Exception {
      V2DeviceGetWarningResources resource = new V2DeviceGetWarningResources();
      DeviceWarningRuleInfo ruleInfo = DeviceWarningRuleInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      if (!this.isExistWarningRule(ruleId)) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"ruleId"});
      } else {
         V2DeviceWarningRule warningRule = new V2DeviceWarningRule();
         DeviceWarningRule rule = ruleInfo.getWarningRule(Long.valueOf(ruleId));
         List thresholdTempList = ruleInfo.getWarningRuleElementList();
         List existThresholdList = rule.getThresholdList();
         if (thresholdTempList != null) {
            for(int idx1 = 0; idx1 < thresholdTempList.size(); ++idx1) {
               for(int idx2 = 0; idx2 < existThresholdList.size(); ++idx2) {
                  if (((DeviceThreshold)thresholdTempList.get(idx1)).getElement_id().equalsIgnoreCase(((DeviceThreshold)existThresholdList.get(idx2)).getElement_id())) {
                     ((DeviceThreshold)thresholdTempList.get(idx1)).setElement_value(((DeviceThreshold)existThresholdList.get(idx2)).getElement_value());
                     break;
                  }
               }
            }
         }

         String organName = userGroupInfo.getGroupNameByGroupId(rule.getOrganization_id());
         warningRule.setRuleId(rule.getRule_id());
         warningRule.setRuleName(rule.getRule_name());
         warningRule.setDeviceType(rule.getDevice_type());
         warningRule.setOrganizationId(rule.getOrganization_id());
         warningRule.setOrganizationName(organName);
         warningRule.setRuleDescription(rule.getRule_description());
         warningRule.setCreateDate(rule.getCreate_date());
         warningRule.setThresholds(rule.getThresholdList());
         warningRule.convertListMaptoObject(rule.getDevice_group_list(), "group");
         resource.setWarningRuleInfo(warningRule);
         ObjectMapper mapper = new ObjectMapper();
         List thresholdList = new ArrayList();
         HashMap map;
         if (thresholdTempList != null) {
            for(int i = 0; i < thresholdTempList.size(); ++i) {
               new V2DeviceThreshold();
               String jsonString = mapper.writeValueAsString(thresholdTempList.get(i));
               Map map = (Map)mapper.readValue(jsonString, new TypeReference() {
               });
               map = ConvertUtil.convertMap(map);
               V2DeviceThreshold threshold = (V2DeviceThreshold)mapper.convertValue(map, V2DeviceThreshold.class);
               thresholdList.add(threshold);
            }
         }

         resource.setThresholdList(thresholdList);
         String userId = SecurityUtils.getLoginUserId();
         List organizationList = new ArrayList();
         User pUser = userInfo.getAllByUserId(userId);
         map = new HashMap();
         map.put("groupId", "0");
         map.put("groupName", "Common");
         organizationList.add(map);
         if (pUser.getRoot_group_id() == 0L) {
            List list = userGroupInfo.getAllOrganizationGroup();
            list = ConvertUtil.convertList(list);

            for(int i = 0; i < list.size(); ++i) {
               organizationList.add(list.get(i));
            }
         } else {
            Map oMap = new HashMap();
            oMap.put("groupId", pUser.getRoot_group_id());
            oMap.put("groupName", pUser.getOrganization());
            organizationList.add(oMap);
         }

         resource.convertListMaptoObject(organizationList);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource getWarningRules(int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder) throws Exception {
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "create_date";
      }

      if (StrUtils.nvl(sortColumn).equals("")) {
         sortOrder = "desc";
      }

      --startIndex;
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      SelectCondition selectCondition = new SelectCondition();
      selectCondition.setSort_name(sortColumn);
      selectCondition.setOrder_dir(sortOrder);
      selectCondition.setStart_index(startIndex);
      selectCondition.setPage_size(pageSize);
      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      selectCondition.setCommonSearchKeyword(searchText);

      try {
         String userId = SecurityUtils.getLoginUserId();
         UserInfo userInfo = UserInfoImpl.getInstance();
         Long org_id = userInfo.getRootGroupIdByUserId(userId);
         if (org_id != null && org_id != 0L) {
            selectCondition.setOrg_id(org_id);
         }
      } catch (Exception var17) {
         selectCondition.setOrg_id((Long)null);
      }

      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      DeviceWarningRuleInfo ruleManager = DeviceWarningRuleInfoImpl.getInstance();
      PagedListInfo info = ruleManager.getPagedList(startIndex, pageSize, condition, "");
      List warningRuleList = info.getPagedResultList();
      List list = new ArrayList();

      for(int i = 0; i < warningRuleList.size(); ++i) {
         DeviceWarningRule warningRule = (DeviceWarningRule)warningRuleList.get(i);
         V2DeviceWarningRule rule = new V2DeviceWarningRule();
         String organName = userGroupInfo.getGroupNameByGroupId(warningRule.getOrganization_id());
         rule.setOrganizationId(warningRule.getOrganization_id());
         rule.setOrganizationName(warningRule.getOrganization_id() == 0L ? "Common" : organName);
         rule.setDeviceType(warningRule.getDevice_type());
         rule.setRuleName(warningRule.getRule_name());
         rule.setRuleDescription(warningRule.getRule_description());
         rule.setCreateDate(warningRule.getCreate_date());
         rule.setRuleId(warningRule.getRule_id());
         if (warningRule.getDevice_group_ids() != null) {
            rule.setDeviceGroupIds(warningRule.getDevice_group_ids());
         }

         list.add(rule);
      }

      V2PageResource newResource = V2PageResource.createPageResource(list, info, pageSize);
      ++startIndex;
      newResource.setStartIndex(startIndex);
      return newResource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody warningRule(String groupId, String mode) throws SQLException {
      ResponseBody responseBody = new ResponseBody();

      try {
         String var10000 = StrUtils.nvl(groupId).equals("") ? "" : groupId;
         var10000 = StrUtils.nvl(mode).equals("") ? "image" : mode;
         Locale locale = SecurityUtils.getLocale();
         UserContainer userContainer = SecurityUtils.getUserContainer();
         boolean var6 = false;

         try {
            if (CommonConfig.get("RMS_MODE") != null && Boolean.valueOf(CommonConfig.get("RMS_MODE"))) {
               var6 = true;
            }
         } catch (Exception var14) {
         }

         MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
         MenuEntity resultMenu = new MenuEntity();
         Menu search;
         Menu tableWidth;
         if (userContainer.checkAuthority("Device Write")) {
            search = new Menu();
            search.setName("COM_BUTTON_ADD");
            search.setUrl("");
            resultMenu.putMenu("deviceWarningRuleAdd", search);
            tableWidth = new Menu();
            tableWidth.setName("COM_BUTTON_DELETE");
            tableWidth.setUrl("");
            tableWidth.setClassName("hasMultiChk");
            resultMenu.putMenu("deviceWarningRuleDelete", tableWidth);
         }

         search = new Menu();
         search.setName("MIS_SID_20_WARNING_RULE");
         resultMenu.putMenu("search", search);
         tableWidth = new Menu();
         tableWidth.setTable("37px");
         tableWidth.setTable("70px");
         tableWidth.setTable("70px");
         tableWidth.setTable("150px");
         tableWidth.setTable("100px");
         tableWidth.setTable("100px");
         tableWidth.setTable("50px");
         resultMenu.putMenu("tableWidth", tableWidth);
         Menu tableHeader = new Menu();
         tableHeader.setTable("check");
         tableHeader.setTable("TABLE_ORGANIZATION_P");
         tableHeader.setTable("TABLE_TYPE_P");
         tableHeader.setTable("LIST_TITLE_NAME");
         tableHeader.setTable("COM_TEXT_INFORMATION_P");
         tableHeader.setTable("TEXT_DATE_P");
         tableHeader.setTable("BUTTON_APPLY");
         resultMenu.putMenu("tableHeader", tableHeader);
         Menu tableSort = new Menu();
         tableSort.setTable("no-sort");
         tableSort.setTable("no-sort");
         tableSort.setTable("");
         tableSort.setTable("");
         tableSort.setTable("");
         tableSort.setTable("");
         tableSort.setTable("no-sort");
         resultMenu.putMenu("tableSort", tableSort);
         Menu tableColumnName = new Menu();
         tableColumnName.setTable("check");
         tableColumnName.setTable("organization_id");
         tableColumnName.setTable("device_type");
         tableColumnName.setTable("rule_name");
         tableColumnName.setTable("rule_description");
         tableColumnName.setTable("create_date");
         tableColumnName.setTable("apply");
         resultMenu.putMenu("tableColumnName", tableColumnName);
         resultMenu.setStatus("success");
         responseBody.setStatus("Success");
         responseBody.setItems(resultMenu);
         return responseBody;
      } catch (Exception var15) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var15.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceWarningRuleElementResoruce createWarningRuleElement(V2DeviceWarningRuleElement element) throws Exception {
      V2DeviceWarningRuleElementResoruce resoruce = new V2DeviceWarningRuleElementResoruce();
      String rule_name = StrUtils.nvl(element.getRuleName()).equals("") ? "" : element.getRuleName();
      String rule_desc = StrUtils.nvl(element.getRuleDesc()).equals("") ? "" : element.getRuleDesc();
      String organization_id = StrUtils.nvl(element.getOrganId()).equals("") ? "" : element.getOrganId();
      String strGroupIds = ConvertUtil.convertListToStringWithSeparator(element.getGroupIds(), ",");
      boolean checkFlag = false;
      if (!strGroupIds.equals("")) {
         Iterator var8 = element.getGroupIds().iterator();

         while(var8.hasNext()) {
            String groupId = (String)var8.next();

            try {
               RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.parseLong(groupId));
            } catch (Exception var22) {
               checkFlag = true;
               break;
            }
         }

         if (checkFlag) {
            RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
         }
      }

      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, Long.parseLong(organization_id));
      String device_group_id = "";
      if (element.getGroupIds() != null) {
         device_group_id = ConvertUtil.convertListToStringWithSeparator(element.getGroupIds(), ",");
      }

      DeviceWarningRuleInfo ruleInfo = DeviceWarningRuleInfoImpl.getInstance();
      DeviceWarningRule rule = new DeviceWarningRule();
      Long rule_id = new Long((long)SequenceDB.getNextValue("MI_DMS_INFO_WARNING_RULE"));
      rule.setRule_id(rule_id);
      rule.setRule_name(rule_name);
      rule.setRule_description(rule_desc);
      rule.setDevice_type("LEDBOX");
      rule.setOrganization_id(Long.valueOf(organization_id));
      List thresholdList = new ArrayList();
      List defaultThresholdList = ruleInfo.getWarningRuleElementList();
      Map defaultThresholdMap = new HashMap();
      if (defaultThresholdList != null && defaultThresholdList.size() > 0) {
         Iterator var15 = defaultThresholdList.iterator();

         while(var15.hasNext()) {
            DeviceThreshold th = (DeviceThreshold)var15.next();
            defaultThresholdMap.put(th.getElement_id(), th);
         }
      }

      if (element.getThresholds() != null) {
         List thresholds = element.getThresholds();

         for(int i = 0; i < thresholds.size(); ++i) {
            DeviceThreshold tmp = new DeviceThreshold();
            V2DeviceThreshold threshold = (V2DeviceThreshold)thresholds.get(i);
            String elementId = threshold.getElementId();
            String elementValue = threshold.getElementValue();
            if ((!elementId.equals("IC") || !isStringDouble(elementValue) || Integer.parseInt(elementValue) < 1 || Integer.parseInt(elementValue) > ((DeviceThreshold)defaultThresholdMap.get("IC")).getMax_value()) && (!elementId.equals("LOD") || !isStringDouble(elementValue) || Integer.parseInt(elementValue) < 1 || Integer.parseInt(elementValue) > ((DeviceThreshold)defaultThresholdMap.get("LOD")).getMax_value()) && (!elementId.equals("POWER") || !isStringDouble(elementValue) || Integer.parseInt(elementValue) < 1 || Integer.parseInt(elementValue) > ((DeviceThreshold)defaultThresholdMap.get("POWER")).getMax_value()) && (!elementId.equals("TEMPERATURE") || !isStringDouble(elementValue) || Integer.parseInt(elementValue) < 1 || Integer.parseInt(elementValue) > ((DeviceThreshold)defaultThresholdMap.get("TEMPERATURE")).getMax_value())) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"elementId"});
            }

            tmp.setRule_id(rule_id);
            tmp.setElement_id(elementId);
            tmp.setElement_value(elementValue);
            tmp.setCondition(">");
            thresholdList.add(tmp);
         }
      }

      rule.setThresholdList(thresholdList);
      ruleInfo.addDeviceWarningRuleInfo(rule);
      ruleInfo.deleteGroupMapByRuleId(rule_id);
      if (device_group_id != null && device_group_id.length() > 0) {
         String[] deviceGroupIdList = device_group_id.split(",");
         if (deviceGroupIdList.length > 0) {
            Long[] lGroupIdList = new Long[deviceGroupIdList.length];
            int index = 0;
            String[] var30 = deviceGroupIdList;
            int var32 = deviceGroupIdList.length;

            for(int var33 = 0; var33 < var32; ++var33) {
               String groupId = var30[var33];
               if (!groupId.isEmpty()) {
                  lGroupIdList[index] = Long.parseLong(groupId);
                  ++index;
               }
            }

            ruleInfo.deleteGroupMapByGroupId(lGroupIdList);

            for(int i = 0; i < deviceGroupIdList.length; ++i) {
               ruleInfo.addMapGroupWarningRule(rule_id, Long.parseLong(deviceGroupIdList[i]));
            }
         }
      }

      resoruce.setRuleId(rule_id);
      resoruce.setStatus("success");
      return resoruce;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceWarningRuleElementResoruce updateWarningRuleElement(String ruleId, V2DeviceWarningRuleElement element) throws Exception {
      V2DeviceWarningRuleElementResoruce resoruce = new V2DeviceWarningRuleElementResoruce();
      if (!this.isExistWarningRule(ruleId)) {
         throw new NotFoundException("Not exist ruleId.");
      } else {
         String tmp_rule_id = ruleId;
         String rule_name = StrUtils.nvl(element.getRuleName()).equals("") ? "" : element.getRuleName();
         String rule_desc = StrUtils.nvl(element.getRuleDesc()).equals("") ? "" : element.getRuleDesc();
         String organization_id = StrUtils.nvl(element.getOrganId()).equals("") ? "" : element.getOrganId();
         String device_group_id = "";
         String strGroupIds = ConvertUtil.convertListToStringWithSeparator(element.getGroupIds(), ",");
         boolean checkFlag = false;
         if (!strGroupIds.equals("")) {
            Iterator var11 = element.getGroupIds().iterator();

            while(var11.hasNext()) {
               String groupId = (String)var11.next();

               try {
                  RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.parseLong(groupId));
               } catch (Exception var25) {
                  checkFlag = true;
                  break;
               }
            }

            if (checkFlag) {
               RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
            }

            device_group_id = strGroupIds;
         }

         RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, Long.parseLong(organization_id));
         DeviceWarningRuleInfo ruleInfo = DeviceWarningRuleInfoImpl.getInstance();
         DeviceWarningRule rule = new DeviceWarningRule();
         Long rule_id = new Long((long)SequenceDB.getNextValue("MI_DMS_INFO_WARNING_RULE"));

         try {
            if (tmp_rule_id != null && tmp_rule_id.length() > 0) {
               rule_id = Long.parseLong(tmp_rule_id);
            }
         } catch (Exception var24) {
            rule_id = new Long((long)SequenceDB.getNextValue("MI_DMS_INFO_WARNING_RULE"));
         }

         rule.setRule_id(rule_id);
         rule.setRule_name(rule_name);
         rule.setRule_description(rule_desc);
         rule.setDevice_type("LEDBOX");
         rule.setOrganization_id(Long.valueOf(organization_id));
         List thresholdList = new ArrayList();
         List defaultThresholdList = ruleInfo.getWarningRuleElementList();
         Map defaultThresholdMap = new HashMap();
         if (defaultThresholdList != null && defaultThresholdList.size() > 0) {
            Iterator var17 = defaultThresholdList.iterator();

            while(var17.hasNext()) {
               DeviceThreshold th = (DeviceThreshold)var17.next();
               defaultThresholdMap.put(th.getElement_id(), th);
            }
         }

         if (element.getThresholds() != null) {
            List thresholds = element.getThresholds();

            for(int i = 0; i < thresholds.size(); ++i) {
               DeviceThreshold tmp = new DeviceThreshold();
               V2DeviceThreshold threshold = (V2DeviceThreshold)thresholds.get(i);
               String elementId = threshold.getElementId();
               String elementValue = threshold.getElementValue();
               if ((!elementId.equals("IC") || !isStringDouble(elementValue) || Integer.parseInt(elementValue) < 1 || Integer.parseInt(elementValue) > ((DeviceThreshold)defaultThresholdMap.get("IC")).getMax_value()) && (!elementId.equals("LOD") || !isStringDouble(elementValue) || Integer.parseInt(elementValue) < 1 || Integer.parseInt(elementValue) > ((DeviceThreshold)defaultThresholdMap.get("LOD")).getMax_value()) && (!elementId.equals("POWER") || !isStringDouble(elementValue) || Integer.parseInt(elementValue) < 1 || Integer.parseInt(elementValue) > ((DeviceThreshold)defaultThresholdMap.get("POWER")).getMax_value()) && (!elementId.equals("TEMPERATURE") || !isStringDouble(elementValue) || Integer.parseInt(elementValue) < 1 || Integer.parseInt(elementValue) > ((DeviceThreshold)defaultThresholdMap.get("TEMPERATURE")).getMax_value())) {
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"elementId"});
               }

               tmp.setRule_id(rule_id);
               tmp.setElement_id(elementId);
               tmp.setElement_value(elementValue);
               tmp.setCondition(">");
               thresholdList.add(tmp);
            }
         }

         rule.setThresholdList(thresholdList);
         ruleInfo.addDeviceWarningRuleInfo(rule);
         ruleInfo.deleteGroupMapByRuleId(rule_id);
         if (device_group_id != null && device_group_id.length() > 0) {
            String[] deviceGroupIdList = device_group_id.split(",");
            if (deviceGroupIdList.length > 0) {
               Long[] lGroupIdList = new Long[deviceGroupIdList.length];
               int index = 0;
               String[] var33 = deviceGroupIdList;
               int var35 = deviceGroupIdList.length;

               for(int var36 = 0; var36 < var35; ++var36) {
                  String groupId = var33[var36];
                  if (!groupId.isEmpty()) {
                     lGroupIdList[index] = Long.parseLong(groupId);
                     ++index;
                  }
               }

               ruleInfo.deleteGroupMapByGroupId(lGroupIdList);

               for(int i = 0; i < deviceGroupIdList.length; ++i) {
                  ruleInfo.addMapGroupWarningRule(rule_id, Long.parseLong(deviceGroupIdList[i]));
               }
            }
         }

         resoruce.setRuleId(rule_id);
         resoruce.setStatus("success");
         return resoruce;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2CommonBulkResultResource deleteWarningRuleElement(V2CommonIds ruleIds) throws Exception {
      V2CommonBulkResultResource result = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      boolean flag = false;
      Iterator var6 = ruleIds.getIds().iterator();

      while(var6.hasNext()) {
         String ruleId = (String)var6.next();

         try {
            ruleId = StrUtils.nvl(ruleId).equals("") ? "" : ruleId;
            if (!this.isExistWarningRule(ruleId)) {
               flag = true;
               break;
            }
         } catch (Exception var12) {
            this.logger.error("", var12);
            flag = true;
            break;
         }
      }

      if (flag) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"ruleId"});
      } else {
         DeviceWarningRuleInfo ruleInfo = DeviceWarningRuleInfoImpl.getInstance();
         Iterator var14 = ruleIds.getIds().iterator();

         while(var14.hasNext()) {
            String ruleId = (String)var14.next();

            V2CommonDeleteFail failData;
            try {
               if (ruleId != null) {
                  String[] ruleIdList = new String[]{ruleId};
                  if (ruleInfo.deleteWarningRuleInfo(ruleIdList)) {
                     ruleInfo.deleteWarningRuleElementByRuleId(Long.parseLong(ruleId));
                     ruleInfo.deleteGroupMapByRuleId(Long.parseLong(ruleId));
                     successList.add(ruleId);
                  } else {
                     failData = new V2CommonDeleteFail();
                     failData.setId(ruleId);
                     failData.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_WARNING_RULE_DELETE_FAIL.getMessage());
                     failData.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_WARNING_RULE_DELETE_FAIL.getCode());
                     failList.add(failData);
                  }
               }
            } catch (Exception var11) {
               this.logger.error("", var11);
               failData = new V2CommonDeleteFail();
               failData.setId(ruleId);
               failData.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getMessage());
               failData.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN.getCode());
               failList.add(failData);
            }
         }

         result.setSuccessList(successList);
         result.setFailList(failList);
         return result;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public String updateDevGroup(String mode, String selectedId, String strGroupId) throws Exception {
      LinkedHashMap resultMap = new LinkedHashMap();
      mode = StrUtils.nvl(mode).equals("") ? "" : mode;
      if (mode != null && mode.equalsIgnoreCase("deviceWarningRule")) {
         String ruleId = StrUtils.nvl(selectedId).equals("") ? "" : selectedId;
         strGroupId = StrUtils.nvl(strGroupId).equals("") ? "" : strGroupId;
         DeviceWarningRuleInfo ruleInfo = DeviceWarningRuleInfoImpl.getInstance();
         ruleInfo.deleteGroupMapByRuleId(Long.parseLong(ruleId));
         if (strGroupId != null && strGroupId.length() > 0) {
            String[] groupIdList = strGroupId.split(",");
            Long[] lGroupIdList = new Long[groupIdList.length];
            int index = 0;
            String[] var10 = groupIdList;
            int var11 = groupIdList.length;

            for(int var12 = 0; var12 < var11; ++var12) {
               String groupId = var10[var12];
               if (!groupId.isEmpty()) {
                  lGroupIdList[index] = Long.parseLong(groupId);
                  ++index;
               }
            }

            ruleInfo.deleteGroupMapByGroupId(lGroupIdList);
            if (groupIdList.length > 0) {
               for(int i = 0; i < groupIdList.length; ++i) {
                  ruleInfo.addMapGroupWarningRule(Long.parseLong(ruleId), Long.parseLong(groupIdList[i]));
               }
            }
         }

         resultMap.put("status", "success");
      }

      return (new JSONObject(resultMap)).toString();
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource errorWarningList(int startIndex, int pageSize, String type, String searchText, String sortColumn, String sortOrder, String warningType, List warningTypes) throws Exception {
      AlarmManager alarmDao = AlarmManagerImpl.getInstance();
      SelectCondition selectCondition = new SelectCondition();
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "client_time";
      }

      if (StrUtils.nvl(sortColumn).equals("")) {
         sortOrder = "desc";
      }

      selectCondition.setSort_name(sortColumn);
      selectCondition.setOrder_dir(sortOrder);
      if (type.equalsIgnoreCase("RESOLVED")) {
         selectCondition.setType("deviceResolved");
      } else if (type.equalsIgnoreCase("WARNING")) {
         if (!CollectionUtils.isEmpty(warningTypes)) {
            if (warningTypes.size() == 2) {
               selectCondition.setType("allWarning");
            } else if (warningTypes.size() == 1) {
               if ("SW".equalsIgnoreCase((String)warningTypes.get(0))) {
                  selectCondition.setType("softwareWarning");
               } else if ("HW".equalsIgnoreCase((String)warningTypes.get(0))) {
                  selectCondition.setType("deviceWarning");
               }
            }
         } else if (warningTypes.size() == 0) {
            selectCondition.setType("NONE");
         } else {
            selectCondition.setType("allWarning");
            if ("SW".equalsIgnoreCase(warningType)) {
               selectCondition.setType("softwareWarning");
            } else if ("HW".equalsIgnoreCase(warningType)) {
               selectCondition.setType("deviceWarning");
            }
         }
      } else {
         selectCondition.setType("deviceError");
         if ("SW".equalsIgnoreCase(warningType)) {
            selectCondition.setType("softwareError");
         }
      }

      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      selectCondition.setSrc_name(searchText);
      String organization = SecurityUtils.getUserContainer().getUser().getOrganization();
      selectCondition.setRole_name(SecurityUtils.getUserContainer().getUser().getRole_name());
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      Long orgId = (long)groupDao.getDeviceGroupForUser(organization);
      if (orgId.intValue() > 0) {
         selectCondition.setOrg_id(orgId);
         selectCondition.setUser_id(SecurityUtils.getUserContainer().getUser().getUser_id());
      }

      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      PagedListInfo info = alarmDao.getPagedList(startIndex, pageSize, condition, "getDeviceErrorWarningList");
      List deviceErrorWarningList = info.getPagedResultList();
      int totalCount = info.getTotalRowCount();
      SimpleDateFormat sdfFrom = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
      new V2PageResource();
      List list = new ArrayList();
      if (deviceErrorWarningList != null) {
         Iterator var21 = deviceErrorWarningList.iterator();

         while(var21.hasNext()) {
            DeviceErrorWarningEntity entity = (DeviceErrorWarningEntity)var21.next();
            if (entity != null) {
               V2DeviceErrorWarningResource errorWarningResource = new V2DeviceErrorWarningResource();
               errorWarningResource.setDeviceId(entity.getDevice_id());
               errorWarningResource.setDeviceName(entity.getDevice_name());
               errorWarningResource.setInProgressCount(entity.getIn_progress_count());
               errorWarningResource.setResolvedCount(entity.getResolved_count());
               if (entity.getFault() != null) {
                  V2ClientFaultResource clientFaultResource = new V2ClientFaultResource();
                  ClientFaultEntity clientFaultEntity = entity.getFault();
                  clientFaultResource.setFaultMapList(clientFaultEntity.getFaultMapList());
                  clientFaultResource.setId(clientFaultEntity.getId());
                  clientFaultResource.setDeviceId(clientFaultEntity.getDeviceId());
                  clientFaultResource.setCategory(clientFaultEntity.getCategory());
                  clientFaultResource.setCode(clientFaultEntity.getCode());
                  clientFaultResource.setLevel(clientFaultEntity.getLevel());
                  errorWarningResource.setWarningType(this.getWarningType(clientFaultEntity.getLevel()));
                  if (clientFaultEntity.getClientTime() != null) {
                     Date clientTime = sdfFrom.parse(clientFaultEntity.getClientTime());
                     clientFaultResource.setClientTime(new Timestamp(clientTime.getTime()));
                  }

                  clientFaultResource.setBodyFormat(clientFaultEntity.getBodyFormat());
                  clientFaultResource.setServerTime(clientFaultEntity.getServerTime());
                  clientFaultResource.setErrorScript(clientFaultEntity.getErrorScript());
                  clientFaultResource.setRecovered(clientFaultEntity.getRecovered());
                  errorWarningResource.setFault(clientFaultResource);
               }

               list.add(errorWarningResource);
            }
         }
      }

      V2PageResource result = V2PageResource.createPageResource(list, info, pageSize);
      result.setStartIndex(startIndex);
      return result;
   }

   private String getWarningType(String level) {
      if (level == null) {
         return "";
      } else {
         byte var3 = -1;
         switch(level.hashCode()) {
         case 65:
            if (level.equals("A")) {
               var3 = 1;
            }
            break;
         case 70:
            if (level.equals("F")) {
               var3 = 2;
            }
            break;
         case 2660:
            if (level.equals("SW")) {
               var3 = 0;
            }
         }

         switch(var3) {
         case 0:
         case 1:
         case 2:
            return "SW";
         default:
            return "HW";
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource errorWarningDetail(String deviceId, int startIndex, int pageSize, String warningType, String type, Long postTime) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      new V2PageResource();
      String sortName = "c.client_time";
      String orderDir = "desc";
      warningType = StrUtils.nvl(warningType).equals("") ? "HW" : warningType.toUpperCase();
      type = type.toUpperCase();
      byte var11 = -1;
      switch(type.hashCode()) {
      case 66247144:
         if (type.equals("ERROR")) {
            var11 = 0;
         }
         break;
      case 1842428796:
         if (type.equals("WARNING")) {
            var11 = 1;
         }
      }

      switch(var11) {
      case 0:
         type = "deviceError";
         break;
      case 1:
         type = "deviceWarning";
         break;
      default:
         type = "deviceResolved";
      }

      if ((type.equals("deviceError") || type.equals("deviceWarning")) && warningType.equals("SW")) {
         type = type.replaceAll("device", "software");
      }

      AlarmManager alarmDao = AlarmManagerImpl.getInstance();
      ListManager listMgr = new ListManager(alarmDao, "commonlist");
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sortName);
      condition.setOrder_dir(orderDir);
      condition.setType(type);
      condition.setDevice_id(deviceId);
      if (null != postTime) {
         condition.setPost_time(new Timestamp(postTime));
      }

      User currentUser = SecurityUtils.getUserContainer().getUser();
      if (currentUser.getRoot_group_id() != 0L) {
         condition.setGroup_id(currentUser.getRoot_group_id());
         condition.setUser_id(currentUser.getUser_id());
      }

      listMgr.addSearchInfo("condition", condition);
      listMgr.setLstSize(Integer.valueOf(pageSize));
      listMgr.setSection("getErrorWarningList");
      List errorWarningList = listMgr.V2dbexecute(startIndex, pageSize);
      List tempList = new ArrayList();
      SimpleDateFormat sdfFrom = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
      if (errorWarningList != null) {
         for(int i = 0; i < errorWarningList.size(); ++i) {
            V2DeviceErrorWarning temp = new V2DeviceErrorWarning();
            temp.setCode(((ErrorWarning)errorWarningList.get(i)).getCode());
            temp.setDeviceId(((ErrorWarning)errorWarningList.get(i)).getDevice_id());
            temp.setFirstFaultId(((ErrorWarning)errorWarningList.get(i)).getFirst_fault_id());
            Date firstReported = sdfFrom.parse(((ErrorWarning)errorWarningList.get(i)).getFirst_reported());
            temp.setFirstReported(new Timestamp(firstReported.getTime()));
            if (((ErrorWarning)errorWarningList.get(i)).getLast_fault() != null) {
               V2ClientFaultResource entity = new V2ClientFaultResource();
               entity.setBodyFormat(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getBodyFormat());
               entity.setCategory(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getCategory());
               Date clientTime = sdfFrom.parse(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getClientTime());
               entity.setClientTime(new Timestamp(clientTime.getTime()));
               entity.setCode(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getCode());
               entity.setDeviceId(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getDeviceId());
               entity.setErrorScript(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getErrorScript());
               entity.setId(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getId());
               entity.setLevel(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getLevel());
               entity.setRecovered(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getRecovered());
               entity.setServerTime(((ErrorWarning)errorWarningList.get(i)).getLast_fault().getServerTime());
               temp.setLastFault(entity);
            }

            temp.setLastFaultId(((ErrorWarning)errorWarningList.get(i)).getLast_fault_id());
            Date lastReported = sdfFrom.parse(((ErrorWarning)errorWarningList.get(i)).getLast_reported());
            temp.setLastReported(new Timestamp(lastReported.getTime()));
            temp.setLevel(((ErrorWarning)errorWarningList.get(i)).getLevel());
            temp.setRecovered(((ErrorWarning)errorWarningList.get(i)).getRecovered());
            temp.setRecoveredTime(((ErrorWarning)errorWarningList.get(i)).getRecovered_time());
            tempList.add(temp);
         }
      }

      PageManager pageMgr = listMgr.getPageManager();
      V2PageResource result = V2PageResource.createPageResource(tempList, pageMgr);
      result.setStartIndex(startIndex);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource errorWarningDetailHistory(String deviceId, String errorCode, String startId, String endId, int startIndex, int pageSize, String warningType, String type) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      new V2PageResource();
      String sort_name = "client_time";
      String order_dir = "desc";
      String search_id = "-1";
      String device_id = StrUtils.nvl(deviceId).equals("") ? "" : deviceId;
      warningType = StrUtils.nvl(warningType).equals("") ? "HW" : warningType.toUpperCase();
      type = type.toUpperCase();
      byte var15 = -1;
      switch(type.hashCode()) {
      case 66247144:
         if (type.equals("ERROR")) {
            var15 = 0;
         }
         break;
      case 1842428796:
         if (type.equals("WARNING")) {
            var15 = 1;
         }
      }

      switch(var15) {
      case 0:
         type = "deviceError";
         break;
      case 1:
         type = "deviceWarning";
         break;
      default:
         type = "deviceResolved";
      }

      if ((type.equals("deviceError") || type.equals("deviceWarning")) && warningType.equals("SW")) {
         type = type.replaceAll("device", "software");
      }

      AlarmManager alarmDao = AlarmManagerImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      ListManager listMgr = new ListManager(alarmDao, "commonlist");
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sort_name);
      condition.setOrder_dir(order_dir);
      condition.setType(type);

      try {
         if (errorCode != null) {
            condition.setCode(errorCode);
         }

         if (startId != null) {
            condition.setStart_id(Long.parseLong(startId));
         }

         if (endId != null) {
            condition.setEnd_id(Long.parseLong(endId));
         }
      } catch (Exception var40) {
         condition.setStart_id((Long)null);
         condition.setEnd_id((Long)null);
      }

      UserContainer userContainer = SecurityUtils.getUserContainer();
      String organization = userContainer.getUser().getOrganization();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      Long orgId = (long)groupDao.getDeviceGroupForUser(organization);
      if (orgId.intValue() != 0) {
         condition.setGroup_id(orgId);
      }

      condition.setDevice_id(device_id);
      listMgr.addSearchInfo("condition", condition);
      if (!search_id.equals("-1")) {
         listMgr.addSearchInfo("search_id", search_id);
      }

      condition.setUser_id(userContainer.getUser().getUser_id());
      listMgr.setLstSize(Integer.valueOf(pageSize));
      listMgr.setSection("getClientFaultHistoryList");
      PageManager pageMgr = null;
      List clientFaultList = listMgr.V2dbexecute(startIndex, pageSize);
      List list = new ArrayList();
      ObjectMapper mapper = new ObjectMapper();
      mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
      mapper.configure(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES, false);
      SimpleDateFormat sdfFrom = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

      for(int i = 0; i < clientFaultList.size(); ++i) {
         ClientFaultEntity clientFault = (ClientFaultEntity)clientFaultList.get(i);
         new V2ClientFaultResource();
         String jsonString = mapper.writeValueAsString(clientFault);

         try {
            Map map = (Map)mapper.readValue(jsonString, new TypeReference() {
            });
            Map map = ConvertUtil.convertMap(map);
            V2ClientFaultResource tempObj = (V2ClientFaultResource)mapper.convertValue(map, V2ClientFaultResource.class);
            Device device = deviceDao.getDeviceMinInfo(clientFault.getDevice_id());
            AlarmManager alarmManager = AlarmManagerImpl.getInstance();
            String error_script = "";
            String category = clientFault.getCategory();
            String bodyContent;
            if (clientFault.getCode() != null && !category.equalsIgnoreCase("LOG")) {
               bodyContent = null;
               if (clientFault.getCode().length() == 8) {
                  bodyContent = clientFault.getCode().substring(0, 3);
               } else {
                  bodyContent = clientFault.getCode();
               }

               error_script = alarmManager.getErrorScript(bodyContent);
            }

            bodyContent = clientFault.getBody_format();
            if (error_script == null || error_script.equals("") || error_script.equalsIgnoreCase("null")) {
               error_script = "Invalid Error Code";
            }

            if (bodyContent != null && !StrUtils.nvl(bodyContent).equals("")) {
               if (category.equalsIgnoreCase("LOG")) {
                  String[] tempFileName = bodyContent.replace('\\', '/').split("/");
                  String url = "servlet/FileLoader?paramPathConfName=JOBS_RESULT_HOME&filepath=" + tempFileName[tempFileName.length - 1] + "&download=D&device_id=" + deviceId;
                  error_script = url.replace('\\', '/');
               } else if (clientFault.getCode().equals("25001")) {
                  String inputSource = DeviceConstants.getInputSourceName(bodyContent);
                  if (inputSource != null && !inputSource.equals("")) {
                     error_script = error_script + " (" + inputSource + ")";
                  }
               } else {
                  error_script = error_script + " (" + bodyContent + ")";
               }
            }

            tempObj.setDeviceName(device.getDevice_name());
            tempObj.setBodyFormat(error_script);
            tempObj.setHighlight(clientFault.getCategory().indexOf(":H") > 0);

            try {
               Long clientTimeLong = sdfFrom.parse(clientFault.getClient_time()).getTime();
               tempObj.setClientTime(new Timestamp(clientTimeLong));
            } catch (Exception var39) {
            }

            list.add(tempObj);
         } catch (Exception var41) {
            this.logger.error("", var41);
         }
      }

      pageMgr = listMgr.getPageManager();
      V2PageResource result = V2PageResource.createPageResource(list, pageMgr);
      result.setStartIndex(startIndex);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceWarningRule getWarningRuleElements() throws Exception {
      DeviceWarningRuleInfo ruleInfo = DeviceWarningRuleInfoImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      List thresholdList = null;

      try {
         thresholdList = ruleInfo.getWarningRuleElementList();
      } catch (Exception var11) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"list of the warning-rule element"});
      }

      String userId = SecurityUtils.getLoginUserId();
      List organizationTempList = new ArrayList();
      User pUser = userInfo.getAllByUserId(userId);
      Map tmpMap = new HashMap();
      tmpMap.put("groupId", "0");
      tmpMap.put("groupName", "Common");
      organizationTempList.add(tmpMap);
      if (pUser.getRoot_group_id() == 0L) {
         List list = userGroupInfo.getAllOrganizationGroup();
         list = ConvertUtil.convertList(list);

         for(int i = 0; i < list.size(); ++i) {
            organizationTempList.add(list.get(i));
         }
      } else {
         Map oMap = new HashMap();
         oMap.put("groupId", pUser.getRoot_group_id());
         oMap.put("groupName", pUser.getOrganization());
         organizationTempList.add(oMap);
      }

      V2DeviceWarningRule resource = new V2DeviceWarningRule();
      resource.setThresholds(thresholdList);
      resource.convertListMaptoObject(organizationTempList, "org");
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ModelAndView errorWarningExport(String warningType, String type, HttpServletResponse response, String localeData, String searchText) {
      warningType = StrUtils.nvl(warningType).equals("") ? "" : warningType.toUpperCase();
      type = type.toUpperCase();
      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      byte var7 = -1;
      switch(type.hashCode()) {
      case 66247144:
         if (type.equals("ERROR")) {
            var7 = 0;
         }
         break;
      case 1842428796:
         if (type.equals("WARNING")) {
            var7 = 1;
         }
      }

      switch(var7) {
      case 0:
         type = "deviceError";
         break;
      case 1:
         type = "allWarning";
         break;
      default:
         type = "deviceResolved";
      }

      if ((type.equals("deviceError") || type.equals("deviceWarning")) && warningType.equals("SW")) {
         type = type.replaceAll("device", "software");
      }

      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      Timestamp nowTime = new Timestamp(System.currentTimeMillis());
      String titleDeviceName = rms.getMessage("TABLE_DEVICE_NAME_P", (Object[])null, locale);
      String titleDeviceId = rms.getMessage("TABLE_MAC_ADDR_P", (Object[])null, locale);
      String titleDeviceModelName = rms.getMessage("TABLE_DEVICE_MODEL_NAME_P", (Object[])null, locale);
      String titleCode = rms.getMessage("ADMIN_DEVICEINFO_DEVICEMODEL_CONTENTS_CODE", (Object[])null, locale);
      String titleDescription = rms.getMessage("TEXT_DESCRIPTION_P", (Object[])null, locale);
      String titleOccuredTime = rms.getMessage("MIS_SID_OCCURRED", (Object[])null, locale);
      String titleRecentlyTime = rms.getMessage("MIS_SID_RECENTLY_REPORTED", (Object[])null, locale);
      String titleStatus = rms.getMessage("TEXT_STATUS_P", (Object[])null, locale);
      String[] columnNames = new String[]{"device_name", "device_id", "device_model_name", "code", "description", "occured", "recent", "status"};
      String[] fieldNames = new String[]{titleDeviceName, titleDeviceId, titleDeviceModelName, titleCode, titleDescription, titleOccuredTime, titleRecentlyTime, titleStatus};
      AlarmManager alarmDao = AlarmManagerImpl.getInstance();
      DeviceInfo deviceMgr = DeviceInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      String organization = SecurityUtils.getUserContainer().getUser().getOrganization();
      HashMap dataMap = new HashMap();

      try {
         int orgId = groupDao.getDeviceGroupForUser(organization);
         List ewList = null;
         if (orgId > 0) {
            ewList = alarmDao.getErrorWarningListBySearchText(type, (long)orgId, SecurityUtils.getUserContainer().getUser().getUser_id(), searchText);
         } else {
            ewList = alarmDao.getErrorWarningListBySearchText(type, (Long)null, (String)null, searchText);
         }

         List dataList = new ArrayList();
         Device device = null;
         Iterator sheetName;
         ErrorWarning ew;
         if (ewList != null) {
            sheetName = ewList.iterator();

            label121:
            while(true) {
               HashMap d;
               do {
                  if (!sheetName.hasNext()) {
                     break label121;
                  }

                  ew = (ErrorWarning)sheetName.next();
                  d = new HashMap();
                  if (device == null || !device.getDevice_id().equals(ew.getDevice_id())) {
                     device = deviceMgr.getDevice(ew.getDevice_id());
                  }
               } while(device == null);

               d.put("device_name", device.getDevice_name());
               d.put("device_id", device.getDevice_id());
               d.put("device_model_name", device.getDevice_model_name());
               d.put("code", ew.getCode());
               d.put("description", ew.getLast_fault().getError_script());
               String firstReportTimeFormat;
               if (ew.getFirst_reported() != null && ew.getLast_reported() != null) {
                  firstReportTimeFormat = "yyyy-MM-dd'T'HH:mm:ss";
                  String lastReportTimeFormat = "yyyy-MM-dd'T'HH:mm:ss";
                  String conversionTimeFormat = "yyyy-MM-dd HH:mm:ss";
                  if (!ew.getFirst_reported().contains("T")) {
                     firstReportTimeFormat = conversionTimeFormat;
                  }

                  if (!ew.getLast_reported().contains("T")) {
                     lastReportTimeFormat = conversionTimeFormat;
                  }

                  Date first = (new SimpleDateFormat(firstReportTimeFormat)).parse(ew.getFirst_reported());
                  Date recent = (new SimpleDateFormat(lastReportTimeFormat)).parse(ew.getLast_reported());
                  d.put("occured", (new SimpleDateFormat(conversionTimeFormat)).format(first));
                  d.put("recent", (new SimpleDateFormat(conversionTimeFormat)).format(recent));
               } else {
                  d.put("occured", "");
                  d.put("recent", "");
               }

               firstReportTimeFormat = "";
               if (!"softwareWarning".equalsIgnoreCase(type)) {
                  if (ew.getRecovered()) {
                     firstReportTimeFormat = rms.getMessage("MIS_SID_RESOLVED", (Object[])null, locale);
                  } else {
                     firstReportTimeFormat = rms.getMessage("MSG_PROCESSING", (Object[])null, locale);
                  }
               }

               d.put("status", firstReportTimeFormat);
               dataList.add(d);
            }
         }

         sheetName = null;
         ew = null;
         byte var41 = -1;
         switch(type.hashCode()) {
         case -1566946990:
            if (type.equals("deviceError")) {
               var41 = 4;
            }
            break;
         case -655587218:
            if (type.equals("deviceResolved")) {
               var41 = 2;
            }
            break;
         case 5930086:
            if (type.equals("deviceWarning")) {
               var41 = 3;
            }
            break;
         case 105672865:
            if (type.equals("softwareError")) {
               var41 = 0;
            }
            break;
         case 1075842037:
            if (type.equals("softwareWarning")) {
               var41 = 1;
            }
         }

         String sheetName;
         String fileName;
         switch(var41) {
         case 0:
            sheetName = "Software Error";
            fileName = "SoftwareErrorList_" + nowTime.toString() + ".xls";
            break;
         case 1:
            sheetName = "Software Warning";
            fileName = "SoftwareWarningList_" + nowTime.toString() + ".xls";
            break;
         case 2:
            sheetName = "Resolved";
            fileName = "DeviceResolvedList_" + nowTime.toString() + ".xls";
            break;
         case 3:
            sheetName = "Device Warning";
            fileName = "DeviceWarningList_" + nowTime.toString() + ".xls";
            break;
         case 4:
         default:
            sheetName = "Device Error";
            fileName = "DeviceErrorList_" + nowTime.toString() + ".xls";
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList.toArray());
      } catch (Exception var36) {
         this.logger.error("", var36);
      }

      this.downloadService = new DeviceStatisticsDownloadService();
      this.downloadService.downloadExcelFile(dataMap, response);
      return null;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2CommonBulkResultResource waringGroupUpdate(V2DeviceWarningGroupConf param) throws Exception {
      String ruleId = StrUtils.nvl(param.getRuleId());
      String groupIds = null;
      if (param.getGroupIds() == null) {
         this.logger.error("[DeviceWarningService][waringGroupUpdate] " + RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID.generateFormattedMessages("groupIds"));
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"groupIds"});
      } else {
         groupIds = ConvertUtil.convertListToStringWithSeparator(param.getGroupIds(), ",");
         boolean checkFlag = false;
         Iterator var5 = param.getGroupIds().iterator();

         while(var5.hasNext()) {
            String groupId = (String)var5.next();

            try {
               RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.parseLong(groupId));
            } catch (Exception var19) {
               checkFlag = true;
               break;
            }
         }

         if (checkFlag) {
            RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
         }

         if (ruleId.equals("")) {
            this.logger.error("[DeviceWarningService][waringGroupUpdate] " + RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID.generateFormattedMessages("ruleId"));
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"ruleId"});
         } else {
            V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
            List successList = new ArrayList();
            List failList = new ArrayList();
            DeviceWarningRuleInfo ruleInfo = DeviceWarningRuleInfoImpl.getInstance();

            try {
               ruleInfo.deleteGroupMapByRuleId(Long.parseLong(ruleId));
            } catch (Exception var17) {
               this.logger.error("[DeviceWarningService][waringGroupUpdate] " + RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL.generateFormattedMessages("group mapped with ruleId"));
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL, new String[]{"group mapped with ruleId"});
            }

            if (groupIds == null) {
               this.logger.error("[DeviceWarningService][waringGroupUpdate] " + RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID.generateFormattedMessages("groupIds"));
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"groupIds"});
            } else {
               if (!groupIds.trim().equals("")) {
                  String[] groupIdList = groupIds.split(",");
                  Long[] lGroupIdList = new Long[groupIdList.length];
                  int index = 0;
                  String[] var12 = groupIdList;
                  int var13 = groupIdList.length;

                  for(int var14 = 0; var14 < var13; ++var14) {
                     String groupId = var12[var14];
                     if (!groupId.isEmpty()) {
                        lGroupIdList[index] = Long.parseLong(groupId);
                        ++index;
                     }
                  }

                  try {
                     ruleInfo.deleteGroupMapByGroupId(lGroupIdList);
                     if (groupIdList.length > 0) {
                        for(int i = 0; i < groupIdList.length; ++i) {
                           try {
                              ruleInfo.addMapGroupWarningRule(Long.parseLong(ruleId), Long.parseLong(groupIdList[i]));
                              successList.add(groupIdList[i]);
                           } catch (Exception var16) {
                              this.logger.error("[DeviceWarningService][waringGroupUpdate]", var16);
                              V2CommonDeleteFail fail = new V2CommonDeleteFail();
                              fail.setId(groupIdList[i]);
                              fail.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR.getMessage());
                              fail.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR.getCode());
                              failList.add(fail);
                           }
                        }
                     }
                  } catch (Exception var18) {
                     this.logger.error("[DeviceWarningService][waringGroupUpdate] " + RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL.generateFormattedMessages("group"));
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_DELETE_FAIL, new String[]{"group"});
                  }
               }

               resource.setSuccessList(successList);
               resource.setFailList(failList);
               return resource;
            }
         }
      }
   }

   public static boolean isStringDouble(String s) {
      try {
         Double.parseDouble(s);
         return true;
      } catch (NumberFormatException var2) {
         return false;
      }
   }

   private boolean isExistWarningRule(String ruleId) throws Exception {
      DeviceWarningRuleInfo ruleInfo = DeviceWarningRuleInfoImpl.getInstance();
      DeviceWarningRule warningRule = ruleInfo.getWarningRuleInfo(Long.valueOf(ruleId));
      return warningRule != null;
   }
}
