package com.samsung.magicinfo.restapi.device.service;

import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonUpdateResult;
import com.samsung.magicinfo.restapi.device.model.V2DeviceModelNameResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSoftwareDeploy;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSoftwareDeployResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSoftwareItem;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSoftwareResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSwDeployInfoResource;
import com.samsung.magicinfo.restapi.device.model.V2SoftwareDeploymentStatus;
import com.samsung.magicinfo.restapi.device.model.V2SoftwareFileResource;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.sql.SQLException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

public interface V2DeviceSoftwaresService {
   V2DeviceSoftwareResource saveSw(String var1, String var2, String var3, String var4, MultipartFile var5, HttpServletRequest var6) throws SQLException;

   V2CommonBulkResultResource deleteSw(List var1, HttpServletRequest var2) throws Exception;

   V2DeviceSoftwareResource editSwSave(V2DeviceSoftwareItem var1, String var2) throws Exception;

   V2DeviceSoftwareDeployResource editSwDeploy(String var1) throws Exception;

   V2DeviceSoftwareDeployResource cancelSwDeploy(String var1, HttpServletRequest var2) throws Exception;

   V2DeviceSoftwareDeployResource saveSwDeploy(String var1, String var2) throws Exception;

   V2DeviceSoftwareResource publishPopup(String var1) throws Exception;

   V2CommonUpdateResult publishSave(V2DeviceSoftwareDeploy var1) throws Exception;

   V2DeviceSwDeployInfoResource swPublishDetail(String var1, int var2, int var3, String var4, String var5, String var6) throws Exception;

   V2DeviceSoftwareDeployResource swDeployRetry(String var1, String var2) throws Exception;

   V2DeviceSoftwareResource editSw(String var1) throws Exception;

   V2PageResource getRegisteredSoftwareList(int var1, int var2, String var3, String var4, String var5, String var6) throws Exception;

   V2SoftwareDeploymentStatus getReservedSoftwareDeploymentStatusDetail(String var1, String var2, int var3, int var4) throws Exception;

   V2PageResource getReservedSoftwareDeploymentStatusList(String var1, int var2, int var3, String var4, String var5, String var6) throws Exception;

   V2DeviceModelNameResource getDeviceNameListByDeviceType(String var1) throws Exception;

   ModelAndView softwareExport(String var1, String var2, String var3, String var4, String var5, HttpServletResponse var6, String var7);

   ModelAndView softwareDepolyExport(String var1, String var2, String var3, String var4, String var5, HttpServletResponse var6, String var7);

   void softwareDownload(String var1, HttpServletRequest var2, HttpServletResponse var3) throws Exception;

   V2SoftwareFileResource registeredDetail(String var1) throws Exception;
}
