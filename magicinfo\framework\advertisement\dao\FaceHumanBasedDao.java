package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.FaceInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.FaceKey;
import com.samsung.magicinfo.framework.advertisement.entity.FaceRawEntity;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.apache.logging.log4j.Logger;

public class FaceHumanBasedDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(FaceHumanBasedDao.class);
   int DOW = 0;
   int QUARTER = 0;
   Timestamp DAY = null;
   Timestamp MONTH = null;
   Timestamp YEAR = null;

   public FaceHumanBasedDao() {
      super();
   }

   private void setDayItems(String logDate) {
      try {
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         Date date = sdf.parse(logDate);
         Calendar cal = Calendar.getInstance();
         cal.setTime(date);
         this.DOW = cal.get(7) - 1;
         this.QUARTER = cal.get(2) / 3 + 1;
      } catch (ParseException var5) {
         this.logger.error("", var5);
      }

      this.DAY = Timestamp.valueOf(logDate + " 00:00:00");
      this.MONTH = Timestamp.valueOf(logDate.substring(0, 7) + "-01 00:00:00");
      this.YEAR = Timestamp.valueOf(logDate.substring(0, 4) + "-01-01 00:00:00");
   }

   public boolean insertFaceHumanBasedHistory(List play_history) throws SQLException {
      int totalRows = play_history.size();
      String[] common_info = (String[])play_history.get(0);
      String device_id = common_info[0];
      String log_date = common_info[1];
      boolean isDuplicated = false;
      this.setDayItems(log_date);
      HashMap mapRawByAmsKey = this.splitContentBasedHistoryByAmsKey(play_history, totalRows);
      if (mapRawByAmsKey.size() == 0) {
         this.logger.error("[STATISTICS] splitted map size is zero");
         return false;
      } else {
         HashMap mapDailyInfo = new HashMap();
         HashMap mapNewDailyInfoToMonth = new HashMap();
         HashMap mapNewDailyInfoToYear = new HashMap();
         Set keySet = mapRawByAmsKey.keySet();

         Iterator iterator;
         FaceKey amsKey;
         FaceInfoEntity originalYearPopInfo;
         for(iterator = keySet.iterator(); iterator.hasNext(); mapDailyInfo.put(amsKey, originalYearPopInfo)) {
            amsKey = (FaceKey)iterator.next();
            ArrayList amsRawList = (ArrayList)mapRawByAmsKey.get(amsKey);
            originalYearPopInfo = new FaceInfoEntity();

            try {
               this.addPopHourInfoList(device_id, amsKey, amsRawList, originalYearPopInfo);
            } catch (Exception var18) {
               isDuplicated = true;
               this.logger.error("[STATISTICS] - Blocking Hour-info-DAO insert by using duplicate key value violates - (device_id: " + device_id + " content_id: " + amsKey + ")");
            }
         }

         try {
            this.addPopDayInfoList(device_id, mapDailyInfo);
         } catch (Exception var17) {
            isDuplicated = true;
            this.logger.error("[STATISTICS] - Blocking Day-info-DAO insert by using duplicate key value violates - (device_id: " + device_id + " log_date: " + log_date + ")");
         }

         if (isDuplicated) {
            return false;
         } else {
            keySet = mapDailyInfo.keySet();
            iterator = keySet.iterator();

            while(iterator.hasNext()) {
               FaceKey amsKey = (FaceKey)iterator.next();
               FaceInfoEntity tmpPopInfoToAdd = (FaceInfoEntity)mapDailyInfo.get(amsKey);
               FaceInfoEntity originalMonthPopInfo = this.getPopMonthInfo(amsKey, device_id);
               if (originalMonthPopInfo != null) {
                  originalMonthPopInfo.addDuration(tmpPopInfoToAdd.getDuration());
                  originalMonthPopInfo.addViewCount(tmpPopInfoToAdd.getView_count());
                  this.setPopMonthInfo(amsKey, device_id, originalMonthPopInfo.getDuration(), originalMonthPopInfo.getView_count());
               } else {
                  mapNewDailyInfoToMonth.put(amsKey, tmpPopInfoToAdd);
               }

               originalYearPopInfo = this.getPopYearInfo(amsKey, device_id);
               if (originalYearPopInfo != null) {
                  originalYearPopInfo.addDuration(tmpPopInfoToAdd.getDuration());
                  originalYearPopInfo.addViewCount(tmpPopInfoToAdd.getView_count());
                  this.setPopYearInfo(amsKey, device_id, originalYearPopInfo.getDuration(), originalYearPopInfo.getView_count());
               } else {
                  mapNewDailyInfoToYear.put(amsKey, tmpPopInfoToAdd);
               }
            }

            if (mapNewDailyInfoToMonth.size() != 0) {
               this.addPopMonthInfoList(device_id, mapNewDailyInfoToMonth);
            }

            if (mapNewDailyInfoToYear.size() != 0) {
               this.addPopYearInfoList(device_id, log_date, mapNewDailyInfoToYear);
            }

            return true;
         }
      }
   }

   private FaceInfoEntity getPopMonthInfo(FaceKey amsKey, String device_id) throws SQLException {
      return ((FaceHumanBasedDaoMapper)this.getMapper()).getPopMonthInfo(device_id, amsKey, this.MONTH);
   }

   private FaceInfoEntity getPopYearInfo(FaceKey amsKey, String device_id) throws SQLException {
      return ((FaceHumanBasedDaoMapper)this.getMapper()).getPopYearInfo(device_id, amsKey, this.YEAR);
   }

   private void addPopDayInfoList(String deviceId, HashMap dailyPlayInfo) throws SQLException {
      ((FaceHumanBasedDaoMapper)this.getMapper()).addPopDayInfoList(deviceId, dailyPlayInfo, this.DAY, this.DOW);
   }

   private HashMap splitContentBasedHistoryByAmsKey(List play_history, int totalRows) {
      HashMap mapRaw = new HashMap();

      for(int i = 1; i < totalRows; ++i) {
         String[] play_info = (String[])play_history.get(i);

         Timestamp start_time;
         try {
            start_time = Timestamp.valueOf(play_info[0]);
         } catch (IllegalArgumentException var11) {
            this.logger.error("[STATISTICS] IllegalArgumentException has been occured for " + play_info[0] + " index : " + i);
            continue;
         }

         FaceRawEntity cbse = new FaceRawEntity(start_time, Integer.valueOf(play_info[3]), Integer.valueOf(play_info[4]));
         FaceKey amsKey = new FaceKey((String)null, play_info[1], play_info[2]);
         ArrayList arrCbse;
         if ((arrCbse = (ArrayList)mapRaw.get(amsKey)) == null) {
            ArrayList newArrCbse = new ArrayList();
            newArrCbse.add(cbse);
            mapRaw.put(amsKey, newArrCbse);
         } else {
            arrCbse.add(cbse);
            mapRaw.put(amsKey, arrCbse);
         }
      }

      return mapRaw;
   }

   private void addPopHourInfoList(String deviceId, FaceKey amsKey, ArrayList amsRawEntity, FaceInfoEntity dailyPlayInfo) throws SQLException {
      Iterator var5 = amsRawEntity.iterator();

      while(var5.hasNext()) {
         FaceRawEntity entity = (FaceRawEntity)var5.next();
         dailyPlayInfo.addViewCount(entity.getCount());
         dailyPlayInfo.addDuration(entity.getDuration());
      }

      ((FaceHumanBasedDaoMapper)this.getMapper()).addPopHourInfoList(deviceId, amsKey, amsRawEntity);
   }

   private void addPopMonthInfoList(String deviceId, HashMap mapNewDailyInfo) throws SQLException {
      ((FaceHumanBasedDaoMapper)this.getMapper()).addPopMonthInfoList(deviceId, mapNewDailyInfo, this.MONTH, this.QUARTER);
   }

   private void addPopYearInfoList(String deviceId, String logDate, HashMap mapNewDailyInfo) throws SQLException {
      ((FaceHumanBasedDaoMapper)this.getMapper()).addPopYearInfoList(deviceId, mapNewDailyInfo, Timestamp.valueOf(logDate.substring(0, 4) + "-01-01 00:00:00"));
   }

   private void setPopMonthInfo(FaceKey amsKey, String device_id, int duration, int playcount) throws SQLException {
      ((FaceHumanBasedDaoMapper)this.getMapper()).setPopMonthInfo(device_id, amsKey, duration, playcount, this.MONTH);
   }

   private void setPopYearInfo(FaceKey amsKey, String device_id, int duration, int playcount) throws SQLException {
      ((FaceHumanBasedDaoMapper)this.getMapper()).setPopYearInfo(device_id, amsKey, duration, playcount, this.YEAR);
   }

   public boolean checkDuplicationByDB(String log_date, String device_id) {
      Long cnt = null;

      try {
         cnt = ((FaceHumanBasedDaoMapper)this.getMapper()).checkDuplicationByDB(device_id, Timestamp.valueOf(log_date + " 00:00:00"));
      } catch (SQLException var5) {
         this.logger.error(var5);
      }

      return cnt.intValue() < 1;
   }
}
