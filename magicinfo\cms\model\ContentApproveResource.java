package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_NULL)
public class ContentApproveResource {
   @ApiModelProperty(
      example = "32FA85B4-2389-476C-845A-0FC6F1D10D81"
   )
   private String contentId = "";
   @NotNull
   @ApiModelProperty(
      example = "APPROVED",
      required = true
   )
   @Pattern(
      regexp = "APPROVED|REJECTED",
      message = "[ContentApproveResource][status] Only APPROVED, REJECTED are available."
   )
   private String status = "UNAPPROVED";
   @ApiModelProperty(
      example = "-"
   )
   @Size(
      min = 0,
      max = 200
   )
   private String opinion = "";

   public ContentApproveResource() {
      super();
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public String getStatus() {
      return this.status;
   }

   public void setStatus(String status) {
      this.status = status;
   }

   public String getOpinion() {
      return this.opinion;
   }

   public void setOpinion(String opinion) {
      this.opinion = opinion;
   }
}
