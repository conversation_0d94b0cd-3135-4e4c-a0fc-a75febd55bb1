package com.samsung.magicinfo.restapi.device.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.MDCTimeStrUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.device.model.V2DeviceDisplayListResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.device.utils.RESTDeviceUtils;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.rms.model.DeviceDisplayConfResource;
import com.samsung.magicinfo.rms.model.GeneralInfoResource;
import com.samsung.magicinfo.rms.util.DeviceModelConverter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2DeviceDisplayService")
@Transactional
public class V2DeviceDisplayServiceImpl implements V2DeviceDisplayService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceApprovalServiceImpl.class);
   private final String PRODUCT_TYPE = "PREMIUM";
   private final String ALL_MDC = "ALL_MDC";
   private final Map supportMap = new HashMap();
   DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
   DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();

   public V2DeviceDisplayServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource getDisplayControlInfo(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var14) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var6 = deviceIds.getIds().iterator();

      while(var6.hasNext()) {
         String deviceId = (String)var6.next();
         DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance("PREMIUM");

         try {
            DeviceDisplayConf deviceDisplayConf = displayDao.getDeviceDisplayConf(deviceId);
            DeviceDisplayConfResource displayInfoCamel = DeviceModelConverter.newConvertDisplayInfoToCamelStyle(deviceDisplayConf);
            displayInfoCamel.setInputSourceList(RESTDeviceUtils.getInputSourceList(RESTDeviceUtils.INPUTSOURCE_CODE, RESTDeviceUtils.INPUTSOURCE_NAME));
            boolean isPcMode = true;
            if (displayInfoCamel.getBasicSource() != null) {
               isPcMode = DeviceUtils.checkPcMode(displayInfoCamel.getBasicSource());
            }

            displayInfoCamel.setPcMode(isPcMode);
            successList.add(displayInfoCamel);
         } catch (NullPointerException var12) {
            this.logger.error("[REST_v2.0][DEVICE DISPLAY SERVICE][getDisplayControlInfo] This ID is not exist : " + deviceId);
            failList.add(deviceId);
         } catch (Exception var13) {
            this.logger.error("[REST_v2.0][DEVICE DISPLAY SERVICE][getDisplayControlInfo]" + deviceId, var13);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceReqServiceResource updateDisplayInfo(DeviceDisplayConfResource param) throws Exception {
      List deviceIds = param.getDeviceIds();
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var16) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      String requestId = UUID.randomUUID().toString();
      Iterator var8 = deviceIds.iterator();

      String deviceId;
      while(var8.hasNext()) {
         deviceId = (String)var8.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      var8 = deviceIds.iterator();

      while(var8.hasNext()) {
         deviceId = (String)var8.next();

         try {
            Device device = this.deviceDao.getDeviceMinInfo(deviceId);
            String deviceModelCode = device.getDevice_model_code();
            String modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
            int var13 = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
         } catch (NullPointerException var15) {
            this.logger.error("[REST_v2.0][DEVICE DISPLAY SERVICE][updateDisplayInfo] This ID is not exist : " + deviceId);
            failList.add(deviceId);
         }

         DeviceDisplayConf deviceDisplayConf = DeviceModelConverter.newConvertDisplayConfToSnakeStyle(param);
         deviceDisplayConf.setDevice_id(deviceId);

         try {
            this.confManager.reqSetDisplayToDevice(deviceDisplayConf, requestId, "ALL_MDC");
            successList.add(deviceId);
         } catch (Exception var14) {
            this.logger.error("[REST_v2.0][DEVICE DISPLAY SERVICE][updateDisplayInfo] This ID is not update : " + deviceId);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      if (successList.size() > 0) {
         this.confManager.reqGetDisplayFromDevice((String)deviceIds.get(0), requestId, "ALL_MDC");
         resource.setRequestId(requestId);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceReqServiceResource getCurrentStatusDisplay(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var12) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      String sessionId = UUID.randomUUID().toString();
      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var7 = deviceIds.getIds().iterator();

      String deviceId;
      while(var7.hasNext()) {
         deviceId = (String)var7.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds.getIds());
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      var7 = deviceIds.getIds().iterator();

      while(var7.hasNext()) {
         deviceId = (String)var7.next();
         new GeneralInfoResource();

         try {
            this.confManager.reqGetDisplayFromDevice(deviceId, sessionId, "ALL_MDC");
            successList.add(deviceId);
         } catch (Exception var11) {
            this.logger.error(var11);
            failList.add(deviceId);
         }
      }

      if (successList.size() > 0) {
         resource.setRequestId(sessionId);
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource getCurrentStatusDisplayWithRequestId(V2DeviceReqServiceConf body) throws Exception {
      List deviceIds = body.getDeviceIds();
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var17) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      String requestId = body.getRequestId();
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      Iterator var9 = deviceIds.iterator();

      while(var9.hasNext()) {
         String deviceId = (String)var9.next();

         try {
            DeviceDisplayConf info = confMgr.getDisplayResultSet(deviceId, requestId, "GET_DEVICE_DISPLAY_CONF", "ALL_MDC");
            if (info == null) {
               this.logger.error("[REST_v2.0][DEVICE DISPLAY SERVICE][getCurrentStatusDisplayWithRequestId] " + deviceId + " , " + requestId);
               failList.add(deviceId);
            } else {
               DeviceDisplayConfManager displayDao = DeviceDisplayConfManagerImpl.getInstance("PREMIUM");
               DeviceDisplayConf deviceDisplayConf = displayDao.getDeviceDisplayConf(deviceId);
               DeviceDisplayConfResource deviceDisplayConfResource = DeviceModelConverter.newConvertDisplayInfoToCamelStyle(deviceDisplayConf);
               deviceDisplayConfResource.setDeviceId(deviceId);
               deviceDisplayConfResource.setInputSourceList(RESTDeviceUtils.getInputSourceList(RESTDeviceUtils.INPUTSOURCE_CODE, RESTDeviceUtils.INPUTSOURCE_NAME));
               deviceDisplayConfResource.setPcMode(DeviceUtils.checkPcMode(deviceDisplayConfResource.getBasicSource()));
               Map summary = confMgr.getDisplayResultSetSummary(deviceId, requestId, "SET_DEVICE_DISPLAY_CONF", "ALL_MDC");
               deviceDisplayConfResource.setSettingResultSummary(summary);
               successList.add(deviceDisplayConfResource);
            }
         } catch (Exception var16) {
            this.logger.error("", var16);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource filterDeviceDisplayList(V2DeviceFilter filter, HttpServletResponse response) throws Exception {
      String productType = "PREMIUM";
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2PageResource result = new V2PageResource();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      userGroupInfo.getOrgGroupIdByName(userContainer.getUser().getOrganization());
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      String groupId = StrUtils.nvl(filter.getGroupId());
      String disconnectPeriod;
      if (!groupId.equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      } else {
         UserInfo userInfo = UserInfoImpl.getInstance();
         long mngOrgId = userContainer.getUser().getRoot_group_id();
         if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
            mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
         }

         disconnectPeriod = userInfo.getOrganGroupName(mngOrgId);
         groupId = String.valueOf(deviceGroupDao.getOrganGroupIdByName(disconnectPeriod));
      }

      boolean isRoot = false;
      Map res = deviceGroupDao.getDeviceOrganizationByGroupId(Integer.parseInt(groupId));
      if (res != null && res.get("GROUP_ID") != null && Long.parseLong(groupId) == (Long)res.get("GROUP_ID")) {
         isRoot = true;
      }

      if (!StrUtils.nvl(filter.getSortColumn()).equals("")) {
         filter.setSortColumn("device_name");
      }

      String connectionStatus = StrUtils.nvl(filter.getConnectionStatus()).equals("") ? "device_status_view_all" : filter.getConnectionStatus();
      disconnectPeriod = null;
      int lastindex = connectionStatus.lastIndexOf("_");
      if (connectionStatus.substring(0, lastindex).equals("device_status_view_disconnection")) {
         disconnectPeriod = connectionStatus.substring(lastindex + 1);
         connectionStatus = connectionStatus.substring(0, lastindex);
      }

      String expirationDate = "device_status_view_all";
      String customInputVal = "0";
      String groupIds = "";
      if (filter.getGroupIds() != null) {
         groupIds = this.convertString(filter.getGroupIds());
      }

      String tagIds = null;
      if (filter.getTagIds() != null) {
         tagIds = this.convertString(filter.getTagIds());
      }

      String alarmTypes = "";
      if (filter.getAlarmTypes() != null) {
         alarmTypes = this.convertString(filter.getAlarmTypes());
      }

      String functionTypes = "";
      if (filter.getFunctionTypes() != null) {
         functionTypes = this.convertString(filter.getFunctionTypes());
      }

      String inputSources = "";
      if (filter.getInputSources() != null) {
         inputSources = this.convertString(filter.getInputSources());
      }

      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      String deviceType = "";
      if (filter.getDeviceType() != null) {
         deviceType = this.convertString(filter.getDeviceType());
      }

      Locale locale = SecurityUtils.getLocale();
      DeviceDisplayConfManager dao = DeviceDisplayConfManagerImpl.getInstance(productType);
      ListManager listMgr = new ListManager(dao, "commonlist");
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(filter.getSortColumn());
      condition.setOrder_dir(filter.getSortOrder());
      condition.setGroup_id(Long.valueOf(groupId));
      condition.setSrc_name(searchText);
      condition.setIsRoot(isRoot);
      condition.setStatus_view_mode(connectionStatus);
      if (disconnectPeriod != null) {
         condition.setDisconn_period(disconnectPeriod);
      }

      condition.setExpiration_date(expirationDate);
      condition.setCustom_input_val(customInputVal);
      condition.setFilter_group_ids(groupIds);
      condition.setRole_name(userContainer.getUser().getRole_name());
      condition.setUser_id(userContainer.getUser().getUser_id());
      condition.setTagFilter(tagIds);
      condition.setSourceFilter(inputSources);
      if (StringUtils.isNotBlank(alarmTypes)) {
         condition.setAlarmFiltersByString(alarmTypes);
      }

      if (StringUtils.isNotBlank(functionTypes)) {
         condition.setFunctionFiltersByString(functionTypes);
      }

      if (StringUtils.isNotBlank(searchText)) {
         condition.setCommonSearchKeyword(searchText);
      }

      if (deviceType != null && !deviceType.equals("")) {
         condition.setDevice_type(deviceType);
      }

      ArrayList sourceList;
      String[] list;
      int i;
      if (StringUtils.isNotBlank(condition.getSourceFilter())) {
         sourceList = new ArrayList();
         list = condition.getSourceFilter().split(",");
         String[] var31 = list;
         int var32 = list.length;

         for(i = 0; i < var32; ++i) {
            String tag = var31[i];
            sourceList.add(Long.parseLong(tag));
         }

         condition.setSourceFilterList(sourceList);
      }

      sourceList = null;
      list = null;
      listMgr.addSearchInfo("condition", condition);
      listMgr.setLstSize(Integer.valueOf(filter.getPageSize()));
      listMgr.setSection("getDeviceDisplayConfList");
      List list = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());
      PageManager pageMgr = listMgr.getPageManager();
      response.setContentType("application/json;charset=UTF-8");
      result.setRecordsReturned(list.size());
      result.setRecordsTotal(pageMgr.getTotalRowCount());
      result.setRecordsFiltered(pageMgr.getTotalRowCount());
      result.setPageSize(pageMgr.getPageSize());
      if (filter.getSortColumn() != null && !filter.getSortColumn().equals("")) {
         result.setSortColumn(filter.getSortColumn());
         result.setSortOrder(filter.getSortOrder());
      }

      int totalChildCount = 0;
      List displayList = new ArrayList();
      i = 0;

      for(int var46 = 0; i < list.size(); ++i) {
         new V2DeviceDisplayListResource();
         DeviceDisplayConf info = (DeviceDisplayConf)list.get(i);
         if (info != null) {
            V2DeviceDisplayListResource resource = this.writeData(productType, info, locale);
            ++var46;
            if (info.getHas_child() != null && info.getHas_child()) {
               totalChildCount = (int)((long)totalChildCount + info.getChild_cnt());
            }

            resource.setChildCount((long)totalChildCount);
            displayList.add(resource);
            if (info.getHas_child() != null && info.getHas_child()) {
               for(int j = 0; (long)j < info.getChild_cnt(); ++j) {
                  DeviceDisplayConf childDevice = dao.getDeviceDisplayConf(info.getDevice_id() + "_" + (j + 1));
                  if (childDevice != null) {
                     new V2DeviceDisplayListResource();
                     V2DeviceDisplayListResource childResource = this.writeData(productType, childDevice, locale);
                     ++var46;
                     displayList.add(childResource);
                  }
               }
            }
         }
      }

      result = V2PageResource.createPageResource(displayList, pageMgr);
      result.setStartIndex(filter.getStartIndex());
      return result;
   }

   private V2DeviceDisplayListResource writeData(String productType, DeviceDisplayConf info, Locale locale) {
      V2DeviceDisplayListResource resource = new V2DeviceDisplayListResource();
      ObjectMapper mapper = new ObjectMapper();
      mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
      mapper.configure(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES, false);

      try {
         String jsonString = mapper.writeValueAsString(info);

         try {
            Map map = (Map)mapper.readValue(jsonString, new TypeReference() {
            });
            Map map = ConvertUtil.convertMap(map);
            resource = (V2DeviceDisplayListResource)mapper.convertValue(map, V2DeviceDisplayListResource.class);
            boolean power = DeviceUtils.isConnected(info.getDevice_id());
            if (power && info.getIs_child() && !DeviceUtils.isConnected(info.getDevice_id().split("_")[0])) {
               power = false;
            }

            resource.setPower(power);
         } catch (IOException var9) {
            this.logger.error("", var9);
         }
      } catch (JsonProcessingException var10) {
         this.logger.error("", var10);
      }

      return resource;
   }

   private boolean isSupport(String deviceType, String functionName) {
      if (deviceType != null && !deviceType.equals("")) {
         Map map = (Map)this.supportMap.get(deviceType);
         return map != null && map.containsKey(functionName) ? (Boolean)map.get(functionName) : true;
      } else {
         return true;
      }
   }

   public String convertString(List list) {
      String result = "";
      if (!list.isEmpty() && list.size() > 0) {
         StringBuffer strBuf = new StringBuffer();

         for(int i = 0; i < list.size(); ++i) {
            strBuf.append(list.get(i));
            if (i < list.size() - 1) {
               strBuf.append(",");
            }
         }

         result = strBuf.toString();
      }

      return result;
   }
}
