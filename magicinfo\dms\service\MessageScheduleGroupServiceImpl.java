package com.samsung.magicinfo.dms.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.dms.model.ScheduleGroupResource;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.scheduler.dao.MessageGroupDao;
import com.samsung.magicinfo.framework.scheduler.entity.MessageGroup;
import com.samsung.magicinfo.framework.user.entity.User;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("MessageScheduleGroupService")
@Transactional
public class MessageScheduleGroupServiceImpl implements MessageScheduleGroupService {
   protected Logger logger = LoggingManagerV2.getLogger(MessageScheduleGroupServiceImpl.class);
   LeftMenuGroupTreeDao tree_dao = new LeftMenuGroupTreeDao();
   String table = "MI_CDS_INFO_MESSAGE_GROUP";
   String menu = "Schedule";

   public MessageScheduleGroupServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody getRootGroups() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      MessageGroupDao messageGroupDao = new MessageGroupDao();
      List lists = new ArrayList();
      String organization = SecurityUtils.getLoginUserOrganization();
      ArrayList result = new ArrayList();

      try {
         if (user.getRoot_group_id() == Long.valueOf("0")) {
            lists = messageGroupDao.getChildGroupList(0, false);
         } else if (organization != null) {
            lists = messageGroupDao.getRootGroupById(organization);
         }

         Iterator var8 = ((List)lists).iterator();

         while(var8.hasNext()) {
            MessageGroup entity = (MessageGroup)var8.next();
            ScheduleGroupResource scheduleGroupResource = new ScheduleGroupResource();
            scheduleGroupResource.setGroupId(entity.getGroup_id());
            scheduleGroupResource.setParentGroupId(entity.getP_group_id());
            scheduleGroupResource.setGroupDepth(entity.getGroup_depth());
            scheduleGroupResource.setGroupName(entity.getGroup_name());
            scheduleGroupResource.setDescription(entity.getDescription());
            result.add(scheduleGroupResource);
         }

         responseBody.setItems(result);
         responseBody.setStatus("Success");
         return responseBody;
      } catch (SQLException var11) {
         this.logger.error(var11.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var11.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody listGroupInfo(Long groupId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      ScheduleGroupResource scheduleGroupResource = new ScheduleGroupResource();
      MessageGroupDao messageGroupDao = new MessageGroupDao();

      try {
         MessageGroup messageGroup = messageGroupDao.getGroup((int)groupId);
         scheduleGroupResource.setGroupId(messageGroup.getGroup_id());
         scheduleGroupResource.setParentGroupId(messageGroup.getP_group_id());
         scheduleGroupResource.setGroupDepth(messageGroup.getGroup_depth());
         scheduleGroupResource.setGroupName(messageGroup.getGroup_name());
         scheduleGroupResource.setDescription(messageGroup.getDescription());
         responseBody.setItems(scheduleGroupResource);
         responseBody.setStatus("Success");
         return responseBody;
      } catch (Exception var6) {
         this.logger.error(var6.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var6.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody getChildGroups(String groupId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      MessageGroupDao messageGroupDao = new MessageGroupDao();
      new ArrayList();
      ArrayList result = new ArrayList();

      try {
         int id = Integer.valueOf(groupId);
         List lists = messageGroupDao.getChildGroupList(id, false);
         Iterator var7 = lists.iterator();

         while(var7.hasNext()) {
            MessageGroup entity = (MessageGroup)var7.next();
            ScheduleGroupResource scheduleGroupResource = new ScheduleGroupResource();
            scheduleGroupResource.setGroupId(entity.getGroup_id());
            scheduleGroupResource.setParentGroupId(entity.getP_group_id());
            scheduleGroupResource.setGroupDepth(entity.getGroup_depth());
            scheduleGroupResource.setGroupName(entity.getGroup_name());
            scheduleGroupResource.setDescription(entity.getDescription());
            result.add(scheduleGroupResource);
         }

         responseBody.setItems(result);
         responseBody.setStatus("Success");
         return responseBody;
      } catch (SQLException var10) {
         this.logger.error(var10.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var10.getMessage());
         return responseBody;
      }
   }
}
