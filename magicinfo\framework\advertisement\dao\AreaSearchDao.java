package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.AreaSearchEntity;
import java.sql.Connection;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class AreaSearchDao {
   Logger logger = LoggingManagerV2.getLogger(AreaSearchDao.class);

   public AreaSearchDao() {
      super();
   }

   public AreaSearchDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAdTypeList() {
      throw new UnsupportedOperationException("AreaSearchDao#getASEngineerByFaultListId is not supported anymore: The table FAULT_LIST_USERS_MATCH does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAdContentTypeList() {
      throw new UnsupportedOperationException("AreaSearchDao#getASEngineerByFaultListId is not supported anymore: The table FAULT_LIST_USERS_MATCH does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaTypeList() {
      throw new UnsupportedOperationException("AreaSearchDao#getASEngineerByFaultListId is not supported anymore: The table FAULT_LIST_USERS_MATCH does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getRecentSearchList() {
      throw new UnsupportedOperationException("AreaSearchDao#getASEngineerByFaultListId is not supported anymore: The table FAULT_LIST_USERS_MATCH does not exists");
   }

   /** @deprecated */
   @Deprecated
   public Object getMaxSearchListId(String user_id) {
      throw new UnsupportedOperationException("AreaSearchDao#getASEngineerByFaultListId is not supported anymore: The table FAULT_LIST_USERS_MATCH does not exists");
   }

   /** @deprecated */
   @Deprecated
   public int saveSearchCondition(int search_id, String user_id, AreaSearchEntity search_condition) {
      throw new UnsupportedOperationException("AreaSearchDao#getASEngineerByFaultListId is not supported anymore: The table FAULT_LIST_USERS_MATCH does not exists");
   }

   /** @deprecated */
   @Deprecated
   public int deleteSearchList(int search_list_id, String user_id) {
      throw new UnsupportedOperationException("AreaSearchDao#deleteSearchList is not supported anymore: The table MI_AD_AREA_SEARCH_CONDITION_LIST does not exists");
   }
}
