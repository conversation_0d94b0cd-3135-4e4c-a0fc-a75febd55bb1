package com.samsung.magicinfo.ems.model;

import java.util.List;

public class DashboardLicenceResource {
   public int totalRecord;
   public int startIndex;
   public String sort;
   public String order;
   public int results;
   public List licenseList;

   public DashboardLicenceResource() {
      super();
   }

   public int getTotalRecord() {
      return this.totalRecord;
   }

   public void setTotalRecord(int totalRecord) {
      this.totalRecord = totalRecord;
   }

   public int getStartIndex() {
      return this.startIndex;
   }

   public void setStartIndex(int startIndex) {
      this.startIndex = startIndex;
   }

   public String getSort() {
      return this.sort;
   }

   public void setSort(String sort) {
      this.sort = sort;
   }

   public String getOrder() {
      return this.order;
   }

   public void setOrder(String order) {
      this.order = order;
   }

   public int getResults() {
      return this.results;
   }

   public void setResults(int results) {
      this.results = results;
   }

   public List getLicenseList() {
      return this.licenseList;
   }

   public void setLicenseList(List licenseList) {
      this.licenseList = licenseList;
   }
}
