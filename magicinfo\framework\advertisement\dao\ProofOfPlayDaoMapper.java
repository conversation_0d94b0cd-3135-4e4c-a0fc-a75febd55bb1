package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.PopSecondInfoRow;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProofOfPlayDaoMapper {
   boolean insertContentPlayHistory(@Param("device_id") String var1, @Param("timestamp") Timestamp var2, @Param("time_range") String var3, @Param("content_id") String var4, @Param("play_count") int var5, @Param("fileName") String var6) throws SQLException;

   PopInfoEntity getPopHourInfo(@Param("device_id") String var1, @Param("contentId") String var2, @Param("log_date") Timestamp var3) throws SQLException;

   PopInfoEntity getPopDayInfo(@Param("device_id") String var1, @Param("contentId") String var2, @Param("day") Timestamp var3) throws SQLException;

   PopInfoEntity getPopMonthInfo(@Param("device_id") String var1, @Param("contentId") String var2, @Param("month") Timestamp var3) throws SQLException;

   PopInfoEntity getPopYearInfo(@Param("device_id") String var1, @Param("contentId") String var2, @Param("year") Timestamp var3) throws SQLException;

   void addPopDayInfoList(@Param("deviceId") String var1, @Param("pop") PopInfoEntity var2, @Param("day") Timestamp var3, @Param("dow") int var4, @Param("contentId") String var5) throws SQLException;

   void addPopHourInfoList(@Param("deviceId") String var1, @Param("contentId") String var2, @Param("list") List var3) throws SQLException;

   void addPopMonthInfoList(@Param("deviceId") String var1, @Param("pop") PopInfoEntity var2, @Param("month") Timestamp var3, @Param("quarter") int var4, @Param("contentId") String var5) throws SQLException;

   void addPopYearInfoList(@Param("deviceId") String var1, @Param("pop") PopInfoEntity var2, @Param("year") Timestamp var3, @Param("contentId") String var4) throws SQLException;

   void setPopHourInfo(@Param("playcount") int var1, @Param("duration") int var2, @Param("timestamp") Timestamp var3, @Param("contentId") String var4, @Param("device_id") String var5) throws SQLException;

   void setPopDayInfo(@Param("playcount") int var1, @Param("duration") int var2, @Param("day") Timestamp var3, @Param("contentId") String var4, @Param("device_id") String var5) throws SQLException;

   void setPopMonthInfo(@Param("playcount") int var1, @Param("duration") int var2, @Param("month") Timestamp var3, @Param("contentId") String var4, @Param("device_id") String var5) throws SQLException;

   void setPopYearInfo(@Param("playcount") int var1, @Param("duration") int var2, @Param("year") Timestamp var3, @Param("contentId") String var4, @Param("device_id") String var5) throws SQLException;

   Long checkDuplicationByDB(@Param("timestamp") Timestamp var1, @Param("device_id") String var2) throws SQLException;

   void addPopSecondInfoList(@Param("pop") PopSecondInfoRow var1) throws SQLException;

   int getMaxPopId() throws SQLException;
}
