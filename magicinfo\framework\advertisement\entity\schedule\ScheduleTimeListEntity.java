package com.samsung.magicinfo.framework.advertisement.entity.schedule;

public class ScheduleTimeListEntity {
   private String user_id = null;
   private String schedule_id = null;
   private int schedule_time_id = -1;
   private String start_date = null;
   private String end_date = null;
   private String start_hour = null;
   private String start_minute = null;
   private String end_hour = null;
   private String end_minute = null;

   public ScheduleTimeListEntity() {
      super();
   }

   public String getUser_id() {
      return this.user_id;
   }

   public void setUser_id(String user_id) {
      this.user_id = user_id;
   }

   public String getSchedule_id() {
      return this.schedule_id;
   }

   public void setSchedule_id(String schedule_id) {
      this.schedule_id = schedule_id;
   }

   public int getSchedule_time_id() {
      return this.schedule_time_id;
   }

   public void setSchedule_time_id(int schedule_time_id) {
      this.schedule_time_id = schedule_time_id;
   }

   public String getStart_date() {
      return this.start_date;
   }

   public void setStart_date(String start_date) {
      this.start_date = start_date;
   }

   public String getEnd_date() {
      return this.end_date;
   }

   public void setEnd_date(String end_date) {
      this.end_date = end_date;
   }

   public String getStart_hour() {
      return this.start_hour;
   }

   public void setStart_hour(String start_hour) {
      this.start_hour = start_hour;
   }

   public String getStart_minute() {
      return this.start_minute;
   }

   public void setStart_minute(String start_minute) {
      this.start_minute = start_minute;
   }

   public String getEnd_hour() {
      return this.end_hour;
   }

   public void setEnd_hour(String end_hour) {
      this.end_hour = end_hour;
   }

   public String getEnd_minute() {
      return this.end_minute;
   }

   public void setEnd_minute(String end_minute) {
      this.end_minute = end_minute;
   }
}
