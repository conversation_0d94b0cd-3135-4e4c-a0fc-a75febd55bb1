package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.AdTimeSlotEntity;
import java.sql.Connection;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class AdTimeSlotDao {
   Logger logger = LoggingManagerV2.getLogger(AdTimeSlotDao.class);

   public AdTimeSlotDao() {
      super();
   }

   public AdTimeSlotDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public List getTimeSlotInfo(String area_id, String start_date, String finish_date, int ad_type_id) {
      throw new UnsupportedOperationException("The AdTimeSlotDao#getTimeSlotInfo method is not longer supported. Table MI_AD_SCHEDULE_STACK does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getTimeSlotMonthInfo(String area_id, String start_date, String finish_date, int ad_type_id) {
      throw new UnsupportedOperationException("The AdTimeSlotDao#getTimeSlotMonthInfo method is not longer supported. Table MI_AD_SCHEDULE_STACK does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public AdTimeSlotEntity getMonthlyTimeSlotInfo(String area_id, String target_date, int ad_type_id) {
      throw new UnsupportedOperationException("The AdTimeSlotDao#getMonthlyTimeSlotInfo method is not longer supported. Table MI_AD_SCHEDULE_STACK does not exists.");
   }
}
