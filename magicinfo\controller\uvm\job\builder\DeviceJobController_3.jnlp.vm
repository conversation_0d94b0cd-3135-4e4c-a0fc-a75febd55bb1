## generateUvmSaveAsJobScheduleJnlp
##
<?xml version='1.0' encoding='UTF-8' ?>
<jnlp spec='1.0'
	codebase='${serverUrl}/uploader/${urlToken}/jnlp/'
	href='${jnlpFileName}'>
	<information>
		<title>JobScheduleUploader</title>
		<vendor>MagicInfo</vendor>
	</information>
	<security>
		<all-permissions/>
	</security>
	<resources arch="amd64">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
		<jar href='../jobUploader.jar' main='true' />
		<jar href='../lib/jnlp.jar' download="eager" />
		<jar href='../lib/commons-httpclient-3.1.jar' download="eager" />
		<jar href='../lib/commons-codec-1.3.jar' download="eager" />
		<jar href='../lib/commons-logging-1.1.1.jar' download="eager" />
		<jar href='../lib/swt64.jar' download="eager" />
		<jar href='../lib/commons-ssl.jar' download="eager" />
	</resources>
	<resources arch="x86_64">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
		<jar href='../jobUploader.jar' main='true' />
		<jar href='../lib/jnlp.jar' download="eager" />
		<jar href='../lib/commons-httpclient-3.1.jar' download="eager" />
		<jar href='../lib/commons-codec-1.3.jar' download="eager" />
		<jar href='../lib/commons-logging-1.1.1.jar' download="eager" />
		<jar href='../lib/swt64.jar' download="eager" />
		<jar href='../lib/commons-ssl.jar' download="eager" />
	</resources>
	<resources arch="x86">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
		<jar href='../jobUploader.jar' main='true' />
		<jar href='../lib/jnlp.jar' download="eager" />
		<jar href='../lib/commons-httpclient-3.1.jar' download="eager" />
		<jar href='../lib/commons-codec-1.3.jar' download="eager" />
		<jar href='../lib/commons-logging-1.1.1.jar' download="eager" />
		<jar href='../lib/swt.jar' download="eager" />
		<jar href='../lib/commons-ssl.jar' download="eager" />
	</resources>
	<resources arch="i386">
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
		<jar href='../jobUploader.jar' main='true' />
		<jar href='../lib/jnlp.jar' download="eager" />
		<jar href='../lib/commons-httpclient-3.1.jar' download="eager" />
		<jar href='../lib/commons-codec-1.3.jar' download="eager" />
		<jar href='../lib/commons-logging-1.1.1.jar' download="eager" />
		<jar href='../lib/swt.jar' download="eager" />
		<jar href='../lib/commons-ssl.jar' download="eager" />
	</resources>
	<application-desc main-class='com.samsung.magicinfo.uploader.JobUploaderApplication'>
		<argument>${requestLocale}</argument>
		<argument>${sessionId}</argument>
		<argument>${loginUserId}</argument>
		<argument>${issuedToken}</argument>
		<argument>${userOrganization}</argument>
		<argument>${jnlpFileName}</argument>
		<argument>${isRootOrganization}</argument>
##		<argument>DEVICE</argument>
		<argument>VENDING</argument>
##		<argument>VIDEOWALL</argument>
		<argument>${job_id}</argument>
		<argument>TRUE</argument>
	</application-desc>
</jnlp>
