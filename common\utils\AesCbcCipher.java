package com.samsung.common.utils;

import java.security.MessageDigest;
import java.security.SecureRandom;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AesCbcCipher {
   public AesCbcCipher() {
      super();
   }

   public static String encrypt(String data, String key) throws Exception {
      if (data != null && key != null) {
         byte[] clean = data.getBytes();
         int ivSize = 16;
         byte[] iv = new byte[ivSize];
         SecureRandom random = new SecureRandom();
         random.nextBytes(iv);
         IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
         MessageDigest digest = MessageDigest.getInstance("SHA-256");
         digest.update(key.getBytes("UTF-8"));
         byte[] keyBytes = new byte[16];
         System.arraycopy(digest.digest(), 0, keyBytes, 0, keyBytes.length);
         SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, "AES");
         Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
         cipher.init(1, secretKeySpec, ivParameterSpec);
         byte[] encrypted = cipher.doFinal(clean);
         byte[] encryptedIVAndText = new byte[ivSize + encrypted.length];
         System.arraycopy(iv, 0, encryptedIVAndText, 0, ivSize);
         System.arraycopy(encrypted, 0, encryptedIVAndText, ivSize, encrypted.length);
         return encryptedIVAndText.toString();
      } else {
         return null;
      }
   }

   public static String decrypt(String data, String key) throws Exception {
      if (data != null && key != null) {
         int ivSize = 16;
         int keySize = 16;
         byte[] encryptedIvTextBytes = data.getBytes();
         byte[] iv = new byte[ivSize];
         System.arraycopy(encryptedIvTextBytes, 0, iv, 0, iv.length);
         IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
         int encryptedSize = encryptedIvTextBytes.length - ivSize;
         byte[] encryptedBytes = new byte[encryptedSize];
         System.arraycopy(encryptedIvTextBytes, ivSize, encryptedBytes, 0, encryptedSize);
         byte[] keyBytes = new byte[keySize];
         MessageDigest md = MessageDigest.getInstance("SHA-256");
         md.update(key.getBytes());
         System.arraycopy(md.digest(), 0, keyBytes, 0, keyBytes.length);
         SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, "AES");
         Cipher cipherDecrypt = Cipher.getInstance("AES/CBC/PKCS5Padding");
         cipherDecrypt.init(2, secretKeySpec, ivParameterSpec);
         byte[] decrypted = cipherDecrypt.doFinal(encryptedBytes);
         return new String(decrypted);
      } else {
         return null;
      }
   }
}
