package com.samsung.magicinfo.exception;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.web.filter.GenericFilterBean;
import org.springframework.web.util.NestedServletException;
import org.springframework.web.util.WebUtils;

public class AjaxSessionTimeoutFilter extends GenericFilterBean {
   private String ajaxHeader;

   public AjaxSessionTimeoutFilter() {
      super();
   }

   public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
      HttpServletRequest req = (HttpServletRequest)request;
      HttpServletResponse res = (HttpServletResponse)response;
      if (this.isAjaxRequest(req)) {
         try {
            chain.doFilter(req, res);
         } catch (AccessDeniedException var8) {
            res.sendError(403);
         } catch (AuthenticationException var9) {
            res.sendError(401);
         } catch (NestedServletException var10) {
            SecurityContextLogoutHandler securityContextLogoutHandler = new SecurityContextLogoutHandler();
            if (securityContextLogoutHandler.isInvalidateHttpSession()) {
               res.sendError(401);
            } else {
               res.sendError(500);
            }
         } catch (Exception var11) {
            res.sendError(500);
         }
      } else {
         Cookie cookie = WebUtils.getCookie((HttpServletRequest)request, "MagicInfoPremiumLanguage");
         if (cookie != null) {
            this.checkLocale(cookie);
         }

         chain.doFilter(req, res);
      }

   }

   private boolean isAjaxRequest(HttpServletRequest req) {
      return req.getHeader(this.ajaxHeader) != null && req.getHeader(this.ajaxHeader).equals(Boolean.TRUE.toString());
   }

   public void setAjaxHeader(String ajaxHeader) {
      this.ajaxHeader = ajaxHeader;
   }

   private void checkLocale(Cookie cookie) {
      if (cookie.getValue() != null && cookie.getValue().length() > 1) {
         String locale = cookie.getValue().substring(0, 2);
         byte var4 = -1;
         switch(locale.hashCode()) {
         case 3121:
            if (locale.equals("ar")) {
               var4 = 2;
            }
            break;
         case 3201:
            if (locale.equals("de")) {
               var4 = 3;
            }
            break;
         case 3241:
            if (locale.equals("en")) {
               var4 = 1;
            }
            break;
         case 3246:
            if (locale.equals("es")) {
               var4 = 4;
            }
            break;
         case 3259:
            if (locale.equals("fa")) {
               var4 = 5;
            }
            break;
         case 3276:
            if (locale.equals("fr")) {
               var4 = 6;
            }
            break;
         case 3371:
            if (locale.equals("it")) {
               var4 = 7;
            }
            break;
         case 3383:
            if (locale.equals("ja")) {
               var4 = 8;
            }
            break;
         case 3428:
            if (locale.equals("ko")) {
               var4 = 0;
            }
            break;
         case 3580:
            if (locale.equals("pl")) {
               var4 = 14;
            }
            break;
         case 3588:
            if (locale.equals("pt")) {
               var4 = 9;
            }
            break;
         case 3651:
            if (locale.equals("ru")) {
               var4 = 10;
            }
            break;
         case 3683:
            if (locale.equals("sv")) {
               var4 = 11;
            }
            break;
         case 3710:
            if (locale.equals("tr")) {
               var4 = 12;
            }
            break;
         case 3886:
            if (locale.equals("zh")) {
               var4 = 13;
            }
         }

         switch(var4) {
         case 0:
         case 1:
         case 2:
         case 3:
         case 4:
         case 5:
         case 6:
         case 7:
         case 8:
         case 9:
         case 10:
         case 11:
         case 12:
         case 13:
         case 14:
            break;
         default:
            cookie.setValue("en");
         }
      } else {
         cookie.setValue("en");
      }

   }
}
