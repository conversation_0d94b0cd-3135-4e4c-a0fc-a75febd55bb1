package com.samsung.magicinfo.ems.model;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;

public class EnvironmentNoticeFilter {
   @ApiModelProperty(
      example = "1"
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "Only number is available."
   )
   private String noticeId;
   @ApiModelProperty(
      example = "testTitle"
   )
   private String noticeTitle;
   @ApiModelProperty(
      example = "testSubject"
   )
   private String noticeSubject;
   @ApiModelProperty(
      example = "2016-09-01"
   )
   private String noticeStartDate;
   @ApiModelProperty(
      example = "2016-09-01"
   )
   private String noticeEndDate;
   @ApiModelProperty(
      example = "false"
   )
   private boolean important;

   public EnvironmentNoticeFilter() {
      super();
   }

   public String getNoticeId() {
      return this.noticeId;
   }

   public void setNoticeId(String noticeId) {
      this.noticeId = noticeId;
   }

   public String getNoticeTitle() {
      return this.noticeTitle;
   }

   public void setNoticeTitle(String noticeTitle) {
      this.noticeTitle = noticeTitle;
   }

   public String getNoticeSubject() {
      return this.noticeSubject;
   }

   public void setNoticeSubject(String noticeSubject) {
      this.noticeSubject = noticeSubject;
   }

   public String getNoticeStartDate() {
      return this.noticeStartDate;
   }

   public void setNoticeStartDate(String noticeStartDate) {
      this.noticeStartDate = noticeStartDate;
   }

   public String getNoticeEndDate() {
      return this.noticeEndDate;
   }

   public void setNoticeEndDate(String noticeEndDate) {
      this.noticeEndDate = noticeEndDate;
   }

   public boolean isImportant() {
      return this.important;
   }

   public void setImportant(boolean important) {
      this.important = important;
   }
}
