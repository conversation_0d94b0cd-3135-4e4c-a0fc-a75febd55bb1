<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.samsung.magicinfo.framework.advertisement.dao.ProofOfPlayDaoMapper">

    <insert id="insertContentPlayHistory">
        INSERT INTO MI_CONTENT_PLAY_HISTORY (DEVICE_ID, PLAY_TIME, TIME_RANGE, CONTENT_ID, PLAY_COUNT, FILE_NAME)
            VALUES (#{device_id}, #{timestamp}, #{time_range}, #{content_id}, #{play_count}, #{fileName})
    </insert>

    <insert id="addPopDayInfoList">
        INSERT INTO MI_STATISTICS_CONTENT_DAY( CONTENT_ID, START_TIME, DEVICE_ID , PLAY_COUNT, DURATION, PLAY_DOW )
        VALUES ( #{contentId}, #{day}, #{deviceId}, #{pop.play_count}, #{pop.duration}, #{dow} )
    </insert>

    <insert id="addPopHourInfoList">
        INSERT INTO MI_STATISTICS_CONTENT_HOUR( CONTENT_ID, START_TIME, DEVICE_ID , PLAY_COUNT, DURATION ) VALUES
        <foreach collection="list" close=")" item="item" open="(" separator="),(">
            #{contentId}, #{item.logDateStr}, #{deviceId}, #{item.play_count}, #{item.duration}
        </foreach>
    </insert>

    <insert id="addPopMonthInfoList">
        INSERT INTO MI_STATISTICS_CONTENT_MONTH( CONTENT_ID, START_TIME, LOG_QUARTER, DEVICE_ID , PLAY_COUNT
        , DURATION ) VALUES (#{contentId}, #{month}, #{quarter}, #{deviceId}, #{pop.play_count}, #{pop.duration})
    </insert>
    
    <insert id="addPopYearInfoList">
        INSERT INTO MI_STATISTICS_CONTENT_YEAR( CONTENT_ID, START_TIME, DEVICE_ID , PLAY_COUNT, DURATION ) 
        VALUES (#{contentId}, #{year}, #{deviceId}, #{pop.play_count}, #{pop.duration})
    </insert>

    <insert id="addPopSecondInfoList">
        <bind name="safe_startTime" value="@com.samsung.common.utils.DaoTools@safeDateTimeString(pop.startTime)" />
        <bind name="safe_duration" value="@com.samsung.common.utils.DaoTools@safeNumeric(pop.duration)" />
        INSERT INTO MI_STATISTICS_CONTENT_SECOND (POP_ID, START_TIME, DURATION, CONTENT_ID, DEVICE_ID)
        VALUES (#{pop.popId}, '${safe_startTime}', '${safe_duration}', #{pop.contentId}, #{pop.deviceId})
    </insert>

    <update id="setPopHourInfo">
        UPDATE MI_STATISTICS_CONTENT_HOUR
        SET PLAY_COUNT = #{playcount}, DURATION = #{duration}
        WHERE START_TIME = #{timestamp} AND CONTENT_ID = #{contentId} AND DEVICE_ID = #{device_id}
    </update>

    <update id="setPopDayInfo">
        UPDATE MI_STATISTICS_CONTENT_DAY
        SET PLAY_COUNT = #{playcount}, DURATION = #{duration}
        WHERE START_TIME = #{day} AND CONTENT_ID = #{contentId} AND DEVICE_ID = #{device_id}
    </update>

    <update id="setPopMonthInfo">
        UPDATE MI_STATISTICS_CONTENT_MONTH
        SET PLAY_COUNT = #{playcount}, DURATION = #{duration}
        WHERE START_TIME = #{month} AND CONTENT_ID = #{contentId} AND DEVICE_ID = #{device_id}
    </update>

    <update id="setPopYearInfo">
        UPDATE MI_STATISTICS_CONTENT_YEAR
        SET PLAY_COUNT = #{playcount}, DURATION = #{duration}
        WHERE START_TIME = #{year} AND CONTENT_ID = #{contentId} AND DEVICE_ID = #{device_id}
    </update>

    <select id="getPopHourInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_CONTENT_HOUR
        WHERE START_TIME = #{log_date} AND CONTENT_ID = #{contentId} AND DEVICE_ID = #{device_id}
    </select>

    <select id="getPopDayInfo" resultType="com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_CONTENT_DAY
        WHERE START_TIME = #{day} AND CONTENT_ID = #{contentId} AND DEVICE_ID = #{device_id}
    </select>
    <select id="getPopMonthInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_CONTENT_MONTH
        WHERE START_TIME = #{month} AND CONTENT_ID = #{contentId} AND DEVICE_ID = #{device_id}
    </select>
    <select id="getPopYearInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_CONTENT_YEAR
        WHERE START_TIME = #{year} AND CONTENT_ID = #{contentId} AND DEVICE_ID = #{device_id}
    </select>

    <select id="checkDuplicationByDB" resultType="java.lang.Long">
	 SELECT COUNT(1) AS CNT 
    WHERE EXISTS (  
        SELECT  1            
        FROM MI_STATISTICS_CONTENT_DAY
        WHERE START_TIME = #{timestamp} AND DEVICE_ID = #{device_id}
    )
    </select>

    <select id="getMaxPopId" resultType="integer">
      SELECT coalesce(MAX(POP_ID)+1,1) FROM MI_STATISTICS_CONTENT_SECOND
    </select>

    <select id="getMaxPopId" resultType="integer" databaseId="mssql">
      SELECT ISNULL(MAX(POP_ID)+1,1) FROM MI_STATISTICS_CONTENT_SECOND
    </select>

</mapper>