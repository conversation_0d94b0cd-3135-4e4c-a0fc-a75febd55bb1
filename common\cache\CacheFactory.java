package com.samsung.common.cache;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import org.apache.logging.log4j.Logger;

public class CacheFactory {
   static Logger logger = LoggingManagerV2.getLogger(CacheFactory.class);
   private static BasicCache instanceCache = null;
   private static BasicCache instanceLocalCache = null;
   private static String cacheType = null;
   public static int CACHE_TYPE_LOCAL = 1;
   public static int CACHE_TYPE_GLOBAL = 0;

   public CacheFactory() {
      super();
   }

   public static String getCacheType() {
      return cacheType;
   }

   public static BasicCache getCache() {
      return instanceCache;
   }

   public static BasicCache getCache(int cacheType) {
      if (instanceLocalCache == null) {
         return instanceCache;
      } else {
         return cacheType == CACHE_TYPE_LOCAL ? instanceLocalCache : instanceCache;
      }
   }

   public static boolean hasLocalCache() {
      return instanceLocalCache != null;
   }

   static {
      try {
         if (CommonConfig.get("cache.type") != null) {
            cacheType = CommonConfig.get("cache.type");
            if (cacheType.equalsIgnoreCase("memcached")) {
               instanceCache = DistributedCache.getInstance();
            } else if (cacheType.equalsIgnoreCase("redis")) {
               instanceCache = RedisCache.getInstance();
            } else if (cacheType.equalsIgnoreCase("jndi")) {
               instanceCache = JndiCache.getInstance();
            }

            instanceLocalCache = EhCache.getInstance(false);
         } else {
            instanceCache = JndiCache.getInstance();
            cacheType = "jndi";
         }
      } catch (ConfigException var1) {
         instanceCache = JndiCache.getInstance();
         cacheType = "jndi";
      }

   }
}
