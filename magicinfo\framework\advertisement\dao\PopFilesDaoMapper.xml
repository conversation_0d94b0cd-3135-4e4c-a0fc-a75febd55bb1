<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.samsung.magicinfo.framework.advertisement.dao.PopFilesDaoMapper">
                   
<select id="checkDuplicationByDB" resultType="java.lang.Long">
SELECT COUNT(1) AS CNT 
WHERE EXISTS (  
				SELECT 1
				 FROM MI_STATISTICS_FILES_DAY
				WHERE DEVICE_ID    = #{device_id}
				   AND START_TIME  = #{log_date} 
				 <if test="pop_file_type.equalsIgnoreCase('VWL')">
					AND POP_FILE_TYPE = 'VWL'
				 </if>
				 <if test="pop_file_type.equalsIgnoreCase('MEDIA')">
					AND POP_FILE_TYPE != 'VWL'
				 </if>
          )	
</select>

<select id="getPopFilesHourInfo" resultType="com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity">
	SELECT PLAY_COUNT, DURATION 
	   FROM MI_STATISTICS_FILES_HOUR
	WHERE DEVICE_ID          = #{device_id}
         AND START_TIME       = #{log_date} 
         AND CONTENT_ID       = #{content_id} 
         AND FILE_ID               = #{file_id}
         AND POP_FILE_TYPE = #{pop_file_type}
</select>

<select id="getPopFilesDayInfo" resultType="com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity">
	SELECT PLAY_COUNT, DURATION 
	   FROM MI_STATISTICS_FILES_DAY
	WHERE DEVICE_ID           = #{device_id}
         AND START_TIME       = #{log_date} 
         AND CONTENT_ID       = #{content_id} 
         AND FILE_ID               = #{file_id}
         AND POP_FILE_TYPE = #{pop_file_type}
</select>

<select id="getPopFilesMonthInfo" resultType="com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity">
	SELECT PLAY_COUNT, DURATION 
	   FROM MI_STATISTICS_FILES_MONTH
	WHERE DEVICE_ID          = #{device_id}
         AND START_TIME       = #{log_date} 
         AND CONTENT_ID       = #{content_id} 
         AND FILE_ID               = #{file_id}
         AND POP_FILE_TYPE = #{pop_file_type}
</select>

<select id="getPopFilesYearInfo" resultType="com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity">
	SELECT PLAY_COUNT, DURATION 
	   FROM MI_STATISTICS_FILES_YEAR
	WHERE DEVICE_ID           = #{device_id}
         AND START_TIME        = #{log_date} 
         AND CONTENT_ID        = #{content_id} 
         AND FILE_ID                = #{file_id}
         AND POP_FILE_TYPE = #{pop_file_type}
</select>

<insert id="insertPopFilesHourInfo">
	INSERT INTO MI_STATISTICS_FILES_HOUR ( DEVICE_ID, START_TIME, CONTENT_ID, FILE_ID, POP_FILE_TYPE, PLAY_COUNT, DURATION) VALUES
	<foreach collection="list" close=")" item="item" open="(" separator="),(">
		#{device_id}, #{item.logDateStr}, #{content_id}, #{file_id}, #{pop_file_type}, #{item.play_count}, #{item.duration}
	</foreach>
</insert>

<update id="updatePopFilesHourInfo">
   UPDATE MI_STATISTICS_FILES_HOUR
         SET PLAY_COUNT      = #{pop.play_count}, 
                DURATION           = #{pop.duration}                
	WHERE DEVICE_ID          = #{device_id}
         AND START_TIME       = #{pop.logDateStr} 
         AND CONTENT_ID       = #{content_id} 
         AND FILE_ID               = #{file_id}
         AND POP_FILE_TYPE = #{pop_file_type}
</update>		

<insert id="insertPopFilesDayInfo">
	INSERT INTO MI_STATISTICS_FILES_DAY(DEVICE_ID, START_TIME, CONTENT_ID, FILE_ID, POP_FILE_TYPE, PLAY_DOW, PLAY_COUNT, DURATION) 
	VALUES (#{device_id}, #{log_date}, #{content_id}, #{file_id}, #{pop_file_type}, #{dow}, #{pop.play_count}, #{pop.duration})
</insert>

<update id="updatePopFilesDayInfo">
   UPDATE MI_STATISTICS_FILES_DAY
         SET PLAY_COUNT      = #{pop.play_count}, 
                DURATION           = #{pop.duration}
	WHERE DEVICE_ID          = #{device_id}
         AND START_TIME       = #{log_date} 
         AND CONTENT_ID       = #{content_id} 
         AND FILE_ID               = #{file_id}
         AND POP_FILE_TYPE = #{pop_file_type}
</update>	

<insert id="insertPopFilesMonthInfo">
	INSERT INTO MI_STATISTICS_FILES_MONTH(DEVICE_ID, START_TIME, CONTENT_ID, FILE_ID, POP_FILE_TYPE, LOG_QUARTER, PLAY_COUNT, DURATION) 
	VALUES (#{device_id}, #{log_date}, #{content_id}, #{file_id}, #{pop_file_type}, #{quarter}, #{pop.play_count}, #{pop.duration})
</insert>

<update id="updatePopFilesMonthInfo">
   UPDATE MI_STATISTICS_FILES_MONTH
         SET PLAY_COUNT       = #{pop.play_count}, 
                DURATION            = #{pop.duration}
	WHERE DEVICE_ID           = #{device_id}
         AND START_TIME       = #{log_date} 
         AND CONTENT_ID       = #{content_id} 
         AND FILE_ID               = #{file_id}
         AND POP_FILE_TYPE = #{pop_file_type}
</update>	

<insert id="insertPopFilesYearInfo">
	INSERT INTO MI_STATISTICS_FILES_YEAR(DEVICE_ID, START_TIME, CONTENT_ID, FILE_ID, POP_FILE_TYPE,  PLAY_COUNT, DURATION, FILE_NAME) 
	VALUES (#{device_id}, #{log_date}, #{content_id}, #{file_id}, #{pop_file_type}, #{pop.play_count}, #{pop.duration}, #{name})
</insert>

<update id="updatePopFilesYearInfo">
   UPDATE MI_STATISTICS_FILES_YEAR
         SET PLAY_COUNT      = #{pop.play_count}, 
             DURATION              = #{pop.duration},
             FILE_NAME             = #{name}
	WHERE DEVICE_ID          = #{device_id}
         AND START_TIME       = #{log_date} 
         AND CONTENT_ID       = #{content_id} 
         AND FILE_ID               = #{file_id}
         AND POP_FILE_TYPE = #{pop_file_type}
</update>	

<insert id="insertPopFileErrInfo">
	INSERT INTO MI_STATISTICS_CONTENT_FILE_ERROR ( DEVICE_ID, CONTENT_ID, START_TIME, END_TIME, ERROR_TYPE,DURATION, THRESHOLD) VALUES
	(#{deviceId},#{contentId},#{startDate},#{endDate},#{errType},#{duration},#{threshold})
</insert>
</mapper>