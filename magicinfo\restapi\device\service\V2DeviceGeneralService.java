package com.samsung.magicinfo.restapi.device.service;

import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGeneral;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;

public interface V2DeviceGeneralService {
   V2CommonBulkResultResource getGeneralInfo(V2CommonIds var1) throws Exception;

   V2DeviceReqServiceResource updateGeneralInfo(V2DeviceGeneral var1) throws Exception;

   V2DeviceReqServiceResource reqGetGeneral(V2CommonIds var1) throws Exception;

   V2CommonBulkResultResource getCurrentGeneralWithRequestId(V2DeviceReqServiceConf var1) throws Exception;
}
