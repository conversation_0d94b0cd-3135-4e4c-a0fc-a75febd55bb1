package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.areainfo.AreaClassifyEntity;
import java.sql.Connection;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class AreaClassifyDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(AreaClassifyDao.class);

   public AreaClassifyDao() {
      super();
   }

   public AreaClassifyDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public List getAreaList() {
      throw new UnsupportedOperationException("The AreaClassifyDao#getAreaList method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getClass1AreaList() {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass1AreaList method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getClass2AreaList() {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass2AreaList method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getClass3AreaList() {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass3AreaList method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getClass2AreaListByClass1(String class_1_name) {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass2AreaListByClass1 method is not supported anymore. The table MI_AD_AREA_CLASSIFY_REFERENCE does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getClass3AreaListByClass2(String class_2_name) {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass3AreaListByClass2 method is not supported anymore. The table MI_AD_AREA_CLASSIFY_REFERENCE does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public Object getClass1AreaNumber() {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass1AreaNumber method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public Object getClass2AreaNumber() {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass2AreaNumber method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public Object getClass3AreaNumber() {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass3AreaNumber method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getNations() {
      throw new UnsupportedOperationException("The AreaClassifyDao#getNations method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getClass1AreaLists(String nation_id) {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass1AreaLists method is not supported anymore. The table MI_AD_AREA_CLASSIFY_REFERENCE does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getClass2AreaLists(String nation_id, String class_1_id) {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass2AreaLists method is not supported anymore. The table MI_AD_AREA_CLASSIFY_REFERENCE does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getClass3AreaLists(String nation_id, String class_1_id, String class_2_id) {
      throw new UnsupportedOperationException("The AreaClassifyDao#getClass3AreaLists method is not supported anymore. The table MI_AD_AREA_CLASSIFY_REFERENCE does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean insertInotAreaClassifyLists(AreaClassifyEntity data) {
      throw new UnsupportedOperationException("The AreaClassifyDao#insertInotAreaClassifyLists method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }

   public List getVendingDeviceGroupList() {
      try {
         return ((AreaClassifyDaoMapper)this.getMapper()).getVendingDeviceGroupList();
      } catch (Exception var2) {
         this.logger.error("", var2);
         return null;
      }
   }

   /** @deprecated */
   @Deprecated
   public List getUndefinedVendingDeviceGroupList() {
      throw new UnsupportedOperationException("The AreaClassifyDao#getUndefinedVendingDeviceGroupList method is not supported anymore. The table MI_AD_AREA_CLASSIFY_INFO does not exists.");
   }
}
