package com.samsung.magicinfo.restapi.device.service;

import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonStatusResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTimeconfResource;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface V2DeviceTimeService {
   V2DeviceReqServiceResource updateTimeInfo(V2DeviceTimeconfResource var1) throws Exception;

   V2CommonBulkResultResource getTimeInfo(V2CommonIds var1) throws Exception;

   V2DeviceReqServiceResource getCurrentStatusTime(V2CommonIds var1) throws Exception;

   V2CommonBulkResultResource getCurrentStatusTimeResult(V2DeviceReqServiceConf var1) throws Exception;

   V2CommonStatusResource reqSetTimeToDevice(String var1, HttpServletRequest var2) throws Exception;

   V2PageResource filterDeviceTimeList(V2DeviceFilter var1, HttpServletRequest var2, HttpServletResponse var3) throws Exception;
}
