package com.samsung.magicinfo.framework.advertisement.entity.schedule;

public class ScheduleInfoListEntity {
   private String user_id = null;
   private String schedule_id = null;
   private int schedule_time_id = -1;
   private String start_date = null;
   private String end_date = null;
   private String start_time = null;
   private String end_time = null;
   private String area_ad_type_name = null;
   private String thumb_nail_image = null;
   private String file_name = null;
   private String file_type = null;
   private int running_time = -1;
   private int requested_play_time = -1;
   private int expected_play_time = -1;

   public ScheduleInfoListEntity() {
      super();
   }

   public String getUser_id() {
      return this.user_id;
   }

   public void setUser_id(String user_id) {
      this.user_id = user_id;
   }

   public String getSchedule_id() {
      return this.schedule_id;
   }

   public void setSchedule_id(String schedule_id) {
      this.schedule_id = schedule_id;
   }

   public int getSchedule_time_id() {
      return this.schedule_time_id;
   }

   public void setSchedule_time_id(int schedule_time_id) {
      this.schedule_time_id = schedule_time_id;
   }

   public String getStart_date() {
      return this.start_date;
   }

   public void setStart_date(String start_date) {
      this.start_date = start_date;
   }

   public String getEnd_date() {
      return this.end_date;
   }

   public void setEnd_date(String end_date) {
      this.end_date = end_date;
   }

   public String getStart_time() {
      return this.start_time;
   }

   public void setStart_time(String start_time) {
      this.start_time = start_time;
   }

   public String getEnd_time() {
      return this.end_time;
   }

   public void setEnd_time(String end_time) {
      this.end_time = end_time;
   }

   public String getArea_ad_type_name() {
      return this.area_ad_type_name;
   }

   public void setArea_ad_type_name(String area_ad_type_name) {
      this.area_ad_type_name = area_ad_type_name;
   }

   public String getThumb_nail_image() {
      return this.thumb_nail_image;
   }

   public void setThumb_nail_image(String thumb_nail_image) {
      this.thumb_nail_image = thumb_nail_image;
   }

   public String getFile_name() {
      return this.file_name;
   }

   public void setFile_name(String file_name) {
      this.file_name = file_name;
   }

   public String getFile_type() {
      return this.file_type;
   }

   public void setFile_type(String file_type) {
      this.file_type = file_type;
   }

   public int getRunning_time() {
      return this.running_time;
   }

   public void setRunning_time(int running_time) {
      this.running_time = running_time;
   }

   public int getRequested_play_time() {
      return this.requested_play_time;
   }

   public void setRequested_play_time(int requested_play_time) {
      this.requested_play_time = requested_play_time;
   }

   public int getExpected_play_time() {
      return this.expected_play_time;
   }

   public void setExpected_play_time(int expected_play_time) {
      this.expected_play_time = expected_play_time;
   }
}
