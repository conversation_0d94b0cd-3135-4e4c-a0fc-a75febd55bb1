package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.schedule.ContentListEntity;
import com.samsung.magicinfo.framework.advertisement.entity.schedule.ScheduleAreaEntity;
import com.samsung.magicinfo.framework.advertisement.entity.schedule.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.schedule.ScheduleTimeListEntity;
import java.sql.Connection;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.Logger;

public class ScheduleSetupDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(ScheduleInfoDao.class);

   public ScheduleSetupDao() {
      super();
   }

   public ScheduleSetupDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public ScheduleAreaEntity getScheudleAreaList(String schedule_id) {
      throw new UnsupportedOperationException("ScheduleSetupDao#getScheudleAreaList is not supported anymore: The table MI_AD_SCHEDULE_AREA_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public int setScheduleAreaList(ScheduleAreaEntity schedule_area) {
      throw new UnsupportedOperationException("ScheduleSetupDao#setScheduleAreaList is not supported anymore: The table MI_AD_SCHEDULE_AREA_LIST does not exists");
   }

   public int setScheduleDeviceGroup(String program_id, String group_id) {
      int result = -1;
      if (program_id == null) {
         program_id = "null";
      }

      Integer iGroupId = Integer.parseInt(group_id);

      try {
         int result = ((ScheduleSetupDaoMapper)this.getMapper()).setScheduleDeviceGroup(program_id, iGroupId);
         return result;
      } catch (Exception var6) {
         this.logger.error("", var6);
         return result;
      }
   }

   /** @deprecated */
   @Deprecated
   public int updateScheduleAreaList(String old_schedule_id, String new_schedule_id) {
      throw new UnsupportedOperationException("ScheduleSetupDao#updateScheduleAreaList is not supported anymore: The table MI_AD_SCHEDULE_AREA_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getScheduleAreaList(String schedule_id) {
      throw new UnsupportedOperationException("ScheduleSetupDao#getScheduleAreaList is not supported anymore: The table MI_AD_SCHEDULE_AREA_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public int setScheduleTimeList(ScheduleTimeListEntity data) {
      throw new UnsupportedOperationException("ScheduleSetupDao#setScheduleTimeList is not supported anymore: The table MI_AD_SCHEDULE_TIME_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public int updateScheduleTimeList(ScheduleTimeListEntity data) {
      throw new UnsupportedOperationException("ScheduleSetupDao#updateScheduleTimeList is not supported anymore: The table MI_AD_SCHEDULE_TIME_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getTimeListTable(String user_id, String schedule_id) {
      throw new UnsupportedOperationException("ScheduleSetupDao#getTimeListTable is not supported anymore: The table MI_AD_SCHEDULE_TIME_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getContentList(String user_id, String schedule_id) {
      throw new UnsupportedOperationException("ScheduleSetupDao#getContentList is not supported anymore: The table MI_AD_SCHEDULE_CONTENT_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getSavedContentList(String user_id, String schedule_id, int index) {
      throw new UnsupportedOperationException("ScheduleSetupDao#getSavedContentList is not supported anymore: The table MI_AD_SCHEDULE_CONTENT_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public int insertContentList(String user_id, String schedule_id, ContentListEntity data) {
      throw new UnsupportedOperationException("ScheduleSetupDao#insertContentList is not supported anymore: The table MI_AD_SCHEDULE_CONTENT_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public int updateContentList(String user_id, String schedule_id, ContentListEntity data) {
      throw new UnsupportedOperationException("ScheduleSetupDao#updateContentList is not supported anymore: The table MI_AD_SCHEDULE_CONTENT_LIST does not exists");
   }

   public String getFileName(String file_id) {
      String result = null;
      Map temp = null;

      try {
         temp = ((ScheduleSetupDaoMapper)this.getMapper()).getFileName(file_id);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      result = (String)temp.get("file_name");
      return result;
   }

   public String getFileType(String file_id) {
      Map temp = null;

      try {
         temp = ((ScheduleSetupDaoMapper)this.getMapper()).getFileType(file_id);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      String result = (String)temp.get("media_type");
      return result;
   }

   /** @deprecated */
   @Deprecated
   public boolean insertScheduleInfo(ScheduleInfoEntity data) {
      throw new UnsupportedOperationException("ScheduleSetupDao#insertScheduleInfo is not supported anymore: The table MI_AD_SCHEDULE_INFO does not exists");
   }

   /** @deprecated */
   @Deprecated
   public boolean deleteScheduleContent(String user_id, String schedule_id) {
      throw new UnsupportedOperationException("ScheduleSetupDao#deleteScheduleContent is not supported anymore: The table MI_AD_SCHEDULE_CONTENT_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public boolean deleteScheduleTimeList(String user_id, String schedule_id) {
      throw new UnsupportedOperationException("ScheduleSetupDao#deleteScheduleTimeList is not supported anymore: The table MI_AD_SCHEDULE_TIME_LIST does not exists");
   }

   /** @deprecated */
   @Deprecated
   public boolean deleteScheduleAreaList(String user_id, String schedule_id) {
      throw new UnsupportedOperationException("ScheduleSetupDao#deleteScheduleAreaList is not supported anymore: The table MI_AD_SCHEDULE_AREA_LIST does not exists");
   }

   public boolean registerSchedule(String user_id, String schedule_id, String program_id, ContentListEntity result_list, int frame_index) {
      int result = 0;
      String start_hour = null;
      start_hour = result_list.getStart_hour();
      start_hour = start_hour.length() == 1 ? "0" + start_hour : start_hour;
      String start_minute = null;
      start_minute = result_list.getStart_minute();
      start_minute = start_minute.length() == 1 ? "0" + start_minute : start_minute;
      String start_time = start_hour + ":" + start_minute + ":00";
      int hour = (Integer.parseInt(result_list.getEnd_hour()) - Integer.parseInt(result_list.getStart_hour())) * 3600;
      int minute = (Integer.parseInt(result_list.getEnd_minute()) - Integer.parseInt(result_list.getStart_minute())) * 60;
      int duration = hour + minute;

      try {
         result = ((ScheduleSetupDaoMapper)this.getMapper()).registerSchedule(program_id, schedule_id, result_list, user_id, duration, start_time, frame_index);
      } catch (Exception var14) {
         this.logger.error("", var14);
      }

      return result > 0;
   }

   /** @deprecated */
   @Deprecated
   public boolean registerProgram(String schedule_id, String schedule_name, String program_id, String user_id) {
      throw new UnsupportedOperationException("ScheduleSetupDao#registerProgram is not supported anymore: The table MI_CDS_INFO_PROGRAM does not exists");
   }

   /** @deprecated */
   @Deprecated
   public boolean registerFrame(String schedule_id, String program_id, String frame_id, String frame_name, int frame_index) {
      throw new UnsupportedOperationException("ScheduleSetupDao#registerProgram is not supported anymore: The table MI_CDS_INFO_PROGRAM does not exists");
   }
}
