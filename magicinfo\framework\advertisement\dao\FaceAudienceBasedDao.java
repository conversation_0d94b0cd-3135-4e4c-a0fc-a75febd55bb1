package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.magicinfo.framework.advertisement.entity.AudienceInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.AudienceKey;
import com.samsung.magicinfo.framework.advertisement.entity.AudienceRawEntity;
import com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity;
import com.samsung.magicinfo.framework.advertisement.entity.TrafficKey;
import com.samsung.magicinfo.framework.statistics.util.FaceConditionFilterUtil;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.apache.logging.log4j.Logger;

public class FaceAudienceBasedDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(FaceAudienceBasedDao.class);
   int DOW = 0;
   int QUARTER = 0;
   int WOY = 1;
   Timestamp DAY = null;
   Timestamp MONTH = null;
   Timestamp YEAR = null;

   public FaceAudienceBasedDao() {
      super();
   }

   private void setDayItems(String logDate) {
      try {
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         Date date = sdf.parse(logDate);
         Calendar cal = Calendar.getInstance();
         cal.setTime(date);
         this.DOW = cal.get(7) - 1;
         this.QUARTER = cal.get(2) / 3 + 1;
         this.WOY = cal.get(3);
      } catch (ParseException var5) {
         this.logger.error("", var5);
      }

      this.DAY = Timestamp.valueOf(logDate.substring(0, 10) + " 00:00:00");
      this.MONTH = Timestamp.valueOf(logDate.substring(0, 7) + "-01 00:00:00");
      this.YEAR = Timestamp.valueOf(logDate.substring(0, 4) + "-01-01 00:00:00");
   }

   public boolean insertFaceAudienceBasedHistroy(List play_history, String logDate) throws SQLException {
      int totalRows = play_history.size();
      String[] common_info = (String[])play_history.get(0);
      String device_id = common_info[3];
      boolean useHourTable = true;

      try {
         String amsCollectionLevel = CommonConfig.get("ams.db.collection.level");
         if (amsCollectionLevel != null) {
            this.logger.error("[AMS-Audience] statistics collection level: " + amsCollectionLevel);
            if (amsCollectionLevel.equalsIgnoreCase("day")) {
               useHourTable = false;
            }
         }
      } catch (ConfigException var22) {
         this.logger.error("", var22);
      }

      this.setDayItems(logDate);
      HashMap mapRawByAudienceKey = this.splitAudienceBasedHistoryByAudienceKey(play_history, totalRows, device_id);
      if (mapRawByAudienceKey.size() == 0) {
         this.logger.error("[STATISTICS] splitted map size is zero");
         return false;
      } else {
         HashMap mapDailyInfo = new HashMap();
         HashMap mapNewDailyInfoToDay = new HashMap();
         HashMap mapNewDailyInfoToMonth = new HashMap();
         HashMap mapNewDailyInfoToYear = new HashMap();
         Set keySet = mapRawByAudienceKey.keySet();

         Iterator iterator;
         AudienceKey audienceKey;
         AudienceInfoEntity originalMonthPopInfo;
         for(iterator = keySet.iterator(); iterator.hasNext(); mapDailyInfo.put(audienceKey, originalMonthPopInfo)) {
            audienceKey = (AudienceKey)iterator.next();
            ArrayList audienceRawList = (ArrayList)mapRawByAudienceKey.get(audienceKey);
            originalMonthPopInfo = new AudienceInfoEntity();

            try {
               this.addPopHourInfoList(device_id, audienceKey, audienceRawList, originalMonthPopInfo, useHourTable);
            } catch (Exception var21) {
               this.logger.error("[STATISTICS] - Blocking Hour-info-DAO insert by using duplicate key value violates - (device_id: " + device_id + " content_id: " + audienceKey + ")");
            }
         }

         keySet = mapDailyInfo.keySet();
         iterator = keySet.iterator();

         while(iterator.hasNext()) {
            AudienceKey audienceKey = (AudienceKey)iterator.next();
            AudienceInfoEntity tmpPopInfoToAdd = (AudienceInfoEntity)mapDailyInfo.get(audienceKey);
            AudienceInfoEntity originalDayPopInfo = this.getPopDayInfo(audienceKey, device_id);
            if (originalDayPopInfo != null) {
               originalDayPopInfo.addDwellTime(tmpPopInfoToAdd.getDwell_time());
               originalDayPopInfo.addTotalCount(tmpPopInfoToAdd.getTotal_count());
               originalDayPopInfo.addAttentionCount(tmpPopInfoToAdd.getAttention_count());
               originalDayPopInfo.addAttentionTime(tmpPopInfoToAdd.getAttention_time());
               this.setPopDayInfo(audienceKey, device_id, originalDayPopInfo);
            } else {
               mapNewDailyInfoToDay.put(audienceKey, tmpPopInfoToAdd);
            }

            originalMonthPopInfo = this.getPopMonthInfo(audienceKey, device_id);
            if (originalMonthPopInfo != null) {
               originalMonthPopInfo.addDwellTime(tmpPopInfoToAdd.getDwell_time());
               originalMonthPopInfo.addTotalCount(tmpPopInfoToAdd.getTotal_count());
               originalMonthPopInfo.addAttentionCount(tmpPopInfoToAdd.getAttention_count());
               originalMonthPopInfo.addAttentionTime(tmpPopInfoToAdd.getAttention_time());
               this.setPopMonthInfo(audienceKey, device_id, originalMonthPopInfo);
            } else {
               mapNewDailyInfoToMonth.put(audienceKey, tmpPopInfoToAdd);
            }

            AudienceInfoEntity originalYearPopInfo = this.getPopYearInfo(audienceKey, device_id);
            if (originalYearPopInfo != null) {
               originalYearPopInfo.addDwellTime(tmpPopInfoToAdd.getDwell_time());
               originalYearPopInfo.addTotalCount(tmpPopInfoToAdd.getTotal_count());
               originalYearPopInfo.addAttentionCount(tmpPopInfoToAdd.getAttention_count());
               originalYearPopInfo.addAttentionTime(tmpPopInfoToAdd.getAttention_time());
               this.setPopYearInfo(audienceKey, device_id, originalYearPopInfo);
            } else {
               mapNewDailyInfoToYear.put(audienceKey, tmpPopInfoToAdd);
            }
         }

         if (mapNewDailyInfoToDay.size() != 0) {
            this.addPopDayInfoList(device_id, mapNewDailyInfoToDay);
         }

         if (mapNewDailyInfoToMonth.size() != 0) {
            this.addPopMonthInfoList(device_id, mapNewDailyInfoToMonth);
         }

         if (mapNewDailyInfoToYear.size() != 0) {
            this.addPopYearInfoList(device_id, logDate, mapNewDailyInfoToYear);
         }

         return true;
      }
   }

   public boolean insertFaceTrafficBasedHistroy(List play_history, String logDate) throws SQLException {
      int totalRows = play_history.size();
      String[] common_info = (String[])play_history.get(0);
      String device_id = common_info[3];
      boolean useHourTable = true;

      try {
         String amsCollectionLevel = CommonConfig.get("ams.db.collection.level");
         if (amsCollectionLevel != null) {
            this.logger.error("[AMS-Traffic] statistics collection level: " + amsCollectionLevel);
            if (amsCollectionLevel.equalsIgnoreCase("day")) {
               useHourTable = false;
            }
         }
      } catch (ConfigException var23) {
         this.logger.error("", var23);
      }

      this.setDayItems(logDate);
      HashMap mapRawByTrafficeKey = this.splitTrafficBasedHistoryByAudienceKey(play_history, totalRows, device_id);
      if (mapRawByTrafficeKey.size() == 0) {
         this.logger.error("[STATISTICS] splitted map size is zero");
         return false;
      } else {
         HashMap mapDailyInfo = new HashMap();
         HashMap mapNewDailyInfoToMonth = new HashMap();
         HashMap mapNewDailyInfoToYear = new HashMap();
         Set keySet = mapRawByTrafficeKey.keySet();

         Iterator iterator;
         TrafficKey trafficKey;
         FaceTrafficEntity originalYearPopInfo;
         for(iterator = keySet.iterator(); iterator.hasNext(); mapDailyInfo.put(trafficKey, originalYearPopInfo)) {
            trafficKey = (TrafficKey)iterator.next();
            ArrayList trafficRawList = (ArrayList)mapRawByTrafficeKey.get(trafficKey);
            originalYearPopInfo = new FaceTrafficEntity();

            try {
               this.addTrafficHourInfo(device_id, trafficKey, trafficRawList, originalYearPopInfo, useHourTable);
            } catch (Exception var22) {
               this.logger.error("[STATISTICS] - Blocking Hour-info-DAO insert by using duplicate key value violates - (device_id: " + device_id + " content_id: " + trafficKey.getContentId() + ")");
            }
         }

         try {
            ((FaceAudienceBasedDaoMapper)this.getMapper()).addTrafficPopDayInfoList(device_id, mapDailyInfo, this.DAY, this.DOW, this.WOY);
         } catch (Exception var21) {
            this.logger.error("[STATISTICS] - Blocking Day-info-DAO insert by using duplicate key value violates - (device_id: " + device_id + " log_date: " + logDate + ")");
            return false;
         }

         keySet = mapDailyInfo.keySet();
         iterator = keySet.iterator();
         boolean newMonth = false;
         boolean newYear = false;

         while(iterator.hasNext()) {
            TrafficKey trafficKey = (TrafficKey)iterator.next();
            FaceTrafficEntity tmpPopInfoToAdd = (FaceTrafficEntity)mapDailyInfo.get(trafficKey);
            FaceTrafficEntity originalMonthPopInfo = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficPopMonthInfo(device_id, trafficKey, this.MONTH);
            if (originalMonthPopInfo != null) {
               originalMonthPopInfo.addTotalCount(tmpPopInfoToAdd.getTotal_count());
               ((FaceAudienceBasedDaoMapper)this.getMapper()).setTrafficPopMonthInfo(originalMonthPopInfo.getTotal_count(), this.MONTH, device_id, originalMonthPopInfo.getContent_id());
            } else {
               newMonth = true;
               mapNewDailyInfoToMonth.put(trafficKey, tmpPopInfoToAdd);
            }

            originalYearPopInfo = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficPopYearInfo(device_id, trafficKey, this.YEAR);
            if (originalYearPopInfo != null) {
               originalYearPopInfo.addTotalCount(tmpPopInfoToAdd.getTotal_count());
               ((FaceAudienceBasedDaoMapper)this.getMapper()).setTrafficPopYearInfo(originalYearPopInfo.getTotal_count(), this.YEAR, device_id, originalYearPopInfo.getContent_id());
            } else {
               newYear = true;
               mapNewDailyInfoToYear.put(trafficKey, tmpPopInfoToAdd);
            }
         }

         if (newMonth) {
            ((FaceAudienceBasedDaoMapper)this.getMapper()).addTrafficPopMonthInfoList(device_id, mapNewDailyInfoToMonth, this.MONTH, this.QUARTER);
         }

         if (newYear) {
            ((FaceAudienceBasedDaoMapper)this.getMapper()).addTrafficPopYearInfoList(device_id, mapNewDailyInfoToYear, this.YEAR);
         }

         return true;
      }
   }

   private HashMap splitAudienceBasedHistoryByAudienceKey(List play_history, int totalRows, String device_id) {
      HashMap mapRaw = new HashMap();

      for(int i = 0; i < totalRows; ++i) {
         String[] play_info = (String[])play_history.get(i);

         Timestamp start_time;
         try {
            start_time = Timestamp.valueOf(play_info[0]);
         } catch (IllegalArgumentException var22) {
            this.logger.error("[STATISTICS] Audience IllegalArgumentException has been occured for " + play_info[0] + " index : " + i);
            continue;
         }

         int totalCount = Integer.valueOf(play_info[7]);
         int attentionCount = Integer.valueOf(play_info[8]);
         int duration = Integer.valueOf(play_info[9]);
         int attentionTime = Integer.valueOf(play_info[10]);
         String contentId = play_info[1];
         String contentName = play_info[2];
         String deviceId = play_info[3];
         String deviceName = play_info[4];
         String gender = play_info[5];
         String age = play_info[6];
         AudienceRawEntity abse = new AudienceRawEntity(start_time, totalCount, duration, attentionCount, attentionTime, contentName, deviceId, deviceName);
         if (play_info[2].isEmpty()) {
            play_info[2] = "NOT SUPPORT";
         }

         AudienceKey audienceKey = new AudienceKey(contentId, gender, age);
         ArrayList arrAbse;
         if ((arrAbse = (ArrayList)mapRaw.get(audienceKey)) == null) {
            ArrayList newArrAbse = new ArrayList();
            newArrAbse.add(abse);
            mapRaw.put(audienceKey, newArrAbse);
         } else {
            arrAbse.add(abse);
            mapRaw.put(audienceKey, arrAbse);
         }
      }

      return mapRaw;
   }

   private HashMap splitTrafficBasedHistoryByAudienceKey(List play_history, int totalRows, String device_id) {
      HashMap mapRaw = new HashMap();

      for(int i = 0; i < totalRows; ++i) {
         String[] play_info = (String[])play_history.get(i);

         Timestamp start_time;
         try {
            start_time = Timestamp.valueOf(play_info[0]);
         } catch (IllegalArgumentException var17) {
            this.logger.error("[STATISTICS] Traffic IllegalArgumentException has been occured for " + play_info[0] + " index : " + i);
            continue;
         }

         String contentId = play_info[1];
         String contentName = play_info[2];
         String deviceId = play_info[3];
         String deviceName = play_info[4];
         int totalCount = Integer.valueOf(play_info[5]);
         FaceTrafficEntity tbse = new FaceTrafficEntity(start_time, contentId, contentName, deviceId, deviceName, totalCount);
         if (play_info[2].isEmpty()) {
            play_info[2] = "NOT SUPPORT";
         }

         TrafficKey trafficKey = new TrafficKey(contentId);
         ArrayList arrTbse;
         if ((arrTbse = (ArrayList)mapRaw.get(trafficKey)) == null) {
            ArrayList newArrTbse = new ArrayList();
            newArrTbse.add(tbse);
            mapRaw.put(trafficKey, newArrTbse);
         } else {
            arrTbse.add(tbse);
            mapRaw.put(trafficKey, arrTbse);
         }
      }

      return mapRaw;
   }

   private void addPopHourInfoList(String deviceId, AudienceKey audienceKey, ArrayList audienceRawEntity, AudienceInfoEntity dailyPlayInfo, boolean useHourTable) throws SQLException {
      Iterator var6 = audienceRawEntity.iterator();

      while(var6.hasNext()) {
         AudienceRawEntity rawEntity = (AudienceRawEntity)var6.next();
         dailyPlayInfo.addTotalCount(rawEntity.getTotalCount());
         dailyPlayInfo.addDwellTime(rawEntity.getDwellTime());
         dailyPlayInfo.addAttentionCount(rawEntity.getAttentionCount());
         dailyPlayInfo.addAttentionTime(rawEntity.getAttentionTime());
         dailyPlayInfo.setContent_name(rawEntity.getContentName());
         dailyPlayInfo.setDevice_name(rawEntity.getDeviceName());
      }

      if (useHourTable) {
         ((FaceAudienceBasedDaoMapper)this.getMapper()).addAudiencePopHourInfoList(deviceId, audienceKey, audienceRawEntity);
      }

   }

   private void addPopDayInfoList(String deviceId, HashMap dailyPlayInfo) throws SQLException {
      ((FaceAudienceBasedDaoMapper)this.getMapper()).addAudiencePopDayInfoList(deviceId, dailyPlayInfo, this.DAY, this.DOW, this.WOY);
   }

   private AudienceInfoEntity getPopDayInfo(AudienceKey audienceKey, String device_id) throws SQLException {
      return ((FaceAudienceBasedDaoMapper)this.getMapper()).getAudiencePopDayInfo(device_id, audienceKey, this.DAY);
   }

   private AudienceInfoEntity getPopMonthInfo(AudienceKey audienceKey, String device_id) throws SQLException {
      return ((FaceAudienceBasedDaoMapper)this.getMapper()).getAudiencePopMonthInfo(device_id, audienceKey, this.MONTH);
   }

   private void setPopDayInfo(AudienceKey audienceKey, String device_id, AudienceInfoEntity entity) throws SQLException {
      ((FaceAudienceBasedDaoMapper)this.getMapper()).setAudiencePopDayInfo(entity, this.DAY, audienceKey, device_id);
   }

   private void setPopMonthInfo(AudienceKey audienceKey, String device_id, AudienceInfoEntity entity) throws SQLException {
      ((FaceAudienceBasedDaoMapper)this.getMapper()).setAudiencePopMonthInfo(entity, this.MONTH, audienceKey, device_id);
   }

   private AudienceInfoEntity getPopYearInfo(AudienceKey audienceKey, String device_id) throws SQLException {
      return ((FaceAudienceBasedDaoMapper)this.getMapper()).getAudiencePopYearInfo(device_id, audienceKey, this.YEAR);
   }

   private void setPopYearInfo(AudienceKey audienceKey, String device_id, AudienceInfoEntity entity) throws SQLException {
      ((FaceAudienceBasedDaoMapper)this.getMapper()).setAudiencePopYearInfo(entity, this.YEAR, audienceKey, device_id);
   }

   private void addPopMonthInfoList(String deviceId, HashMap mapNewDailyInfo) throws SQLException {
      ((FaceAudienceBasedDaoMapper)this.getMapper()).addAudiencePopMonthInfoList(deviceId, mapNewDailyInfo, this.MONTH, this.QUARTER);
   }

   private void addPopYearInfoList(String deviceId, String logDate, HashMap mapNewDailyInfo) throws SQLException {
      Timestamp startTime = Timestamp.valueOf(logDate.substring(0, 4) + "-01-01 00:00:00");
      ((FaceAudienceBasedDaoMapper)this.getMapper()).addAudiencePopYearInfoList(deviceId, mapNewDailyInfo, startTime);
   }

   private HashMap parseSelectedConditions(HashMap mapFilter) {
      HashMap conditionMap = new HashMap();
      Set keySet = mapFilter.keySet();
      Iterator iterator = keySet.iterator();

      while(iterator.hasNext()) {
         String condition = (String)iterator.next();
         if (!FaceConditionFilterUtil.isFilterKey(condition)) {
            ArrayList optionList = (ArrayList)mapFilter.get(condition);
            conditionMap.put(condition, optionList);
         }
      }

      return conditionMap;
   }

   public List getYesterdayListBy(String[] deviceList, String unit, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getYesterdayListBy(deviceList, (String)null, (HashMap)null, unit.toUpperCase(), type);
      } catch (SQLException var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getWeekListBy(String[] deviceList, String unit, String amsAudienceBasedShow, String type) {
      List data_list = null;
      String[] thisWeek = DateUtils.getThisWeek();
      String[] lastWeek = DateUtils.getLastWeek();
      Timestamp startDate = null;
      Timestamp endDate = null;

      try {
         if (amsAudienceBasedShow.equalsIgnoreCase("last_week")) {
            startDate = Timestamp.valueOf(lastWeek[0] + " 00:00:00");
            endDate = Timestamp.valueOf(lastWeek[1] + " 23:59:59");
            data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getWeekListBy(deviceList, startDate, endDate, unit.toUpperCase(), type);
         } else {
            startDate = Timestamp.valueOf(thisWeek[0] + " 00:00:00");
            endDate = Timestamp.valueOf(thisWeek[1] + " 23:59:59");
            data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getWeekListBy(deviceList, startDate, endDate, unit.toUpperCase(), type);
         }
      } catch (SQLException var11) {
         this.logger.error("", var11);
      }

      return data_list;
   }

   public List getMonthListByMonth(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getMonthListByMonth(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getTrafficMonthListByDay(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficMonthListByDay(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getTrafficMonthListByWeek(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficMonthListByWeek(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getTrafficMonthListByHour(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficMonthListByHour(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getMonthListByDay(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getMonthListByDay(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getMonthListByHour(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getMonthListByHour(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getMonthListByDow(String[] deviceIdList, boolean isThis) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getMonthListByDow(deviceIdList, isThis);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      return data_list;
   }

   public List getQuarterListByQuarter(String[] deviceIdList, boolean isThis) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getQuarterListByQuarter(deviceIdList, isThis);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      return data_list;
   }

   public List getYearListByYear(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getYearListByYear(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getYearListByQuarter(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getYearListByQuarter(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getCustomList(String start_date, String end_date, String[] deviceIdList, String unit, String type) {
      List data_list = null;
      Timestamp startDate = Timestamp.valueOf(start_date + " 00:00:00");
      Timestamp endDate = Timestamp.valueOf(end_date + " 23:59:59");
      String unitParameter = null;
      if (unit.equalsIgnoreCase("DAY")) {
         unitParameter = "DAY";
      } else if (unit.equalsIgnoreCase("HOUR")) {
         unitParameter = "HOUR";
      } else if (unit.equalsIgnoreCase("WEEK")) {
         unitParameter = "WEEK";
      } else if (unit.equalsIgnoreCase("MONTH")) {
         unitParameter = "MONTH";
      } else if (unit.equalsIgnoreCase("YEAR")) {
         unitParameter = "YEAR";
      } else {
         if (!unit.equalsIgnoreCase("DOW")) {
            this.logger.error("input param error (unit)");
            throw new NullPointerException();
         }

         unitParameter = "DOW";
      }

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getCustomList(startDate, endDate, deviceIdList, unitParameter, type);
      } catch (Exception var11) {
         this.logger.error("", var11);
      }

      return data_list;
   }

   public List getQuarterListByMonth(String[] deviceIdList, boolean isThis) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getQuarterListByMonth(deviceIdList, isThis);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      return data_list;
   }

   private void addTrafficHourInfo(String deviceId, TrafficKey trafficKey, ArrayList audienceRawEntity, FaceTrafficEntity dailyPlayInfo, boolean useHourTable) throws SQLException {
      Iterator var6 = audienceRawEntity.iterator();

      while(var6.hasNext()) {
         FaceTrafficEntity rawEntity = (FaceTrafficEntity)var6.next();
         dailyPlayInfo.addTotalCount(rawEntity.getTotal_count());
         dailyPlayInfo.setContent_name(rawEntity.getContent_name());
         dailyPlayInfo.setDevice_name(rawEntity.getDevice_name());
      }

      if (useHourTable) {
         ((FaceAudienceBasedDaoMapper)this.getMapper()).addTrafficPopHourInfoList(deviceId, trafficKey, audienceRawEntity);
      }

   }

   public List getTrafficYesterdayListBy(String[] deviceList, String unit, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficYesterdayListBy(deviceList, unit.toUpperCase(), type);
      } catch (SQLException var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getTrafficWeekListBy(String[] deviceList, String unit, String amsAudienceBasedShow, String type) {
      List data_list = null;
      String[] thisWeek = DateUtils.getThisWeek();
      String[] lastWeek = DateUtils.getLastWeek();
      Timestamp startDate = null;
      Timestamp endDate = null;

      try {
         if (amsAudienceBasedShow.equalsIgnoreCase("last_week")) {
            startDate = Timestamp.valueOf(lastWeek[0] + " 00:00:00");
            endDate = Timestamp.valueOf(lastWeek[1] + " 23:59:59");
            data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficWeekListBy(deviceList, startDate, endDate, unit.toUpperCase(), type);
         } else {
            startDate = Timestamp.valueOf(thisWeek[0] + " 00:00:00");
            endDate = Timestamp.valueOf(thisWeek[1] + " 23:59:59");
            data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficWeekListBy(deviceList, startDate, endDate, unit.toUpperCase(), type);
         }
      } catch (SQLException var11) {
         this.logger.error("", var11);
      }

      return data_list;
   }

   public List getTrafficMonthListByDow(String[] deviceIdList, boolean isThis) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficMonthListByDow(deviceIdList, isThis);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      return data_list;
   }

   public List getTrafficMonthListByMonth(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficMonthListByMonth(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getTrafficQuarterListByQuarter(String[] deviceIdList, boolean isThis) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficQuarterListByQuarter(deviceIdList, isThis);
      } catch (Exception var5) {
         this.logger.error("", var5);
      }

      return data_list;
   }

   public List getTrafficYearListByQuarter(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficYearListByQuarter(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getTrafficYearListByYear(String[] deviceIdList, boolean isThis, String type) {
      List data_list = null;

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficYearListByYear(deviceIdList, isThis, type);
      } catch (Exception var6) {
         this.logger.error("", var6);
      }

      return data_list;
   }

   public List getTrafficCustomList(String start_date, String end_date, String[] deviceIdList, String unit, String type) {
      List data_list = null;
      Timestamp startDate = Timestamp.valueOf(start_date + " 00:00:00");
      Timestamp endDate = Timestamp.valueOf(end_date + " 23:59:59");
      String unitParameter = null;
      if (unit.equalsIgnoreCase("DAY")) {
         unitParameter = "DAY";
      } else if (unit.equalsIgnoreCase("HOUR")) {
         unitParameter = "HOUR";
      } else if (unit.equalsIgnoreCase("WEEK")) {
         unitParameter = "WEEK";
      } else if (unit.equalsIgnoreCase("MONTH")) {
         unitParameter = "MONTH";
      } else if (unit.equalsIgnoreCase("YEAR")) {
         unitParameter = "YEAR";
      } else {
         if (!unit.equalsIgnoreCase("DOW")) {
            this.logger.error("input param error (unit)");
            throw new NullPointerException();
         }

         unitParameter = "DOW";
      }

      try {
         data_list = ((FaceAudienceBasedDaoMapper)this.getMapper()).getTrafficCustomList(startDate, endDate, deviceIdList, unitParameter, type);
      } catch (Exception var11) {
         this.logger.error("", var11);
      }

      return data_list;
   }
}
