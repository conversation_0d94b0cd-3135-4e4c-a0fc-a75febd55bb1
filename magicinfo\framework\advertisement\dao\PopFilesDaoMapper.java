package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PopFilesDaoMapper {
   Long checkDuplicationByDB(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("pop_file_type") String var3) throws SQLException;

   PopInfoEntity getPopFilesHourInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5) throws SQLException;

   void insertPopFilesHourInfo(@Param("device_id") String var1, @Param("content_id") String var2, @Param("file_id") String var3, @Param("pop_file_type") String var4, @Param("list") List var5) throws SQLException;

   int updatePopFilesHourInfo(@Param("device_id") String var1, @Param("content_id") String var2, @Param("file_id") String var3, @Param("pop_file_type") String var4, @Param("pop") PopInfoEntity var5) throws SQLException;

   PopInfoEntity getPopFilesDayInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5) throws SQLException;

   void insertPopFilesDayInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5, @Param("pop") PopInfoEntity var6, @Param("dow") int var7) throws SQLException;

   int updatePopFilesDayInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5, @Param("pop") PopInfoEntity var6) throws SQLException;

   PopInfoEntity getPopFilesMonthInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5) throws SQLException;

   void insertPopFilesMonthInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5, @Param("pop") PopInfoEntity var6, @Param("quarter") int var7) throws SQLException;

   int updatePopFilesMonthInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5, @Param("pop") PopInfoEntity var6) throws SQLException;

   PopInfoEntity getPopFilesYearInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5) throws SQLException;

   void insertPopFilesYearInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5, @Param("pop") PopInfoEntity var6, @Param("name") String var7) throws SQLException;

   int updatePopFilesYearInfo(@Param("device_id") String var1, @Param("log_date") Timestamp var2, @Param("content_id") String var3, @Param("file_id") String var4, @Param("pop_file_type") String var5, @Param("pop") PopInfoEntity var6, @Param("name") String var7) throws SQLException;

   boolean insertPopFileErrInfo(@Param("deviceId") String var1, @Param("contentId") String var2, @Param("startDate") Timestamp var3, @Param("endDate") Timestamp var4, @Param("errType") String var5, @Param("duration") int var6, @Param("threshold") int var7);
}
