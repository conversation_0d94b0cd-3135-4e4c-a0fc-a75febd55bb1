package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.magicinfo.framework.advertisement.entity.schedule.ContentListEntity;
import java.sql.SQLException;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface ScheduleSetupDaoMapper {
   int setScheduleDeviceGroup(@Param("program_id") String var1, @Param("group_id") Integer var2) throws SQLException;

   Map getFileName(@Param("file_id") String var1) throws SQLException;

   Map getFileType(@Param("file_id") String var1) throws SQLException;

   int registerSchedule(@Param("program_id") String var1, @Param("schedule_id") String var2, @Param("result_list") ContentListEntity var3, @Param("user_id") String var4, @Param("duration") int var5, @Param("start_time") String var6, @Param("frame_index") int var7) throws SQLException;
}
