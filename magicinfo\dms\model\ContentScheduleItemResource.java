package com.samsung.magicinfo.dms.model;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;

public class ContentScheduleItemResource {
   @ApiModelProperty(
      example = "Newschedule"
   )
   String contentName = "";
   @ApiModelProperty(
      example = "1"
   )
   int channelNo = 0;
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000"
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[ContentScheduleItemResource][FrameId] Only number is available."
   )
   String frameId = "";
   @ApiModelProperty(
      example = "0"
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[ContentScheduleItemResource][FrameIndex] Only number is available."
   )
   String frameIndex;
   @ApiModelProperty(
      example = "2016-09-05"
   )
   @Pattern(
      regexp = "^[0-9]{4}\\-[0-9]{2}\\-[0-9]{2}$",
      message = "[ContentScheduleItemResource][startDate] not date format(yyyy-mm-dd)"
   )
   String startDate = "";
   @ApiModelProperty(
      example = "2016-09-05"
   )
   @Pattern(
      regexp = "^[0-9]{4}\\-[0-9]{2}\\-[0-9]{2}$",
      message = "[ContentScheduleItemResource][startDate] not date format(yyyy-mm-dd)"
   )
   String stopDate = "";
   @ApiModelProperty(
      example = "06:30:00"
   )
   @Pattern(
      regexp = "^[0-9]{4}\\:[0-9]{2}\\:[0-9]{2}$",
      message = "[ContentScheduleItemResource][startTime] not time format(hh:mm:ss)"
   )
   String startTime = "";
   @ApiModelProperty(
      example = "8999"
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[ContentScheduleItemResource][duration] Only number is available."
   )
   String duration = "";
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000"
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "[ContentScheduleItemResource][contentId] Not UUID pattern."
   )
   String contentId = "";
   @ApiModelProperty(
      example = "IMAGE"
   )
   String contentType = "";
   @ApiModelProperty(
      example = "once"
   )
   String repeatType = "";
   @ApiModelProperty(
      example = ""
   )
   String weekdays = "";
   @ApiModelProperty(
      example = ""
   )
   String monthday = "";
   @ApiModelProperty(
      example = "LFD"
   )
   @Pattern(
      regexp = "LFD|VWL|SYNC|ADV",
      message = "[ContentScheduleItemResource][isSync] Only LFD|VWL|SYNC|ADV are available."
   )
   String scheduleType = "";
   @ApiModelProperty(
      example = ""
   )
   String inputSource = "";
   @ApiModelProperty(
      example = "false"
   )
   @Pattern(
      regexp = "true|false",
      message = "[ContentScheduleItemResource][isSync]Only true, false are available."
   )
   String isSync = "";
   @ApiModelProperty(
      example = "5"
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[ContentScheduleItemResource][cifsSlideTime] Only number is available."
   )
   String cifsSlideTime = "";
   @ApiModelProperty(
      example = "single"
   )
   String playerMode = "";
   int priority = 0;
   String safetyLock = "";

   public ContentScheduleItemResource() {
      super();
   }

   public String getSafetyLock() {
      return this.safetyLock;
   }

   public void setSafetyLock(String safetyLock) {
      this.safetyLock = safetyLock;
   }

   public int getPriority() {
      return this.priority;
   }

   public void setPriority(int priority) {
      this.priority = priority;
   }

   public String getContentName() {
      return this.contentName;
   }

   public void setContentName(String contentName) {
      this.contentName = contentName;
   }

   public int getChannelNo() {
      return this.channelNo;
   }

   public void setChannelNo(int channelNo) {
      this.channelNo = channelNo;
   }

   public String getFrameId() {
      return this.frameId;
   }

   public void setFrameId(String frameId) {
      this.frameId = frameId;
   }

   public String getFrameIndex() {
      return this.frameIndex;
   }

   public void setFrameIndex(String frame_index) {
      this.frameIndex = frame_index;
   }

   public String getStartDate() {
      return this.startDate;
   }

   public void setStartDate(String startDate) {
      this.startDate = startDate;
   }

   public String getStopDate() {
      return this.stopDate;
   }

   public void setStopDate(String stopDate) {
      this.stopDate = stopDate;
   }

   public String getStartTime() {
      return this.startTime;
   }

   public void setStartTime(String startTime) {
      this.startTime = startTime;
   }

   public String getDuration() {
      return this.duration;
   }

   public void setDuration(String duration) {
      this.duration = duration;
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public String getContentType() {
      return this.contentType;
   }

   public void setContentType(String contentType) {
      this.contentType = contentType;
   }

   public String getRepeatType() {
      return this.repeatType;
   }

   public void setRepeatType(String repeatType) {
      this.repeatType = repeatType;
   }

   public String getWeekdays() {
      return this.weekdays;
   }

   public void setWeekdays(String weekdays) {
      this.weekdays = weekdays;
   }

   public String getMonthday() {
      return this.monthday;
   }

   public void setMonthday(String monthday) {
      this.monthday = monthday;
   }

   public String getScheduleType() {
      return this.scheduleType;
   }

   public void setScheduleType(String scheduleType) {
      this.scheduleType = scheduleType;
   }

   public String getInputSource() {
      return this.inputSource;
   }

   public void setInputSource(String inputSource) {
      this.inputSource = inputSource;
   }

   public String getIsSync() {
      return this.isSync;
   }

   public void setIsSync(String isSync) {
      this.isSync = isSync;
   }

   public String getCifsSlideTime() {
      return this.cifsSlideTime;
   }

   public void setCifsSlideTime(String cifsSlideTime) {
      this.cifsSlideTime = cifsSlideTime;
   }

   public String getPlayerMode() {
      return this.playerMode;
   }

   public void setPlayerMode(String playerMode) {
      this.playerMode = playerMode;
   }
}
