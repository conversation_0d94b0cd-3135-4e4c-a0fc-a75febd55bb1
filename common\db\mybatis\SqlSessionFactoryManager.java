package com.samsung.common.db.mybatis;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.exception.MyBatisConfigException;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.logging.log4j.Logger;

public class SqlSessionFactoryManager {
   private static final String MY_BATIS_CONFIG = "mybatis-config.xml";
   private static volatile SqlSessionFactoryManager dbOperationManager;
   private static final Logger logger = LoggingManagerV2.getLogger(SqlSessionFactoryManager.class);
   private SqlSessionFactory sqlSessionFactory;

   private SqlSessionFactoryManager(SqlSessionFactory sqlSessionFactory) {
      super();
      this.sqlSessionFactory = sqlSessionFactory;
   }

   public static SqlSessionFactoryManager getInstance() {
      if (dbOperationManager == null) {
         Class var0 = SqlSessionFactoryManager.class;
         synchronized(SqlSessionFactoryManager.class) {
            if (dbOperationManager == null) {
               String pathMyBatisCfg = File.separator + "MyBatis" + File.separator + "mybatis-config.xml";

               try {
                  InputStream cfgInputStream = SqlSessionFactoryManager.class.getClassLoader().getResourceAsStream(pathMyBatisCfg);
                  Throwable var3 = null;

                  try {
                     if (cfgInputStream == null) {
                        throw new MyBatisConfigException("Can't find " + SqlSessionFactoryManager.class.getResource(pathMyBatisCfg));
                     }

                     dbOperationManager = createInstance(cfgInputStream);
                  } catch (Throwable var15) {
                     var3 = var15;
                     throw var15;
                  } finally {
                     if (cfgInputStream != null) {
                        if (var3 != null) {
                           try {
                              cfgInputStream.close();
                           } catch (Throwable var14) {
                              var3.addSuppressed(var14);
                           }
                        } else {
                           cfgInputStream.close();
                        }
                     }

                  }
               } catch (MyBatisConfigException | NumberFormatException | ConfigException | IOException var17) {
                  logger.error("Failed to initialize " + SqlSessionFactoryManager.class.toString() + " instance. " + var17.getMessage());
                  throw new ExceptionInInitializerError(var17);
               }
            }
         }
      }

      return dbOperationManager;
   }

   public SqlSessionFactory getSqlSessionFactory() {
      return this.sqlSessionFactory;
   }

   private static SqlSessionFactoryManager createInstance(InputStream iBatisCfgStream) throws MyBatisConfigException, NumberFormatException, ConfigException {
      if (iBatisCfgStream == null) {
         throw new MyBatisConfigException("Can't find mybatis config file");
      } else {
         String driver = CommonConfig.get("wsrm.driver").trim();
         String url = CommonConfig.get("wsrm.url").trim();
         String username = CommonConfig.get("wsrm.username").trim();
         String password = CommonConfig.get("wsrm.password").trim();
         String removeAvandoned = CommonConfig.get("wsrm.removeAvandoned") == null ? "-" : CommonConfig.get("wsrm.removeAvandoned").trim();
         int max_active = Integer.parseInt(CommonConfig.get("wsrm.max_active"));
         int max_wait = Integer.parseInt(CommonConfig.get("wsrm.max_wait"));
         int init_size = Integer.parseInt(CommonConfig.get("wsrm.initial_size"));
         Properties props = new Properties();
         props.setProperty("driver", driver);
         props.setProperty("url", url);
         props.setProperty("username", username);
         props.setProperty("password", password);
         props.setProperty("poolMaximumActiveConnections", String.valueOf(max_active));
         props.setProperty("poolTimeToWait", String.valueOf(max_wait));
         props.setProperty("initSize", String.valueOf(init_size));
         if (Boolean.valueOf(removeAvandoned)) {
            logger.info("SqlSessionFactory - removeAvandoned option is set.");
            props.setProperty("removeAvandoned", "true");
            props.setProperty("removeAbandonedTimeout", "60");
         }

         props.setProperty("validationQuery", "SELECT NOW()");
         SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryWrapper((new SqlSessionFactoryBuilder()).build(iBatisCfgStream, props));
         return new SqlSessionFactoryManager(sqlSessionFactory);
      }
   }
}
