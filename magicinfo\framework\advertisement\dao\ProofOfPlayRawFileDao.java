package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import java.sql.SQLException;
import java.util.List;

public class ProofOfPlayRawFileDao extends SqlSessionBaseDao {
   public ProofOfPlayRawFileDao() {
      super();
   }

   private boolean isExistWeeklyReport(String weeklyCsvFileName) throws SQLException {
      return ((ProofOfPlayRawFileDaoMapper)this.getMapper()).isExistWeeklyReport(weeklyCsvFileName);
   }

   private boolean isExistMonthlyReport(String monthlyCsvFileName) throws SQLException {
      return ((ProofOfPlayRawFileDaoMapper)this.getMapper()).isExistMonthlyReport(monthlyCsvFileName);
   }

   public void addWeeklyReport(long root_group_id, int year, int month, int week_of_year, int week_of_month, boolean is_ext, boolean is_exp, boolean is_event, String weeklyCsvFileName) throws SQLException {
      if (!this.isExistWeeklyReport(weeklyCsvFileName)) {
         ((ProofOfPlayRawFileDaoMapper)this.getMapper()).addWeeklyReport(root_group_id, year, month, week_of_year, week_of_month, is_ext, is_exp, is_event, weeklyCsvFileName);
      }

   }

   public void addMonthlyReport(long root_group_id, int year, int month, int week_of_year, boolean is_ext, boolean is_exp, boolean is_event, String monthlyCsvFileName) throws SQLException {
      if (!this.isExistMonthlyReport(monthlyCsvFileName)) {
         ((ProofOfPlayRawFileDaoMapper)this.getMapper()).addMonthlyReport(root_group_id, year, month, week_of_year, is_ext, is_exp, is_event, monthlyCsvFileName);
      }

   }

   public List getDetailReportByRootGroupId(int root_group_id) throws SQLException {
      return ((ProofOfPlayRawFileDaoMapper)this.getMapper()).getDetailReportByRootGroupId(root_group_id);
   }

   public List getYearListByRootGroupId(int root_group_id) throws SQLException {
      return ((ProofOfPlayRawFileDaoMapper)this.getMapper()).getYearListByRootGroupId(root_group_id);
   }

   public List getMonthReportListByRootGroupId(String year, int root_group_id, boolean is_ext, boolean is_exp, boolean is_event) throws SQLException {
      return ((ProofOfPlayRawFileDaoMapper)this.getMapper()).getMonthReportListByRootGroupId(root_group_id, year, is_ext, is_exp, is_event);
   }

   public List getWeekReportListByRootGroupId(String year, int root_group_id, boolean is_ext, boolean is_exp, boolean is_event) throws SQLException {
      return ((ProofOfPlayRawFileDaoMapper)this.getMapper()).getWeekReportListByRootGroupId(root_group_id, year, is_ext, is_exp, is_event);
   }
}
