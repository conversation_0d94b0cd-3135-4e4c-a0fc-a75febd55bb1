package com.samsung.magicinfo.dms.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_NULL)
public class MessageScheduleResource {
   @ApiModelProperty(
      example = "32FA85B4-2389-476C-845A-0FC6F1D10D81"
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "[MessageScheduleResource][messageId] Not UUID pattern."
   )
   private String messageId;
   @ApiModelProperty(
      example = "new",
      required = true
   )
   @Size(
      max = 50,
      message = "[MessageScheduleResource][messageName] max size is 50."
   )
   private String messageName = "";
   @ApiModelProperty(
      example = "SPLAYER",
      required = true
   )
   private String deviceType = "";
   @ApiModelProperty(
      example = "2.0",
      required = true
   )
   @Pattern(
      regexp = "1.0|2.0|3.0|4.0|5.0|6.0|7.0",
      message = "[MessageScheduleResource][deviceTypeVersion] Only 1.0,2.0,3.0,4.0,5.0,6.0,7.0 are available."
   )
   private String deviceTypeVersion = "1.0";
   private String deviceGroupIds = "";
   private String deviceGroupNames = "";
   @ApiModelProperty(
      example = "1",
      required = true
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[MessageScheduleResource][messageGroupId] Only number is available."
   )
   private String messageGroupId = "1";
   @ApiModelProperty(
      example = "default",
      required = true
   )
   private String messageGroupName = "";
   @ApiModelProperty(
      example = "Y"
   )
   private String isInstant = "Y";
   @ApiModelProperty(
      example = "admin",
      required = true
   )
   @Size(
      max = 20,
      message = "[MessageScheduleResource][creatorId] max size is 20."
   )
   private String creatorId = "";
   @ApiModelProperty(
      example = "2016-01-01 00:00:00"
   )
   private String modifiedDate;
   private String messageStatus;
   private List messageList;

   public String getMessageStatus() {
      return this.messageStatus;
   }

   public void setMessageStatus(String messageStatus) {
      this.messageStatus = messageStatus;
   }

   public MessageScheduleResource() {
      super();
   }

   public String getModifiedDate() {
      return this.modifiedDate;
   }

   public void setModifiedDate(String modifiedDate) {
      this.modifiedDate = modifiedDate;
   }

   public String getDeviceGroupNames() {
      return this.deviceGroupNames;
   }

   public void setDeviceGroupNames(String deviceGroupNames) {
      this.deviceGroupNames = deviceGroupNames;
   }

   public String getMessageGroupName() {
      return this.messageGroupName;
   }

   public void setMessageGroupName(String messageGroupName) {
      this.messageGroupName = messageGroupName;
   }

   public String getCreatorId() {
      return this.creatorId;
   }

   public void setCreatorId(String creatorId) {
      this.creatorId = creatorId;
   }

   public String getMessageId() {
      return this.messageId;
   }

   public void setMessageId(String messageId) {
      this.messageId = messageId;
   }

   public String getMessageName() {
      return this.messageName;
   }

   public void setMessageName(String messageName) {
      this.messageName = messageName;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public String getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(String deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public String getDeviceGroupIds() {
      return this.deviceGroupIds;
   }

   public void setDeviceGroupIds(String deviceGroupIds) {
      this.deviceGroupIds = deviceGroupIds;
   }

   public String getMessageGroupId() {
      return this.messageGroupId;
   }

   public void setMessageGroupId(String messageGroupId) {
      this.messageGroupId = messageGroupId;
   }

   public String getIsInstant() {
      return this.isInstant;
   }

   public void setIsInstant(String isInstant) {
      this.isInstant = isInstant;
   }

   public List getMessageList() {
      return this.messageList;
   }

   public void setMessageList(List messageList) {
      this.messageList = messageList;
   }
}
