package com.samsung.common.db;

public class SearchParameter {
   public static final String WHERE = " WHERE ";
   public static final String EQUAL = "=";
   public static final String GREATER_EQUAL = ">=";
   public static final String GREATER = ">";
   public static final String LESSER = "<";
   public static final String LESSER_EQUAL = "<=";
   public static final String LIKE = " LIKE ";
   public static final String IN = " IN ";
   private String fieldName;
   private String condition;
   private String[] value;

   public SearchParameter(String fieldName, String value) {
      this(fieldName, value, "=");
   }

   public SearchParameter(String fieldName, String value, String condition) {
      this(fieldName, new String[]{value}, condition);
   }

   public SearchParameter(String fieldName, String[] value, String condition) {
      super();
      this.fieldName = null;
      this.condition = "=";
      this.value = null;
      this.fieldName = fieldName;
      this.value = value;
      this.condition = condition;
   }

   public String getSearchString() {
      if (!this.condition.trim().equalsIgnoreCase("in")) {
         return this.fieldName + this.condition + "'" + this.value[0].replaceAll("'", "''") + "'";
      } else {
         StringBuffer tmpValBuff = new StringBuffer("'" + this.value[0].replaceAll("'", "''") + "'");

         for(int i = 1; i < this.value.length; ++i) {
            tmpValBuff.append("'" + this.value[i].replaceAll("'", "''") + "'");
         }

         return this.fieldName + this.condition + "(" + tmpValBuff.toString() + ")";
      }
   }

   public String getValue() {
      return this.value[0];
   }

   public String[] getValues() {
      return this.value;
   }

   public String getFieldName() {
      return this.fieldName;
   }

   public String getCondition() {
      return this.condition;
   }

   public void setCondition(String condition) {
      this.condition = condition;
   }

   public void setFieldName(String fieldName) {
      this.fieldName = fieldName;
   }

   public void setValue(String value) {
      if (this.value == null) {
         this.value = new String[1];
         this.value[0] = value;
      } else {
         this.value[0] = value;
      }

   }

   public void setValues(String[] values) {
      this.value = values;
   }
}
