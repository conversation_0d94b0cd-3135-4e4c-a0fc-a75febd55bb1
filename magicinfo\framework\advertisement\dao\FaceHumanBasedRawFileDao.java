package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import java.sql.SQLException;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class FaceHumanBasedRawFileDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(FaceHumanBasedRawFileDao.class);

   public FaceHumanBasedRawFileDao() {
      super();
   }

   private boolean isExistWeeklyReport(String weeklyCsvFileName) throws SQLException {
      return ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).isExistWeeklyReport(weeklyCsvFileName);
   }

   private boolean isExistMonthlyReport(String monthlyCsvFileName) throws SQLException {
      return ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).isExistMonthlyReport(monthlyCsvFileName);
   }

   public void addWeeklyReport(long root_group_id, int year, int month, int week_of_year, int week_of_month, String weeklyCsvFileName) throws SQLException {
      if (!this.isExistWeeklyReport(weeklyCsvFileName)) {
         ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).addWeeklyReport(root_group_id, year, month, week_of_year, week_of_month, weeklyCsvFileName);
      }

   }

   public void addMonthlyReport(long root_group_id, int year, int month, int week_of_year, String monthlyCsvFileName) throws SQLException {
      if (!this.isExistMonthlyReport(monthlyCsvFileName)) {
         ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).addMonthlyReport(root_group_id, year, month, week_of_year, monthlyCsvFileName);
      }

   }

   public List getDetailReportByRootGroupId(int root_group_id) throws SQLException {
      return ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).getDetailReportByRootGroupId(root_group_id);
   }

   public List getYearListByRootGroupId(int root_group_id) throws SQLException {
      return ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).getYearListByRootGroupId(root_group_id);
   }

   public List getYearListByAmsRootGroupId(int root_group_id) throws SQLException {
      return ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).getYearListByAmsRootGroupId(root_group_id);
   }

   public List getMonthReportListByRootGroupId(String year, int root_group_id) throws SQLException {
      return ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).getMonthReportListByRootGroupId(root_group_id, year);
   }

   public List getMonthReportListByAmsRootGroupId(String year, int root_group_id) throws SQLException {
      return ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).getMonthReportListByAmsRootGroupId(root_group_id, year);
   }

   public List getWeekReportListByRootGroupId(String year, int root_group_id) throws SQLException {
      return ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).getWeekReportListByRootGroupId(root_group_id, year);
   }

   public List getWeekReportListByAmsRootGroupId(String year, int root_group_id) throws SQLException {
      return ((FaceHumanBasedRawFileDaoMapper)this.getMapper()).getWeekReportListByAmsRootGroupId(root_group_id, year);
   }
}
