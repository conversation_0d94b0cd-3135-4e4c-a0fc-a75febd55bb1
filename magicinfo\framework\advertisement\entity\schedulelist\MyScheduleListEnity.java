package com.samsung.magicinfo.framework.advertisement.entity.schedulelist;

import java.sql.Timestamp;

public class MyScheduleListEnity {
   private String user_id = null;
   private String schedule_id = null;
   private String schedule_name = null;
   private Timestamp requested_date = null;
   private Timestamp approved_date = null;
   private Timestamp start_date = null;
   private Timestamp end_date = null;
   private long requested_play_time = -1L;
   private long expected_play_time = -1L;
   private long net_play_time = -1L;
   private String target_area_id = null;

   public MyScheduleListEnity() {
      super();
   }

   public String getUser_id() {
      return this.user_id;
   }

   public void setUser_id(String user_id) {
      this.user_id = user_id;
   }

   public String getSchedule_id() {
      return this.schedule_id;
   }

   public void setSchedule_id(String schedule_id) {
      this.schedule_id = schedule_id;
   }

   public String getSchedule_name() {
      return this.schedule_name;
   }

   public void setSchedule_name(String schedule_name) {
      this.schedule_name = schedule_name;
   }

   public Timestamp getRequested_date() {
      return this.requested_date;
   }

   public void setRequested_date(Timestamp requested_date) {
      this.requested_date = requested_date;
   }

   public Timestamp getApproved_date() {
      return this.approved_date;
   }

   public void setApproved_date(Timestamp approved_date) {
      this.approved_date = approved_date;
   }

   public Timestamp getStart_date() {
      return this.start_date;
   }

   public void setStart_date(Timestamp start_date) {
      this.start_date = start_date;
   }

   public Timestamp getEnd_date() {
      return this.end_date;
   }

   public void setEnd_date(Timestamp end_date) {
      this.end_date = end_date;
   }

   public long getRequested_play_time() {
      return this.requested_play_time;
   }

   public void setRequested_play_time(long requested_play_time) {
      this.requested_play_time = requested_play_time;
   }

   public long getExpected_play_time() {
      return this.expected_play_time;
   }

   public void setExpected_play_time(long expected_play_time) {
      this.expected_play_time = expected_play_time;
   }

   public long getNet_play_time() {
      return this.net_play_time;
   }

   public void setNet_play_time(long net_play_time) {
      this.net_play_time = net_play_time;
   }

   public String getTarget_area_id() {
      return this.target_area_id;
   }

   public void setTarget_area_id(String target_area_id) {
      this.target_area_id = target_area_id;
   }
}
