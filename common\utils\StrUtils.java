package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.URL;
import java.net.UnknownHostException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;

public class StrUtils {
   static Logger logger = LoggingManagerV2.getLogger(StrUtils.class);

   public StrUtils() {
      super();
   }

   public static String getCommaString(String str) {
      StringBuffer sb = new StringBuffer();
      int len = str.length();
      int commaIndex = (len - 1) % 3;

      for(int i = 0; i < len; ++i) {
         char aChar = str.charAt(i);
         sb.append(aChar);
         if (i == commaIndex && i < len - 1) {
            sb.append(',');
            commaIndex += 3;
         }
      }

      return sb.toString();
   }

   public static String getCommaString(int n) {
      return getCommaString(Integer.toString(n));
   }

   public static String getLeftFilledString(String str, String fill, int len) {
      StringBuffer sb = new StringBuffer();

      for(int i = 0; i < len; ++i) {
         sb.append(fill);
      }

      sb.append(str);
      return sb.substring(sb.length() - len);
   }

   public static String getRightFilledString(String str, String fill, int len) {
      StringBuffer sb = new StringBuffer();
      sb.append(str);

      for(int i = 0; i < len; ++i) {
         sb.append(fill);
      }

      return sb.substring(0, len);
   }

   public static String getZeroString(int num, int len) {
      return getLeftFilledString(Integer.toString(num), "0", len);
   }

   public static String strZeroPlus(int val) {
      String rtn = "";
      if (val < 10) {
         rtn = "0" + Integer.toString(val);
      } else {
         rtn = Integer.toString(val);
      }

      return rtn;
   }

   public String replaceAll(String inputString, String fromString, String toString) {
      for(int index = inputString.indexOf(fromString); index > -1; index = inputString.indexOf(fromString, index + toString.length())) {
         inputString = inputString.substring(0, index) + toString + inputString.substring(index + fromString.length());
      }

      return inputString;
   }

   public String replaceFirst(String inputString, String fromString, String toString) {
      int index = inputString.indexOf(fromString);
      if (index > -1) {
         inputString = inputString.substring(0, index) + toString + inputString.substring(index + fromString.length());
      }

      return inputString;
   }

   public byte[] removeSpaces(byte[] inputBytes) {
      if (inputBytes == null) {
         return null;
      } else {
         byte[] retBytes = new byte[inputBytes.length];
         int j = 0;
         boolean bSkip = true;

         for(int i = 0; i < inputBytes.length; ++i) {
            if (inputBytes[i] == 60) {
               bSkip = false;
            }

            if (!bSkip || inputBytes[i] != 32 && inputBytes[i] != 10) {
               retBytes[j++] = inputBytes[i];
            }

            if (inputBytes[i] == 62) {
               bSkip = true;
            }
         }

         return (new String(retBytes, 0, j)).getBytes();
      }
   }

   public static String nvl(String str, String ifNull) {
      return str == null ? ifNull : str;
   }

   public static String nvl(String str) {
      return str == null ? "" : str;
   }

   public static boolean isNull(String str) {
      return str == null || str.equals("");
   }

   public static boolean isAbsoluteIP(String str) {
      Pattern p = Pattern.compile("(\\d{1,3}|[\\*])(\\.(\\d{1,3}|[\\*])){3}");
      Matcher m = p.matcher(str);
      return m.matches();
   }

   public static String swapFirstLetterCase(String str) {
      StringBuffer sbuf = new StringBuffer(str);
      sbuf.deleteCharAt(0);
      if (Character.isLowerCase(str.substring(0, 1).toCharArray()[0])) {
         sbuf.insert(0, str.substring(0, 1).toUpperCase());
      } else {
         sbuf.insert(0, str.substring(0, 1).toLowerCase());
      }

      return sbuf.toString();
   }

   public static String trim(String origString, String trimString) {
      int startPosit = origString.indexOf(trimString);
      if (startPosit != -1) {
         int endPosit = trimString.length() + startPosit;
         return origString.substring(0, startPosit) + origString.substring(endPosit);
      } else {
         return origString;
      }
   }

   public static String getLastString(String origStr, String strToken) {
      StringTokenizer str = new StringTokenizer(origStr, strToken);

      String lastStr;
      for(lastStr = ""; str.hasMoreTokens(); lastStr = str.nextToken()) {
      }

      return lastStr;
   }

   public static String[] getStringArray(String str, String strToken) {
      if (str.indexOf(strToken) == -1) {
         return new String[]{str};
      } else {
         StringTokenizer st = new StringTokenizer(str, strToken);
         String[] stringArray = new String[st.countTokens()];

         for(int i = 0; st.hasMoreTokens(); ++i) {
            stringArray[i] = st.nextToken();
         }

         return stringArray;
      }
   }

   public static boolean isNotEmpty(String str) {
      return !isEmpty(str);
   }

   public static boolean isEmpty(String str) {
      return str == null || str.length() == 0;
   }

   public static String replace(String str, String replacedStr, String replaceStr) {
      String newStr = "";
      if (str.indexOf(replacedStr) != -1) {
         String s1 = str.substring(0, str.indexOf(replacedStr));
         String s2 = str.substring(str.indexOf(replacedStr) + 1);
         newStr = s1 + replaceStr + s2;
      }

      return newStr;
   }

   public static int string2integer(String str) {
      int ret = Integer.parseInt(str.trim());
      return ret;
   }

   public static String integer2string(int integer) {
      return "" + integer;
   }

   public static boolean isPatternMatching(String str, String pattern) throws Exception {
      if (pattern.indexOf(42) >= 0) {
         pattern = pattern.replaceAll("\\*", ".*");
      }

      pattern = "^" + pattern + "$";
      return Pattern.matches(pattern, str);
   }

   public static boolean containsMaxSequence(String str, String maxSeqNumber) {
      int occurence = 1;
      int max = string2integer(maxSeqNumber);
      if (str == null) {
         return false;
      } else {
         int sz = str.length();

         for(int i = 0; i < sz - 1; ++i) {
            if (str.charAt(i) == str.charAt(i + 1)) {
               ++occurence;
               if (occurence == max) {
                  return true;
               }
            } else {
               occurence = 1;
            }
         }

         return false;
      }
   }

   public static boolean containsInvalidChars(String str, char[] invalidChars) {
      if (str != null && invalidChars != null) {
         int strSize = str.length();
         int validSize = invalidChars.length;

         for(int i = 0; i < strSize; ++i) {
            char ch = str.charAt(i);

            for(int j = 0; j < validSize; ++j) {
               if (invalidChars[j] == ch) {
                  return true;
               }
            }
         }

         return false;
      } else {
         return false;
      }
   }

   public static boolean containsInvalidChars(String str, String invalidChars) {
      return str != null && invalidChars != null ? containsInvalidChars(str, invalidChars.toCharArray()) : true;
   }

   public static boolean isAlphaNumeric(String str) {
      if (str == null) {
         return false;
      } else {
         int sz = str.length();
         if (sz == 0) {
            return false;
         } else {
            for(int i = 0; i < sz; ++i) {
               if (!Character.isLetterOrDigit(str.charAt(i))) {
                  return false;
               }
            }

            return true;
         }
      }
   }

   public static boolean isAlpha(String str) {
      if (str == null) {
         return false;
      } else {
         int sz = str.length();
         if (sz == 0) {
            return false;
         } else {
            for(int i = 0; i < sz; ++i) {
               if (!Character.isLetter(str.charAt(i))) {
                  return false;
               }
            }

            return true;
         }
      }
   }

   public static boolean isNumeric(String str) {
      if (str == null) {
         return false;
      } else {
         int sz = str.length();
         if (sz == 0) {
            return false;
         } else {
            for(int i = 0; i < sz; ++i) {
               if (!Character.isDigit(str.charAt(i))) {
                  return false;
               }
            }

            return true;
         }
      }
   }

   public static String reverse(String str) {
      return str == null ? null : (new StringBuffer(str)).reverse().toString();
   }

   public static String fillString(String originalStr, char ch, int cipers) {
      int originalStrLength = originalStr.length();
      if (cipers < originalStrLength) {
         return null;
      } else {
         int difference = cipers - originalStrLength;
         StringBuffer strBuf = new StringBuffer();

         for(int i = 0; i < difference; ++i) {
            strBuf.append(ch);
         }

         strBuf.append(originalStr);
         return strBuf.toString();
      }
   }

   public static final boolean isEmptyTrimmed(String foo) {
      return foo == null || foo.trim().length() == 0;
   }

   public static List getTokens(String lst, String separator) {
      List tokens = new ArrayList();
      if (lst != null) {
         StringTokenizer st = new StringTokenizer(lst, separator);

         while(st.hasMoreTokens()) {
            try {
               String en = st.nextToken().trim();
               tokens.add(en);
            } catch (Exception var8) {
               logger.error("", var8);
            } finally {
               ;
            }
         }
      }

      return tokens;
   }

   public static List getTokens(String lst) {
      return getTokens(lst, ",");
   }

   public static String convertToCamelCase(String targetString, char posChar) {
      StringBuffer result = new StringBuffer();
      boolean nextUpper = false;
      String allLower = targetString.toLowerCase();

      for(int i = 0; i < allLower.length(); ++i) {
         char currentChar = allLower.charAt(i);
         if (currentChar == posChar) {
            nextUpper = true;
         } else {
            if (nextUpper) {
               currentChar = Character.toUpperCase(currentChar);
               nextUpper = false;
            }

            result.append(currentChar);
         }
      }

      return result.toString();
   }

   public static String convertToCamelCase(String underScore) {
      return convertToCamelCase(underScore, '_');
   }

   public static String convertToUnderScore(String camelCase) {
      String result = "";

      for(int i = 0; i < camelCase.length(); ++i) {
         char currentChar = camelCase.charAt(i);
         if (i > 0 && Character.isUpperCase(currentChar)) {
            result = result.concat("_");
         }

         result = result.concat(Character.toString(currentChar).toLowerCase());
      }

      return result;
   }

   public static String cutCharLen(String argInput, int argLimit) {
      Object var2 = null;

      byte[] utf8;
      try {
         utf8 = argInput.getBytes("UTF-8");
      } catch (UnsupportedEncodingException var6) {
         return argInput;
      }

      if (utf8.length <= argLimit) {
         return argInput;
      } else {
         int n16 = 0;
         boolean extraLong = false;
         int i = 0;

         while(i < argLimit) {
            n16 += extraLong ? 2 : 1;
            extraLong = false;
            if ((utf8[i] & 128) == 0) {
               ++i;
            } else if ((utf8[i] & 192) == 128) {
               i += 2;
            } else if ((utf8[i] & 224) == 192) {
               i += 3;
            } else {
               i += 4;
               extraLong = true;
            }
         }

         if (n16 > argInput.length()) {
            return argInput.substring(0, argInput.length());
         } else {
            return argInput.substring(0, n16) + "...";
         }
      }
   }

   public static String makeTag(String str) {
      String rv = "";
      rv = str.replace("\r\n", "<br>");
      return rv;
   }

   public static String getLiteDeviceModelName(String deviceModelName) {
      String result = "";
      if (deviceModelName == null) {
         return result;
      } else {
         if (deviceModelName.startsWith("LITE_")) {
            result = deviceModelName;
         } else {
            result = "LITE_" + deviceModelName;
         }

         return result;
      }
   }

   public static String getOrigDeviceModelName(String deviceModelName) {
      String result = "";
      if (deviceModelName == null) {
         return result;
      } else {
         if (deviceModelName.startsWith("LITE_")) {
            result = deviceModelName.substring(5);
         } else {
            result = deviceModelName;
         }

         return result;
      }
   }

   public static boolean isSupportClientAppVer(String clientAppVer, Float baseVer) {
      boolean isSupport = true;
      int idx = clientAppVer.lastIndexOf(45);

      try {
         Float deviceVer = Float.parseFloat(clientAppVer.substring(idx + 1));
         if (baseVer <= deviceVer) {
            isSupport = true;
         } else {
            isSupport = false;
         }
      } catch (Exception var5) {
         logger.error("Error point :isSupportClientAppVer  -clientAppVer " + clientAppVer + " -baseVer: " + baseVer);
      }

      return isSupport;
   }

   public static void printCommonConfigLicenseInfo(String commentStr) {
      try {
         StringBuffer sb = new StringBuffer("\n");
         sb.append(commentStr + "\n REG_LIC_LFD\t\t: ").append(CommonConfig.get("BOOL_REG_LIC_LFD")).append("\n");
         sb.append(" REG_LIC_LITE \t\t: ").append(CommonConfig.get("BOOL_REG_LIC_LITE")).append("\n");
         sb.append(" REG_LIC_VIDEOWALL\t: ").append(CommonConfig.get("BOOL_REG_LIC_VIDEOWALL")).append("\n");
         sb.append(" REG_LIC_DATALINK\t: ").append(CommonConfig.get("BOOL_REG_LIC_DATALINK")).append("\n");
         sb.append(" REG_LIC_MOBILE\t\t: ").append(CommonConfig.get("BOOL_REG_LIC_MOBILE")).append("\n");
         sb.append(" REG_LIC_SOC \t\t: ").append(CommonConfig.get("BOOL_REG_LIC_SOC")).append("\n");
         sb.append(" REG_LIC_RMS \t\t: ").append(CommonConfig.get("BOOL_REG_LIC_RMS")).append("\n");
         logger.debug(sb.toString());
      } catch (Exception var2) {
         logger.error("Exception in" + commentStr, var2);
      }

   }

   public static String getLedTime(Timestamp sTime) {
      SimpleDateFormat sdfCurrent = new SimpleDateFormat("yyyy-MM-dd HH:mm");
      String displayTime = sdfCurrent.format(sTime);
      return displayTime;
   }

   public static String getDiffMin(Timestamp sTime) {
      SimpleDateFormat sdfCurrent = new SimpleDateFormat("yyyy-MM-dd");
      return sdfCurrent.format(sTime);
   }

   public static String getDiffDate(Timestamp sTime) {
      String dateFormat = null;
      if (SecurityUtils.getUserContainer().getUser().getDate_format() != null && !SecurityUtils.getUserContainer().getUser().getDate_format().equals("") && SecurityUtils.getUserContainer().getUser().getTime_format() != null && !SecurityUtils.getUserContainer().getUser().getTime_format().equals("")) {
         dateFormat = SecurityUtils.getUserContainer().getUser().getDate_format();
      } else {
         dateFormat = "YYYY-MM-dd";
      }

      SimpleDateFormat sdfCurrent = new SimpleDateFormat(dateFormat);
      return sdfCurrent.format(sTime).toString();
   }

   public static String getDiffMin(Timestamp sTime, boolean diffFlag) {
      String res = "";
      Long diff = (System.currentTimeMillis() - Long.valueOf(sTime.getTime())) / 60000L;
      SimpleDateFormat sdfCurrent = new SimpleDateFormat("yyyy-MM-dd HH:mm");
      new SimpleDateFormat("HH:mm:ss");
      String displayTime = sdfCurrent.format(sTime);
      if (diff >= 0L) {
         if (diff > 518400L) {
            res = displayTime;
         } else if (diff > 43200L) {
            if (diff / 43200L == 1L) {
               res = "about" + diff / 43200L + " month ago";
            } else {
               res = "about " + diff / 43200L + " months ago";
            }
         } else if (diff > 1440L) {
            if (diff / 1440L == 1L) {
               res = "about" + diff / 1440L + " day ago";
            } else {
               res = "about " + diff / 1440L + " days ago";
            }
         } else if (diff > 60L) {
            if (diff / 60L == 1L) {
               res = "about " + diff / 60L + " hour ago ";
            } else {
               res = "about " + diff / 60L + " hours ago ";
            }
         } else if (diff > 0L) {
            if (diff == 1L) {
               res = "about " + diff + " minute ago ";
            } else {
               res = "about " + diff + " minutes ago ";
            }
         } else {
            res = "a moment ago";
         }
      }

      return diffFlag ? res : "[" + displayTime + "]";
   }

   public static String getDiffMin(Timestamp sTime, boolean diffFlag, Locale locale) {
      String dateFormat = null;
      if (SecurityUtils.getUserContainer().getUser().getDate_format() != null && !SecurityUtils.getUserContainer().getUser().getDate_format().equals("") && SecurityUtils.getUserContainer().getUser().getTime_format() != null && !SecurityUtils.getUserContainer().getUser().getTime_format().equals("")) {
         String timeFormat = null;
         if (SecurityUtils.getUserContainer().getUser().getTime_format().equals("ampm")) {
            timeFormat = " hh:mm a";
         } else {
            timeFormat = " HH:mm";
         }

         dateFormat = SecurityUtils.getUserContainer().getUser().getDate_format() + timeFormat;
      } else {
         dateFormat = "YYYY-MM-dd HH:mm";
      }

      return getDiffMin(sTime, diffFlag, locale, dateFormat);
   }

   public static String getDiffMin(Timestamp sTime, boolean diffFlag, Locale locale, String dateTimeFormat) {
      String dateFormat = null;
      if (dateTimeFormat != null && !dateTimeFormat.equals("")) {
         dateFormat = dateTimeFormat;
      } else {
         dateFormat = "YYYY-MM-dd HH:mm";
      }

      String res = "";
      Long diff = (System.currentTimeMillis() - Long.valueOf(sTime.getTime())) / 60000L;
      SimpleDateFormat sdfCurrent = new SimpleDateFormat(dateFormat);
      String displayTime = sdfCurrent.format(sTime);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      if (locale == null) {
         locale = new Locale("en");
      }

      String text_about = "MIS_TEXT_ABOUT_P";
      String text_month_ago = "MIS_TEXT_MONTH_AGO_P";
      String text_months_ago = "MIS_TEXT_MONTHS_AGO_P";
      String text_day_ago = "MIS_TEXT_DAY_AGO_P";
      String text_days_ago = "MIS_TEXT_DAYS_AGO_P";
      String text_hour_ago = "MIS_TEXT_HOUR_AGO_P";
      String text_hours_ago = "MIS_TEXT_HOURS_AGO_P";
      String text_min_ago = "MIS_TEXT_MINUTE_AGO_P";
      String text_mins_ago = "MIS_TEXT_MINUTES_AGO_P";
      String text_a_moment_ago = "MIS_TEXT_A_MOMENT_AGO_P";
      String text_year_ago = "MIS_SID_1_YEAR_AGO";
      String text_years_ago = "COM_TV_SID_MIX_YEARS_AGO";
      String about = rms.getMessage(text_about, (Object[])null, locale);
      String monthAgo = rms.getMessage(text_month_ago, (Object[])null, locale);
      monthAgo = monthAgo.replace("\r\n", "");
      String monthsAgo = rms.getMessage(text_months_ago, (Object[])null, locale);
      String dayAgo = rms.getMessage(text_day_ago, (Object[])null, locale);
      String daysAgo = rms.getMessage(text_days_ago, (Object[])null, locale);
      String hourAgo = rms.getMessage(text_hour_ago, (Object[])null, locale);
      String hoursAgo = rms.getMessage(text_hours_ago, (Object[])null, locale);
      String minAgo = rms.getMessage(text_min_ago, (Object[])null, locale);
      String minsAgo = rms.getMessage(text_mins_ago, (Object[])null, locale);
      String aMomentAgo = rms.getMessage(text_a_moment_ago, (Object[])null, locale);
      String year = rms.getMessage(text_year_ago, (Object[])null, locale);
      String years = rms.getMessage(text_years_ago, (Object[])null, locale);
      if (diff >= 0L) {
         if (diff > 518400L) {
            if (diff / 518400L == 1L) {
               res = about + " " + year + "<br>(" + displayTime + ")";
            } else {
               years = years.replace("<<A>>", String.valueOf(diff / 518400L));
               res = about + " " + years + "<br>(" + displayTime + ")";
            }
         } else if (diff > 43200L) {
            if (diff / 43200L == 1L) {
               res = about + " " + diff / 43200L + " " + monthAgo + "<br>(" + displayTime + ")";
            } else {
               res = about + " " + diff / 43200L + " " + monthsAgo + "<br>(" + displayTime + ")";
            }
         } else if (diff > 1440L) {
            if (diff / 1440L == 1L) {
               res = about + " " + diff / 1440L + " " + dayAgo + "<br>(" + displayTime + ")";
            } else {
               res = about + " " + diff / 1440L + " " + daysAgo + "<br>(" + displayTime + ")";
            }
         } else if (diff > 60L) {
            if (diff / 60L == 1L) {
               res = about + " " + diff / 60L + " " + hourAgo + "<br>(" + displayTime + ")";
            } else {
               res = about + " " + diff / 60L + " " + hoursAgo + "<br>(" + displayTime + ")";
            }
         } else if (diff > 0L) {
            if (diff == 1L) {
               res = about + " " + diff + " " + minAgo + "<br>(" + displayTime + ")";
            } else {
               res = about + " " + diff + " " + minsAgo + "<br>(" + displayTime + ")";
            }
         } else {
            res = aMomentAgo + "<br>(" + displayTime + ")";
         }
      } else {
         res = "<br>(" + displayTime + ")";
      }

      return diffFlag ? res : displayTime;
   }

   public static double convertToByteDouble(String num) {
      double resultUsage = 0.0D;
      if (num == null) {
         return resultUsage;
      } else {
         String[] arrUsages = num.split(";");
         if (arrUsages.length > 0) {
            String tempString = null;
            String[] arrTempUsage = null;

            for(int j = 0; j < arrUsages.length; ++j) {
               tempString = arrUsages[j];
               arrTempUsage = tempString.split(":");

               try {
                  if (arrTempUsage.length < 2) {
                     resultUsage = Double.parseDouble(arrTempUsage[0]);
                  } else {
                     resultUsage = Double.parseDouble(arrTempUsage[1]);
                  }
               } catch (NumberFormatException var8) {
                  resultUsage = 0.0D;
               }
            }
         }

         return resultUsage;
      }
   }

   public static String convertToByte(String num) {
      if (num == null) {
         return null;
      } else if (!num.equals("") && !num.equals("-") && !num.equals("FAIL")) {
         if (num == null || !num.contains("GB") && !num.contains("MB")) {
            String tempUsage = "";
            String resultUsage = "";
            String[] arrUsages = num.split(";");
            if (arrUsages.length > 0) {
               String tempString = null;
               String[] arrTempUsage = null;

               for(int j = 0; j < arrUsages.length; ++j) {
                  tempString = arrUsages[j];
                  arrTempUsage = tempString.split(":");
                  if (arrTempUsage.length < 2) {
                     if (arrTempUsage[0].length() > 6) {
                        tempUsage = String.format("%.2f", Double.parseDouble(arrTempUsage[0]) / 1024.0D / 1024.0D) + " GB";
                     } else if (arrTempUsage[0].length() > -1 && arrTempUsage[0].length() < 7) {
                        tempUsage = String.format("%.2f", Double.parseDouble(arrTempUsage[0]) / 1024.0D) + " MB";
                     }
                  } else if (arrTempUsage[1].length() > 6) {
                     tempUsage = arrTempUsage[0] + ": " + String.format("%.2f", Double.parseDouble(arrTempUsage[1]) / 1024.0D / 1024.0D) + " GB";
                  } else if (arrTempUsage[1].length() > -1 && arrTempUsage[1].length() < 7) {
                     tempUsage = arrTempUsage[0] + ": " + String.format("%.2f", Double.parseDouble(arrTempUsage[1]) / 1024.0D) + " MB";
                  }

                  resultUsage = resultUsage + tempUsage + "  ";
               }
            }

            return resultUsage;
         } else {
            return num;
         }
      } else {
         return num;
      }
   }

   public static String replaceJsonText(String text) {
      String result = text.replace("\r\n", "<br>");
      result = result.replace("\n", "<br>");
      result = result.replace("\"", "\\\"");
      return result;
   }

   public static String getInt2Alphabet(int val) {
      String rtnStr = "";
      String[] arAlpha = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};
      if (val > 9) {
         val -= 10;
         rtnStr = arAlpha[val];
      } else {
         rtnStr = Integer.toString(val);
      }

      return rtnStr;
   }

   public static int getAlphabet2Int(String val) {
      int rtn = false;
      String[] arAlpha = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

      int rtn;
      for(int i = 0; i < arAlpha.length; ++i) {
         if (arAlpha[i].equals(val.toUpperCase())) {
            rtn = i + 10;
         }
      }

      rtn = Integer.parseInt(val);
      return rtn;
   }

   public static String getJnlpFile(HttpServletRequest request, String browserType, String jnlpFileName) {
      try {
         StringBuilder url = new StringBuilder("");
         url.append(RequestUtils.getWebUrl(request)).append("/servlet/");
         if (browserType.equalsIgnoreCase("IE")) {
            url.append("FileLoader?paramPathConfName=JNLP_HOME&download=B&filepath=");
         } else if (browserType.equalsIgnoreCase("ETC")) {
            url.append("FileLoader?paramPathConfName=JNLP_HOME&download=D&filepath=");
         }

         url.append(jnlpFileName);
         return url.toString();
      } catch (ConfigException var4) {
         logger.error("Cannot read 'web_url' property", var4);
         return "Missing 'web_url'";
      }
   }

   public static String cutOffUTF8String(String str, int maxByteSize, String trail) {
      try {
         if (str == null) {
            return null;
         } else if (str.length() == 0) {
            return str;
         } else {
            byte[] strByte = str.getBytes("UTF-8");
            if (strByte.length <= maxByteSize) {
               return str;
            } else {
               int trailByteSize = 0;
               if (trail != null) {
                  trailByteSize = trail.getBytes("UTF-8").length;
               }

               maxByteSize -= trailByteSize;
               int endPos = 0;
               int currByte = 0;

               for(int i = 0; i < str.length(); ++i) {
                  char ch = str.charAt(i);
                  currByte += availibleByteNum(ch);
                  if (currByte > maxByteSize) {
                     endPos = currByte - availibleByteNum(ch);
                     break;
                  }
               }

               byte[] newStrByte = new byte[endPos];
               System.arraycopy(strByte, 0, newStrByte, 0, endPos);
               String newStr = new String(newStrByte, "UTF-8");
               newStr = newStr + trail;
               return newStr;
            }
         }
      } catch (Exception var9) {
         return "";
      }
   }

   public static int availibleByteNum(char c) {
      int ONE_BYTE_MIN = 0;
      int ONE_BYTE_MAX = 127;
      int TWO_BYTE_MIN = 2048;
      int TWO_BYTE_MAX = 2047;
      int THREE_BYTE_MIN = 2048;
      int THREE_BYTE_MAX = '\uffff';
      int SURROGATE_MIN = 65536;
      int SURROGATE_MAX = 1114111;
      if (ONE_BYTE_MIN <= c && c <= ONE_BYTE_MAX) {
         return 1;
      } else if (TWO_BYTE_MIN <= c && c <= TWO_BYTE_MAX) {
         return 2;
      } else if (THREE_BYTE_MIN <= c && c <= THREE_BYTE_MAX) {
         return 3;
      } else {
         return SURROGATE_MIN <= c && c <= SURROGATE_MAX ? 4 : -1;
      }
   }

   public static String getNoData() {
      int cnt = 0;
      int startIndex = 1;
      StringBuffer buffer = new StringBuffer("");
      buffer.append("{\"recordsReturned\":" + cnt + ",");
      buffer.append("\"totalRecords\":" + cnt + ",");
      buffer.append("\"startIndex\":" + startIndex + ",");
      buffer.append("\"recCntOfThisPage\":" + cnt + ",");
      buffer.append("\"records\":[");
      buffer.append("]}");
      return buffer.toString();
   }

   public static String convertDateFormat(String date, String oldFormat, String newFormat) {
      if (date != null && oldFormat != null && newFormat != null) {
         SimpleDateFormat old_sdf = new SimpleDateFormat(oldFormat);
         SimpleDateFormat new_sdf = new SimpleDateFormat(newFormat);

         Date d;
         try {
            d = old_sdf.parse(date);
         } catch (ParseException var7) {
            d = null;
         }

         return new_sdf.format(d);
      } else {
         return null;
      }
   }

   public static boolean containsSpecialCharacter(String s) {
      Pattern pattern = Pattern.compile("[\\\\/:*?\"<>|&~!@#$%^'`;{}=\\[\\]]");
      return pattern.matcher(s).find();
   }

   public static boolean specialCharacterCheckForServerName(String s) {
      Pattern pattern = Pattern.compile("[\\\\/:*?\"<>|&~!@#$%^'`;+{}=\\[\\]]");
      return pattern.matcher(s).find();
   }

   public static String arrayToString(List list, String delimiter) {
      if (list == null) {
         return "";
      } else {
         StringBuilder sb = new StringBuilder();
         Iterator var3 = list.iterator();

         while(var3.hasNext()) {
            Object obj = var3.next();
            if (obj != null && !"".equals(obj)) {
               if (sb.length() > 0) {
                  sb.append(delimiter);
               }

               sb.append(obj);
            }
         }

         return sb.toString();
      }
   }

   public static boolean checkBooleanValue(String str) {
      try {
         if (isEmpty(str)) {
            return false;
         } else {
            return "TRUE".equalsIgnoreCase(str);
         }
      } catch (Exception var2) {
         return false;
      }
   }

   public static String checkSearchText(String searchText) {
      searchText = nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      return searchText;
   }

   public static String sanitizeInput(String str) {
      return str.replaceAll("<", "&lt;").replaceAll(">", "&gt;").replaceAll("&", "&amp;").replaceAll("\"", "&quot;").replaceAll("'", "&#39;").replaceAll("'", "&apos;");
   }

   public static boolean isValidUrl(String url) {
      try {
         (new URL(url)).toURI();
         return true;
      } catch (Exception var2) {
         return false;
      }
   }

   public static Boolean isValidIpAddress(String ip) {
      try {
         InetAddress ipAddress = InetAddress.getByName(ip);
         return true;
      } catch (UnknownHostException var2) {
         logger.error("Invalid Ipaddress");
         return false;
      }
   }
}
