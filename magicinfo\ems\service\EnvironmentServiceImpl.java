package com.samsung.magicinfo.ems.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.ems.model.CategoryResource;
import com.samsung.magicinfo.ems.model.DashboardEnvironmentResource;
import com.samsung.magicinfo.ems.model.DashboardEnvironmentStorageResource;
import com.samsung.magicinfo.ems.model.DashboardLicenceResource;
import com.samsung.magicinfo.ems.model.DashboardLoginResource;
import com.samsung.magicinfo.ems.model.DashboardNoticeResource;
import com.samsung.magicinfo.ems.model.DashboardTagResource;
import com.samsung.magicinfo.ems.model.EditTagInfoFilter;
import com.samsung.magicinfo.ems.model.EnvironmentNoticeFilter;
import com.samsung.magicinfo.ems.model.EnvironmentPriorityFilter;
import com.samsung.magicinfo.ems.model.LicenceCheckResource;
import com.samsung.magicinfo.ems.model.LicenseDetailResource;
import com.samsung.magicinfo.ems.model.MenuCountResource;
import com.samsung.magicinfo.ems.model.ServerSetupResource;
import com.samsung.magicinfo.ems.model.Tag;
import com.samsung.magicinfo.ems.model.TagResource;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.dashboard.entity.UserDashboardEntity;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManager;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.notice.dao.NoticeDao;
import com.samsung.magicinfo.framework.notice.entity.NoticeEntity;
import com.samsung.magicinfo.framework.scheduler.dao.MessageGroupDao;
import com.samsung.magicinfo.framework.scheduler.entity.MessageGroup;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.dao.SlmLicenseDao;
import com.samsung.magicinfo.framework.setup.entity.CategoryEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseEntity;
import com.samsung.magicinfo.framework.setup.entity.SlmLicenseStatusEntity;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.custom.domain.notice.NoticeIF;
import com.samsung.magicinfo.openapi.custom.domain.notice.NoticeImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.constants.CommonConstants;
import com.samsung.magicinfo.restapi.user.dkms.PIIDataManager;
import java.io.File;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.ftpserver.db.DownloadInfo;
import org.apache.ftpserver.db.DownloadInfoImpl;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("EnvironmentService")
@Transactional
public class EnvironmentServiceImpl implements EnvironmentService {
   protected Logger logger = LoggingManagerV2.getLogger(EnvironmentServiceImpl.class);
   @Autowired
   private PIIDataManager piiDataManager;
   public static final String DASHBOARD_NAME = "dashboardName";
   public static final String PRIORITY = "priority";
   public static final String SIGN_DASHBOARD = "signInfoDashboard";
   public static final String NOTICE_DASHBOARD = "noticeInfoDashboard";
   public static final String STORAGE_DASHBOARD = "storageInfoDashboard";
   public static final String CONTENT_DASHBOARD = "contentInfoDashboard";
   public static final String PLAYLIST_DASHBOARD = "playlistInfoDashboard";
   public static final String SCHEDULE_DASHBOARD = "scheduleInfoDashboard";
   public static final String DEVICE_DASHBOARD = "deviceInfoDashboard";
   public static final String USER_DASHBOARD = "userInfoDashboard";
   public static final String DN_SERVER_DASHBOARD = "downloadServerDashBoard";
   public static final String RM_SERVER_DASHBOARD = "rmServerDashBoard";
   public static final String DL_SERVER_DASHBOARD = "datalinkServerDashBoard";
   public static final String ROOT_PATH = "rootPath";
   public static final String FREE_SIZE = "freeSize";
   public static final String USED_SIZE = "usedSize";
   public static final String TOTAL_SIZE = "totalSize";
   public static final String PERCENT = "percent";
   public static final String STORAGE_LIST = "storageList";
   public static final String GIGA_BYTE = "GB";
   public static final String NOTICE_START_ID = "1";
   public static final String TOTAL_RECORD = "totalRecord";
   public static final String NOTICE_TITLE = "noticeTitle";
   public static final String NOTICE_ID = "noticeId";
   public static final String NOTICE_USER_ID = "noticeUserId";
   public static final String NOTICE_WRITE_DATE = "noticeWriteDate";
   public static final String NOTICE_IMPORTANT = "noticeImportant";
   public static final String NOTICE_LIST = "noticeList";
   public static final String NOTICE = "notice";
   public static final String START_DATE = "startDate";
   public static final String END_DATE = "endDate";
   public static final String DATE_FORMAT_YY_MM_DD = "yyyy-MM-dd";
   public static final String USER_ID = "userId";
   public static final String USER_NAME = "userName";
   public static final String ROLE = "role";
   public static final String LAST_SIGN_IN = "lastSignIn";
   public static final String DESC = "desc";
   public static final String REG_DATE = "reg_date";
   public static final String SORT = "sort";
   public static final String ORDER = "order";
   public static final String START_INDEX = "startIndex";
   public static final String RESULTS = "results";
   public static final String LICENSE_KEY = "licenseKey";
   public static final String PRODUCT_CODE = "productCode";
   public static final String LICENSE_TYPE = "licenseType";
   public static final String CHARGED = "charged";
   public static final String FREE_OF_CHARGED = "freeOfCharged";
   public static final String FREE = "free";
   public static final String MAX_CLIENTS = "maxClients";
   public static final String LICENSE_LIST = "licenseList";
   public static final String DATE_FORMAT_HH_MM_SS = "HH-mm-ss";
   public static final String MODE = "mode";
   public static final String WRITE = "write";
   public static final String VIEW = "view";

   public EnvironmentServiceImpl() {
      super();
   }

   public ResponseBody listDashboardInfo() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      HashMap data = new HashMap();

      try {
         List list = dashboardInfo.getDashboard(userId);
         Iterator var7 = list.iterator();

         while(var7.hasNext()) {
            UserDashboardEntity entity = (UserDashboardEntity)var7.next();
            data.put(entity.getDashboard_name(), true);
         }

         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var9) {
         this.logger.error("", var9);
         responseBody.setErrorMessage(var9.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody createDashboard(String dashboardName) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      DashboardEnvironmentResource data = new DashboardEnvironmentResource();
      DashboardManagerImpl dashboardInfo = DashboardManagerImpl.getInstance();

      try {
         int id = dashboardInfo.getDashboardWidgetId(dashboardName);
         int priority = dashboardInfo.addDashboardWidget(userId, id);
         if (priority > 0) {
            data.setDashboardName(dashboardName);
            data.setPriority(priority);
         }

         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var9) {
         this.logger.error("", var9);
         responseBody.setErrorMessage(var9.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody deleteDashboard(String dashboardName) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      DashboardManagerImpl dashboardInfo = DashboardManagerImpl.getInstance();

      try {
         dashboardInfo.removeDashboardWidget(userId, dashboardName);
         responseBody.setItems(dashboardName);
         responseBody.setStatus("Success");
      } catch (Exception var7) {
         this.logger.error("", var7);
         responseBody.setErrorMessage(var7.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody updateDashboardPriority(EnvironmentPriorityFilter filter) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();

      try {
         List dashboardLists = new ArrayList();
         String combineData = "";
         if (filter.getSignInfoDashboard() > 0) {
            combineData = combineData + "signInfoDashboard:" + filter.getSignInfoDashboard() + ",";
         }

         if (filter.getNoticeInfoDashboard() > 0) {
            combineData = combineData + "noticeInfoDashboard:" + filter.getNoticeInfoDashboard() + ",";
         }

         if (filter.getStorageInfoDashboard() > 0) {
            combineData = combineData + "storageInfoDashboard:" + filter.getStorageInfoDashboard() + ",";
         }

         if (filter.getContentInfoDashboard() > 0) {
            combineData = combineData + "contentInfoDashboard:" + filter.getContentInfoDashboard() + ",";
         }

         if (filter.getPlaylistInfoDashboard() > 0) {
            combineData = combineData + "playlistInfoDashboard:" + filter.getPlaylistInfoDashboard() + ",";
         }

         if (filter.getScheduleInfoDashboard() > 0) {
            combineData = combineData + "scheduleInfoDashboard:" + filter.getScheduleInfoDashboard() + ",";
         }

         if (filter.getDeviceInfoDashboard() > 0) {
            combineData = combineData + "deviceInfoDashboard:" + filter.getDeviceInfoDashboard() + ",";
         }

         if (filter.getUserInfoDashboard() > 0) {
            combineData = combineData + "userInfoDashboard:" + filter.getUserInfoDashboard() + ",";
         }

         if (filter.getDownloadServerDashBoard() > 0) {
            combineData = combineData + "downloadServerDashBoard:" + filter.getDownloadServerDashBoard() + ",";
         }

         if (filter.getRmServerDashBoard() > 0) {
            combineData = combineData + "rmServerDashBoard:" + filter.getRmServerDashBoard() + ",";
         }

         if (filter.getDatalinkServerDashBoard() > 0) {
            combineData = combineData + "datalinkServerDashBoard:" + filter.getDatalinkServerDashBoard() + ",";
         }

         String[] dashboardList = combineData.split(",");
         String[] var8 = dashboardList;
         int var9 = dashboardList.length;

         for(int var10 = 0; var10 < var9; ++var10) {
            String dashboardTemp = var8[var10];
            String[] dashboard = dashboardTemp.split(":", 2);
            if (dashboard.length == 2) {
               UserDashboardEntity entity = new UserDashboardEntity();
               entity.setDashboard_name(dashboard[0]);
               entity.setPriority(Integer.valueOf(dashboard[1]));
               entity.setUser_id(userId);
               dashboardLists.add(entity);
            }
         }

         DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
         dashboardInfo.updateDashboardPriority(dashboardLists);
         responseBody.setStatus("Success");
      } catch (Exception var14) {
         this.logger.error("", var14);
         responseBody.setErrorMessage(var14.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody listDashboardStorageInfo() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      DashboardEnvironmentStorageResource data = new DashboardEnvironmentStorageResource();
      ArrayList storageList = new ArrayList();

      try {
         File[] roots = File.listRoots();

         for(int i = 0; roots != null && i < roots.length; ++i) {
            if (roots[i].getTotalSpace() > 0L) {
               LinkedHashMap storage = new LinkedHashMap();
               long totalSizeLong = roots[i].getTotalSpace();
               long freeSizeLong = roots[i].getFreeSpace();
               long usedSizeLong = totalSizeLong - freeSizeLong;
               String freeSize = formatSizeGB(freeSizeLong);
               String usedSize = formatSizeGB(usedSizeLong);
               String totalSize = formatSizeGB(totalSizeLong);
               String percent = String.valueOf((int)((double)usedSizeLong / (double)totalSizeLong * 100.0D));
               storage.put("rootPath", roots[i].getPath());
               storage.put("freeSize", freeSize);
               storage.put("usedSize", usedSize);
               storage.put("totalSize", totalSize);
               storage.put("percent", percent);
               storageList.add(storage);
            }
         }

         data.setStorageList(storageList);
         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var17) {
         this.logger.error("", var17);
         responseBody.setErrorMessage(var17.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public static String formatSizeGB(Object obj) {
      long bytes = -1L;
      if (obj instanceof Long) {
         bytes = (Long)obj;
      } else if (obj instanceof Integer) {
         bytes = (long)(Integer)obj;
      }

      long gbytes = bytes / 1073741824L;
      return gbytes + "GB";
   }

   public ResponseBody listDashboardNoticeInfo() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      DashboardNoticeResource data = new DashboardNoticeResource();
      List dataList = new ArrayList();
      NoticeIF noticeImp = NoticeImpl.getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      String start = "1";
      NoticeDao noticeDao = new NoticeDao();
      List noticeResult = null;

      try {
         noticeResult = noticeImp.getPagedNoticeList(userId, Integer.valueOf(start), 10);
      } catch (NumberFormatException var16) {
         this.logger.error("", var16);
      } catch (Exception var17) {
         this.logger.error("", var17);
      }

      try {
         Long rootGroupId = userInfo.getRootGroupIdByUserId(userId);
         int total_record;
         if (rootGroupId.intValue() == 0) {
            total_record = noticeDao.getNoticeListTotalRecordScopeAll(userId);
         } else {
            total_record = noticeDao.getNoticeListTotalRecordScopeGroup(userId);
         }

         data.setTotalRecord(total_record);
         Iterator var13 = noticeResult.iterator();

         while(var13.hasNext()) {
            NoticeEntity notice = (NoticeEntity)var13.next();
            LinkedHashMap noticeMap = new LinkedHashMap();
            noticeMap.put("noticeTitle", StrUtils.cutOffUTF8String(notice.getNotice_title(), 35, "..."));
            noticeMap.put("noticeId", notice.getNotice_id());
            noticeMap.put("noticeUserId", StrUtils.cutOffUTF8String(notice.getUser_id(), 10, "..."));
            noticeMap.put("noticeWriteDate", StrUtils.getDiffMin(notice.getWrite_date()));
            noticeMap.put("noticeImportant", notice.getPriority());
            dataList.add(noticeMap);
         }

         data.setNoticeList(dataList);
         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var18) {
         this.logger.error("", var18);
         responseBody.setErrorMessage(var18.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public ResponseBody listEditNoticeInfo(String noticeId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      LinkedHashMap data = new LinkedHashMap();

      try {
         NoticeDao noticeDao = new NoticeDao();
         NoticeEntity notice = noticeDao.getNoticeData(noticeId);
         if (notice.getUser_id().equals(userId)) {
            data.put("mode", "write");
         } else {
            data.put("mode", "view");
         }

         data.put("noticeId", notice.getNotice_id());
         data.put("noticeTitle", notice.getNotice_title());
         data.put("notice", notice.getNotice_subject());
         data.put("noticeImportant", notice.getPriority());
         data.put("startDate", StrUtils.getDiffMin(notice.getStart_date()));
         data.put("endDate", StrUtils.getDiffMin(notice.getEnd_date()));
         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var8) {
         this.logger.error("", var8);
         responseBody.setErrorMessage(var8.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public ResponseBody createNoticeInfo(EnvironmentNoticeFilter filter, boolean create) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();

      try {
         NoticeDao noticeDao = new NoticeDao();
         NoticeEntity notice = new NoticeEntity();
         if (!create) {
            notice.setNotice_id(Integer.valueOf(filter.getNoticeId()));
         }

         DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
         Date enddate = dateFormat.parse(filter.getNoticeEndDate());
         Date startdate = dateFormat.parse(filter.getNoticeStartDate());
         long endTime = enddate.getTime();
         long startTime = startdate.getTime();
         notice.setNotice_title(filter.getNoticeTitle());
         notice.setNotice_subject(filter.getNoticeSubject());
         notice.setStart_date(new Timestamp(startTime));
         notice.setEnd_date(new Timestamp(endTime));
         if (filter.isImportant()) {
            notice.setPriority(0);
         } else {
            notice.setPriority(1);
         }

         notice.setShow_range(0);
         notice.setHas_limit(false);
         notice.setUser_id(userId);
         if (create) {
            noticeDao.insertNotice(notice);
         } else {
            noticeDao.updateNotice(notice);
         }

         responseBody.setStatus("Success");
      } catch (Exception var15) {
         this.logger.error("", var15);
         responseBody.setErrorMessage(var15.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public ResponseBody deleteNoticeInfo(String noticeId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();

      try {
         NoticeDao noticeDao = new NoticeDao();
         if (noticeDao.deleteNotice(userId, noticeId)) {
            responseBody.setStatus("Success");
         } else {
            responseBody.setStatus("Fail");
         }
      } catch (Exception var6) {
         this.logger.error("", var6);
         responseBody.setErrorMessage(var6.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody listDashboardLoginInfo() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      DashboardLoginResource data = new DashboardLoginResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      Object locales = null;

      try {
         User user = userContainer.getUser();
         data.setUserId(user.getUser_id());
         data.setUserName(this.piiDataManager.decryptData(user.getUser_name()));
         data.setRole(user.getRole_name());
         data.setLastSignIn(StrUtils.getDiffMin(user.getLast_login_date(), false, (Locale)locales));
         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var6) {
         this.logger.error("", var6);
         responseBody.setErrorMessage(var6.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public ResponseBody listLicense() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      String order_dir = "desc";
      String sort_name = "reg_date";
      String createTime = "";
      int startIndex = 1;
      int results = 20;
      new ArrayList();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
      SlmLicenseDao licenseDao = new SlmLicenseDao();
      SlmLicenseManager LicenseMag = SlmLicenseManagerImpl.getInstance();
      DashboardLicenceResource data = new DashboardLicenceResource();
      List dataList = new ArrayList();
      PagedListInfo listInfo = null;
      Map condition = new HashMap();
      condition.put("sort", sort_name);
      condition.put("order", order_dir);
      List integrateList = new ArrayList();
      List premiumIList = new ArrayList();
      List liteList = new ArrayList();
      List mobileList = new ArrayList();
      List premiumSList = new ArrayList();
      List tizenList = new ArrayList();
      List signageList = new ArrayList();
      List androidList = new ArrayList();
      List rmsList = new ArrayList();
      int integrateCnt = 0;
      int premiumICnt = 0;
      int liteCnt = 0;
      int mobileCnt = 0;
      int premiumSCnt = 0;
      int tizenCnt = 0;
      int signageCnt = 0;
      int androidCnt = 0;
      int rmsCnt = 0;

      try {
         listInfo = licenseDao.getLicenseList(startIndex, results, condition);
         List list = listInfo.getPagedResultList();
         data.setTotalRecord(listInfo.getTotalRowCount());
         data.setStartIndex(startIndex);
         data.setSort(sort_name);
         data.setOrder(order_dir);
         data.setResults(listInfo.getTotalRowCount());

         for(int i = 0; i < list.size(); ++i) {
            LinkedHashMap licenseDetailInfo = new LinkedHashMap();
            SlmLicenseEntity info = (SlmLicenseEntity)list.get(i);
            licenseDetailInfo.put("licenseKey", info.getLicense_key());
            licenseDetailInfo.put("productCode", LicenseMag.getProductCode(info.getProduct_code()));
            if (info.getLicense_type().equals("11")) {
               licenseDetailInfo.put("licenseType", "charged");
            } else if (info.getLicense_type().equals("12")) {
               licenseDetailInfo.put("licenseType", "freeOfCharged");
            } else if (info.getLicense_type().equals("13")) {
               licenseDetailInfo.put("licenseType", "free");
            }

            licenseDetailInfo.put("maxClients", info.getMax_clients());
            if (info.getStart_date() != null) {
               createTime = this.cutDate(sdf.format(info.getStart_date()));
            }

            licenseDetailInfo.put("startDate", createTime);
            if (info.getEnd_date() != null) {
               if (Math.abs(info.getStart_date().getYear() - info.getEnd_date().getYear()) < 100) {
                  createTime = this.cutDate(sdf.format(info.getEnd_date()));
               } else {
                  createTime = "-";
               }
            }

            licenseDetailInfo.put("endDate", createTime);
            if (info.getReg_date() != null) {
               createTime = this.cutDate(sdf.format(info.getReg_date()));
            }

            licenseDetailInfo.put("reg_date", createTime);
            String tempProductCode = info.getProduct_code();
            Long tempCnt = info.getMax_clients();
            if (tempProductCode.equals("01014A") || tempProductCode.equals("01015A")) {
               integrateList.add(licenseDetailInfo);
               integrateCnt = (int)((long)integrateCnt + tempCnt);
            }

            if (tempProductCode.equals("010120")) {
               premiumIList.add(licenseDetailInfo);
               premiumICnt = (int)((long)premiumICnt + tempCnt);
            } else if (tempProductCode.equals("010311")) {
               liteList.add(licenseDetailInfo);
               liteCnt = (int)((long)liteCnt + tempCnt);
            } else if (tempProductCode.equals("010121")) {
               premiumSList.add(licenseDetailInfo);
               premiumSCnt = (int)((long)premiumSCnt + tempCnt);
            } else if (tempProductCode.equals("010121")) {
               tizenList.add(licenseDetailInfo);
               tizenCnt = (int)((long)tizenCnt + tempCnt);
            } else if (tempProductCode.equals("010V31")) {
               signageList.add(licenseDetailInfo);
               signageCnt = (int)((long)signageCnt + tempCnt);
            } else if (tempProductCode.equals("01011N")) {
               androidList.add(licenseDetailInfo);
               androidCnt = (int)((long)androidCnt + tempCnt);
            } else if (tempProductCode.equals("01064A")) {
               rmsList.add(licenseDetailInfo);
               rmsCnt = (int)((long)rmsCnt + tempCnt);
            }
         }

         LinkedHashMap tempData;
         if (integrateCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "I, S, Signage, LED Player");
            tempData.put("maxClients", integrateCnt);
            tempData.put("licenseList", integrateList);
            dataList.add(dataList.size(), tempData);
         }

         if (premiumICnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "I Player");
            tempData.put("maxClients", premiumICnt);
            tempData.put("licenseList", premiumIList);
            dataList.add(dataList.size(), tempData);
         }

         if (liteCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Lite Player");
            tempData.put("maxClients", liteCnt);
            tempData.put("licenseList", liteList);
            dataList.add(dataList.size(), tempData);
         }

         if (mobileCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Mobile Player");
            tempData.put("maxClients", Integer.valueOf(mobileCnt));
            tempData.put("licenseList", mobileList);
            dataList.add(dataList.size(), tempData);
         }

         if (premiumSCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "S Player");
            tempData.put("maxClients", premiumSCnt);
            tempData.put("licenseList", premiumSList);
            dataList.add(dataList.size(), tempData);
         }

         if (tizenCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Tizen");
            tempData.put("maxClients", tizenCnt);
            tempData.put("licenseList", tizenList);
            dataList.add(dataList.size(), tempData);
         }

         if (signageCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Signage Player");
            tempData.put("maxClients", signageCnt);
            tempData.put("licenseList", signageList);
            dataList.add(dataList.size(), tempData);
         }

         if (androidCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "Android Player");
            tempData.put("maxClients", androidCnt);
            tempData.put("licenseList", androidList);
            dataList.add(dataList.size(), tempData);
         }

         if (rmsCnt > 0) {
            tempData = new LinkedHashMap();
            tempData.put("productCode", "RM Player");
            tempData.put("maxClients", rmsCnt);
            tempData.put("licenseList", rmsList);
            dataList.add(dataList.size(), tempData);
         }

         data.setLicenseList(dataList);
         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var38) {
         this.logger.error("", var38);
         responseBody.setErrorMessage(var38.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody listMenu() throws Exception {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      ArrayList menuList = new ArrayList();

      try {
         if (CommonUtils.checkAvailable("content")) {
            menuList.add("content");
         }

         if (CommonUtils.checkAvailable("playlist")) {
            menuList.add("playlist");
         }

         if (CommonUtils.checkAvailable("schedule")) {
            menuList.add("schedule");
         }

         if (CommonUtils.checkAvailable("device")) {
            menuList.add("device");
         }

         responseBody.setItems(menuList);
         responseBody.setStatus("Success");
      } catch (Exception var4) {
         responseBody.setErrorMessage(var4.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public String cutDate(String date) {
      String[] rtn = null;
      rtn = date.split(" ");
      return rtn[0];
   }

   public ResponseBody createTag(List params) throws SQLException {
      ResponseBody responsebody = new ResponseBody();
      TagInfoImpl tagInfo = TagInfoImpl.getInstance();

      try {
         DashboardTagResource result = new DashboardTagResource();
         UserContainer userContainer = SecurityUtils.getUserContainer();
         String organization = userContainer.getUser().getOrganization();
         Long organ = CommonConstants.COMMON_ORGAN_ID;
         if (organization != null) {
            UserGroupInfo userGroupDao = UserGroupInfoImpl.getInstance();
            organ = userGroupDao.getOrgGroupIdByName(organization);
            if (organ == 0L) {
               organ = CommonConstants.COMMON_ORGAN_ID;
            }
         }

         for(int i = 0; i < params.size(); ++i) {
            Map data = new HashMap();
            Tag tagEntity = (Tag)params.get(i);
            String status = "";
            String tagName = tagEntity.getTagName().trim();
            Integer tagType = tagEntity.getTagType();
            String tagValue = tagEntity.getTagValue().trim();
            String description = tagEntity.getTagDescription().trim();
            data.put("tagName", tagName);
            if (tagInfo.chkTagName(tagName) > 0) {
               status = "fail";
               data.put("errorMessage", "already exists.");
            } else {
               tagInfo.addTagInfo(tagName, organ, description, (long)tagType);
               TagEntity tag = tagInfo.getTagByName(tagName);
               data.put("tagId", tag.getTag_id());
               if (tagEntity.getTagType() == 0 && tagValue != null) {
                  String[] temp = tagValue.split(",");
                  if (temp != null && temp.length > 0) {
                     for(int t = 0; t < temp.length; ++t) {
                        if (!"".equals(temp[t]) && temp[t] != null) {
                           tagInfo.addCondition((long)tag.getTag_id(), temp[t]);
                        }
                     }
                  }
               }

               status = "success";
            }

            data.put("status", status);
            result.setData(data);
         }

         responsebody.setItems(result);
         responsebody.setStatus("Success");
      } catch (Exception var19) {
         this.logger.error("", var19);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var19.getMessage());
      }

      return responsebody;
   }

   public ResponseBody getTagListInfo(TagResource tagResource) throws SQLException {
      ResponseBody responsebody = new ResponseBody();
      TagInfo tagInfo = TagInfoImpl.getInstance();
      Map condition = new HashMap();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      if (!RoleUtils.isServerAdminRole(userContainer.getUser())) {
         UserGroupInfo userGroupDao = UserGroupInfoImpl.getInstance();
         long id = 0L;
         String organization = userContainer.getUser().getOrganization();
         if (organization != null) {
            id = userGroupDao.getOrgGroupIdByName(organization);
            String organId = "-2," + id;
            condition.put("organId", organId);
         }
      } else {
         condition.put("organId", "all");
      }

      condition.put("sortOrder", "desc");
      condition.put("searchText", "");
      condition.put("sortColumn", "create_date");
      String section = "getTagList";

      try {
         PagedListInfo info = tagInfo.getPagedList(tagResource.getStartIndex(), tagResource.getPageSize(), condition, section);
         List tagList = info.getPagedResultList();
         ArrayList tagEntityList = new ArrayList();

         for(int i = 0; i < tagList.size(); ++i) {
            Map data = new HashMap();
            TagEntity tagEntity = (TagEntity)tagList.get(i);
            data.put("tagId", tagEntity.getTag_id());
            data.put("tagValue", tagEntity.getTag_value());
            data.put("tagDescription", tagEntity.getTag_desc());
            data.put("tagOrgan", tagEntity.getTag_organ());
            data.put("tagType", tagEntity.getTag_type());
            tagEntityList.add(data);
         }

         tagResource.setTagList(tagEntityList);
         responsebody.setItems(tagResource);
      } catch (Exception var13) {
         this.logger.error("", var13);
      }

      responsebody.setStatus("Success");
      return responsebody;
   }

   public ResponseBody getTagInfo(String tagId) throws SQLException {
      ResponseBody responsebody = new ResponseBody();
      TagInfoImpl tagDao = TagInfoImpl.getInstance();

      try {
         UserContainer userContainer = SecurityUtils.getUserContainer();
         Map data = new HashMap();
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         TagEntity tag = tagDao.getTag(Integer.valueOf(tagId));
         Long tagOrg = tag.getTag_organ();
         String tagOrgName = "";
         if (tagOrg == -2L) {
            tagOrgName = "Common";
         } else {
            tagOrgName = userGroupInfo.getGroupById(tag.getTag_organ()).getGroup_name();
         }

         List tagConditionList = new ArrayList();
         List tagMap = tagDao.getTagConditionFromTagId((long)tag.getTag_id());
         if (tagMap != null) {
            Iterator var12 = tagMap.iterator();

            while(var12.hasNext()) {
               Map tagStr = (Map)var12.next();
               LinkedHashMap tagCondition = new LinkedHashMap();
               tagCondition.put("tag_condition", tagStr.get("tag_condition"));
               tagCondition.put("tag_condition_id", tagStr.get("tag_condition_id"));
               tagConditionList.add(tagCondition);
            }

            data.put("tagConditionList", tagConditionList);
         }

         data.put("tagType", tag.getTag_type());
         data.put("tagId", tag.getTag_id());
         data.put("tagName", tag.getTag_name());
         data.put("tagDescription", tag.getTag_desc());
         data.put("tagOrgName", tagOrgName);
         responsebody.setItems(data);
         responsebody.setStatus("Success");
      } catch (Exception var15) {
         this.logger.error("", var15);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var15.getMessage());
      }

      return responsebody;
   }

   public ResponseBody editTagInfo(String tagId, EditTagInfoFilter filter) throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         String tag_id = StrUtils.nvl(tagId);
         String tagName = StrUtils.nvl(filter.getTagName().trim());
         String tagDescription = StrUtils.nvl(filter.getTagDescription().trim());
         String tagType = StrUtils.nvl(filter.getTagType());
         TagInfo tagDao = TagInfoImpl.getInstance();
         new TagEntity();
         Map data = new HashMap();
         TagEntity tagForCheck = tagDao.getTagByName(tagName);
         if (tagForCheck != null && tagForCheck.getTag_id() != Integer.parseInt(tag_id)) {
            throw new Exception("The tag does not exist.");
         }

         TagEntity tag = tagDao.getTag(Integer.valueOf(tag_id));
         tag.setTag_name(tagName);
         tag.setTag_value(tagName);
         tag.setTag_desc(tagDescription);
         tag.setTag_type(Long.valueOf(tagType));
         tagDao.updateTagInfo(tag, true);
         String createDate = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S")).format(tag.getCreate_date());
         data.put("contentCount", tag.getContent_count());
         data.put("createDate", createDate);
         data.put("tagCondition", tag.getTag_condition());
         data.put("tagDescription", tag.getTag_desc());
         data.put("tagId", tag.getTag_id());
         data.put("tagName", tag.getTag_name());
         data.put("tagOrgName", tag.getTag_organ());
         data.put("tagType", tag.getTag_type());
         data.put("tagValue", tag.getTag_value());
         data.put("totalSize", tag.getTotal_size());
         responsebody.setItems(data);
         responsebody.setStatus("Success");
      } catch (Exception var13) {
         this.logger.error("", var13);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var13.getMessage());
      }

      return responsebody;
   }

   public ResponseBody deleteTagInfo(String tagIdList) throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         boolean messageFlag = false;
         tagIdList = StrUtils.nvl(tagIdList);
         TagInfo tagDao = TagInfoImpl.getInstance();
         String[] tagId = tagIdList.split(",");
         Map res = new HashMap();

         for(int i = 0; i < tagId.length; ++i) {
            int deviceMapCnt = tagDao.getTotalTagMappingCnt(Integer.parseInt(tagId[i]));
            if (deviceMapCnt > 0) {
               messageFlag = true;
            } else {
               tagDao.deleteTagInfo(Integer.parseInt(tagId[i]));
            }

            res.put("tagId", tagId[i]);
         }

         if (messageFlag) {
            throw new Exception("NOT_DELETED");
         }

         responsebody.setItems(res);
         responsebody.setStatus("Success");
      } catch (Exception var9) {
         this.logger.error("", var9);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var9.getMessage());
      }

      return responsebody;
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody getCategoryInfo(String categoryId) throws SQLException {
      ResponseBody responsebody = new ResponseBody();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      String organization = userContainer.getUser().getOrganization();
      CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
      String mode = "view";
      List list = new ArrayList();
      List prevCheckedList = null;
      List groupList = null;
      List categoryList;
      Iterator var20;
      CategoryEntity category;
      CategoryResource data;
      Iterator var23;
      LinkedHashMap state;
      CategoryEntity entity;
      LinkedHashMap state;
      if (categoryId != null && !categoryId.equals("") && !categoryId.equals("0")) {
         categoryList = categoryInfo.getCategoryWithPgroupId(Long.valueOf(categoryId));

         for(var20 = categoryList.iterator(); var20.hasNext(); list.add(data)) {
            category = (CategoryEntity)var20.next();
            data = new CategoryResource();
            data.setId(category.getGroup_id());
            data.setParent(category.getP_group_id().toString());
            if (mode != null && mode.equals("view")) {
               data.setText(category.getGroup_name());
            } else {
               data.setText(category.getGroup_name() + "<div class='menu' role='" + category.getGroup_id() + "'/>");
            }

            data.setType(category.getDescription());
            if (prevCheckedList != null && ((List)prevCheckedList).size() > 0) {
               var23 = ((List)prevCheckedList).iterator();

               while(var23.hasNext()) {
                  entity = (CategoryEntity)var23.next();
                  if (category.getGroup_id() == entity.getGroup_id()) {
                     state = new LinkedHashMap();
                     state.put("selected", true);
                     data.setState(state);
                  }
               }
            }

            if (groupList != null && ((List)groupList).size() > 0 && ((List)groupList).contains(String.valueOf(category.getGroup_id()))) {
               state = new LinkedHashMap();
               state.put("selected", true);
               data.setState(state);
            }
         }
      } else {
         if (userContainer.getUser().getGroup_id() == 0L) {
            organization = "ALL";
         }

         categoryList = categoryInfo.getCategoryWithPgroupId(0L, organization);
         if (categoryList == null || categoryList != null && categoryList.size() < 1) {
            this.logger.error("[MagicInfo_Category] init organization! id : " + userId);
            DeviceGroupInfo deviceInfo = DeviceGroupInfoImpl.getInstance();
            List organizationList = deviceInfo.getOrganizationGroup();
            Iterator var14 = organizationList.iterator();

            while(var14.hasNext()) {
               DeviceGroup group = (DeviceGroup)var14.next();
               String org = group.getGroup_name();
               long group_id = (long)SequenceDB.getNextValue("MI_CATEGORY_INFO_GROUP");
               CategoryEntity category = new CategoryEntity();
               category.setGroup_name(org);
               category.setP_group_id(0L);
               category.setGroup_id(group_id);
               category.setGroup_depth(0L);
               category.setDescription("Organization");
               category.setCreator_id("admin");
               category.setCreate_date(new Timestamp(System.currentTimeMillis()));
               categoryInfo.addCategory(category);
            }

            categoryList = categoryInfo.getCategoryWithPgroupId(0L, organization);
         }

         for(var20 = categoryList.iterator(); var20.hasNext(); list.add(data)) {
            category = (CategoryEntity)var20.next();
            data = new CategoryResource();
            data.setId(category.getGroup_id());
            data.setParent("#");
            data.setType(category.getDescription());
            if (mode != null && mode.equals("view")) {
               data.setText(category.getGroup_name());
            } else {
               data.setText(category.getGroup_name() + "<div class='menu' role='" + category.getGroup_id() + "'/>");
            }

            if (prevCheckedList != null && ((List)prevCheckedList).size() > 0) {
               var23 = ((List)prevCheckedList).iterator();

               while(var23.hasNext()) {
                  entity = (CategoryEntity)var23.next();
                  if (category.getGroup_id() == entity.getGroup_id()) {
                     state = new LinkedHashMap();
                     state.put("selected", true);
                     data.setState(state);
                  }
               }
            }

            if (groupList != null && ((List)groupList).size() > 0 && ((List)groupList).contains(String.valueOf(category.getGroup_id()))) {
               state = new LinkedHashMap();
               state.put("selected", true);
               data.setState(state);
            }
         }
      }

      responsebody.setItems(list);
      responsebody.setStatus("Success");
      return responsebody;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public ResponseBody checkLicenseStatus() throws SQLException {
      ResponseBody responseBody = new ResponseBody();

      try {
         SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
         LicenceCheckResource data = new LicenceCheckResource();
         if (!licenseMgr.licenseChk()) {
            data.setLicenseCheck(false);
         } else {
            data.setLicenseCheck(true);
         }

         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var4) {
         responseBody.setErrorMessage(var4.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Server Setup Manage Authority')")
   public ResponseBody getLicenseCheckDetail() throws SQLException {
      ResponseBody responseBody = new ResponseBody();

      try {
         SlmLicenseManager licenseMgr = SlmLicenseManagerImpl.getInstance();
         LicenseDetailResource data = new LicenseDetailResource();
         List licenseList = licenseMgr.getInvaildLicenseList();
         data.setInvalidLicenseList(licenseList);
         List totalDeviceList = new ArrayList();
         long afterDay = 30L;

         try {
            List list = licenseMgr.getListLicenseStatus();
            DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
            if (list != null && list.size() > 0) {
               Iterator var10 = list.iterator();

               label66:
               while(true) {
                  SlmLicenseStatusEntity status;
                  List deviceList;
                  do {
                     do {
                        if (!var10.hasNext()) {
                           break label66;
                        }

                        status = (SlmLicenseStatusEntity)var10.next();
                        deviceList = licenseMgr.getDeviceListWithProductCode(status.getProduct_code());
                     } while(deviceList == null);
                  } while(deviceList.size() <= 0);

                  int maxClient = licenseMgr.getLicenseCountByProductCode(status.getProduct_code());
                  int excess = deviceList.size() - maxClient;
                  List deleteDeviceList = new ArrayList();

                  for(int i = 0; i < excess; ++i) {
                     deleteDeviceList.add(deviceList.get(i));
                  }

                  List rtn = deviceDao.getDeviceListFromDeviceId(deleteDeviceList);
                  if (rtn != null && rtn.size() > 0) {
                     Iterator var17 = rtn.iterator();

                     while(var17.hasNext()) {
                        Map device = (Map)var17.next();
                        Map tmp = new HashMap();
                        tmp.put("deviceId", device.get("DEVICE_ID"));
                        tmp.put("deviceName", device.get("DEVICE_NAME"));
                        tmp.put("deviceType", device.get("DEVICE_TYPE"));
                        tmp.put("deviceTypeVersion", device.get("DEVICE_TYPE_VERSION"));
                        tmp.put("createDate", device.get("CREATE_DATE"));
                        totalDeviceList.add(tmp);
                     }
                  }

                  String expriedKey = licenseMgr.decrypt(status.getExpired_key());
                  String[] licenseInfo = expriedKey.split("\\|");
                  if (licenseInfo.length == 3) {
                     Date now = new Date(System.currentTimeMillis());
                     Date expired = new Date(Long.valueOf(licenseInfo[2]));
                     long diff = expired.getTime() - now.getTime();
                     long tempAfterDay = diff / 86400000L;
                     if (tempAfterDay < afterDay) {
                        afterDay = tempAfterDay;
                     }
                  }
               }
            }
         } catch (Exception var25) {
            this.logger.error("", var25);
            responseBody.setErrorMessage(var25.getMessage());
            responseBody.setStatus("Fail");
         }

         data.setDeviceList(totalDeviceList);
         data.setAfterDay(afterDay);
         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var26) {
         responseBody.setErrorMessage(var26.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody getServerSetupInfo() throws SQLException {
      ResponseBody responseBody = new ResponseBody();

      try {
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         UserInfo userInfo = UserInfoImpl.getInstance();
         UserContainer userContainer = SecurityUtils.getUserContainer();
         String userId = userContainer.getUser().getUser_id();
         Long orgId = userInfo.getRootGroupIdByUserId(userId);
         Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
         ServerSetupResource data = new ServerSetupResource();
         AbilityUtils ability = new AbilityUtils();
         if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
            data.setContentApprovalEnable(ability.isContentApprovalAuthority());
         }

         responseBody.setItems(data);
         responseBody.setStatus("Success");
      } catch (Exception var10) {
         responseBody.setErrorMessage(var10.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody getUsedDeviceTypeInfoByLicense() throws SQLException {
      ResponseBody responseBody = new ResponseBody();
      new LinkedHashMap();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      List deviceList = new ArrayList();
      boolean showAllDeviceType = false;

      try {
         if (CommonConfig.get("show.allDeviceType.enable") != null && CommonConfig.get("show.allDeviceType.enable").equalsIgnoreCase("true")) {
            showAllDeviceType = true;
         }
      } catch (ConfigException var12) {
         responseBody.setErrorMessage(var12.getMessage());
         responseBody.setStatus("Fail");
      }

      try {
         String[] var8;
         int var9;
         int var10;
         String deviceType;
         if (!showAllDeviceType) {
            String organization = userContainer.getUser().getOrganization();
            var8 = CommonDataConstants.ALL_DEVICE_ARRAY;
            var9 = var8.length;

            for(var10 = 0; var10 < var9; ++var10) {
               deviceType = var8[var10];
               if (deviceInfo.getAllDeviceCountByOrganization(deviceType, organization) > 0) {
                  deviceList.add(deviceType);
               }
            }
         } else {
            var8 = CommonDataConstants.ALL_DEVICE_ARRAY;
            var9 = var8.length;

            for(var10 = 0; var10 < var9; ++var10) {
               deviceType = var8[var10];
               deviceList.add(deviceType);
            }
         }

         responseBody.setItems(deviceList);
         responseBody.setStatus("Success");
      } catch (Exception var13) {
         responseBody.setErrorMessage(var13.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody getMenuItemCount() throws SQLException {
      ResponseBody responseBody = new ResponseBody();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      MenuCountResource menuCount = new MenuCountResource();

      try {
         ContentDao contentDao = new ContentDao();
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         Map condition = new HashMap();
         condition.put("listType", "ALL");
         condition.put("creatorID", userId);
         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
         long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
         boolean contentsApprovalEnable = false;
         if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
            contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         }

         AbilityUtils ability = new AbilityUtils();
         if (contentsApprovalEnable && !ability.isContentApprovalAuthority()) {
            condition.put("isContentApprove", true);
         }

         menuCount.setContent(contentDao.getContentListCnt(condition));
         Map unapprovedContentCondition = new HashMap();
         unapprovedContentCondition.put("content_type", "CONTENT");
         unapprovedContentCondition.put("listType", "SUBMITTED");
         unapprovedContentCondition.put("creatorID", userContainer.getUser().getUser_id());
         unapprovedContentCondition.put("canReadUnshared", true);
         unapprovedContentCondition.put("groupID", userContainer.getUser().getGroup_id() + "");
         int unapprovedCnt = contentDao.getContentListCnt(unapprovedContentCondition);
         menuCount.setUnapprovedContent(unapprovedCnt);
         PlaylistDao playlistDao = new PlaylistDao();
         menuCount.setPlaylist(playlistDao.getPlaylistListCnt(condition));
         ScheduleInfo info = ScheduleInfoImpl.getInstance();
         if (RoleUtils.isServerAdminRole(userContainer.getUser())) {
            menuCount.setContentSchedule(CommonUtils.safeLongToInt(info.getAllScheduleCount()));
         } else {
            menuCount.setContentSchedule(CommonUtils.safeLongToInt(info.getAllScheduleCount(userContainer.getUser().getOrganization())));
         }

         MessageInfo messageDao = MessageInfoImpl.getInstance();
         if (RoleUtils.isServerAdminRole(userContainer.getUser())) {
            menuCount.setMessageSchedule(CommonUtils.safeLongToInt(messageDao.getAllScheduleCount()));
         } else {
            new ArrayList();
            MessageGroupDao messageGroupDao = new MessageGroupDao();
            List lists = messageGroupDao.getRootGroupById(userContainer.getUser().getOrganization());
            int count = 0;

            List list;
            for(Iterator var24 = lists.iterator(); var24.hasNext(); count += list.size()) {
               MessageGroup group = (MessageGroup)var24.next();
               list = messageGroupDao.getChildMessageCount(group.getGroup_id().intValue(), true);
            }

            menuCount.setMessageSchedule(count);
         }

         DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
         Map searchParam = new HashMap();
         new HashMap();
         int count = false;
         int unapprovedCount = false;
         SelectCondition deviceCondition = new SelectCondition();
         DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
         Long group_id = deviceGroupDao.getOrganGroupIdByName(userContainer.getUser().getOrganization());
         deviceCondition.setGroup_id(group_id);
         deviceCondition.setIsRoot(true);
         deviceCondition.setStatus_view_mode("device_status_view_all");
         deviceCondition.setRole_name(userContainer.getUser().getRole_name());
         deviceCondition.setUser_id(userContainer.getUser().getUser_id());
         searchParam.put("condition", deviceCondition);
         PagedListInfo approvedInfo = deviceInfo.getPagedList(1, 0, searchParam, "getApprovedDeviceList");
         int count = approvedInfo.getTotalRowCount();
         menuCount.setDevice(count);
         int unapprovedCount = deviceInfo.getNonApprovalDeviceCount((String)null);
         menuCount.setUnapprovedDevice(unapprovedCount);
         responseBody.setItems(menuCount);
         responseBody.setStatus("Success");
      } catch (Exception var30) {
         responseBody.setErrorMessage(var30.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   public ResponseBody setFtpAccount(String id, String oldEncPassword, String newEncPassword) throws Exception {
      ResponseBody responseBody = new ResponseBody();

      try {
         DownloadInfo dao = DownloadInfoImpl.getInstance();
         dao.setFtpAccount(id, oldEncPassword, newEncPassword);
         responseBody.setStatus("Success");
      } catch (Exception var6) {
         responseBody.setErrorMessage(var6.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }
}
