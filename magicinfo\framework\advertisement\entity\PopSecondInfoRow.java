package com.samsung.magicinfo.framework.advertisement.entity;

import java.sql.Timestamp;
import org.apache.commons.lang.Validate;

public class PopSecondInfoRow {
   private Integer popId;
   private String contentId;
   private String deviceId;
   private Timestamp startTime;
   private Integer duration;

   public PopSecondInfoRow(Integer popId, String contentId, String deviceId, Timestamp startTime, Integer duration) {
      super();
      Validate.notNull(popId, "POP ID is required");
      Validate.notNull(contentId, "Content ID is required");
      Validate.notNull(deviceId, "Device ID is required");
      Validate.notNull(startTime, "Start time is required");
      Validate.notNull(duration, "Duration is required");
      this.popId = popId;
      this.contentId = contentId;
      this.deviceId = deviceId;
      this.startTime = startTime;
      this.duration = duration;
   }

   public Integer getPopId() {
      return this.popId;
   }

   public String getContentId() {
      return this.contentId;
   }

   public String getDeviceId() {
      return this.deviceId;
   }

   public Timestamp getStartTime() {
      return this.startTime;
   }

   public Integer getDuration() {
      return this.duration;
   }
}
