package com.samsung.common.utils;

import com.samsung.scas.core.LynkAisrDecrypt;
import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

public class AISRUtils {
   public static final String AISR_EXT = "aisr";

   public AISRUtils() {
      super();
   }

   public static void aisrDecrypt(File srcFile, File dstFile, String contentType, boolean deleteSrcFile) throws Exception {
      File dst = new File(SecurityUtils.directoryTraversalChecker(dstFile.getAbsolutePath(), (String)null));
      byte[] encryptedData = Files.readAllBytes(Paths.get(srcFile.getAbsolutePath()));
      byte var8 = -1;
      switch(contentType.hashCode()) {
      case 69775675:
         if (contentType.equals("IMAGE")) {
            var8 = 0;
         }
         break;
      case 73549584:
         if (contentType.equals("MOVIE")) {
            var8 = 1;
         }
      }

      byte[] decryptedData;
      switch(var8) {
      case 0:
         decryptedData = getDecryptedJpg(encryptedData);
         break;
      case 1:
         decryptedData = getDecryptedMp4(encryptedData);
         break;
      default:
         throw new Exception(String.format("%s is not supported.", contentType));
      }

      FileOutputStream outputStream = new FileOutputStream(dst);
      Throwable var19 = null;

      try {
         outputStream.write(decryptedData);
      } catch (Throwable var17) {
         var19 = var17;
         throw var17;
      } finally {
         if (outputStream != null) {
            if (var19 != null) {
               try {
                  outputStream.close();
               } catch (Throwable var16) {
                  var19.addSuppressed(var16);
               }
            } else {
               outputStream.close();
            }
         }

      }

      if (deleteSrcFile) {
         srcFile.delete();
      }

   }

   private static byte[] getDecryptedJpg(byte[] encryptedData) {
      LynkAisrDecrypt aisrDecrypt = new LynkAisrDecrypt();
      aisrDecrypt.init(0);
      byte[] decryptedData = aisrDecrypt.decrypt(encryptedData);
      return decryptedData;
   }

   private static byte[] getDecryptedMp4(byte[] encryptedData) {
      LynkAisrDecrypt aisrDecrypt = new LynkAisrDecrypt();
      aisrDecrypt.init(1);
      int startIndex = -1;
      int moovSize = 0;

      for(int i = 0; i < encryptedData.length - 4; ++i) {
         if (encryptedData[i] == 109 && encryptedData[i + 1] == 111 && encryptedData[i + 2] == 111 && encryptedData[i + 3] == 118) {
            startIndex = i - 4;
            int size = encryptedData[startIndex];

            for(int j = 1; j <= 3; ++j) {
               size <<= 8;
               int n = 255 & encryptedData[startIndex + j];
               size |= n;
            }

            moovSize = size;
            break;
         }
      }

      byte[] moov = new byte[moovSize];
      System.arraycopy(encryptedData, startIndex, moov, 0, moovSize);
      byte[] decryptedMoov = aisrDecrypt.decrypt(moov);
      byte[] resultData = new byte[encryptedData.length];
      System.arraycopy(encryptedData, 0, resultData, 0, encryptedData.length);
      System.arraycopy(decryptedMoov, 0, resultData, startIndex, moov.length);
      return resultData;
   }

   public static boolean isAISRSupportedFile(String fileExtension) {
      return StringUtils.equalsIgnoreCase(fileExtension, "JPG") || StringUtils.equalsIgnoreCase(fileExtension, "JPEG") || StringUtils.equalsIgnoreCase(fileExtension, "MP4");
   }

   public static boolean isEncryptedFile(String fileName) {
      String ext = FilenameUtils.getExtension(fileName);
      return StringUtils.equalsIgnoreCase(ext, "aisr");
   }
}
