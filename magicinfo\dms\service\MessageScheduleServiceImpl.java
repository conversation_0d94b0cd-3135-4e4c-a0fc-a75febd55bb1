package com.samsung.magicinfo.dms.service;

import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.dms.model.MessageResource;
import com.samsung.magicinfo.dms.model.MessageScheduleFilter;
import com.samsung.magicinfo.dms.model.MessageScheduleResource;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.MessageSourceManager;
import com.samsung.magicinfo.framework.common.MessageSourceManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.MessageInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.CommonMessageEntity;
import com.samsung.magicinfo.framework.scheduler.entity.MessageEntity;
import com.samsung.magicinfo.framework.scheduler.entity.SelectConditionMessageAdmin;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.common.MessageInterface;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("MessageScheduleService")
@Transactional
public class MessageScheduleServiceImpl implements MessageScheduleService {
   MessageInfoDAO dao = new MessageInfoDAO();
   MessageInfo messageInfo = MessageInfoImpl.getInstance();
   MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
   MessageInterface mInterface = DAOFactory.getMessageInfoImpl("PREMIUM");
   DeviceInfo dInfo = DeviceInfoImpl.getInstance();
   protected final Log logger = LogFactory.getLog(this.getClass());

   public MessageScheduleServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody listAllMessageSchedule(int startIndex, int pageSize) {
      ResponseBody responsebody = new ResponseBody();
      SelectConditionMessageAdmin condition = new SelectConditionMessageAdmin();

      try {
         List messageList = this.dao.getMessageListByCondition(condition, startIndex, pageSize);
         List resourceList = new ArrayList();
         if (messageList != null) {
            for(int i = 0; i < messageList.size(); ++i) {
               Map message = (Map)messageList.get(i);
               MessageScheduleResource resource = new MessageScheduleResource();
               String messageId = message.get("message_id").toString();
               resource.setMessageId(messageId);
               resource.setMessageName(message.get("message_name").toString());
               resource.setDeviceType(message.get("device_type").toString());
               resource.setDeviceTypeVersion(message.get("device_type_version").toString());
               resource.setDeviceGroupIds(message.get("device_group_id").toString());
               resource.setMessageGroupId(message.get("message_id").toString());
               resource.setCreatorId(message.get("user_id").toString());
               resource.setMessageGroupName(message.get("group_name").toString());
               String modifyDate = message.get("modify_date").toString();
               SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

               try {
                  Date date = sd.parse(modifyDate);
                  modifyDate = DateUtils.date2String(date, "yyyy-MM-dd HH:mm:ss");
               } catch (ParseException var15) {
                  this.logger.error(var15.getMessage().toString());
                  responsebody.setStatus("Fail");
                  responsebody.setErrorMessage(var15.getMessage());
                  return responsebody;
               }

               resource.setModifiedDate(modifyDate);
               String devGrpNames = null;
               Map m = this.messageInfo.getDeviceGroupIdsAndName(messageId);
               if (m != null && m.size() > 0) {
                  devGrpNames = (String)m.get("group_names");
               }

               resource.setDeviceGroupNames(devGrpNames);
               resourceList.add(resource);
            }
         }

         Long totalCount = this.dao.getMessageListCntByCondition(condition, startIndex, pageSize);
         responsebody.setTotalCount(Integer.parseInt(totalCount.toString()));
         responsebody.setItems(resourceList);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (SQLException var16) {
         this.logger.error(var16.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var16.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody listMessage(MessageScheduleFilter filter) {
      ResponseBody responsebody = new ResponseBody();
      UserContainer userContainer = SecurityUtils.getUserContainer();

      try {
         SelectConditionMessageAdmin originFilter = new SelectConditionMessageAdmin();
         originFilter.setSort_name(filter.getSortColumn());
         originFilter.setOrder_dir(filter.getSortOrder());
         originFilter.setNameLike(filter.getSearchText());
         originFilter.setStart_index(filter.getStartIndex());
         originFilter.setPage_size(filter.getPageSize());
         if (filter.getGroupId() != null && filter.getGroupId() != "") {
            originFilter.setGroupType(filter.getGroupId());
         }

         originFilter.setMessage_id(filter.getMessageId());
         originFilter.setGroupId(filter.getGroupId());
         originFilter.setGroupName(filter.getGroupName());
         originFilter.setStatus(filter.getStatus());
         originFilter.setDevice_type(filter.getDeviceType());
         originFilter.setDevice_type_version(filter.getDeviceTypeVersion());
         String strRootGroup = null;
         strRootGroup = userContainer.getUser().getOrganization();
         originFilter.setUserRootGroup(strRootGroup);
         List messageList = this.dao.getMessageListByCondition(originFilter, originFilter.getStart_index(), originFilter.getPage_size());
         List resourceList = new ArrayList();
         if (messageList != null) {
            for(int i = 0; i < messageList.size(); ++i) {
               Map message = (Map)messageList.get(i);
               MessageScheduleResource resource = new MessageScheduleResource();
               String messageId = message.get("message_id").toString();
               resource.setMessageId(messageId);
               resource.setMessageName(message.get("message_name").toString());
               resource.setDeviceType(message.get("device_type").toString());
               resource.setDeviceTypeVersion(message.get("device_type_version").toString());
               resource.setDeviceGroupIds(message.get("device_group_id").toString());
               resource.setMessageGroupId(message.get("message_id").toString());
               resource.setCreatorId(message.get("message_id").toString());
               resource.setMessageGroupName(message.get("group_name").toString());
               String modifyDate = message.get("modify_date").toString();
               SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

               try {
                  sd.parse(modifyDate);
                  modifyDate = TimeUtil.getGMTTime(Timestamp.valueOf(modifyDate));
               } catch (ParseException var16) {
                  this.logger.error(var16.getMessage().toString());
                  responsebody.setStatus("Fail");
                  responsebody.setErrorMessage(var16.getMessage());
                  return responsebody;
               }

               resource.setModifiedDate(modifyDate);
               String devGrpNames = null;
               Map m = this.messageInfo.getDeviceGroupIdsAndName(messageId);
               if (m != null && m.size() > 0) {
                  devGrpNames = (String)m.get("group_names");
               }

               resource.setDeviceGroupNames(devGrpNames);
               resourceList.add(resource);
            }
         }

         Long totalCount = this.dao.getMessageListCntByCondition(originFilter, originFilter.getStart_index(), originFilter.getPage_size());
         responsebody.setTotalCount(Integer.parseInt(totalCount.toString()));
         responsebody.setItems(resourceList);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (SQLException var17) {
         this.logger.error(var17.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var17.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody deleteMessageSchedule(String scheduleIds) {
      ResponseBody responsebody = new ResponseBody();

      try {
         String[] msgIdsArr = scheduleIds.split(",");
         boolean deleted = false;
         if (msgIdsArr != null && msgIdsArr.length != 0) {
            String[] var5 = msgIdsArr;
            int var6 = msgIdsArr.length;

            for(int var7 = 0; var7 < var6; ++var7) {
               String strMesgId = var5[var7];
               Map m = null;
               String devGrpIds = null;
               m = this.messageInfo.getDeviceGroupIdsAndName(strMesgId);
               if (m != null && m.size() > 0) {
                  devGrpIds = (String)m.get("device_group_ids");
               }

               deleted = this.messageInfo.setDeleteMessage(strMesgId);
               if (devGrpIds != null && devGrpIds.length() > 0) {
                  String[] devGrpIdArr = devGrpIds.split(",");
                  String[] var12 = devGrpIdArr;
                  int var13 = devGrpIdArr.length;

                  for(int var14 = 0; var14 < var13; ++var14) {
                     String devGrpId = var12[var14];
                     if (devGrpId != null && devGrpId.length() != 0) {
                        List devList = this.dInfo.getDeviceIdListByGroup(Integer.parseInt(devGrpId));
                        if (devList != null) {
                           Iterator var17 = devList.iterator();

                           while(var17.hasNext()) {
                              Map dMap = (Map)var17.next();
                              if (dMap != null) {
                                 String deviceId = (String)dMap.get("device_id");
                                 this.logger.info("Deploy default message to device " + deviceId);
                                 this.messageInfo.deployMessage(deviceId, "00000000-0000-0000-0000-000000000000", 1L);
                              }
                           }
                        }
                     }
                  }
               }
            }

            if (deleted) {
               responsebody.setStatus("Success");
            } else {
               responsebody.setStatus("Fail");
            }
         } else {
            responsebody.setStatus("Fail");
         }

         return responsebody;
      } catch (Exception var20) {
         this.logger.error(var20.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var20.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody permanentlyDeleteMessageSchedule(String scheduleIds) {
      ResponseBody responsebody = new ResponseBody();

      try {
         String[] msgIdsArr = scheduleIds.split(",");
         boolean deleted = false;
         if (msgIdsArr != null && msgIdsArr.length != 0) {
            String[] var5 = msgIdsArr;
            int var6 = msgIdsArr.length;

            for(int var7 = 0; var7 < var6; ++var7) {
               String strMesgId = var5[var7];
               Map m = null;
               String devGrpIds = null;
               m = this.messageInfo.getDeviceGroupIdsAndName(strMesgId);
               if (m != null && m.size() > 0) {
                  devGrpIds = (String)m.get("device_group_ids");
               }

               deleted = this.messageInfo.deleteMessage(strMesgId);
               if (devGrpIds != null && devGrpIds.length() > 0) {
                  MessageInfo mInfo = MessageInfoImpl.getInstance();
                  DeviceInfo dInfo = DeviceInfoImpl.getInstance();
                  String[] devGrpIdArr = devGrpIds.split(",");
                  String[] var14 = devGrpIdArr;
                  int var15 = devGrpIdArr.length;

                  for(int var16 = 0; var16 < var15; ++var16) {
                     String devGrpId = var14[var16];
                     if (devGrpId != null && devGrpId.length() != 0) {
                        List devList = dInfo.getDeviceIdListByGroup(Integer.parseInt(devGrpId));
                        if (devList != null) {
                           Iterator var19 = devList.iterator();

                           while(var19.hasNext()) {
                              Map dMap = (Map)var19.next();
                              if (dMap != null) {
                                 String deviceId = (String)dMap.get("device_id");
                                 this.logger.info("Deploy default message to device " + deviceId);
                                 mInfo.deployMessage(deviceId, "00000000-0000-0000-0000-000000000000", 1L);
                              }
                           }
                        }
                     }
                  }
               }
            }
         } else {
            responsebody.setStatus("Fail");
         }

         if (deleted) {
            responsebody.setStatus("Success");
         } else {
            responsebody.setStatus("Fail");
         }

         return responsebody;
      } catch (Exception var22) {
         this.logger.error(var22.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var22.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody getMessageInfo(String messageId) throws ConfigException, SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         List messageList = this.mInterface.getMessageList(messageId);
         MessageScheduleResource msResource = new MessageScheduleResource();
         if (messageList != null && messageList.size() > 0) {
            MessageEntity tmpMessageEntity = (MessageEntity)messageList.get(0);
            Map m = null;
            String devGrpNames = null;
            String devGrpIds = null;
            m = this.messageInfo.getDeviceGroupIdsAndName(messageId);
            if (m != null && m.size() > 0) {
               devGrpNames = (String)m.get("group_names");
               devGrpIds = (String)m.get("device_group_ids");
            }

            if (devGrpNames != null && devGrpNames.length() > 0) {
               msResource.setDeviceGroupNames(devGrpNames);
               msResource.setDeviceGroupIds(devGrpIds);
            } else {
               msResource.setDeviceGroupNames("");
            }

            msResource.setCreatorId(tmpMessageEntity.getUser_id());
            msResource.setDeviceType(tmpMessageEntity.getDevice_type());
            msResource.setDeviceTypeVersion(tmpMessageEntity.getDevice_type_version().toString());
            msResource.setIsInstant(tmpMessageEntity.getIs_instant());
            msResource.setMessageGroupId(tmpMessageEntity.getMessage_group_id() + "");
            msResource.setMessageGroupName(tmpMessageEntity.getMessage_group_name());
            msResource.setMessageId(messageId);
            msResource.setMessageName(tmpMessageEntity.getMessage_name());
            msResource.setModifiedDate(TimeUtil.getGMTTime(tmpMessageEntity.getModify_date()));
            long currTime = System.currentTimeMillis();
            Timestamp current_time = new Timestamp(currTime);
            List resourceList = new ArrayList();

            MessageResource resource;
            for(Iterator var13 = messageList.iterator(); var13.hasNext(); resourceList.add(resource)) {
               MessageEntity messageEntity = (MessageEntity)var13.next();
               new MessageResource();
               resource = this.convertEntityToResource(messageEntity);
               Long duration = messageEntity.getDuration();
               if (!messageEntity.getDevice_type().equals("LPLAYER") && (!messageEntity.getDevice_type().equals("SPLAYER") || !messageEntity.getDevice_type_version().equals("1.0"))) {
                  String start_time = messageEntity.getStart_date() + " " + messageEntity.getStart_time();
                  String end_time = messageEntity.getEnd_date();
                  end_time = end_time + " 00:00:00";
                  Timestamp startTime = Timestamp.valueOf(start_time);
                  Timestamp endTime = Timestamp.valueOf(end_time);
                  long endTimeTemp = endTime.getTime() + duration * 1000L;
                  endTime = new Timestamp(endTimeTemp);
                  if (msResource.getDeviceGroupIds() != null && !msResource.getDeviceGroupIds().equals("")) {
                     if (current_time.after(startTime) && current_time.before(endTime)) {
                        msResource.setMessageStatus("displaying");
                     } else if (startTime.getTime() > currTime) {
                        msResource.setMessageStatus("reservation");
                     } else if (endTime.getTime() < currTime) {
                        msResource.setMessageStatus("completed");
                     }
                  } else {
                     msResource.setMessageStatus("noUsed");
                  }
               } else {
                  Timestamp startTime = messageEntity.getModify_date();
                  if (msResource.getDeviceGroupIds() != null && !msResource.getDeviceGroupIds().equals("")) {
                     if (startTime.getTime() > currTime) {
                        msResource.setMessageStatus("reservation");
                     } else if (startTime.getTime() + duration * 1000L > currTime) {
                        msResource.setMessageStatus("displaying");
                     } else {
                        msResource.setMessageStatus("completed");
                     }
                  } else {
                     msResource.setMessageStatus("noUsed");
                  }

                  resource.setStartDate(msResource.getModifiedDate().split(" ", 2)[0]);
               }
            }

            msResource.setMessageList(resourceList);
         }

         responsebody.setItems(msResource);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (ConfigException var23) {
         this.logger.error(var23.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var23.getMessage());
         return responsebody;
      } catch (SQLException var24) {
         this.logger.error(var24.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var24.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody createMessageSchedule(MessageScheduleResource params) throws ConfigException, SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         MessageInterface msInfo = DAOFactory.getMessageInfoImpl("PREMIUM");
         UserContainer userContainer = SecurityUtils.getUserContainer();
         String messageId = UUID.randomUUID().toString();
         String messageName = params.getMessageName();
         String deviceType = params.getDeviceType();
         String deviceTypeVersion = params.getDeviceTypeVersion();
         String deviceGroupIds = params.getDeviceGroupIds();
         String messageGroupId = params.getMessageGroupId();
         String isInstant = params.getIsInstant();
         List messageList = params.getMessageList();
         String userId = "admin";
         if (userContainer != null) {
            userId = SecurityUtils.getLoginUserId();
         }

         boolean ret = this.messageInfo.isMessageNameUnique(messageName, messageId, Integer.parseInt(messageGroupId));
         if (!ret) {
            responsebody.setErrorCode(ExceptionCode.RES904[0]);
            responsebody.setErrorMessage(ExceptionCode.RES904[2]);
            responsebody.setStatus("Fail");
            return responsebody;
         } else {
            if (messageList != null) {
               for(int i = 0; i < messageList.size(); ++i) {
                  MessageResource tmpMessage = (MessageResource)messageList.get(i);
                  MessageEntity message = new MessageEntity();
                  message.setToDefaultMessageEntity();
                  message.setIdx((long)i);
                  Date today = new Date();
                  message.setCreate_date(new Timestamp(today.getTime()));
                  message.setMessage_id(messageId);
                  message.setMessage_name(messageName);
                  message.setDevice_type(deviceType);
                  message.setDevice_type_version(Float.valueOf(deviceTypeVersion));
                  message.setDevice_groups_ids(deviceGroupIds);
                  message.setMessage_group_id(Long.parseLong(messageGroupId));
                  message.setUser_id(userId);
                  message.setIs_instant(isInstant);
                  message.setMessage_text(tmpMessage.getMessageText());
                  message.setFont(tmpMessage.getFont());
                  message.setSize(tmpMessage.getSize());
                  message.setColor(tmpMessage.getColor());
                  message.setBg_color(tmpMessage.getBgColor());
                  message.setBold(tmpMessage.getBold());
                  message.setItalic(tmpMessage.getItalic());
                  message.setUnderline(tmpMessage.getUnderline());
                  message.setRepeat_type(tmpMessage.getRepeatType());
                  message.setWeekdays(tmpMessage.getWeekdays());
                  message.setMonthdays(tmpMessage.getMonthdays());
                  message.setPosition(tmpMessage.getPosition());
                  message.setDirection(tmpMessage.getDirection());
                  message.setSpeed(tmpMessage.getSpeed());
                  message.setDuration(tmpMessage.getDuration());
                  if (deviceType.equals("LPLAYER")) {
                     message.setStart_date("");
                     message.setEnd_date("");
                     message.setStart_time((new Timestamp(System.currentTimeMillis())).toString());
                  } else if (deviceType.equals("SPLAYER") && deviceTypeVersion.equals("1")) {
                     if (isInstant != null && !isInstant.equals("") && isInstant.equals("false")) {
                        message.setEnd_date("2999-12-31");
                        message.setStart_time(tmpMessage.getStartDate() + " " + tmpMessage.getStartTime());
                     } else {
                        Date now = new Date();
                        SimpleDateFormat date = new SimpleDateFormat("yyyy-MM-dd");
                        SimpleDateFormat time = new SimpleDateFormat("hh:mm:ss");
                        message.setStart_date(date.format(now));
                        message.setEnd_date("2999-12-31");
                        message.setStart_time(time.format(now));
                        message.setStart_time(tmpMessage.getStartDate() + " " + tmpMessage.getStartTime());
                     }
                  } else {
                     message.setStart_date(tmpMessage.getStartDate());
                     message.setEnd_date(tmpMessage.getEndDate());
                     message.setStart_time(tmpMessage.getStartDate() + " " + tmpMessage.getStartTime());
                  }

                  try {
                     this.messageInfo.addMessage(message);
                  } catch (Exception var23) {
                     this.logger.error(var23.getMessage().toString());
                     responsebody.setStatus("Fail");
                     responsebody.setErrorMessage(var23.getMessage());
                     return responsebody;
                  }

                  try {
                     msInfo.deployMessage(message);
                  } catch (Exception var22) {
                     this.logger.error(var22.getMessage().toString());
                     responsebody.setStatus("Fail");
                     responsebody.setErrorMessage(var22.getMessage());
                     return responsebody;
                  }
               }
            }

            responsebody.setStatus("Success");
            responsebody.setItems(messageId);
            return responsebody;
         }
      } catch (SQLException var24) {
         this.logger.error(var24.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var24.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody editMessageSchedule(String messageId, MessageScheduleResource params) throws ConfigException, SQLException {
      ResponseBody responsebody = new ResponseBody();
      String messageName = params.getMessageName();
      String deviceType = params.getDeviceType();
      String deviceTypeVersion = params.getDeviceTypeVersion();
      String deviceGroupIds = params.getDeviceGroupIds();
      String messageGroupId = params.getMessageGroupId();
      String isInstant = params.getIsInstant();
      List messageList = params.getMessageList();
      MessageInterface msInfo = DAOFactory.getMessageInfoImpl("PREMIUM");
      CommonMessageEntity message = msInfo.getMessage(messageId);
      boolean resultDao = false;

      try {
         if (message != null) {
            msInfo.checkDeviceMapping(messageId, deviceGroupIds);
            msInfo.deleteMessage(message.getMessage_id());
            message.setVersion(message.getVersion() + 1L);
            message.setUser_id(SecurityUtils.getLoginUserId());
            Date today = new Date();
            message.setModify_date(new Timestamp(today.getTime()));
            if (messageList != null) {
               for(int i = 0; i < messageList.size(); ++i) {
                  MessageResource tmpMessage = (MessageResource)messageList.get(i);
                  MessageEntity me = new MessageEntity();
                  me.setVersion(message.getVersion());
                  me.setMessage_id(messageId);
                  me.setDevice_type(deviceType);
                  me.setDevice_type_version(Float.valueOf(deviceTypeVersion));
                  me.setDevice_groups_ids(deviceGroupIds);
                  me.setMessage_group_id(Long.valueOf(messageGroupId));
                  me.setMessage_name(messageName);
                  me.setIdx((long)i);
                  me.setMessage_text(tmpMessage.getMessageText());
                  me.setFont(tmpMessage.getFont());
                  me.setSize(tmpMessage.getSize());
                  me.setColor(tmpMessage.getColor());
                  me.setBg_color(tmpMessage.getBgColor());
                  me.setBold(tmpMessage.getBold());
                  me.setItalic(tmpMessage.getItalic());
                  me.setUnderline(tmpMessage.getUnderline());
                  me.setDirection(tmpMessage.getDirection());
                  me.setSpeed(tmpMessage.getSpeed());
                  me.setPosition(tmpMessage.getPosition());
                  me.setStart_date(tmpMessage.getStartDate());
                  me.setEnd_date(tmpMessage.getEndDate());
                  me.setRepeat_type(tmpMessage.getRepeatType());
                  me.setWeekdays(tmpMessage.getWeekdays());
                  me.setMonthdays(tmpMessage.getMonthdays());
                  me.setDuration(tmpMessage.getDuration());
                  me.setUser_id(tmpMessage.getUserId());
                  me.setIs_instant("Y");
                  me.setLed_meta_type("");
                  me.setLed_left("0");
                  me.setLed_top("0");
                  me.setLed_width("0");
                  me.setLed_height("0");
                  me.setLed_play_style("0");
                  me.setLed_speed("0");
                  me.setLed_color("0");
                  if (deviceType.equals("LPLAYER")) {
                     me.setStart_date("");
                     me.setEnd_date("");
                     me.setStart_time((new Timestamp(System.currentTimeMillis())).toString());
                  } else if (deviceType.equals("SPLAYER") && deviceTypeVersion.equals("1")) {
                     if (isInstant != null && !isInstant.equals("") && isInstant.equals("false")) {
                        me.setEnd_date("2999-12-31");
                        me.setStart_time(tmpMessage.getStartDate() + " " + tmpMessage.getStartTime());
                     } else {
                        Date now = new Date();
                        SimpleDateFormat date = new SimpleDateFormat("yyyy-MM-dd");
                        SimpleDateFormat time = new SimpleDateFormat("hh:mm:ss");
                        me.setStart_date(date.format(now));
                        me.setEnd_date("2999-12-31");
                        me.setStart_time(time.format(now));
                        me.setStart_time(tmpMessage.getStartDate() + " " + tmpMessage.getStartTime());
                     }
                  } else {
                     me.setStart_time(tmpMessage.getStartDate() + " " + tmpMessage.getStartTime());
                  }

                  resultDao = msInfo.setMessage(me);
                  if (me.getDevice_type().equalsIgnoreCase("SPLAYER") && me.getDevice_type_version() < 2.0F || me.getDevice_type().equalsIgnoreCase("LPLAYER")) {
                     msInfo.deployMessage(me);
                  }

                  if (!resultDao) {
                     responsebody.setStatus("Fail");
                     return responsebody;
                  }

                  if ((!message.getDevice_type().equalsIgnoreCase("SPLAYER") || !(message.getDevice_type_version() < 2.0F)) && !me.getDevice_type().equalsIgnoreCase("LPLAYER")) {
                     msInfo.deployMessage(message);
                  }
               }
            }
         }

         responsebody.setStatus("Success");
         responsebody.setItems(params);
         return responsebody;
      } catch (Exception var21) {
         this.logger.error(var21.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var21.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody deployMessageSchedule(String messageId, MessageScheduleResource params) throws ConfigException, SQLException {
      ResponseBody responsebody = new ResponseBody();
      List oldGroupId = null;
      UserContainer userContainer = SecurityUtils.getUserContainer();

      try {
         String strGroupId = params.getDeviceGroupIds();
         oldGroupId = this.messageInfo.getDeviceMessageMapList(messageId);
         String[] strGroupIdArr = strGroupId.split(",");
         List oldGroupIdList = new ArrayList();
         if (oldGroupId != null && oldGroupId.size() > 0) {
            for(int j = 0; j < oldGroupId.size(); ++j) {
               Map dev_map = (Map)oldGroupId.get(j);
               oldGroupIdList.add((Long)dev_map.get("device_group_id"));
            }
         }

         boolean deleted = this.messageInfo.updateDeviceGroup(strGroupId, messageId, userContainer.getUser().getUser_id());
         CommonMessageEntity message = this.mInterface.getMessage(messageId);
         DeviceInfo dInfo = DeviceInfoImpl.getInstance();
         if (!"".equals(strGroupId)) {
            String[] var12 = strGroupIdArr;
            int var13 = strGroupIdArr.length;

            for(int var14 = 0; var14 < var13; ++var14) {
               String newGroupId = var12[var14];
               Long longNewGroupId = Long.parseLong(newGroupId);
               if (!oldGroupIdList.contains(longNewGroupId)) {
                  List devList = dInfo.getDeviceIdListByGroup(longNewGroupId.intValue());
                  if (devList != null) {
                     Iterator var18 = devList.iterator();

                     while(var18.hasNext()) {
                        Map dMap = (Map)var18.next();
                        if (dMap != null) {
                           String deviceId = (String)dMap.get("device_id");
                           this.logger.info("Deploy new message to device " + deviceId);
                           this.messageInfo.deployMessage(deviceId, messageId, message.getVersion());
                        }
                     }
                  }
               }
            }
         }

         Iterator var24 = oldGroupIdList.iterator();

         while(true) {
            List devList;
            do {
               if (!var24.hasNext()) {
                  if (deleted) {
                     responsebody.setItems(messageId);
                     responsebody.setStatus("Success");
                     return responsebody;
                  }

                  responsebody.setStatus("Fail");
                  return responsebody;
               }

               Long longOldGroupId = (Long)var24.next();
               devList = dInfo.getDeviceIdListByGroup(longOldGroupId.intValue());
            } while(devList == null);

            Iterator var27 = devList.iterator();

            while(var27.hasNext()) {
               Map dMap = (Map)var27.next();
               if (dMap != null) {
                  String deviceId = (String)dMap.get("device_id");
                  this.logger.info("Deploy default message to device " + deviceId);
                  this.messageInfo.deployMessage(deviceId, "00000000-0000-0000-0000-000000000000", 1L);
               }
            }
         }
      } catch (Exception var21) {
         this.logger.error(var21.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var21.getMessage());
         return responsebody;
      }
   }

   public MessageResource convertEntityToResource(MessageEntity entity) {
      MessageResource resource = new MessageResource();
      resource.setToDefaultMessageEntity();
      resource.setBgColor(entity.getBg_color());
      resource.setBgImage(entity.getBg_image());
      resource.setBgTransparency(entity.getBg_transparency());
      resource.setBold(entity.getBold());
      resource.setColor(entity.getColor());
      resource.setCreateDate(entity.getCreate_date());
      resource.setDirection(entity.getDirection());
      resource.setDuration(entity.getDuration());
      resource.setEndDate(entity.getEnd_date());
      resource.setFont(entity.getFont());
      resource.setIdx(entity.getIdx());
      resource.setItalic(entity.getItalic());
      resource.setMessageId(entity.getMessage_id());
      resource.setMessageText(entity.getMessage_text());
      resource.setModifiedDate(TimeUtil.getGMTTime(entity.getModify_date()));
      resource.setMonthdays(entity.getMonthdays());
      resource.setPosition(entity.getPosition());
      resource.setRepeatType(entity.getRepeat_type());
      resource.setSize(entity.getSize());
      resource.setSpeed(entity.getSpeed());
      resource.setStartDate(entity.getStart_date());
      resource.setStartTime(entity.getStart_time());
      resource.setUnderline(entity.getUnderline());
      resource.setUserId(entity.getUser_id());
      resource.setVersion(entity.getVersion());
      resource.setWeekdays(entity.getWeekdays());
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody copyMessageSchedule(String messageId, MessageScheduleResource params) throws ConfigException, SQLException {
      ResponseBody responsebody = new ResponseBody();
      String newMessageId = UUID.randomUUID().toString();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      new LinkedHashMap();
      MessageInfo messageDao = MessageInfoImpl.getInstance();
      MessageInterface msInfo = DAOFactory.getMessageInfoImpl("PREMIUM");
      boolean resultDao = false;

      try {
         List messageList = messageDao.getMessageList(messageId);
         if (messageList == null) {
            responsebody.setStatus("Fail");
            return responsebody;
         } else {
            for(int i = 0; i < messageList.size(); ++i) {
               MessageEntity message = (MessageEntity)messageList.get(i);
               message.setMessage_id(newMessageId);
               message.setDevice_type(message.getDevice_type());
               message.setDevice_type_version(message.getDevice_type_version());
               message.setDevice_groups_ids(params.getDeviceGroupIds());
               message.setMessage_group_id(Long.valueOf(params.getMessageGroupId()));
               message.setMessage_name(params.getMessageName());
               message.setUser_id(SecurityUtils.getLoginUserId());
               if (message.getDevice_type().equals("LPLAYER")) {
                  message.setStart_date("");
                  message.setEnd_date("");
                  message.setStart_time((new Timestamp(System.currentTimeMillis())).toString());
               } else if (message.getDevice_type().equals("SPLAYER") && message.getDevice_type_version() < 2.0F) {
                  Date now = new Date();
                  SimpleDateFormat date = new SimpleDateFormat("yyyy-MM-dd");
                  SimpleDateFormat time = new SimpleDateFormat("hh:mm:ss");
                  message.setStart_date(date.format(now));
                  message.setEnd_date("2999-12-31");
                  message.setStart_time(time.format(now));
               } else {
                  message.setStart_time(message.getStart_date() + " " + message.getStart_time());
               }

               resultDao = msInfo.addMessage(message);
               if (!resultDao) {
                  responsebody.setStatus("Fail");
                  return responsebody;
               }

               msInfo.deployMessage(message);
            }

            responsebody.setStatus("Success");
            return responsebody;
         }
      } catch (Exception var17) {
         this.logger.error(var17.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var17.getMessage());
         return responsebody;
      }
   }
}
