package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.area.CartListEntity;
import java.sql.SQLException;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class AdCartDao {
   Logger logger = LoggingManagerV2.getLogger(AdCartDao.class);

   public AdCartDao() {
      super();
   }

   /** @deprecated */
   @Deprecated
   public int insertIntoMyCartList(String user_id, String area_id) throws SQLException {
      throw new SQLException("java.sql.SQLException: ERROR: relation \"mi_ad_cart_list\" does not exist");
   }

   /** @deprecated */
   @Deprecated
   public int selectFromMyCartList(String user_id, String area_id) throws SQLException {
      throw new SQLException("java.sql.SQLException: ERROR: relation \"mi_ad_cart_list\" does not exist");
   }

   /** @deprecated */
   @Deprecated
   public List getMyCartList(String user_id) throws SQLException {
      throw new SQLException("java.sql.SQLException: ERROR: relation \"mi_ad_area_info\" does not exist");
   }

   /** @deprecated */
   @Deprecated
   public List getOjectTypeMyCartList(String user_id) throws SQLException {
      throw new SQLException("java.sql.SQLException: ERROR: relation \"mi_ad_area_info\" does not exist");
   }

   /** @deprecated */
   @Deprecated
   public int deleteFromMyCart(String user_id, String area_id) throws SQLException {
      throw new SQLException("java.sql.SQLException: ERROR: relation \"mi_ad_cart_list\" does not exist");
   }

   /** @deprecated */
   @Deprecated
   public int deleteFromMyCart(CartListEntity temp) throws SQLException {
      throw new SQLException("java.sql.SQLException: ERROR: relation \"mi_ad_cart_list\" does not exist");
   }
}
