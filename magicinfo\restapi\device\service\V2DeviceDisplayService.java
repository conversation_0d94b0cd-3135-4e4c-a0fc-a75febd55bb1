package com.samsung.magicinfo.restapi.device.service;

import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.rms.model.DeviceDisplayConfResource;
import javax.servlet.http.HttpServletResponse;

public interface V2DeviceDisplayService {
   V2CommonBulkResultResource getDisplayControlInfo(V2CommonIds var1) throws Exception;

   V2DeviceReqServiceResource updateDisplayInfo(DeviceDisplayConfResource var1) throws Exception;

   V2DeviceReqServiceResource getCurrentStatusDisplay(V2CommonIds var1) throws Exception;

   V2CommonBulkResultResource getCurrentStatusDisplayWithRequestId(V2DeviceReqServiceConf var1) throws Exception;

   V2PageResource filterDeviceDisplayList(V2DeviceFilter var1, HttpServletResponse var2) throws Exception;
}
