package com.samsung.common.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.Charset;

public class XPathUtils {
   public XPathUtils() {
      super();
   }

   public static String escapeXPathLiteral(String param) {
      if (!param.contains("\"")) {
         return "\"" + param + "\"";
      } else if (!param.contains("'")) {
         return "'" + param + "'";
      } else {
         StringBuilder sb = new StringBuilder();
         sb.append("concat(");
         String[] substrings = param.split("\"", -1);

         for(int i = 0; i < substrings.length; ++i) {
            boolean needComma = i > 0;
            if (!"".equals(substrings[i])) {
               if (i > 0) {
                  sb.append(", ");
               }

               sb.append("\"");
               sb.append(substrings[i]);
               sb.append("\"");
               needComma = true;
            }

            if (i < substrings.length - 1) {
               if (needComma) {
                  sb.append(", ");
               }

               sb.append("'\"'");
            }
         }

         sb.append(")");
         return sb.toString();
      }
   }

   public static boolean validateOWASPXPathParameter(String param) throws UnsupportedEncodingException {
      boolean isValid = true;
      if (param != null && !"".equals(param)) {
         String xpathCharList = "()='[]:,*/ ";
         String decodedValue = URLDecoder.decode(param, Charset.defaultCharset().name());
         char[] var4 = decodedValue.toCharArray();
         int var5 = var4.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            char c = var4[var6];
            if (xpathCharList.indexOf(c) != -1) {
               isValid = false;
               break;
            }
         }
      }

      return isValid;
   }
}
