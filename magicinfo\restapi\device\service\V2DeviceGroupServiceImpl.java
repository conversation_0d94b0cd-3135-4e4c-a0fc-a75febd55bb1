package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.common.Menu;
import com.samsung.magicinfo.framework.common.manager.EncryptionManager;
import com.samsung.magicinfo.framework.common.manager.EncryptionManagerImpl;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.BackupPlayEntity;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceMonitoring;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemSetupConf;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfo;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocInfoImpl;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManager;
import com.samsung.magicinfo.framework.device.noc.manager.DeviceNocManagerImpl;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfo;
import com.samsung.magicinfo.framework.device.preconfig.manager.DevicePreconfigInfoImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.dao.VwlLayoutDao;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlDeviceLayout;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlVwtLayout;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManager;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManagerImpl;
import com.samsung.magicinfo.framework.monitoring.dao.ScreenCaptureDAO;
import com.samsung.magicinfo.framework.monitoring.entity.ScheduleInfoEntity;
import com.samsung.magicinfo.framework.monitoring.entity.ScreenCaptureEntity;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleAdminDao;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.openapi.auth.TokenRegistry;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonGroupResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2ListQueryFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceAvailableMenuResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceBackupMapInfo;
import com.samsung.magicinfo.restapi.device.model.V2DeviceBackupModeResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceBackupPlayInfoReq;
import com.samsung.magicinfo.restapi.device.model.V2DeviceBackupPlayInfoRes;
import com.samsung.magicinfo.restapi.device.model.V2DeviceBackupResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupChildResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupCreation;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupDeletion;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupMovement;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupRename;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupSearch;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGroupTreeResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSetSboxLayoutResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSubVwlPreviewResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTag;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTargetResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceVwlPreviewResorce;
import com.samsung.magicinfo.restapi.device.model.V2VwlLayoutManagerResource;
import com.samsung.magicinfo.restapi.device.utils.RESTDeviceUtils;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.rms.model.DeviceGroupFilter;
import com.samsung.magicinfo.rms.model.DeviceGroupResource;
import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2DeviceGroupService")
@Transactional
public class V2DeviceGroupServiceImpl implements V2DeviceGroupService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceGroupServiceImpl.class);
   LeftMenuGroupTreeDao treeDao = new LeftMenuGroupTreeDao();
   DeviceGroupDao deviceGroupDao = new DeviceGroupDao();
   ContentDao contentTreeDao = new ContentDao();
   public static final String LEFT_MENU = "leftmenu";
   public static final String ROOT_GROUP_ID = "0";
   public static final String TRUE = "true";
   public static final String DEVICE = "DEVICE";
   public static final String DISABLE = "disabled";
   public static final String TYPE = "type";
   public static final String GROUP_ID = "groupId";
   public static final String PARENT_GROUP_ID = "pGroupId";
   public static final String VIDEO_WALL_MODE = "videoWallMode";
   public static final String TAG_STATUS = "tagStatus";
   public static final String RESOURCE_COUNT = "resourceCount";
   public static final String GROUP_NAME = "groupName";
   public static final String CHILDREN_STATUS = "childrenStatus";
   public static final String ANALYSIS_STATUS = "analysisStatus";
   StringBuffer strResult = new StringBuffer("");
   long count = 0L;

   public V2DeviceGroupServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody listGroupInfo(Long groupId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, groupId);
      ResponseBody responseBody = new ResponseBody();
      DeviceGroupResource deviceGroupResource = new DeviceGroupResource();
      DeviceGroupDao deviceGroupDao = new DeviceGroupDao();

      try {
         DeviceGroup deviceGroup = deviceGroupDao.getGroup((int)groupId);
         deviceGroupResource.setGroupId(deviceGroup.getGroup_id());
         deviceGroupResource.setParentGroupId(deviceGroup.getP_group_id());
         deviceGroupResource.setGroupDepth(deviceGroup.getGroup_depth());
         deviceGroupResource.setGroupName(deviceGroup.getGroup_name());
         deviceGroupResource.setDescription(deviceGroup.getDescription());
         deviceGroupResource.setIsRoot(deviceGroup.getIs_root());
         deviceGroupResource.setCreatorId(deviceGroup.getCreator_id());
         deviceGroupResource.setCreateDate(deviceGroup.getCreate_date());
         deviceGroupResource.setMinPriority(deviceGroup.getMin_priority());
         deviceGroupResource.setAnalysisStatus(deviceGroup.getAnalysis_status());
         responseBody.setItems(deviceGroupResource);
         responseBody.setStatus("Success");
      } catch (Exception var6) {
         this.logger.error("", var6);
         responseBody.setErrorMessage(var6.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody getDeviceGroupList(V2ListQueryFilter params) throws Exception {
      DeviceGroupFilter filter = new DeviceGroupFilter();
      filter.setStartIndex(params.getStartIndex() - 1);
      filter.setPageSize(params.getPageSize());
      filter.setSortName(params.getSortColumn());
      filter.setOrderDir(params.getSortOrder());
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, filter.getGroupId());
      ResponseBody responseBody = new ResponseBody();
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      String groupId = "";
      this.logger.info("[REST][DEVICE GROUP][filterDeviceGropuList] organization = " + userContainer.getUser().getOrganization());
      List deviceGroupList = deviceGroupDao.getGroupList(filter);
      List deviceGroupResources = new ArrayList();
      Iterator var10 = deviceGroupList.iterator();

      while(var10.hasNext()) {
         DeviceGroup deviceGroup = (DeviceGroup)var10.next();
         DeviceGroupResource deviceGroupResource = new DeviceGroupResource();
         deviceGroupResource.setGroupId(deviceGroup.getGroup_id());
         deviceGroupResource.setDescription(deviceGroup.getDescription());
         deviceGroupResource.setParentGroupId(deviceGroup.getP_group_id());
         deviceGroupResource.setGroupName(deviceGroup.getGroup_name());
         deviceGroupResource.setAnalysisStatus(deviceGroup.getAnalysis_status());
         deviceGroupResource.setCreateDate(deviceGroup.getCreate_date());
         deviceGroupResource.setCreatorId(deviceGroup.getCreator_id());
         deviceGroupResource.setGroupDepth(deviceGroup.getGroup_depth());
         deviceGroupResource.setIsRoot(deviceGroup.getIs_root());
         deviceGroupResource.setMinPriority(deviceGroup.getMin_priority());
         deviceGroupResources.add(deviceGroupResource);
      }

      responseBody.setItems(deviceGroupResources);
      responseBody.setStatus("Success");
      int totalCount = false;
      int totalCount = deviceGroupDao.getGroupListCnt(filter);
      responseBody.setStartIndex(params.getStartIndex());
      responseBody.setPageSize(params.getPageSize());
      responseBody.setTotalCount(totalCount);
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public List listGroups(String groupId) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      List groupList = new ArrayList();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupDao deviceGroupDao = new DeviceGroupDao();
      List list = new ArrayList();
      Boolean permissions_func = null;

      try {
         permissions_func = DeviceUtils.isDeviceGroupAuth(user);
      } catch (Exception var31) {
         this.logger.error("[DEVICE] [GROUPSERVICE] listGroups " + RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE.getMessage(), var31);
         throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"device group"});
      }

      try {
         long id = 0L;
         String target = "leftmenu";
         String type = "PREMIUM";
         Long p_group_id = null;
         String organization = null;
         String videoWallMode = null;
         String syncMode = null;
         boolean onlyExactDeviceType = false;
         long priority = 0L;
         if (groupId != null && groupId.equals("")) {
            if (user.getRoot_group_id() == Long.valueOf("0")) {
               groupId = "0";
            } else {
               organization = user.getOrganization();
            }
         }

         if (organization != null) {
            id = groupDao.getOrganGroupIdByName(organization);
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, id);
            DeviceGroup deviceGroup = groupDao.getGroup(Integer.valueOf(String.valueOf(id)));
            ((List)list).add(deviceGroup);
         } else {
            id = Long.valueOf(groupId);
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, id);
            list = groupDao.getGroupById(type, "DEVICE", id, false);
         }

         for(int i = 0; i < ((List)list).size(); ++i) {
            long pGroupId = ((DeviceGroup)((List)list).get(i)).getP_group_id();
            if (permissions_func && pGroupId != Long.valueOf("0") && (long)groupDao.checkChildPermissions2(user.getUser_id(), ((DeviceGroup)((List)list).get(i)).getGroup_id()) == Long.valueOf("0")) {
               DeviceUtils.getDeviceGroupList((List)list, user.getUser_id(), ((DeviceGroup)((List)list).get(i)).getGroup_id(), pGroupId);
               ((List)list).remove(i);
               --i;
            } else {
               p_group_id = pGroupId;
               V2DeviceGroupChildResource group = new V2DeviceGroupChildResource();
               long minPriority = ((DeviceGroup)((List)list).get(i)).getMin_priority();
               String groupType = ((DeviceGroup)((List)list).get(i)).getGroup_type();
               String deviceType;
               if (p_group_id == 0L) {
                  deviceType = "ORGANIZATION";
                  if (videoWallMode != null && !((String)videoWallMode).equals("") && ((String)videoWallMode).equals("true")) {
                     group.setDisabled(true);
                  }

                  if (syncMode != null && !((String)syncMode).equals("") && ((String)syncMode).equals("true")) {
                     group.setDisabled(true);
                  }
               } else {
                  deviceType = DeviceUtils.getDeviceType(minPriority, groupType);
               }

               group.setType(deviceType);
               if (minPriority != -1L) {
                  if (onlyExactDeviceType) {
                     if (!deviceType.equals("SPLAYER") && !deviceType.equals("S2PLAYER") && !deviceType.equals("S3PLAYER") && !deviceType.equals("S4PLAYER")) {
                        if (priority != minPriority && (target == null || !"leftmenu".equals(target))) {
                           group.setDisabled(true);
                        }
                     } else if (priority != 5L && priority != 10L && priority != 20L && (target == null || !"leftmenu".equals(target))) {
                        group.setDisabled(true);
                     }
                  } else if (priority > minPriority && (target == null || !"leftmenu".equals(target))) {
                     group.setDisabled(true);
                  }
               }

               group.setGroupId(((DeviceGroup)((List)list).get(i)).getGroup_id());
               group.setParentGroupId(p_group_id);
               if (videoWallMode != null && !((String)videoWallMode).equals("") && ((String)videoWallMode).equals("true")) {
                  if (((DeviceGroup)((List)list).get(i)).getVwt_id() != null && !((DeviceGroup)((List)list).get(i)).getVwt_id().equals("")) {
                     if (target == null || !"leftmenu".equals(target)) {
                        group.setDisabled(false);
                     }

                     group.setVideoWallMode(true);
                  } else if (target == null || !"leftmenu".equals(target)) {
                     group.setDisabled(true);
                  }
               }

               if (syncMode != null && !((String)syncMode).equals("") && ((String)syncMode).equals("true")) {
                  if (DeviceUtils.getDeviceTagStatus(((DeviceGroup)((List)list).get(i)).getGroup_id())) {
                     group.setTagStatus(true);
                  } else {
                     if (target == null || !"leftmenu".equals(target)) {
                        group.setDisabled(true);
                     }

                     group.setTagStatus(false);
                  }
               }

               List deviceList = null;
               if (pGroupId == 0L) {
                  if (permissions_func) {
                     deviceList = deviceGroupDao.getChildDeviceIdListByGroupPermission(((DeviceGroup)((List)list).get(i)).getGroup_id().intValue(), true, user.getUser_id());
                  } else {
                     deviceList = groupDao.getChildDeviceIdList(((DeviceGroup)((List)list).get(i)).getGroup_id().intValue(), true);
                  }

                  this.count = (long)deviceList.size();
               } else {
                  List tempList = deviceDao.getDeviceListByGroupId(((DeviceGroup)((List)list).get(i)).getGroup_id());
                  if (tempList != null) {
                     this.count = (long)tempList.size();
                  }
               }

               group.setResourceCount(this.count);
               group.setGroupName(((DeviceGroup)((List)list).get(i)).getGroup_name());
               group.setChildrenStatus(true);
               group.setAnalysisStatus(((DeviceGroup)((List)list).get(i)).getAnalysis_status());
               group.setGroupDepth(((DeviceGroup)((List)list).get(i)).getGroup_depth());
               groupList.add(group);
            }
         }

         return groupList;
      } catch (Exception var32) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ResponseBody listGroupsInfo(String[] groupIds) {
      ResponseBody responseBody = new ResponseBody();
      DeviceGroupDao deviceGroupDao = new DeviceGroupDao();

      try {
         List groupList = new ArrayList();
         if (groupIds != null && groupIds.length > 0) {
            String[] groupArr = groupIds;

            for(int i = 0; i < groupArr.length; ++i) {
               Long groupId = Long.valueOf(groupArr[i]);
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, groupId);
               DeviceGroup deviceGroup = deviceGroupDao.getGroup(Integer.valueOf(groupArr[i]));
               DeviceGroupResource deviceGroupResource = new DeviceGroupResource();
               if (deviceGroup == null) {
                  deviceGroupResource.setGroupId(groupId);
                  deviceGroupResource.setDescription("Does not exist.");
                  groupList.add(deviceGroupResource);
               } else {
                  deviceGroupResource.setGroupId(deviceGroup.getGroup_id());
                  deviceGroupResource.setParentGroupId(deviceGroup.getP_group_id());
                  deviceGroupResource.setGroupDepth(deviceGroup.getGroup_depth());
                  deviceGroupResource.setGroupName(deviceGroup.getGroup_name());
                  deviceGroupResource.setDescription(deviceGroup.getDescription());
                  deviceGroupResource.setIsRoot(deviceGroup.getIs_root());
                  deviceGroupResource.setCreatorId(deviceGroup.getCreator_id());
                  deviceGroupResource.setCreateDate(deviceGroup.getCreate_date());
                  deviceGroupResource.setMinPriority(deviceGroup.getMin_priority());
                  deviceGroupResource.setAnalysisStatus(deviceGroup.getAnalysis_status());
                  groupList.add(deviceGroupResource);
               }
            }
         }

         responseBody.setItems(groupList);
         responseBody.setTotalCount(groupList.size());
         responseBody.setStatus("Success");
         return responseBody;
      } catch (Exception var10) {
         this.logger.error("[REST_v2.0][DEVICE GROUP SERVICE]", var10);
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceGroupCreation createGroup(DeviceGroupResource deviceGroupResource) throws Exception {
      RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceGroupResource.getParentGroupId());
      V2DeviceGroupCreation groupCreation = new V2DeviceGroupCreation();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      int groupId = false;
      Long root_group_id = -1L;
      String pGroupId = deviceGroupResource.getParentGroupId().toString();
      String newName = deviceGroupResource.getGroupName();
      String table = "MI_DMS_INFO_GROUP";
      String menu = "PremiumDevice";
      if (deviceGroupResource.getParentGroupId() <= 0L) {
         groupCreation.setStatus("Fail");
         groupCreation.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_CREATE_DEVICE_GROUP.getMessage());
         groupCreation.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_CREATE_DEVICE_GROUP.getCode());
         return groupCreation;
      } else {
         long depth = this.treeDao.get_GroupDepth(pGroupId, table);
         int groupId = this.treeDao.setGroupTreeCreate(menu, table, pGroupId, newName, depth + "", root_group_id, user.getUser_id());
         if (groupId != 0) {
            DeviceGroup newDeviceGroup = this.deviceGroupDao.getGroup((int)((long)groupId));
            groupCreation.setGroupId(newDeviceGroup.getGroup_id());
            groupCreation.setGroupName(newDeviceGroup.getGroup_name());
            groupCreation.setParentGroupId(newDeviceGroup.getP_group_id());
            groupCreation.setStatus("Success");
         } else {
            groupCreation.setStatus("Fail");
            groupCreation.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_CREATE_DEVICE_GROUP.getMessage());
            groupCreation.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_CREATE_DEVICE_GROUP.getCode());
         }

         return groupCreation;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Delete Authority')")
   public V2DeviceGroupDeletion deleteGroup(DeviceGroupResource deviceGroupResource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceGroupResource.getGroupId());
      V2DeviceGroupDeletion groupDeletion = new V2DeviceGroupDeletion();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      boolean result = false;
      String groupName = "";
      String groupId = String.valueOf(deviceGroupResource.getGroupId());
      String userOrg = userContainer.getUser().getOrganization();
      String userId = user.getUser_id();
      String table = "MI_DMS_INFO_GROUP";
      String menu = "PremiumDevice";
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      DeviceGroup group = groupDao.getGroup(Integer.parseInt(groupId));
      if (group.getP_group_id() <= 0L) {
         groupDeletion.setStatus("Fail");
         groupDeletion.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_CREATE_DEVICE_GROUP.getMessage());
         groupDeletion.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_CREATE_DEVICE_GROUP.getCode());
         return groupDeletion;
      } else {
         result = this.treeDao.setGroupTreeRemove(menu, table, userOrg, groupId, userId, (HttpServletRequest)null, (HttpServletResponse)null);
         if (result) {
            DeviceGroupDao deviceGroupDao = new DeviceGroupDao();
            groupName = deviceGroupDao.getGroupNameByGroupId(deviceGroupResource.getGroupId());
            deviceGroupResource.setGroupName(groupName);
            groupDeletion.setGroupId(String.valueOf(deviceGroupResource.getGroupId()));
            groupDeletion.setStatus("Success");
         } else {
            groupDeletion.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_DELETE_DEVICE_GROUP.getMessage());
            groupDeletion.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_DELETE_DEVICE_GROUP.getCode());
            groupDeletion.setStatus("Fail");
         }

         return groupDeletion;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceGroupRename renameGroup(String groupId, V2DeviceGroupRename body) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      V2DeviceGroupRename groupRename = new V2DeviceGroupRename();
      String menu = "PremiumDevice";
      String table = "MI_DMS_INFO_GROUP";
      LeftMenuGroupTreeDao tree_dao = new LeftMenuGroupTreeDao();
      User user = SecurityUtils.getLoginUser();
      if (user != null) {
         String userId = user.getUser_id();
         DeviceGroupInfo var9 = DeviceGroupInfoImpl.getInstance();
         DeviceGroup var10 = var9.getGroup(Integer.parseInt(groupId));
         if (var10.getP_group_id() <= 0L) {
            groupRename.setStatus("Fail");
            groupRename.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_RENAME_DEVICE_GROUP.getMessage());
            groupRename.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_RENAME_DEVICE_GROUP.getCode());
            return groupRename;
         } else {
            tree_dao.setGroupTreeRename(menu, table, groupId, body.getNewGroupName(), userId);
            DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
            deviceGroupInfo.updateCacheDeviceGroup();
            if (DeviceUtils.isSupportNOC()) {
               DeviceNocInfo nocDao = DeviceNocInfoImpl.getInstance();
               Boolean nocGroup = nocDao.isNocSupportGroup(Long.parseLong(groupId));
               if (nocGroup != null && nocGroup) {
                  DeviceNocManager nocService = DeviceNocManagerImpl.getInstance();
                  nocService.thingworxUpdateDeviceGroup(Long.parseLong(groupId));
               }
            }

            groupRename.setNewGroupName(body.getNewGroupName());
            groupRename.setStatus("Success");
            return groupRename;
         }
      } else {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      }
   }

   public static int safeLongToInt(long l) {
      if (l >= -2147483648L && l <= 2147483647L) {
         return (int)l;
      } else {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_CANNOT_CAST_VALUE, new String[]{String.valueOf(l)});
      }
   }

   private List treeResourcesBuilder(List deviceList, String searchGroupName, Long devicePriority, List groupList, boolean possibleSelectOrganization, boolean allEnabled, boolean disableByExistOfDevice, boolean videoWallMode, boolean syncMode, String isNumOfDevGroup) throws Exception {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      List res = new ArrayList();
      Iterator var13 = deviceList.iterator();

      while(var13.hasNext()) {
         DeviceGroup group = (DeviceGroup)var13.next();
         new V2DeviceGroupTreeResource();
         Long groupId = group.getGroup_id();
         Long parentGroupId = group.getP_group_id();
         String type = "";
         String groupName = "";
         Long deviceCount = 0L;
         Long priority = 0L;
         List groups = null;
         boolean isDataExist = false;
         boolean disabled = true;
         boolean opened = false;
         boolean selected = false;
         long deviceCountIncludedSubGroup = 0L;
         Long depth = group.getGroup_depth();
         new LinkedHashMap();
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         if (StringUtils.isEmpty(searchGroupName) && group.getGroup_depth() == 1L) {
            type = "ORGANIZATION";
            groupName = group.getGroup_name();
            if (possibleSelectOrganization) {
               disabled = false;
            }

            parentGroupId = group.getP_group_id();
            if (DeviceUtils.isDeviceGroupAuth(SecurityUtils.getUserContainer().getUser())) {
               deviceCountIncludedSubGroup = group.getTotal_count();
            } else {
               deviceCountIncludedSubGroup = groupDao.getTotalOrganizationDeviceCountByGroupId(groupId);
            }

            deviceCount = deviceCountIncludedSubGroup;
         } else {
            if (!StringUtils.isEmpty(searchGroupName)) {
               parentGroupId = group.getP_group_id();
            }

            deviceCountIncludedSubGroup = group.getTotal_count();
            priority = group.getMin_priority();
            String groupType = group.getGroup_type();
            String deviceType = DeviceUtils.getDeviceType(priority, groupType);
            type = deviceType;
            if (devicePriority >= 0L) {
               disabled = !allEnabled && devicePriority > priority;
            } else {
               disabled = !allEnabled && devicePriority != priority;
            }

            if (videoWallMode) {
               if (group.getVwt_id() != null && !group.getVwt_id().equals("")) {
                  disabled = false;
                  videoWallMode = true;
               } else {
                  disabled = true;
               }
            }

            groupName = group.getGroup_name();
            if (isNumOfDevGroup != null && isNumOfDevGroup.equalsIgnoreCase("true")) {
               deviceCount = group.getTotal_count();
            } else {
               deviceCount = group.getDevice_count();
            }

            if (groupList != null && groupList.size() > 0 && groupList.contains(group.getGroup_id())) {
               selected = true;
               opened = true;
            }

            if (disableByExistOfDevice) {
               if (group.getTotal_count() > 0L) {
                  disabled = false;
               } else {
                  disabled = true;
               }
            }

            if (!StringUtils.isEmpty(searchGroupName)) {
               groups = this.convertV2DeviceGroupList(deviceGroupInfo.getParentGroupNamePathByGroupId(group.getGroup_id()));
            }
         }

         V2DeviceGroupTreeResource treeResource = V2DeviceGroupTreeResource.V2DeviceGroupTreeResourceBuilder.aV2DeviceGroupTreeResource().groupId(groupId).groupName(groupName).parentGroupId(parentGroupId).priority(priority).type(type).videoWallMode(videoWallMode).resourceCount(deviceCount).isDataExist(isDataExist).statusDisapled(disabled).statusOpened(opened).statusSelected(selected).groupDepth(depth).parentsGroupIds(groups).deviceCountIncludedSubGroup(deviceCountIncludedSubGroup).build();
         res.add(treeResource);
      }

      return res;
   }

   @PreAuthorize("hasAnyAuthority('User Approval Authority', 'Statistics Manage Authority', 'Device Read Authority', 'Device Write Authority', 'Device Approval Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public List getDeviceGroupsWithChildren(V2DeviceGroupSearch body, HttpServletRequest request) throws Exception {
      String mode = body.getMode();
      boolean videoWallMode = false;
      boolean syncMode = false;
      String searchKey = body.getSearchKey();
      String programId = body.getProgramId();
      String searchText = StrUtils.nvl(body.getSearchText());
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      Long priority = body.getPriority();
      videoWallMode = body.isVideoWallMode();
      syncMode = body.isSyncMode();
      if (videoWallMode && syncMode) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_DEVICE_MODE);
      } else {
         List defaultCheckedIds = body.getDefaultCheckedIds();
         boolean allEnabled = body.isAllEnabled();
         boolean disableByExistOfDevice = body.isDisableByExistOfDevice();
         boolean possibleSelectOrganization = body.isPossibleSelectOrganization();
         DeviceGroupInfo deviceGroup = DeviceGroupInfoImpl.getInstance();
         List groupList = new ArrayList();
         User user = SecurityUtils.getUserContainer().getUser();
         V2DeviceGroupServiceImpl.ScheduleType scheduleType = null;
         if (programId != null) {
            searchKey = "".equals(StrUtils.nvl(searchKey)) ? "CONTENT_SCHEDULE_ID" : searchKey;
            scheduleType = V2DeviceGroupServiceImpl.ScheduleType.findBy(searchKey);
            if (defaultCheckedIds == null) {
               List checkGroupList = null;
               switch(scheduleType) {
               case CONTENT_SCHEDULE_ID:
                  checkGroupList = deviceGroup.getScheduleMappingDeviceGroupAuth(programId, (String)null, true);
                  break;
               case MESSAGE_SCHEDULE_ID:
                  checkGroupList = deviceGroup.getMessageMappingDeviceGroupAuth(programId, (String)null, true);
                  break;
               case EVENT_SCHEDULE_ID:
                  checkGroupList = deviceGroup.getEventMappingDeviceGroupAuth(programId, (String)null, true);
               }

               if (checkGroupList != null && checkGroupList.size() > 0) {
                  Iterator var20 = checkGroupList.iterator();

                  while(var20.hasNext()) {
                     DeviceGroup checkGroup = (DeviceGroup)var20.next();
                     groupList.add(checkGroup.getGroup_id());
                  }
               }
            } else if (defaultCheckedIds != null && !defaultCheckedIds.isEmpty()) {
               Iterator var37 = defaultCheckedIds.iterator();

               while(var37.hasNext()) {
                  Long temp = (Long)var37.next();
                  groupList.add(temp);
               }
            }
         }

         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         long deviceOrganizationId = 0L;
         long userOrganizationId = 0L;
         String organName = SecurityUtils.getUserContainer().getUser().getOrganization();
         Long currentOrganizationId = 0L;
         if (user.isMu()) {
            UserInfo userInfo = UserInfoImpl.getInstance();
            currentOrganizationId = userInfo.getCurMngOrgId(user.getUser_id());
            organName = userInfo.getOrganGroupName(currentOrganizationId);
         }

         if (body.getOrganizationId() != null) {
            organName = userGroupInfo.getGroupNameByGroupId(body.getOrganizationId());
            deviceOrganizationId = deviceGroup.getOrganGroupIdByName(organName);
            userOrganizationId = body.getOrganizationId();
         } else if (!RoleUtils.isServerAdminRole(SecurityUtils.getUserContainer().getUser())) {
            deviceOrganizationId = deviceGroup.getOrganGroupIdByName(organName);
            userOrganizationId = userGroupInfo.getOrgGroupIdByName(organName);
         }

         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         Map infoMap = serverSetupDao.getServerInfoByOrgId(userOrganizationId);
         if (user.isMu()) {
            infoMap = serverSetupDao.getServerInfoByOrgId(currentOrganizationId);
         }

         String isNumOfDevGroup = null;
         if (infoMap != null && infoMap.get("DEVICE_TOTAL_COUNT") != null) {
            isNumOfDevGroup = infoMap.get("DEVICE_TOTAL_COUNT").toString();
         }

         List deviceList = null;
         AbilityUtils ability = new AbilityUtils();
         boolean scheduleWrite = ability.checkAuthority("Content Schedule Write");
         boolean scheduleManage = ability.checkAuthority("Content Schedule Manage");
         Boolean permissions_func;
         if (!body.getAllGroup() || !scheduleWrite && !scheduleManage) {
            permissions_func = DeviceUtils.isDeviceGroupAuth(user);
         } else {
            permissions_func = false;
         }

         List deviceOrgIds;
         if (permissions_func) {
            deviceOrgIds = deviceGroup.getAllAuthorityDeviceGroups(user.getUser_id());
            if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
               deviceList = this.getDeviceGroupPermissionList(searchText, deviceOrganizationId, deviceOrgIds);
            }
         } else {
            if (user.getRoot_group_id() == 0L) {
               deviceOrgIds = DeviceUtils.getOrgGroupIdByUserId(user.getUser_id());
               if (deviceOrgIds != null && deviceOrgIds.size() > 0) {
                  deviceList = deviceGroup.getAllDeviceGroupsByGroupName(deviceOrgIds, searchText);
               }
            }

            if (deviceList == null) {
               deviceList = deviceGroup.getAllDeviceGroupsByGroupName(deviceOrganizationId, searchText);
            }
         }

         List res = new ArrayList();
         if (deviceList != null && !deviceList.isEmpty()) {
            res = this.treeResourcesBuilder(deviceList, searchText, priority, groupList, possibleSelectOrganization, allEnabled, disableByExistOfDevice, videoWallMode, syncMode, isNumOfDevGroup);
         }

         return (List)res;
      }
   }

   private Map calcGroupCountMap(Map pGroupCountMap, DeviceGroup group, boolean add) {
      Long count = (Long)pGroupCountMap.get(group.getP_group_id());
      if (add) {
         if (null == count) {
            count = 0L;
         }

         pGroupCountMap.put(group.getP_group_id(), count + group.getTotal_count());
      } else {
         if (null == count) {
            return pGroupCountMap;
         }

         pGroupCountMap.put(group.getP_group_id(), count - group.getTotal_count());
      }

      return pGroupCountMap;
   }

   public static List deepCopy(List origin) {
      return (List)origin.stream().map(DeviceGroup::clone).collect(Collectors.toList());
   }

   public List getDeviceGroupPermissionList(String searchGroupName, long deviceOrganizationId, List permissionDeviceGroupList) throws SQLException {
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      List deviceGroupList = deepCopy(deviceGroupInfo.getAllDeviceGroupsByGroupName(deviceOrganizationId, searchGroupName));
      Map pGroupCountMap = new HashMap();
      User user = SecurityUtils.getLoginUser();
      long organId = deviceGroupInfo.getOrganGroupIdByName(user.getOrganization());
      Iterator itr;
      if (organId == 0L) {
         itr = permissionDeviceGroupList.iterator();

         while(itr.hasNext()) {
            Long groupId = (Long)itr.next();
            Map organMap = deviceGroupInfo.getDeviceOrganizationByGroupId(groupId.intValue());
            ((Map)pGroupCountMap).put((Long)organMap.get("GROUP_ID"), 0L);
         }
      } else {
         ((Map)pGroupCountMap).put(organId, 0L);
      }

      itr = deviceGroupList.iterator();

      while(true) {
         while(true) {
            DeviceGroup group;
            do {
               if (!itr.hasNext()) {
                  itr = deviceGroupList.iterator();

                  while(itr.hasNext()) {
                     group = (DeviceGroup)itr.next();
                     if (group.getGroup_depth() <= 1L) {
                        Long count = (Long)((Map)pGroupCountMap).get(group.getGroup_id());
                        if (null == count) {
                           itr.remove();
                        } else {
                           group.setTotal_count(count);
                        }
                     }
                  }

                  return deviceGroupList;
               }

               group = (DeviceGroup)itr.next();
            } while(group.getGroup_depth() <= 1L);

            if (!permissionDeviceGroupList.contains(group.getGroup_id())) {
               itr.remove();
               Iterator subItr = deviceGroupList.iterator();

               while(subItr.hasNext()) {
                  DeviceGroup subGroup = (DeviceGroup)subItr.next();
                  if (subGroup.getGroup_depth() > 1L && group.getGroup_id().equals(subGroup.getP_group_id())) {
                     subGroup.setP_group_id(group.getP_group_id());
                  }
               }
            } else {
               pGroupCountMap = this.calcGroupCountMap((Map)pGroupCountMap, group, true);
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Move Authority')")
   public V2CommonGroupResource moveGroup(Long groupId, Long destinationGroupId, Long changeInGroupDepth) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, groupId);
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, destinationGroupId);
      V2CommonGroupResource group = new V2CommonGroupResource();
      String table = "MI_DMS_INFO_GROUP";
      String strGroupId = String.valueOf(groupId);
      String parentGroupId = String.valueOf(destinationGroupId);
      LeftMenuGroupTreeDao tree_dao = new LeftMenuGroupTreeDao();
      if ("#".equals(parentGroupId)) {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"destination group ID"});
      } else {
         long depth = tree_dao.get_GroupDepth(parentGroupId, table);
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         String gId = groupDao.getDeviceGroupRoot(Integer.parseInt(strGroupId));
         String pgId = groupDao.getDeviceGroupRoot(Integer.parseInt(parentGroupId));
         if (!gId.equals(pgId)) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_HAVE_TO_MOVE_SAME_ORGANIZATION);
         } else {
            String beforeOrgName = groupDao.getOrgNameByGroupId((long)Integer.valueOf(strGroupId));
            boolean result = groupDao.moveGroup(Integer.valueOf(strGroupId), Integer.valueOf(parentGroupId), changeInGroupDepth);
            groupDao.updateOrgNameByGroupId(beforeOrgName, Long.valueOf(strGroupId), Long.valueOf(parentGroupId));
            groupDao.updateCacheDeviceGroup();
            if (result) {
               group.setGroupDepth(depth);
               group.setGroupId(groupId);
               group.setParentGroupId(Long.valueOf(parentGroupId));
               return group;
            } else {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
            }
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Move Authority')")
   public V2CommonBulkResultResource moveDevices(List deviceIds, String moveGroupId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(moveGroupId));
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var34) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupDao devGroupDao = new DeviceGroupDao();
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
      MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
      MessageInfo msgInfo = MessageInfoImpl.getInstance();
      ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
      JobManager jobMgr = JobManagerImpl.getInstance();
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      User user = SecurityUtils.getLoginUser();
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      String orgName = null;
      String userId = null;
      if (user == null) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_NOT_FOUND_USER_DETAIL);
      } else {
         orgName = user.getOrganization();
         userId = user.getUser_id();
         Map var18 = deviceGroupDao.getOrgGroupId(orgName);
         String var19 = ((Long)var18.get("group_id")).toString();
         Iterator var20 = deviceIds.iterator();

         while(var20.hasNext()) {
            String deviceId = (String)var20.next();

            try {
               V2DeviceGroupMovement result = new V2DeviceGroupMovement();
               boolean isVwlGroup = deviceGroupDao.isVwlGroup(moveGroupId);
               if (isVwlGroup) {
                  this.logger.error("vwllayout_group_delete_fail");
                  failList.add(deviceId);
               } else {
                  String moveGroupName = deviceGroupDao.getGroup(Integer.parseInt(moveGroupId)).getGroup_name();
                  Map map = new HashMap();
                  map.put("device_id", deviceId);
                  map.put("group_id", Long.parseLong(moveGroupId));
                  String prvGroupName = (String)deviceGroupDao.getGroupNameByDeviceId(deviceId).get("group_name");
                  Device device = deviceDao.getDevice(deviceId);
                  map.put("device_type", device.getDevice_type());
                  String organization = null;
                  if (map.get("organization") == null) {
                     organization = devGroupDao.getOrgNameByGroupId((Long)map.get("group_id"));
                  } else {
                     organization = (String)map.get("organization");
                  }

                  map.put("organization", organization);
                  if (device.getChild_cnt() != null && device.getChild_cnt() != 0L) {
                     map.put("child_cnt", device.getChild_cnt());
                  }

                  result.setDeviceId(deviceId);
                  result.setGroupId(moveGroupId);
                  result.setDeviceType(device.getDevice_type());
                  result.setOrganizationName(organization);
                  result.setChildDeviceCount(device.getChild_cnt());
                  String oldGroupId = deviceDao.getDeviceGroupIdByDeviceId(deviceId);
                  deviceDao.removeDeviceTotalCount(Long.valueOf(oldGroupId), 1);
                  deviceDao.deviceTotalCount(Long.valueOf(moveGroupId), 1);
                  int cnt = deviceDao.setDeviceGroupId(map);
                  if (cnt > 0) {
                     DevicePreconfigInfo preconfigInfo = DevicePreconfigInfoImpl.getInstance();
                     preconfigInfo.deployToDevice(device);
                  }

                  monMgr.scheduleReload(deviceId, 1);
                  ScheduleInfoEntity schEntity = monMgr.getScheduleStatus(deviceId);
                  if (schEntity != null) {
                     schInfo.deploySchedule(deviceId, schEntity.getScheduleId(), schEntity.getScheduleVersion());
                     msgInfo.deployMessage(deviceId, schEntity.getMessageId(), schEntity.getMessageVersion());
                     Device tmpDev = deviceDao.getDevice(deviceId);
                     jobMgr.deployJobSchedule("", tmpDev);
                     result.setResult("Success");
                     result.setReason("device_group_move_success");
                     deviceGroupDao.updateCacheDeviceGroup();
                     successList.add(result);
                  } else {
                     failList.add(deviceId);
                  }
               }
            } catch (Exception var33) {
               this.logger.error("[REST_v2.0][DEVICE GROUP SERVICE][moveDevices] " + ExceptionCode.RES909[2] + " deviceId : " + deviceId, var33);
               failList.add(deviceId);
            }
         }

         resource.setSuccessList(successList);
         resource.setFailList(failList);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource backupTagChecking(String groupId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceSystemSetupConfManager SystemSetupDao = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
      List successList = new ArrayList();
      List failList = new ArrayList();
      List deviceList = deviceDao.getNotChildDeviceIdListByGroup(Integer.parseInt(groupId));
      String tagValueList = null;
      String tempDeviceId = null;
      String tempDeviceName = null;

      for(int i = 0; i < deviceList.size(); ++i) {
         try {
            tempDeviceId = (String)((Map)deviceList.get(i)).get("device_id");
            tagValueList = SystemSetupDao.getTagValueListString(tempDeviceId);
            tempDeviceName = deviceDao.getDeviceNameById(tempDeviceId);
            V2DeviceTag tagInfo = new V2DeviceTag();
            tagInfo.setDeviceName(tempDeviceName);
            tagInfo.setDeviceId(tempDeviceId);
            tagInfo.setTagNameList(tagValueList);
            successList.add(tagInfo);
         } catch (Exception var13) {
            this.logger.error("", var13);
            failList.add(tempDeviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource backupClear(String groupId, HttpServletRequest request) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      groupId = StrUtils.nvl(groupId);
      this.logger.info("[BACKUP - Clear] groupID: " + groupId);
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      DeviceConfManager deviceConfManager = DeviceConfManagerImpl.getInstance();
      int tempGroupId = Integer.parseInt(groupId);
      List backupPlayerList = groupDao.getRedundantDeviceIdbyGroupId(tempGroupId);
      List backupPlayerListString = new ArrayList();

      String refresh_interval;
      for(int i = 0; i < backupPlayerList.size(); ++i) {
         refresh_interval = ((Map)backupPlayerList.get(i)).get("device_id").toString();

         try {
            backupPlayerListString.add(refresh_interval);
            deviceDao.addRedundancyStatus(refresh_interval, false);
            deviceDao.deleteWaitingMo(refresh_interval);
            deviceConfManager.reqSetDeviceConnStatus(refresh_interval, ".MO.DEVICE_CONF.PROCESS.CONN_STATUS", "Restore");
            successList.add(refresh_interval);
         } catch (Exception var20) {
            this.logger.error("[BACKUP PLAY - CLEAR] BACKUP PLAYER  Restore Tags -send MO MO.DEVICE_CONF.PROCESS.CONN_STATUS -- backupDeviceId: " + refresh_interval + " backupChangeInfoValue: Restore", var20);
            failList.add(refresh_interval);
         }
      }

      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      refresh_interval = CommonConfig.get("device.monitoring.refresh_interval");
      List deviceList = deviceDao.getDeviceIdListByGroup(Integer.parseInt(groupId));

      for(int j = 0; j < deviceList.size(); ++j) {
         DeviceSystemSetupConf info = new DeviceSystemSetupConf();
         String tempDeviceID = (String)((Map)deviceList.get(j)).get("device_id");

         try {
            if (backupPlayerListString.contains(tempDeviceID)) {
               Device device = deviceDao.getDevice(tempDeviceID);
               if (device.getContents_download_mode() != null) {
                  info.setContents_download_mode(CommonDataConstants.DOWNLOAD_MODE_ASSIGNED_ONLY);
               }
            }

            info.setMonitoring_interval(Long.parseLong(refresh_interval));
            info.setDevice_id(tempDeviceID);
            confManager.reqSetSystemSetupToDevice(info, request.getSession().getId(), "PREMIUM");
            this.logger.info("[BACKUP - CLEAR] Restore Monitoring Interval deviceID: " + tempDeviceID + " Monitoring Interval: " + refresh_interval);
            if (!successList.contains(tempDeviceID)) {
               successList.add(tempDeviceID);
            }
         } catch (Exception var21) {
            this.logger.error("[BACKUP - CLEAR] Restore Monitoring Interval deviceID: " + tempDeviceID + " Monitoring Interval: " + refresh_interval, var21);
            if (!failList.contains(tempDeviceID)) {
               failList.add(tempDeviceID);
            }
         }
      }

      try {
         groupDao.setIsRedundancy(Long.parseLong(groupId), false);
         deviceDao.deleteBackupPlayer(tempGroupId);
         deviceDao.deleteBackupTargetPlayer(tempGroupId);
      } catch (Exception var19) {
         this.logger.error(var19);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SQLERROR);
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceSetSboxLayoutResource connectionCheckByGroupId(Long groupId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, groupId);
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2DeviceSetSboxLayoutResource resource = new V2DeviceSetSboxLayoutResource();
      Locale locale = SecurityUtils.getLocale();
      String groupIdStr = groupId.toString();
      boolean isAllConncected = true;
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      List deviceIdList = deviceGroupInfo.getChildDeviceIdList(Integer.parseInt(groupIdStr), false);

      for(int i = 0; i < deviceIdList.size(); ++i) {
         if (!DeviceUtils.isConnected((String)deviceIdList.get(i))) {
            isAllConncected = false;
            break;
         }
      }

      if (isAllConncected) {
         EncryptionManager encManager = EncryptionManagerImpl.getInstance();
         String token = null;
         String loginId = userContainer.getUser().getUser_id();
         TokenRegistry tr = TokenRegistry.getTokenRegistry();
         token = tr.issueToken(loginId, userContainer, "PREMIUM");
         String userPassword = encManager.getEncryptionPassword(loginId, token);
         resource.setUserId(loginId);
         resource.setToken(token);
         resource.setLayoutAuthorPath("/" + StrUtils.nvl(CommonConfig.get("layoutauthor.context")) + "/index.jsp");
         resource.setLanguage(String.valueOf(locale));
         resource.setDeviceGroupId(groupIdStr);
         resource.setUserPassword(userPassword);
         return resource;
      } else {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_ALL_DEVICE_CONNECT_FAIL);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2VwlLayoutManagerResource vwlLayoutManager(Long groupId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, groupId);
      String groupIdStr = groupId.toString();
      VwlLayoutManager vwlLayoutDao = VwlLayoutManagerImpl.getInstance();
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      String curr_vwt_id = deviceGroupDao.getVwlLayoutIdByGroupId(groupIdStr);
      List vwlLayoutList = vwlLayoutDao.getVwlLayoutsByGroupId(Integer.parseInt(groupIdStr));
      V2VwlLayoutManagerResource resource = new V2VwlLayoutManagerResource();
      resource.setVwlLayoutList(vwlLayoutList);
      resource.setGroupId(groupId);
      resource.setCurrVwtId(curr_vwt_id);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceBackupModeResource backupModeSelect(String groupId, V2CommonIds body) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      V2DeviceBackupModeResource resource = new V2DeviceBackupModeResource();
      String programName = "-";
      List backupPlay = body.getIds();
      int listSize = 0;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      List targetList = new ArrayList();
      List backupList = new ArrayList();
      Long groupIdLong = Long.parseLong(groupId);
      List backupDeviceGroupList = null;
      DeviceMonitoring programInfo = null;

      try {
         backupDeviceGroupList = deviceDao.getDeviceListByGroupId(groupIdLong);
         programInfo = deviceDao.getProgramInfoByDeviceGroupId(groupIdLong);
      } catch (Exception var18) {
         this.logger.error("[REST_v2.0][DEVICE GROUP SERVICE][backupModeSelect] Invalid groupId.", var18);
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"groupId"});
      }

      if (programInfo != null) {
         programName = programInfo.getProgram_name();
      }

      if (backupDeviceGroupList != null) {
         listSize = backupDeviceGroupList.size();
      }

      if (listSize <= 0) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"list of device information by groupId"});
      } else {
         boolean flag = false;

         for(int i = 0; i < listSize; ++i) {
            Device tempDevice = (Device)backupDeviceGroupList.get(i);
            if (tempDevice == null) {
               flag = true;
               break;
            }

            boolean isChecked = true;

            for(int j = 0; j < backupPlay.size(); ++j) {
               if (tempDevice.getDevice_id().equalsIgnoreCase((String)backupPlay.get(j))) {
                  isChecked = false;
                  break;
               }
            }

            if (isChecked) {
               V2DeviceTargetResource targetInfo = new V2DeviceTargetResource();
               targetInfo.setDeviceName(tempDevice.getDevice_name());
               targetInfo.setDeviceId(tempDevice.getDevice_id());
               targetInfo.setScheduleName(programName);
               targetInfo.setIpAddress(tempDevice.getIp_address());
               targetList.add(targetInfo);
            } else {
               V2DeviceBackupResource backupInfo = new V2DeviceBackupResource();
               backupInfo.setBackupPlayerId(tempDevice.getDevice_id());
               backupInfo.setBackupPlayerName(tempDevice.getDevice_name());
               backupList.add(backupInfo);
            }
         }

         if (flag) {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"device information"});
         } else {
            resource.setTargetList(targetList);
            resource.setBackupList(backupList);
            return resource;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceBackupPlayInfoRes saveBackupPlayConfiguration(long groupId, V2DeviceBackupPlayInfoReq backupPlayInfo) throws Exception {
      List deviceIds = backupPlayInfo.getDeviceIds();
      String backupMode = backupPlayInfo.getBackupMode();
      List backupMapInfo = backupPlayInfo.getBackupMapInfos();
      Integer tempBackupCnt = backupPlayInfo.getDeviceCount();
      boolean isOwnContent = backupPlayInfo.getIsOwnContent();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      boolean is_auto = false;
      if (backupMode.equalsIgnoreCase("AUTOMATE")) {
         is_auto = true;
      }

      int backupDeviceCnt = 0;
      if (tempBackupCnt != null) {
         backupDeviceCnt = tempBackupCnt;
      }

      this.logger.info("[BACKUP - SET] groupID: " + groupId + " Backup Mode: " + backupMode + " Backup Device(s): " + ConvertUtil.convertListToStringWithSeparator(deviceIds, ",") + " ownContent : " + isOwnContent);
      String tempDeviceID = null;
      BackupPlayEntity backupInfo = new BackupPlayEntity();
      V2DeviceBackupPlayInfoRes res = new V2DeviceBackupPlayInfoRes();
      backupInfo.setGroup_id(groupId);
      backupInfo.setBackup_mode(backupMode);
      if (isOwnContent) {
         backupInfo.setOwn_content(true);
      }

      for(int i = 0; i < backupDeviceCnt; ++i) {
         tempDeviceID = (String)deviceIds.get(i);
         deviceDao.addRedundancyStatus(tempDeviceID, true);

         try {
            deviceDao.addDeviceWaitingMo(tempDeviceID);
         } catch (Exception var28) {
            this.logger.info(var28.getMessage());
         }

         backupInfo.setBackup_device_id(tempDeviceID);
         backupInfo.setBusy_level(0);

         try {
            deviceDao.addBackupPlayer(backupInfo);
         } catch (Exception var27) {
            this.logger.info(var27.getMessage());
         }
      }

      DeviceSystemSetupConf info = new DeviceSystemSetupConf();
      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      Long backupRefreshInterval = 3L;
      if (CommonConfig.get("device.monitoring.backupPlay_refresh_interval") != null && !CommonConfig.get("device.monitoring.backupPlay_refresh_interval").isEmpty()) {
         backupRefreshInterval = Long.parseLong(CommonConfig.get("device.monitoring.backupPlay_refresh_interval"));
      }

      List backupPlayerMappings = new ArrayList();
      if (!is_auto && backupMapInfo != null && !backupMapInfo.isEmpty()) {
         for(int j = 0; j < backupMapInfo.size(); ++j) {
            V2DeviceBackupMapInfo backupMap = (V2DeviceBackupMapInfo)backupMapInfo.get(j);
            Map tempBackupMap = new HashMap();
            if (backupMap != null && !backupMap.getBackupPlayId().isEmpty() && !backupMap.getTargetDeviceId().isEmpty()) {
               tempBackupMap.put("backupPlayId", backupMap.getBackupPlayId());
               tempBackupMap.put("targetDeviceId", backupMap.getTargetDeviceId());
            }

            backupPlayerMappings.add(j, tempBackupMap);
         }
      }

      List deviceList = deviceDao.getDeviceIdListByGroup(CommonUtils.safeLongToInt(groupId));

      for(int j = 0; j < deviceList.size(); ++j) {
         tempDeviceID = (String)((Map)deviceList.get(j)).get("device_id");
         boolean isBackupPlayer = deviceDao.isRedundancyDevice(tempDeviceID);
         if (isBackupPlayer) {
            Device device = deviceDao.getDevice(tempDeviceID);
            if (device.getContents_download_mode() != null) {
               info.setContents_download_mode(CommonDataConstants.DOWNLOAD_MODE_ALL);
            }
         } else {
            backupInfo.setDevice_id(tempDeviceID);
            if (is_auto) {
               backupInfo.setBackup_device_id("");
            } else {
               for(int k = 0; k < backupPlayerMappings.size(); ++k) {
                  String tempTargetDeviceId = StrUtils.nvl((String)((Map)backupPlayerMappings.get(k)).get("targetDeviceId"));
                  String tempBackupPlayId = StrUtils.nvl((String)((Map)backupPlayerMappings.get(k)).get("backupPlayId"));
                  if (!tempTargetDeviceId.isEmpty() && tempTargetDeviceId.equalsIgnoreCase(tempDeviceID) && !tempBackupPlayId.isEmpty()) {
                     backupInfo.setBackup_device_id(tempBackupPlayId);
                  }
               }
            }

            try {
               deviceDao.addBackupTargetPlayer(backupInfo);
            } catch (Exception var26) {
               this.logger.info(var26.getMessage());
            }
         }

         info.setMonitoring_interval(backupRefreshInterval);
         info.setDevice_id(tempDeviceID);
         confManager.reqSetSystemSetupToDevice(info, UUID.randomUUID().toString(), "PREMIUM");
         this.logger.info("[BACKUP - SET] Set Monitoring Interval deviceID: " + tempDeviceID + " Monitoring Interval: " + backupRefreshInterval);
      }

      groupDao.setIsRedundancy(groupId, true);
      res.setDeviceId(backupInfo.getDevice_id());
      res.setBackupDeviceId(backupInfo.getBackup_device_id());
      res.setBusyLevel(backupInfo.getBusy_level());
      res.setWaitingMoCount(backupInfo.getWaiting_mo_count());
      res.setBackupMode(backupInfo.getBackup_mode());
      res.setGroupId(backupInfo.getGroup_id());
      res.setIsOwnContent(backupInfo.getOwn_content());
      res.setBackupRefreshInterval(backupRefreshInterval);
      return res;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceAvailableMenuResource availableMenu(String groupId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      V2DeviceAvailableMenuResource resource = new V2DeviceAvailableMenuResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      boolean isRedundancySet = false;
      boolean isRedundancyGroup = false;
      boolean isEnableRedundancy = false;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      DeviceGroup deviceGroup = groupDao.getGroup(Integer.parseInt(groupId));
      Long groupPriority = deviceGroup.getMin_priority();
      boolean isVwlGroup;
      Menu backupPlay;
      Menu backupPlaySetting;
      if (!groupId.equals("0") && deviceGroup.getGroup_depth() > 1L && userContainer.checkAuthority("Device Control")) {
         String deviceGroupType = deviceGroup.getGroup_type();
         if (deviceGroupType != null && !deviceGroupType.equals("") && (groupPriority >= 10L || groupPriority == 110L) && !deviceGroupType.equalsIgnoreCase("3rdPartyPLAYER")) {
            boolean allowVwlLayoutMng = true;
            if (deviceGroupType.equalsIgnoreCase("LEDBOX")) {
               allowVwlLayoutMng = false;
            }

            if (allowVwlLayoutMng) {
               Menu vwlLayoutMng = new Menu();
               vwlLayoutMng.setName("vwlLayoutMng");
               vwlLayoutMng.setSubMenu(true);
               resource.setVwlLayoutMng(vwlLayoutMng);
               Menu vwlLayoutSetting = new Menu();
               vwlLayoutSetting.setName("COM_TEXT_SETTING_P");
               vwlLayoutSetting.setUrl("");
               vwlLayoutSetting.setSubKey("vwlLayoutNew");
               vwlLayoutMng.addSubMenu(vwlLayoutSetting);
               isVwlGroup = groupDao.isVwlGroup(groupId);
               if (isVwlGroup) {
                  backupPlay = new Menu();
                  backupPlay.setName("COM_DID_VWL_PREVIEW");
                  backupPlay.setUrl("");
                  backupPlay.setSubKey("vwlLayoutPreview");
                  vwlLayoutMng.addSubMenu(backupPlay);
                  backupPlaySetting = new Menu();
                  backupPlaySetting.setName("BUTTON_CANCEL_P");
                  backupPlaySetting.setUrl("");
                  backupPlaySetting.setSubKey("vwlLayoutCancel");
                  vwlLayoutMng.addSubMenu(backupPlaySetting);
               }
            }
         }
      }

      ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
      Map infoMap = serverSetupDao.getServerInfoByOrgId(0L);
      int deviceCount = deviceDao.getDeviceCountBygroupId(Long.parseLong(groupId));
      String deviceType = deviceGroup.getGroup_type();
      isVwlGroup = (Boolean)infoMap.get("REDUNDANCY_ENABLE");
      isEnableRedundancy = false;
      if (isVwlGroup) {
         isRedundancyGroup = groupDao.isRedundancyGroup(Integer.parseInt(groupId));
         if (isRedundancyGroup) {
            if (groupDao.getRedundantDeviceIdbyGroupId(Integer.parseInt(groupId)).isEmpty()) {
               isRedundancySet = false;
            } else {
               isRedundancySet = true;
            }
         }

         if (deviceType != null && (groupPriority >= 10L || groupPriority == -2L)) {
            isEnableRedundancy = true;
         }
      }

      if (isEnableRedundancy) {
         backupPlay = new Menu();
         backupPlay.setName("MIS_SID_20_BACKUP_PLAY");
         backupPlay.setSubMenu(true);
         if (isRedundancySet) {
            resource.setBackupPlay(backupPlay);
            backupPlaySetting = new Menu();
            backupPlaySetting.setName("BUTTON_CANCEL_P");
            backupPlaySetting.setSubKey("backupPlayCancel");
            backupPlay.addSubMenu(backupPlaySetting);
         } else if (deviceCount > 1) {
            resource.setBackupPlay(backupPlay);
            backupPlaySetting = new Menu();
            backupPlaySetting.setName("COM_TEXT_SETTING_P");
            backupPlaySetting.setSubKey("backupPlaySetting");
            backupPlay.addSubMenu(backupPlaySetting);
         }
      }

      SlmLicenseManager slmlicenseMgr = SlmLicenseManagerImpl.getInstance();
      int result = slmlicenseMgr.getLicenseCountByProductCode("010D0E");
      if (result > 0 && !groupId.toString().equals("0") && deviceGroup.getGroup_depth() > 1L) {
         Menu analyticsStoreSetting = new Menu();
         analyticsStoreSetting.setName("Analytics Store");
         analyticsStoreSetting.setSubMenu(true);
         Menu analyticsStoreEnable = new Menu();
         analyticsStoreEnable.setName("TEXT_ENABLE2_P");
         analyticsStoreEnable.setSubKey("analyticsStoreEnable");
         analyticsStoreSetting.addSubMenu(analyticsStoreEnable);
         Menu analyticsStoreCancel = new Menu();
         analyticsStoreCancel.setName("BUTTON_CANCEL_P");
         analyticsStoreCancel.setSubKey("analyticsStoreCancel");
         analyticsStoreSetting.addSubMenu(analyticsStoreCancel);
         resource.setAnalyticsStoreSetting(analyticsStoreSetting);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceVwlPreviewResorce vwlPreview(String groupId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      VwlLayoutDao vwldao = new VwlLayoutDao();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      String vwt_id = groupDao.getVwlLayoutIdByGroupId(groupId);
      if (StrUtils.nvl(vwt_id).equals("")) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"vwtId"});
      } else {
         ArrayList layoutResult = new ArrayList();
         vwldao.getVwlLayoutGroupId(layoutResult, groupId, vwt_id);
         List vwlLayout = null;
         vwlLayout = vwldao.getVwlLayoutVwt(vwt_id);
         new ScreenCaptureEntity();
         ScreenCaptureDAO scDao = new ScreenCaptureDAO();
         V2DeviceVwlPreviewResorce resource = new V2DeviceVwlPreviewResorce();
         if (((VwlVwtLayout)vwlLayout.get(0)).getIs_linear()) {
            resource.setLinear(true);
         } else {
            resource.setLinear(false);
         }

         int width = 0;
         int height = 0;
         resource.setxCount(String.valueOf(((VwlVwtLayout)vwlLayout.get(0)).getX_count()));
         resource.setyCount(String.valueOf(((VwlVwtLayout)vwlLayout.get(0)).getY_count()));
         resource.setVwtId(((VwlVwtLayout)vwlLayout.get(0)).getVwt_id());
         List list = new ArrayList();

         for(int i = 0; i < layoutResult.size(); ++i) {
            V2DeviceSubVwlPreviewResource monitor = new V2DeviceSubVwlPreviewResource();
            long position_x = 0L;
            long position_y = 0L;
            long bezel_leftright = 0L;
            long bezel_topbottom = 0L;
            if (((VwlDeviceLayout)layoutResult.get(i)).getPosition_x() != null) {
               position_x = ((VwlDeviceLayout)layoutResult.get(i)).getPosition_x();
            }

            if (((VwlDeviceLayout)layoutResult.get(i)).getPosition_y() != null) {
               position_y = ((VwlDeviceLayout)layoutResult.get(i)).getPosition_y();
            }

            if (((VwlDeviceLayout)layoutResult.get(i)).getBezel_leftright() != null) {
               bezel_leftright = ((VwlDeviceLayout)layoutResult.get(i)).getBezel_leftright();
            }

            if (((VwlDeviceLayout)layoutResult.get(i)).getBezel_topbottom() != null) {
               bezel_topbottom = ((VwlDeviceLayout)layoutResult.get(i)).getBezel_topbottom();
            }

            monitor.setDeviceId(((VwlDeviceLayout)layoutResult.get(i)).getDevice_id());
            monitor.setWidth(((VwlDeviceLayout)layoutResult.get(i)).getWidth());
            monitor.setHeight(((VwlDeviceLayout)layoutResult.get(i)).getHeight());
            monitor.setPositionX((int)position_x);
            monitor.setPositionY((int)position_y);
            monitor.setBezelLeftright((int)bezel_leftright);
            monitor.setBezelLeftright((int)bezel_topbottom);
            monitor.setMapId(String.valueOf(((VwlDeviceLayout)layoutResult.get(i)).getMap_id()));
            monitor.setIpAddress(String.valueOf(((VwlDeviceLayout)layoutResult.get(i)).getIp_address()));
            monitor.setDeviceName(String.valueOf(((VwlDeviceLayout)layoutResult.get(i)).getDevice_name()));
            ScreenCaptureEntity scInfo = scDao.selInfoById(((VwlDeviceLayout)layoutResult.get(i)).getDevice_id());
            if (scInfo != null) {
               String[] fileName = scInfo.getFile_name().split("\\.");
               if (fileName.length >= 2) {
                  monitor.setImageUrl("/restapi/v2.0/cms/contents/thumbnails/CAPTURE?capturedFileName=" + ((VwlDeviceLayout)layoutResult.get(i)).getDevice_id() + ".jpg");
                  monitor.setImageId("CAPTURE");
                  monitor.setImageFilename("capturedFileName=" + ((VwlDeviceLayout)layoutResult.get(i)).getDevice_id() + ".jpg");
               }
            }

            if (width <= safeLongToInt(((VwlDeviceLayout)layoutResult.get(i)).getPosition_x()) + safeLongToInt(((VwlDeviceLayout)layoutResult.get(i)).getWidth())) {
               width = safeLongToInt(((VwlDeviceLayout)layoutResult.get(i)).getPosition_x()) + safeLongToInt(((VwlDeviceLayout)layoutResult.get(i)).getWidth());
            }

            if (height <= safeLongToInt(((VwlDeviceLayout)layoutResult.get(i)).getPosition_y()) + safeLongToInt(((VwlDeviceLayout)layoutResult.get(i)).getHeight())) {
               height = safeLongToInt(((VwlDeviceLayout)layoutResult.get(i)).getPosition_y()) + safeLongToInt(((VwlDeviceLayout)layoutResult.get(i)).getHeight());
            }

            list.add(monitor);
         }

         if (!((VwlVwtLayout)vwlLayout.get(0)).getIs_linear()) {
            resource.setDocumentWidth(String.valueOf(width));
            resource.setDocumentHeight(String.valueOf(height));
         } else {
            resource.setDocumentWidth(String.valueOf(((VwlDeviceLayout)layoutResult.get(0)).getWidth()));
            resource.setDocumentHeight(String.valueOf(((VwlDeviceLayout)layoutResult.get(0)).getHeight()));
         }

         resource.setMonitorList(list);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public String cancelVwl(String groupId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      Long longGroupId = Long.parseLong(groupId);

      try {
         this.cancelVwllayoutGroup(groupId);
         DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
         String defaultProgramId = deviceGroupDao.getDefaultProgramId(longGroupId);
         ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
         schInfo.setDefaultProgramId(longGroupId, defaultProgramId);
         ScheduleInfoDAO dao = new ScheduleInfoDAO();
         ScheduleAdminDao adminDao = new ScheduleAdminDao();
         List listPrograms = new ArrayList();
         ProgramEntity program = dao.getProgram(defaultProgramId);
         program.setDevice_group_ids(groupId.toString());
         listPrograms.add(program);
         if (listPrograms != null && listPrograms.size() > 0) {
            adminDao.deployDefaultPrograms(listPrograms);
         }

         return groupId;
      } catch (Exception var10) {
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_UNKNOWN);
      }
   }

   public boolean cancelVwllayoutGroup(String groupId) throws Exception, SQLException {
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      List deviceIdList = deviceGroupInfo.getChildDeviceIdList(Integer.parseInt(groupId), false);
      int loopSize = deviceIdList.size();

      for(int i = 0; i < loopSize; ++i) {
         Device device = deviceInfo.getDevice((String)deviceIdList.get(i));
         device.setVwt_id("");
         deviceInfo.setDevice(device);
         deviceInfo.deleteVwtInfo((String)deviceIdList.get(i));
      }

      deviceGroupInfo.cancelVwlGroup(groupId);
      VwlLayoutManager VwlLayoutManager = VwlLayoutManagerImpl.getInstance();
      String file_path = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + "EMPTY_LAYOUT.VWL";
      Iterator var9 = deviceIdList.iterator();

      while(var9.hasNext()) {
         String deviceId = (String)var9.next();
         VwlLayoutManager.deployVwlLayout(deviceId, "REMOVE_LAYOUT", "0", file_path);
      }

      DeviceGroupInfo groupInfo = DeviceGroupInfoImpl.getInstance();
      groupInfo.updateCacheDeviceGroup();
      return true;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority', 'Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public List getDeviceTagsByGroupIds(V2CommonGroupIds ids) throws SQLException {
      return RESTDeviceUtils.getDeviceTagsByGroupIds(ids);
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2CommonGroupIds analysticEnable(Long groupId) throws Exception {
      if (groupId != null) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, groupId);
         SlmLicenseManagerImpl var2 = SlmLicenseManagerImpl.getInstance();
         int licCnt = var2.getLicenseCountByProductCode("010D0E");
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         int curCnt = groupDao.getCntAnalysisDeviceGroup();
         V2CommonGroupIds groupIds = new V2CommonGroupIds();
         if (curCnt >= licCnt) {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_LICENSE_QUANTITY_EXCEED);
         } else {
            boolean result = groupDao.setAnalysisDeviceGroup(groupId, true);
            if (result) {
               List ids = new ArrayList();
               ids.add(groupId);
               groupIds.setIds(ids);
               return groupIds;
            } else {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_DEVICE_GROUP_NOT_EXIST);
            }
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"groupId"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2CommonGroupIds analysticCancel(Long groupId) throws Exception {
      if (groupId != null) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, groupId);
         DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
         V2CommonGroupIds groupIds = new V2CommonGroupIds();
         boolean var4 = groupDao.setAnalysisDeviceGroup(groupId, false);
         if (var4) {
            List ids = new ArrayList();
            ids.add(groupId);
            groupIds.setIds(ids);
            return groupIds;
         } else {
            throw new RestServiceException(RestExceptionCode.BAD_REQUEST_DEVICE_GROUP_NOT_EXIST);
         }
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"groupId"});
      }
   }

   private List convertV2DeviceGroupList(List groups) {
      List list = null;
      if (groups != null && groups.size() > 0) {
         list = new ArrayList();
         Iterator var3 = groups.iterator();

         while(var3.hasNext()) {
            DeviceGroup group = (DeviceGroup)var3.next();
            V2DeviceGroupTreeResource treeResource = V2DeviceGroupTreeResource.V2DeviceGroupTreeResourceBuilder.aV2DeviceGroupTreeResource().groupName(group.getGroup_name()).build();
            list.add(treeResource);
         }
      }

      return list;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Content Schedule Write Authority')")
   public Boolean checkAllDevicesTagged(Long groupId) throws Exception {
      if (groupId != null) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, groupId);
         return DeviceUtils.getDeviceTagStatus(groupId);
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_EMPTY, new String[]{"groupId"});
      }
   }

   private static enum ScheduleType {
      CONTENT_SCHEDULE_ID,
      MESSAGE_SCHEDULE_ID,
      EVENT_SCHEDULE_ID,
      DEVICE_PERMISSION,
      DEVICE_WARNING_RULE,
      GROUPS_NOTIFY_MENU;

      private static final Map stringToEnum = new HashMap();

      private ScheduleType() {
      }

      public static V2DeviceGroupServiceImpl.ScheduleType findBy(String type) {
         return (V2DeviceGroupServiceImpl.ScheduleType)stringToEnum.get(type);
      }

      static {
         stringToEnum.put("CONTENT_SCHEDULE_ID", CONTENT_SCHEDULE_ID);
         stringToEnum.put("EVENT_SCHEDULE_ID", EVENT_SCHEDULE_ID);
         stringToEnum.put("MESSAGE_SCHEDULE_ID", MESSAGE_SCHEDULE_ID);
         stringToEnum.put("DEVICE_PERMISSION", DEVICE_PERMISSION);
         stringToEnum.put("DEVICE_WARNING_RULE", DEVICE_WARNING_RULE);
         stringToEnum.put("GROUPS_NOTIFY_MENU", GROUPS_NOTIFY_MENU);
      }
   }
}
