package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_NULL)
public class PlaylistResource {
   @ApiModelProperty(
      example = "32FA85B4-2389-476C-845A-0FC6F1D10D81"
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "[PlaylistResource][playlistId] Not UUID pattern."
   )
   private String playlistId;
   @ApiModelProperty(
      example = "SPLAYER",
      required = true
   )
   private String deviceType = "";
   @ApiModelProperty(
      example = "3.0",
      required = true
   )
   @Pattern(
      regexp = "1.0|2.0|3.0|4.0|5.0|6.0|7.0|8.0|9.0",
      message = "[PlaylistResource][deviceTypeVersion] Only 1.0,2.0,3.0,4.0,5.0,6.0,7.0,8.0,9.0 are available."
   )
   private String deviceTypeVersion = "1.0";
   @ApiModelProperty(
      example = "New Playlist",
      required = true
   )
   @Size(
      max = 60,
      message = "max size is 60."
   )
   private String playlistName = "NO_TITLE";
   @ApiModelProperty(
      example = "1",
      required = true
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[PlaylistResource][groupId] Only number is available."
   )
   private String groupId = "0";
   @ApiModelProperty(
      example = "default",
      required = true
   )
   private String groupName = "";
   @ApiModelProperty(
      example = "1",
      required = true
   )
   @Min(0L)
   @Max(1L)
   private int shareFlag = 1;
   @ApiModelProperty(
      example = "-",
      required = true
   )
   @Size(
      max = 200,
      message = "[PlaylistResource][metaData] max size is 200."
   )
   private String metaData = "-";
   @ApiModelProperty(
      example = "N",
      required = true
   )
   @Pattern(
      regexp = "Y|N",
      message = "[PlaylistResource][shuffleFlag] Only Y, N are available."
   )
   private String shuffleFlag = "N";
   @ApiModelProperty(
      example = "0",
      required = true
   )
   private int contentCount = 0;
   @ApiModelProperty(
      example = "0",
      required = true
   )
   private Long playTime = 0L;
   private Long totalSize = 0L;
   @ApiModelProperty(
      example = "2016-01-01 00:00:00",
      required = true
   )
   private String lastModifiedDate = "";
   @ApiModelProperty(
      example = "admin",
      required = true
   )
   @Size(
      max = 20,
      message = "[PlaylistResource][creatorId] max size is 20."
   )
   private String creatorId = "";
   @KPI
   @ApiModelProperty(
      example = "0",
      required = true
   )
   private String playlistType = "0";
   @ApiModelProperty(
      example = "1",
      required = true
   )
   private Long versionId = 1L;
   private List contentList;
   private String thumbFilePath = "";
   private String thumbFileName = "";
   private List categoryList;
   @JsonIgnore
   private String amsMode = "";
   @JsonIgnore
   private boolean amsDirectPlay = false;
   private int syncPlayGroupCount = 0;

   public PlaylistResource() {
      super();
   }

   public List getCategoryList() {
      return this.categoryList;
   }

   public void setCategoryList(List categoryList) {
      this.categoryList = categoryList;
   }

   public String getThumbFilePath() {
      return this.thumbFilePath;
   }

   public void setThumbFilePath(String thumbFilePath) {
      this.thumbFilePath = thumbFilePath;
   }

   public String getThumbFileName() {
      return this.thumbFileName;
   }

   public void setThumbFileName(String thumbFileName) {
      this.thumbFileName = thumbFileName;
   }

   public List getContentList() {
      return this.contentList;
   }

   public void setContentList(List contentList) {
      this.contentList = contentList;
   }

   public String getGroupName() {
      return this.groupName;
   }

   public void setGroupName(String groupName) {
      this.groupName = groupName;
   }

   public Long getTotalSize() {
      return this.totalSize;
   }

   public void setTotalSize(Long totalSize) {
      this.totalSize = totalSize;
   }

   public String getLastModifiedDate() {
      return this.lastModifiedDate;
   }

   public void setLastModifiedDate(String lastModifiedDate) {
      this.lastModifiedDate = lastModifiedDate;
   }

   public String getCreatorId() {
      return this.creatorId;
   }

   public void setCreatorId(String creatorId) {
      this.creatorId = creatorId;
   }

   public String getPlaylistType() {
      return this.playlistType;
   }

   public void setPlaylistType(String playlistType) {
      this.playlistType = playlistType;
   }

   public Long getVersionId() {
      return this.versionId;
   }

   public void setVersionId(Long versionId) {
      this.versionId = versionId;
   }

   public Long getPlayTime() {
      return this.playTime;
   }

   public void setPlayTime(Long playTime) {
      this.playTime = playTime;
   }

   public String getPlaylistId() {
      return this.playlistId;
   }

   public void setPlaylistId(String playlistId) {
      this.playlistId = playlistId;
   }

   public int getContentCount() {
      return this.contentCount;
   }

   public void setContentCount(int contentCount) {
      this.contentCount = contentCount;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public String getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(String deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public String getPlaylistName() {
      return this.playlistName;
   }

   public void setPlaylistName(String playlistName) {
      this.playlistName = playlistName;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public int getShareFlag() {
      return this.shareFlag;
   }

   public void setShareFlag(int shareFlag) {
      this.shareFlag = shareFlag;
   }

   public String getMetaData() {
      return this.metaData;
   }

   public void setMetaData(String metaData) {
      this.metaData = metaData;
   }

   public String getShuffleFlag() {
      return this.shuffleFlag;
   }

   public void setShuffleFlag(String shuffleFlag) {
      this.shuffleFlag = shuffleFlag;
   }

   public String getAmsMode() {
      return this.amsMode;
   }

   public void setAmsMode(String amsMode) {
      this.amsMode = amsMode;
   }

   public boolean getAmsDirectPlay() {
      return this.amsDirectPlay;
   }

   public void setAmsDirectPlay(boolean amsDirectPlay) {
      this.amsDirectPlay = amsDirectPlay;
   }

   public int getSyncPlayGroupCount() {
      return this.syncPlayGroupCount;
   }

   public void setSyncPlayGroupCount(int syncPlayGroupCount) {
      this.syncPlayGroupCount = syncPlayGroupCount;
   }
}
