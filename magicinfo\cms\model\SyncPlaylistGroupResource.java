package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

@JsonInclude(Include.NON_NULL)
public class SyncPlaylistGroupResource {
   @ApiModelProperty(
      example = "0",
      allowableValues = "range[0, infinity]"
   )
   @Min(
      value = 0L,
      message = "[SyncPlaylistGroupResource][groupOrder] minimum value is 0."
   )
   private int groupOrder = 0;
   @ApiModelProperty(
      example = "N",
      required = true
   )
   @Pattern(
      regexp = "Y|N",
      message = "[SyncPlaylistGroupResource][syncStatus] Only Y, N are available."
   )
   private String syncStatus = "Y";
   @Valid
   private List contents;

   public SyncPlaylistGroupResource() {
      super();
   }

   public int getGroupOrder() {
      return this.groupOrder;
   }

   public void setGroupOrder(int groupOrder) {
      this.groupOrder = groupOrder;
   }

   public String getSyncStatus() {
      return this.syncStatus;
   }

   public void setSyncStatus(String syncStatus) {
      this.syncStatus = syncStatus;
   }

   public List getContents() {
      return this.contents;
   }

   public void setContents(List contents) {
      this.contents = contents;
   }
}
