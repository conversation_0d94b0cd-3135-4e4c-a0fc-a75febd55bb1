package com.samsung.magicinfo.dms.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@JsonInclude(Include.NON_NULL)
public class ScheduleFilter {
   private int startIndex = 1;
   private int pageSize = 10;
   @ApiModelProperty(
      example = "modify_date"
   )
   @Pattern(
      regexp = "modify_date",
      message = "[ScheduleFilter][sortColumn] Only modify_date are available."
   )
   private String sortColumn;
   @ApiModelProperty(
      example = "desc"
   )
   @Pattern(
      regexp = "desc|asc",
      message = "[ScheduleFilter][sortOrder] Only asc, desc are available."
   )
   private String sortOrder = "desc";
   private int results;
   @ApiModelProperty(
      example = "test"
   )
   @Size(
      max = 20,
      message = "[ScheduleFilter][searchText] max size is 20."
   )
   private String searchText;
   private String searchContentIds = "";
   private String searchDevGroupIds = "";
   @ApiModelProperty(
      example = "ALL"
   )
   private String groupType = "ALL";
   @ApiModelProperty(
      example = "1"
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[ScheduleFilter][frameCount] Only number is available."
   )
   private String frameCount;
   private String endModifiedDate = "";
   private String startModifiedDate = "";
   @ApiModelProperty(
      example = "LFD"
   )
   private String scheduleType = "";
   private int orderNumber = 7;
   @ApiModelProperty(
      example = "-1"
   )
   private String searchId = "-1";
   @ApiModelProperty(
      example = "SPLAYER"
   )
   private String deviceType;
   private String selectId;
   private String isSelect;
   @ApiModelProperty(
      example = "PREMIUM"
   )
   @Pattern(
      regexp = "PREMIUM",
      message = "[ScheduleFilter][productType] Only PREMIUM are available."
   )
   private String productType = "PREMIUM";
   @ApiModelProperty(
      example = "test"
   )
   @Size(
      max = 100,
      message = "[ScheduleFilter][groupName] max size is 100."
   )
   private String groupName;
   @ApiModelProperty(
      example = "0"
   )
   private String groupId;

   public ScheduleFilter() {
      super();
   }

   public String getSortOrder() {
      return this.sortOrder;
   }

   public void setSortOrder(String sortOrder) {
      this.sortOrder = sortOrder;
   }

   public int getResults() {
      return this.results;
   }

   public void setResults(int results) {
      this.results = results;
   }

   public String getSearchText() {
      return this.searchText;
   }

   public void setSearchText(String searchText) {
      this.searchText = searchText;
   }

   public String getGroupType() {
      return this.groupType;
   }

   public void setGroupType(String groupType) {
      this.groupType = groupType;
   }

   public String getFrameCount() {
      return this.frameCount;
   }

   public void setFrameCount(String frameCount) {
      this.frameCount = frameCount;
   }

   public String getEndModifiedDate() {
      return this.endModifiedDate;
   }

   public void setEndModifiedDate(String endModifiedDate) {
      this.endModifiedDate = endModifiedDate;
   }

   public String getStartModifiedDate() {
      return this.startModifiedDate;
   }

   public void setStartModifiedDate(String startModifiedDate) {
      this.startModifiedDate = startModifiedDate;
   }

   public String getSearchContentIds() {
      return this.searchContentIds;
   }

   public void setSearchContentIds(String searchContentIds) {
      this.searchContentIds = searchContentIds;
   }

   public String getSearchDevGroupIds() {
      return this.searchDevGroupIds;
   }

   public void setSearchDevGroupIds(String searchDevGroupIds) {
      this.searchDevGroupIds = searchDevGroupIds;
   }

   public String getScheduleType() {
      return this.scheduleType;
   }

   public void setScheduleType(String scheduleType) {
      this.scheduleType = scheduleType;
   }

   public int getOrderNumber() {
      return this.orderNumber;
   }

   public void setOrderNumber(int orderNumber) {
      this.orderNumber = orderNumber;
   }

   public String getSearchId() {
      return this.searchId;
   }

   public void setSearchId(String searchId) {
      this.searchId = searchId;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public String getIsSelect() {
      return this.isSelect;
   }

   public void setIsSelect(String isSelect) {
      this.isSelect = isSelect;
   }

   public String getSelectId() {
      return this.selectId;
   }

   public void setSelectId(String selectId) {
      this.selectId = selectId;
   }

   public String getProductType() {
      return this.productType;
   }

   public void setProductType(String productType) {
      this.productType = productType;
   }

   public String getGroupName() {
      return this.groupName;
   }

   public void setGroupName(String groupName) {
      this.groupName = groupName;
   }

   public String getSortColumn() {
      return this.sortColumn;
   }

   public void setSortColumn(String sortColumn) {
      this.sortColumn = sortColumn;
   }

   public String getGroupId() {
      return this.groupId;
   }

   public void setGroupId(String groupId) {
      this.groupId = groupId;
   }

   public int getStartIndex() {
      return this.startIndex;
   }

   public void setStartIndex(int startIndex) {
      this.startIndex = startIndex;
   }

   public int getPageSize() {
      return this.pageSize;
   }

   public void setPageSize(int pageSize) {
      this.pageSize = pageSize;
   }
}
