package com.samsung.magicinfo.auth.model.json.response;

import com.samsung.magicinfo.auth.model.base.ModelBase;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;

public class AuthenticationResponse extends ModelBase {
   private static final long serialVersionUID = -6624726180748515507L;
   private String token;
   private String dateFormat;
   private String timeFormat;
   private String scheduleFirstDay;
   private OTPAuthType otpAuthType;

   public OTPAuthType getOtpAuthType() {
      return this.otpAuthType;
   }

   public void setOtpAuthType(OTPAuthType otpAuthType) {
      this.otpAuthType = otpAuthType;
   }

   public AuthenticationResponse() {
      super();
   }

   public AuthenticationResponse(String token) {
      super();
      this.setToken(token);
   }

   public AuthenticationResponse(String token, String dateFormat, String timeFormat, String scheduleFirstDay) {
      super();
      this.setToken(token);
      this.setDateFormat(dateFormat);
      this.setTimeFormat(timeFormat);
      this.setScheduleFirstDay(scheduleFirstDay);
   }

   public String getToken() {
      return this.token;
   }

   public void setToken(String token) {
      this.token = token;
   }

   public String getDateFormat() {
      return this.dateFormat;
   }

   public void setDateFormat(String dateFormat) {
      this.dateFormat = dateFormat;
   }

   public String getTimeFormat() {
      return this.timeFormat;
   }

   public void setTimeFormat(String timeFormat) {
      this.timeFormat = timeFormat;
   }

   public String getScheduleFirstDay() {
      return this.scheduleFirstDay;
   }

   public void setScheduleFirstDay(String scheduleFirstDay) {
      this.scheduleFirstDay = scheduleFirstDay;
   }
}
