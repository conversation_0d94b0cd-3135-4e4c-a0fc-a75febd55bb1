package com.samsung.magicinfo.framework.advertisement.entity.schedule;

import java.sql.Timestamp;

public class ScheduleInfoEntity {
   private String user_id = null;
   private String user_name = null;
   private String schedule_id = null;
   private String schedule_name = null;
   private Timestamp requested_date = null;
   private Timestamp approved_date = null;
   private Timestamp rejected_date = null;
   private int status_id = -1;
   private String rejected_reason = null;

   public ScheduleInfoEntity() {
      super();
   }

   public String getUser_id() {
      return this.user_id;
   }

   public void setUser_id(String user_id) {
      this.user_id = user_id;
   }

   public String getUser_name() {
      return this.user_name;
   }

   public void setUser_name(String user_name) {
      this.user_name = user_name;
   }

   public String getSchedule_id() {
      return this.schedule_id;
   }

   public void setSchedule_id(String schedule_id) {
      this.schedule_id = schedule_id;
   }

   public String getSchedule_name() {
      return this.schedule_name;
   }

   public void setSchedule_name(String schedule_name) {
      this.schedule_name = schedule_name;
   }

   public Timestamp getRequested_date() {
      return this.requested_date;
   }

   public void setRequested_date(Timestamp requested_date) {
      this.requested_date = requested_date;
   }

   public Timestamp getApproved_date() {
      return this.approved_date;
   }

   public void setApproved_date(Timestamp approved_date) {
      this.approved_date = approved_date;
   }

   public Timestamp getRejected_date() {
      return this.rejected_date;
   }

   public void setRejected_date(Timestamp rejected_date) {
      this.rejected_date = rejected_date;
   }

   public int getStatus_id() {
      return this.status_id;
   }

   public void setStatus_id(int status_id) {
      this.status_id = status_id;
   }

   public String getRejected_reason() {
      return this.rejected_reason;
   }

   public void setRejected_reason(String rejected_reason) {
      this.rejected_reason = rejected_reason;
   }
}
