package com.samsung.magicinfo.ems.model;

public class MenuCountResource {
   public int content;
   public int playlist;
   public int contentSchedule;
   public int messageSchedule;
   public int device;
   public int unapprovedDevice;
   public int unapprovedContent;

   public MenuCountResource() {
      super();
   }

   public int getContent() {
      return this.content;
   }

   public void setContent(int content) {
      this.content = content;
   }

   public int getPlaylist() {
      return this.playlist;
   }

   public void setPlaylist(int playlist) {
      this.playlist = playlist;
   }

   public int getContentSchedule() {
      return this.contentSchedule;
   }

   public void setContentSchedule(int contentSchedule) {
      this.contentSchedule = contentSchedule;
   }

   public int getMessageSchedule() {
      return this.messageSchedule;
   }

   public void setMessageSchedule(int messageSchedule) {
      this.messageSchedule = messageSchedule;
   }

   public int getDevice() {
      return this.device;
   }

   public void setDevice(int device) {
      this.device = device;
   }

   public int getUnapprovedDevice() {
      return this.unapprovedDevice;
   }

   public void setUnapprovedDevice(int unapprovedDevice) {
      this.unapprovedDevice = unapprovedDevice;
   }

   public int getUnapprovedContent() {
      return this.unapprovedContent;
   }

   public void setUnapprovedContent(int unapprovedContent) {
      this.unapprovedContent = unapprovedContent;
   }
}
