package com.samsung.common.utils;

import java.sql.Timestamp;
import java.util.Comparator;
import org.apache.commons.collections.map.CaseInsensitiveMap;

public class MapComparator implements Comparator {
   private final String key;

   public MapComparator(String key) {
      super();
      this.key = key;
   }

   public int compare(CaseInsensitiveMap first, CaseInsensitiveMap second) {
      Object o1 = first.get(this.key);
      Object o2 = second.get(this.key);
      if (o1 instanceof String) {
         String firstValue = (String)o1;
         String secondValue = (String)o2;
         return firstValue.toLowerCase().compareTo(secondValue.toLowerCase());
      } else if (o1 instanceof Timestamp) {
         Timestamp firstValue = (Timestamp)o1;
         Timestamp secondValue = (Timestamp)o2;
         return firstValue.compareTo(secondValue);
      } else {
         return 0;
      }
   }
}
