package com.samsung.magicinfo.restapi.device.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(Include.NON_NULL)
public class V2ChildDeviceScanInfo {
   @ApiModelProperty("Id of specific device")
   private String deviceId;
   @ApiModelProperty("Value of signal command type")
   private String signageCommand;
   @ApiModelProperty("Value of the Count of connected one or multiple sub-devices")
   private String countOfChildDevice;
   @ApiModelProperty("")
   private String requestId;

   public V2ChildDeviceScanInfo() {
      super();
   }

   public String getDeviceId() {
      return this.deviceId;
   }

   public void setDeviceId(String deviceId) {
      this.deviceId = deviceId;
   }

   public String getSignageCommand() {
      return this.signageCommand;
   }

   public void setSignageCommand(String signageCommand) {
      this.signageCommand = signageCommand;
   }

   public String getCountOfChildDevice() {
      return this.countOfChildDevice;
   }

   public void setCountOfChildDevice(String countOfChildDevice) {
      this.countOfChildDevice = countOfChildDevice;
   }

   public String getRequestId() {
      return this.requestId;
   }

   public void setRequestId(String requestId) {
      this.requestId = requestId;
   }
}
