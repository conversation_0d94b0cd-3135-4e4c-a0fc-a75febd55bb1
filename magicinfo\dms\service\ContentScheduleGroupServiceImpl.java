package com.samsung.magicinfo.dms.service;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.dms.model.ScheduleGroupResource;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.scheduler.dao.ProgramGroupDao;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ProgramGroupInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("ContentScheduleGroupService")
@Transactional
public class ContentScheduleGroupServiceImpl implements ContentScheduleGroupService {
   protected Logger logger = LoggingManagerV2.getLogger(ContentScheduleGroupServiceImpl.class);
   protected String table = "MI_CDS_INFO_PROGRAM_GROUP";
   protected String menu = "Schedule";

   public ContentScheduleGroupServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody getRootGroups() {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      UserContainer userContainer = SecurityUtils.getUserContainer();
      User user = userContainer.getUser();
      ProgramGroupInfo groupDao = ProgramGroupInfoImpl.getInstance();
      List lists = new ArrayList();
      String organization = SecurityUtils.getLoginUserOrganization();
      ArrayList result = new ArrayList();

      try {
         if (user.getRoot_group_id() == Long.valueOf("0")) {
            lists = groupDao.getChildGroupList(0, false);
         } else if (organization != null) {
            lists = groupDao.getRootGroupById("PREMIUM", "SCHGRP", organization);
         }

         Iterator var8 = ((List)lists).iterator();

         while(var8.hasNext()) {
            ProgramGroup entity = (ProgramGroup)var8.next();
            ScheduleGroupResource scheduleGroupResource = new ScheduleGroupResource();
            scheduleGroupResource.setGroupId(entity.getGroup_id());
            scheduleGroupResource.setParentGroupId(entity.getP_group_id());
            scheduleGroupResource.setGroupDepth(entity.getGroup_depth());
            scheduleGroupResource.setGroupName(entity.getGroup_name());
            scheduleGroupResource.setDescription(entity.getDescription());
            result.add(scheduleGroupResource);
         }

         responseBody.setItems(result);
         responseBody.setStatus("Success");
      } catch (SQLException var11) {
         this.logger.error("", var11);
         responseBody.setErrorMessage(var11.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody listGroupInfo(Long groupId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      ScheduleGroupResource scheduleGroupResource = new ScheduleGroupResource();
      ProgramGroupDao programGroupDao = new ProgramGroupDao();

      try {
         ProgramGroup programGroup = programGroupDao.getGroup((int)groupId);
         scheduleGroupResource.setGroupId(programGroup.getGroup_id());
         scheduleGroupResource.setParentGroupId(programGroup.getP_group_id());
         scheduleGroupResource.setGroupDepth(programGroup.getGroup_depth());
         scheduleGroupResource.setGroupName(programGroup.getGroup_name());
         scheduleGroupResource.setDescription(programGroup.getDescription());
         responseBody.setItems(scheduleGroupResource);
         responseBody.setStatus("Success");
      } catch (Exception var6) {
         this.logger.error("", var6);
         responseBody.setErrorMessage(var6.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody getChildGroups(String groupId) {
      ResponseBody responseBody = new ResponseBody();
      responseBody.setApiVersion("1.0");
      ProgramGroupInfo groupDao = ProgramGroupInfoImpl.getInstance();
      new ArrayList();
      ArrayList result = new ArrayList();

      try {
         int id = Integer.parseInt(groupId);
         List lists = groupDao.getChildGroupList(id, false);
         Iterator var7 = lists.iterator();

         while(var7.hasNext()) {
            ProgramGroup entity = (ProgramGroup)var7.next();
            ScheduleGroupResource scheduleGroupResource = new ScheduleGroupResource();
            scheduleGroupResource.setGroupId(entity.getGroup_id());
            scheduleGroupResource.setParentGroupId(entity.getP_group_id());
            scheduleGroupResource.setGroupDepth(entity.getGroup_depth());
            scheduleGroupResource.setGroupName(entity.getGroup_name());
            scheduleGroupResource.setDescription(entity.getDescription());
            result.add(scheduleGroupResource);
         }

         responseBody.setItems(result);
         responseBody.setStatus("Success");
      } catch (SQLException var10) {
         this.logger.error("", var10);
         responseBody.setErrorMessage(var10.getMessage());
         responseBody.setStatus("Fail");
      }

      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority', 'Content Schedule Manage Authority')")
   public ResponseBody createGroup(ProgramGroup group) throws Exception {
      ResponseBody responsebody = new ResponseBody();
      LeftMenuGroupTreeDao treeDao = new LeftMenuGroupTreeDao();
      ScheduleInfoDAO sInfoDao = new ScheduleInfoDAO();
      String table = "MI_CDS_INFO_PROGRAM_GROUP";
      String menu = "Schedule";

      try {
         String parentGroupId = group.getP_group_id().toString();
         String newName = group.getGroup_name();
         long depth = treeDao.get_GroupDepth(parentGroupId, table);
         Long root_group_id = null;
         root_group_id = sInfoDao.getProgramGroupRoot(group.getP_group_id().intValue());
         User user = SecurityUtils.getLoginUser();
         String userId = null;
         if (user != null) {
            userId = user.getUser_id();
            treeDao.setGroupTreeCreate(menu, table, parentGroupId, newName, depth + "", root_group_id, userId);
            responsebody.setStatus("Success");
            return responsebody;
         } else {
            throw new AccessDeniedException("no userId");
         }
      } catch (SQLException var14) {
         this.logger.error("", var14);
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var14.getMessage());
         return responsebody;
      }
   }
}
