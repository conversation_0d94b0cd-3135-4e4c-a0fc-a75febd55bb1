package com.samsung.common.cache;

import net.sf.ehcache.statistics.CacheUsageListener;

public class Eh<PERSON>acheUsageListener implements CacheUsageListener {
   public EhCacheUsageListener() {
      super();
   }

   public void dispose() {
   }

   public void notifyCacheElementEvicted() {
   }

   public void notifyCacheElementExpired() {
   }

   public void notifyCacheElementPut() {
   }

   public void notifyCacheElementRemoved() {
   }

   public void notifyCacheElementUpdated() {
      System.out.println("notifyCacheElementUpdated");
   }

   public void notifyCacheHitInMemory() {
   }

   public void notifyCacheHitOffHeap() {
   }

   public void notifyCacheHitOnDisk() {
   }

   public void notifyCacheMissInMemory() {
   }

   public void notifyCacheMissOffHeap() {
   }

   public void notifyCacheMissOnDisk() {
   }

   public void notifyCacheMissedWithExpired() {
   }

   public void notifyCacheMissedWithNotFound() {
   }

   public void notifyCacheSearch(long arg0) {
   }

   public void notifyGetTimeNanos(long arg0) {
   }

   public void notifyRemoveAll() {
   }

   public void notifyStatisticsAccuracyChanged(int arg0) {
   }

   public void notifyStatisticsCleared() {
   }

   public void notifyStatisticsEnabledChanged(boolean arg0) {
   }

   public void notifyTimeTakenForGet(long arg0) {
   }

   public void notifyXaCommit() {
   }

   public void notifyXaRollback() {
   }
}
