package com.samsung.magicinfo.auth.security.strategies.model;

import com.samsung.common.utils.CommonUtils;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;
import com.samsung.magicinfo.auth.security.otp.UserAuthDevice;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

public class AuthModel implements Serializable {
   private static final long serialVersionUID = -414584131104585162L;
   private String userId;
   private boolean enable;
   private String secretKey;
   private Timestamp createDate;
   private Timestamp expiredDate;
   private int otp;
   private long loginFailCount = 0L;
   private Timestamp lastLoginDate;
   OTPAuthType otpAuthType;
   UserAuthDevice userAuthDevice;

   public AuthModel() {
      super();
      Date date = new Date();
      this.lastLoginDate = new Timestamp(date.getTime());
   }

   public long getLoginFailCount() {
      return this.loginFailCount;
   }

   public long addLoginFailCount() {
      ++this.loginFailCount;
      return this.loginFailCount;
   }

   public void setLoginFailCount(long loginFailCount) {
      this.loginFailCount = loginFailCount;
   }

   public Timestamp getLastLoginDate() {
      return this.lastLoginDate;
   }

   public void setLastLoginDate(Timestamp lastLoginDate) {
      this.lastLoginDate = lastLoginDate;
   }

   public Timestamp setCurrentTimeToLastLoginDate() {
      Date date = new Date();
      this.lastLoginDate = new Timestamp(date.getTime());
      return this.lastLoginDate;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public boolean isEnable() {
      return this.enable;
   }

   public void setEnable(boolean enable) {
      this.enable = enable;
   }

   public String getSecretKey() {
      return this.secretKey;
   }

   public void setSecretKey(String secretKey) {
      this.secretKey = secretKey;
   }

   public Timestamp getCreateDate() {
      return this.createDate;
   }

   public void setCreateDate(Timestamp createDate) {
      this.createDate = createDate;
   }

   public Timestamp getExpiredDate() {
      return this.expiredDate;
   }

   public void setExpiredDate(Timestamp expiredDate) {
      this.expiredDate = expiredDate;
   }

   public int getOtp() {
      return this.otp;
   }

   public void setOtp(String otp) {
      if (!CommonUtils.isInteger(otp)) {
         throw new RestServiceException(RestExceptionCode.WRONG_FORMAT_OTP_VALUE);
      } else if (otp.length() != 6) {
         throw new RestServiceException(RestExceptionCode.WRONG_FORMAT_OTP_VALUE);
      } else {
         this.otp = Integer.parseInt(otp);
      }
   }

   public void setOtp(int otp) {
      this.otp = otp;
   }

   public OTPAuthType getOtpAuthType() {
      return this.otpAuthType;
   }

   public void setOtpAuthType(OTPAuthType otpAuthType) {
      this.otpAuthType = otpAuthType;
   }

   public UserAuthDevice getUserAuthDevice() {
      return this.userAuthDevice;
   }

   public void setUserAuthDevice(UserAuthDevice userAuthDevice) {
      this.userAuthDevice = userAuthDevice;
   }
}
