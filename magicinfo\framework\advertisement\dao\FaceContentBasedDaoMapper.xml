<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.samsung.magicinfo.framework.advertisement.dao.FaceContentBasedDaoMapper">

    <insert id="addPopDayInfoList">
        INSERT INTO MI_STATISTICS_FACE_CONTENT_DAY (CONTENT_ID, START_TIME, DEVICE_ID, VIEW_COUNT, DURATION
        , PLAY_DOW, GENDER, AGE)
        VALUES
        <foreach collection="map" open="(" close=")" separator="),(" item="item" index="key">
            #{key.contentId}, #{day}, #{deviceId}, #{item.view_count},
            #{item.duration}, #{dow}, #{key.gender}, #{key.age}
        </foreach>
    </insert>

    <insert id="addPopHourInfoList">
        INSERT INTO MI_STATISTICS_FACE_CONTENT_HOUR(CONTENT_ID, START_TIME, DEVICE_ID, GENDER, AGE, VIEW_COUNT
        , DURATION) VALUES
        <foreach collection="entities" open="(" close=")" separator="),(" item="entity" index="index">
            #{faceKey.contentId}, #{entity.time}, #{deviceId}, #{faceKey.gender},
            #{faceKey.age},
            #{entity.duration}, #{entity.count}
        </foreach>
    </insert>
    <insert id="addPopYearInfoList">
        INSERT INTO MI_STATISTICS_FACE_CONTENT_YEAR( CONTENT_ID, START_TIME, DEVICE_ID , VIEW_COUNT, DURATION, GENDER,
        AGE ) VALUES
        <foreach collection="map" open="(" close=")" separator="),(" item="item" index="key">
            #{key.contentId}, #{startTime}, #{deviceId}, #{item.view_count},
            #{item.duration}, #{key.gender}, #{key.age}
        </foreach>
    </insert>
    <insert id="addPopMonthInfoList">
        INSERT INTO MI_STATISTICS_FACE_CONTENT_MONTH (CONTENT_ID, START_TIME, LOG_QUARTER, DEVICE_ID, VIEW_COUNT, DURATION, GENDER, AGE) VALUES
        <foreach collection="map" open="(" close=")" separator="),(" item="item" index="key">
            #{key.contentId}, #{month}, #{quarter}, #{deviceId}, #{item.view_count},
            #{item.duration}, #{key.gender}, #{key.age}
        </foreach>
    </insert>
    <update id="setPopYearInfo">
        UPDATE MI_STATISTICS_FACE_CONTENT_YEAR
        SET VIEW_COUNT = #{view_count}, DURATION = #{duration}
        WHERE START_TIME = #{year} AND CONTENT_ID = #{faceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{faceKey.gender}
              AND AGE = #{faceKey.age}
    </update>
    <update id="setPopMonthInfo">
        UPDATE MI_STATISTICS_FACE_CONTENT_MONTH
        SET VIEW_COUNT = #{view_count}, DURATION = #{duration}
        WHERE START_TIME = #{month} AND CONTENT_ID = #{faceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{faceKey.gender}
              AND AGE = #{faceKey.age}
    </update>

    <select id="getPopMonthInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_FACE_CONTENT_MONTH
        WHERE START_TIME = #{month} AND CONTENT_ID = #{faceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{faceKey.gender} AND AGE = #{faceKey.age}
    </select>

    <select id="getPopYearInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_FACE_CONTENT_YEAR
        WHERE START_TIME = #{year} AND CONTENT_ID = #{faceKey.contentId} AND DEVICE_ID = #{device_id}
              AND GENDER = #{faceKey.gender} AND AGE = #{faceKey.age}
    </select>
    <select id="checkDuplicationByDB" resultType="java.lang.Long">
        SELECT
            COUNT(CONTENT_ID)
        FROM MI_STATISTICS_FACE_CONTENT_DAY
        WHERE START_TIME = #{date} AND DEVICE_ID = #{device_id}
    </select>

</mapper>