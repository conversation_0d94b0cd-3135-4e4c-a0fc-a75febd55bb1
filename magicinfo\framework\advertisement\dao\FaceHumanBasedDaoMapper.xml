<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.samsung.magicinfo.framework.advertisement.dao.FaceHumanBasedDaoMapper">

    <insert id="addPopDayInfoList">
        INSERT INTO MI_STATISTICS_FACE_HUMAN_DAY (START_TIME, DEVICE_ID, VIEW_COUNT, DURATION, PLAY_DOW, GENDER, AGE)
        VALUES
        <foreach collection="map" close=")" index="index" item="item" open="(" separator="),(">
            #{day}, #{deviceId}, #{item.view_count}, #{item.duration}, #{dow}, #{index.gender}, #{index.age}
        </foreach>
    </insert>

    <insert id="addPopHourInfoList">
        INSERT INTO MI_STATISTICS_FACE_HUMAN_HOUR( START_TIME, DEVICE_ID , GENDER, AGE, VIEW_COUNT, DURATION ) VALUES
        <foreach collection="list" close=")" item="item" open="(" separator="),(">
            #{item.time}, #{deviceId}, #{key.gender}, #{key.age}, #{item.duration}, #{item.count}
        </foreach>
    </insert>

    <insert id="addPopMonthInfoList">
        INSERT INTO MI_STATISTICS_FACE_HUMAN_MONTH( START_TIME, LOG_QUARTER, DEVICE_ID , VIEW_COUNT, DURATION, GENDER
        , AGE) VALUES
        <foreach collection="map" close=")" index="index" item="item" open="(" separator="),(">
            #{month}, #{quarter}, #{deviceId}, #{item.view_count}, #{item.duration}, #{index.gender}, #{index.age}
        </foreach>
    </insert>

    <insert id="addPopYearInfoList">
        INSERT INTO MI_STATISTICS_FACE_HUMAN_YEAR( START_TIME, DEVICE_ID , VIEW_COUNT, DURATION, GENDER, AGE ) VALUES
        <foreach collection="map" close=")" index="index" item="item" open="(" separator="),(">
            #{timestamp},#{deviceId}, #{item.view_count}, #{item.duration}, #{index.gender}, #{index.age}
        </foreach>
    </insert>

    <update id="setPopMonthInfo">
        UPDATE MI_STATISTICS_FACE_HUMAN_MONTH
        SET VIEW_COUNT = #{playcount}, DURATION = #{duration}
        WHERE START_TIME = #{month} AND DEVICE_ID = #{device_id} AND GENDER = #{key.gender} AND AGE = #{key.age}
    </update>

    <update id="setPopYearInfo">
        UPDATE MI_STATISTICS_FACE_HUMAN_YEAR
        SET VIEW_COUNT = #{playcount}, DURATION = #{duration}
        WHERE START_TIME = #{year} AND DEVICE_ID = #{device_id} AND GENDER = #{key.gender} AND AGE = #{key.age}
    </update>

    <select id="getPopMonthInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_FACE_HUMAN_MONTH
        WHERE START_TIME = #{month} AND DEVICE_ID = #{device_id} AND GENDER = #{key.gender} AND AGE = #{key.age}
    </select>

    <select id="getPopYearInfo"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.FaceInfoEntity">
        SELECT
            *
        FROM MI_STATISTICS_FACE_HUMAN_YEAR
        WHERE START_TIME = #{year} AND DEVICE_ID = #{device_id} AND GENDER = #{key.gender} AND AGE = #{key.age}
    </select>

    <select id="checkDuplicationByDB" resultType="java.lang.Long">
        SELECT
            COUNT(DEVICE_ID)
        FROM MI_STATISTICS_FACE_HUMAN_DAY
        WHERE START_TIME = #{timestamp} AND DEVICE_ID = #{device_id}
    </select>

</mapper>