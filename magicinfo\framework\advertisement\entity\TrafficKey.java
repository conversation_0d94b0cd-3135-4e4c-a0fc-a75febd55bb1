package com.samsung.magicinfo.framework.advertisement.entity;

public class TrafficKey {
   String contentId;

   public TrafficKey(String contentId) {
      super();
      this.contentId = contentId;
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public int hashCode() {
      int prime = true;
      int result = 1;
      int result = 31 * result + (this.contentId == null ? 0 : this.contentId.hashCode());
      return result;
   }

   public boolean equals(Object obj) {
      if (this == obj) {
         return true;
      } else if (obj == null) {
         return false;
      } else if (this.getClass() != obj.getClass()) {
         return false;
      } else {
         TrafficKey other = (TrafficKey)obj;
         if (this.contentId == null) {
            if (other.contentId != null) {
               return false;
            }
         } else if (!this.contentId.equals(other.contentId)) {
            return false;
         }

         return true;
      }
   }
}
