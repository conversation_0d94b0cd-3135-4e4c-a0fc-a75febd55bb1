package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.MDCTimeStrUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDao;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceGroupDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSystemInfoConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeHolidayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceTimeTimerConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemInfoConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemInfoConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSystemSetupConfManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceTimeConfManagerImpl;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManager;
import com.samsung.magicinfo.framework.device.ruleProcessing.Manager.AlarmManagerImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfo;
import com.samsung.magicinfo.framework.scheduler.manager.MessageInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonStatusResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTimeListResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTimeconfResource;
import com.samsung.magicinfo.restapi.device.utils.RESTDeviceUtils;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.rms.model.DeviceTimeHolidayResource;
import com.samsung.magicinfo.rms.util.DeviceModelConverter;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2DeviceTimeService")
@Transactional
public class V2DeviceTimeServiceImpl implements V2DeviceTimeService {
   private static final String BLANK_STRING = "-";
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceTimeServiceImpl.class);
   DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
   DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
   DeviceGroupDao devGroupDao = new DeviceGroupDao();
   DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
   MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
   DeviceDao dao = new DeviceDao((SqlSession)null);
   AlarmManager alarmManager = AlarmManagerImpl.getInstance();
   DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
   JobManager jobMgr = JobManagerImpl.getInstance();
   MonitoringManagerInfo deviceMonitoringDao = MonitoringManagerInfoImpl.getInstance("PREMIUM");
   DeviceTimeConfManager deviceConf = DeviceTimeConfManagerImpl.getInstance("PREMIUM");
   DeviceSystemSetupConfManager deviceSetupConf = DeviceSystemSetupConfManagerImpl.getInstance("PREMIUM");
   MessageInfo msgInfo = MessageInfoImpl.getInstance();
   ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
   ContentInfo contentDao = ContentInfoImpl.getInstance();
   ScheduleInfoDAO scheduleDao = new ScheduleInfoDAO();
   final String ALL_MDC = "ALL_MDC";
   final String MODEL_KIND_NEW = "NEW";
   final String REQUEST_ID = "requestId";

   public V2DeviceTimeServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceReqServiceResource updateTimeInfo(V2DeviceTimeconfResource obj) throws Exception {
      boolean checkFlag = false;
      List deviceIds = obj.getDeviceIds();
      Iterator modelKind = deviceIds.iterator();

      while(modelKind.hasNext()) {
         String deviceId = (String)modelKind.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var27) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      modelKind = null;
      int timerCnt = false;
      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var10 = deviceIds.iterator();

      while(var10.hasNext()) {
         String deviceId = (String)var10.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      String requestId = UUID.randomUUID().toString();
      Iterator var32 = deviceIds.iterator();

      while(var32.hasNext()) {
         String deviceId = (String)var32.next();

         String modelKind;
         int timerCnt;
         try {
            Device device = this.deviceDao.getDeviceMinInfo(deviceId);
            String deviceModelCode = device.getDevice_model_code();
            modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
            timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
            if (StringUtils.equalsIgnoreCase(modelKind, "NEW") && !obj.isSeparatableDevice()) {
               if (obj.getTimerTimer1() != null) {
                  obj.setTimerTimer1(MDCTimeStrUtils.convert15to13(obj.getTimerTimer1()));
               }

               if (obj.getTimerTimer2() != null) {
                  obj.setTimerTimer2(MDCTimeStrUtils.convert15to13(obj.getTimerTimer2()));
               }

               if (obj.getTimerTimer3() != null) {
                  obj.setTimerTimer3(MDCTimeStrUtils.convert15to13(obj.getTimerTimer3()));
               }

               if (timerCnt == 7) {
                  if (obj.getTimerTimer4() != null) {
                     obj.setTimerTimer4(MDCTimeStrUtils.convert15to13(obj.getTimerTimer4()));
                  }

                  if (obj.getTimerTimer5() != null) {
                     obj.setTimerTimer5(MDCTimeStrUtils.convert15to13(obj.getTimerTimer5()));
                  }

                  if (obj.getTimerTimer6() != null) {
                     obj.setTimerTimer6(MDCTimeStrUtils.convert15to13(obj.getTimerTimer6()));
                  }

                  if (obj.getTimerTimer7() != null) {
                     obj.setTimerTimer7(MDCTimeStrUtils.convert15to13(obj.getTimerTimer7()));
                  }
               }
            }

            obj.setTimer_holiday((String)null);
            if (obj.getTimeClockConf() != null) {
               obj.combineTimeClock();
            }
         } catch (NullPointerException var25) {
            this.logger.error("[REST_v2.0][DEVICE TIME SERVICE][getTimeInfo] This ID is not exist : " + deviceId);
            failList.add(deviceId);
            continue;
         } catch (Exception var26) {
            this.logger.error("", var26);
            failList.add(deviceId);
            continue;
         }

         if (obj.getTimerConfTimerList() != null && obj.getTimerConfTimerList().size() > 0) {
            List timeList = obj.getTimerConfTimerList();

            for(int i = 0; i < timeList.size(); ++i) {
               if (timeList.get(i) != null && i + 1 != ((DeviceTimeTimerConf)timeList.get(i)).getTimerSeq()) {
                  int seq = i + 1;
                  int timerSeq = ((DeviceTimeTimerConf)timeList.get(i)).getTimerSeq();
                  throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_PATTERN, new String[]{"parameter TimerConfTimerList : Timer Sequence[" + timerSeq + "]", "sequence number of the array[" + seq + "]"});
               }
            }

            try {
               obj.combineTimerValues(obj.getTimerConfTimerList());
            } catch (Exception var22) {
               this.logger.error("", var22);
               failList.add(deviceId);
               continue;
            }
         }

         try {
            new DeviceTimeConf();
            Device device = this.deviceDao.getDeviceMinInfo(deviceId);
            String deviceModelCode = device.getDevice_model_code();
            modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
            timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
            boolean isSeparatableDevice = true;
            if ("NEW".equalsIgnoreCase(modelKind)) {
               DeviceTimeConf tempTimeConf = this.deviceConf.getDeviceNewTimeConf(deviceId, timerCnt);
               isSeparatableDevice = this.separatableDevice(tempTimeConf, timerCnt);
            }

            this.logger.error("[SF00197953] isSeparatableDev just before converting timers: " + isSeparatableDevice);
            DeviceTimeConf deviceTimeConf = obj.converToTimeConf(isSeparatableDevice);
            deviceTimeConf.setDevice_id(deviceId);
            this.confManager.reqSetTimeToDevice(deviceTimeConf, requestId);
            List holidayList = obj.getDeviceTimeHolidayList();
            if (holidayList != null && holidayList.size() > 0) {
               DeviceTimeConf info = new DeviceTimeConf();
               info.setDevice_id(deviceId);
               Iterator var19 = holidayList.iterator();

               while(var19.hasNext()) {
                  DeviceTimeHolidayResource holiday = (DeviceTimeHolidayResource)var19.next();
                  String tmp = holiday.getHolidaySeq() + ";" + Integer.parseInt(holiday.getMonth1()) + ";" + Integer.parseInt(holiday.getDay1()) + ";" + Integer.parseInt(holiday.getMonth2()) + ";" + Integer.parseInt(holiday.getDay2());
                  info.setTimer_holiday(tmp);
                  this.confManager.reqSetTimeToDevice(info, requestId);
               }
            }

            successList.add(deviceId);
         } catch (NullPointerException var23) {
            this.logger.error("[REST_v2.0][DEVICE TIME SERVICE][getTimeInfo] This ID is not exist : " + deviceId);
            failList.add(deviceId);
         } catch (Exception var24) {
            this.logger.error("", var24);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      resource.setRequestId(requestId);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource filterDeviceTimeList(V2DeviceFilter filter, HttpServletRequest request, HttpServletResponse response) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      response.setContentType("application/json;charset=UTF-8");
      V2PageResource result = new V2PageResource();
      String productType = "PREMIUM";
      String sortColumn = "device_name";
      String sortOrder = filter.getSortOrder();
      String groupId = StrUtils.nvl(filter.getGroupId());
      String disconnectPeriod;
      if (!groupId.equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      } else {
         UserInfo userInfo = UserInfoImpl.getInstance();
         long mngOrgId = userContainer.getUser().getRoot_group_id();
         if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
            mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
         }

         disconnectPeriod = userInfo.getOrganGroupName(mngOrgId);
         groupId = String.valueOf(this.deviceGroupDao.getOrganGroupIdByName(disconnectPeriod));
      }

      boolean isRoot = false;
      Map res = this.deviceGroupDao.getDeviceOrganizationByGroupId(Integer.parseInt(groupId));
      if (res != null && res.get("GROUP_ID") != null && Long.parseLong(groupId) == (Long)res.get("GROUP_ID")) {
         isRoot = true;
      }

      if (!StrUtils.nvl(filter.getSortColumn()).equals("")) {
         sortColumn = filter.getSortColumn();
      }

      String connectionStatus = StrUtils.nvl(filter.getConnectionStatus()).equals("") ? "device_status_view_all" : filter.getConnectionStatus();
      disconnectPeriod = null;
      int lastindex = connectionStatus.lastIndexOf("_");
      if (connectionStatus.substring(0, lastindex).equals("device_status_view_disconnection")) {
         disconnectPeriod = connectionStatus.substring(lastindex + 1);
         connectionStatus = connectionStatus.substring(0, lastindex);
      }

      String expirationDate = "device_status_view_all";
      String customInputVal = "0";
      String groupIds = "";
      if (filter.getGroupIds() != null) {
         groupIds = this.convertString(filter.getGroupIds());
      }

      String tagIds = null;
      if (filter.getTagIds() != null) {
         tagIds = this.convertString(filter.getTagIds());
      }

      String alarmTypes = "";
      if (filter.getAlarmTypes() != null) {
         alarmTypes = this.convertString(filter.getAlarmTypes());
      }

      String functionTypes = "";
      if (filter.getFunctionTypes() != null) {
         functionTypes = this.convertString(filter.getFunctionTypes());
      }

      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      String inputSources = "";
      if (filter.getInputSources() != null) {
         inputSources = this.convertString(filter.getInputSources());
      }

      String deviceType = "";
      if (filter.getDeviceType() != null) {
         deviceType = this.convertString(filter.getDeviceType());
      }

      DeviceSystemInfoConfManager dao = DeviceSystemInfoConfManagerImpl.getInstance(productType);
      ListManager listMgr = new ListManager(dao, "commonlist");
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sortColumn);
      condition.setOrder_dir(sortOrder);
      condition.setGroup_id(Long.valueOf(groupId));
      condition.setSrc_name(filter.getSearchText());
      condition.setIsRoot(isRoot);
      condition.setStatus_view_mode(connectionStatus);
      if (disconnectPeriod != null) {
         condition.setDisconn_period(disconnectPeriod);
      }

      condition.setExpiration_date(expirationDate);
      condition.setCustom_input_val(customInputVal);
      condition.setFilter_group_ids(groupIds);
      condition.setRole_name(SecurityUtils.getUserContainer().getUser().getRole_name());
      condition.setUser_id(SecurityUtils.getUserContainer().getUser().getUser_id());
      condition.setTagFilter(tagIds);
      condition.setSourceFilter(inputSources);
      if (StringUtils.isNotBlank(alarmTypes)) {
         condition.setAlarmFiltersByString(alarmTypes);
      }

      if (StringUtils.isNotBlank(functionTypes)) {
         condition.setFunctionFiltersByString(functionTypes);
      }

      if (deviceType != null && !deviceType.equals("")) {
         condition.setDevice_type(deviceType);
      }

      ArrayList sourceList;
      String[] list;
      int totalChildCount;
      int i;
      if (StringUtils.isNotBlank(condition.getSourceFilter())) {
         sourceList = new ArrayList();
         list = condition.getSourceFilter().split(",");
         String[] var29 = list;
         totalChildCount = list.length;

         for(i = 0; i < totalChildCount; ++i) {
            String tag = var29[i];
            sourceList.add(Long.parseLong(tag));
         }

         condition.setSourceFilterList(sourceList);
      }

      searchText = StrUtils.nvl(searchText).equals("") ? "" : searchText;
      if (StringUtils.isNotBlank(searchText)) {
         condition.setCommonSearchKeyword(searchText);
      }

      sourceList = null;
      list = null;
      listMgr.addSearchInfo("condition", condition);
      listMgr.setLstSize(Integer.valueOf(filter.getPageSize()));
      listMgr.setSection("getDeviceSystemInfoConfList");
      List list = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());
      PageManager pageMgr = listMgr.getPageManager();
      response.setContentType("application/json;charset=UTF-8");
      List timeList = new ArrayList();
      result.setRecordsReturned(list.size());
      result.setRecordsTotal(pageMgr.getTotalRowCount());
      result.setRecordsFiltered(pageMgr.getTotalRowCount());
      result.setStartIndex(filter.getStartIndex());
      if (sortColumn != null && !sortColumn.equals("")) {
         result.setSortColumn(sortColumn);
         result.setSortOrder(sortOrder);
      }

      result.setPageSize(pageMgr.getInfo().getPageSize());
      totalChildCount = 0;
      i = 0;

      for(int cnt = 0; i < list.size(); ++i) {
         new V2DeviceTimeListResource();
         DeviceSystemInfoConf info = (DeviceSystemInfoConf)list.get(i);
         V2DeviceTimeListResource resource = this.writeData(cnt + "", productType, info, request);
         ++cnt;
         if (info.getHas_child() != null && info.getHas_child()) {
            totalChildCount = (int)((long)totalChildCount + info.getChild_cnt());
         }

         resource.setChildCount((long)totalChildCount);
         timeList.add(resource);
         if (info.getHas_child() != null && info.getHas_child()) {
            for(int j = 0; (long)j < info.getChild_cnt(); ++j) {
               new V2DeviceTimeListResource();
               DeviceSystemInfoConf childDevice = dao.getDeviceSystemInfoConf(info.getDevice_id() + "_" + (j + 1));
               if (childDevice != null) {
                  V2DeviceTimeListResource childResource = this.writeData(cnt + "", productType, childDevice, request);
                  timeList.add(childResource);
                  ++cnt;
               }
            }
         }
      }

      result = V2PageResource.createPageResource(timeList, pageMgr);
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource getTimeInfo(V2CommonIds body) throws Exception {
      List deviceIds = body.getIds();
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var20) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var7 = deviceIds.iterator();

      while(true) {
         while(var7.hasNext()) {
            String deviceId = (String)var7.next();
            Device device = this.deviceDao.getDeviceMinInfo(deviceId);
            if (device == null) {
               this.logger.error("[REST_v2.0][DEVICE SERVICE][getGeneralInfo] " + deviceId);
               failList.add(deviceId);
            } else {
               String deviceModelCode = device.getDevice_model_code();
               int timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
               new DeviceTimeConf();
               String modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
               Boolean isSeparatableDevice = true;
               DeviceTimeConf timeConf;
               if ("NEW".equalsIgnoreCase(modelKind)) {
                  timeConf = this.deviceConf.getDeviceNewTimeConf(deviceId, timerCnt);
                  timeConf.fillTimeClock();
                  timeConf.newfillTimerValues();
                  isSeparatableDevice = this.separatableDevice(timeConf, timerCnt);
               } else {
                  timeConf = this.deviceConf.getDeviceOldTimeConf(deviceId);
               }

               V2DeviceTimeconfResource timeConfCamel = DeviceModelConverter.newConvertTimeinfoToCamelStyle(timeConf);
               timeConfCamel.setModelKind(modelKind);
               timeConfCamel.setSeparatableDevice(isSeparatableDevice);
               timeConfCamel.setSupport(false);
               if ("0".equals(device.getTime_zone_version())) {
                  timeConfCamel.setSupport(true);
               }

               timeConfCamel.setDeviceCheck("sucess");
               timeConfCamel.setTimerCount(timerCnt);
               List tmpHolidayList = this.deviceConf.getDeviceTimeHolidayConf(deviceId);
               if (tmpHolidayList != null && tmpHolidayList.size() > 0) {
                  for(int i = 0; i < tmpHolidayList.size(); ++i) {
                     DeviceTimeHolidayConf tmp = (DeviceTimeHolidayConf)tmpHolidayList.get(i);
                     DeviceTimeHolidayResource holiday = new DeviceTimeHolidayResource();
                     holiday.setDeviceId(tmp.getDevice_id());
                     holiday.setMonth1(tmp.getMonth1());
                     holiday.setMonth2(tmp.getMonth2());
                     holiday.setDay1(tmp.getDay1());
                     holiday.setDay2(tmp.getDay2());
                     holiday.setHolidaySeq(0);
                     timeConfCamel.addDeviceTimeHolidayResource(holiday);
                  }
               }

               timeConfCamel.setInputSourceList(RESTDeviceUtils.getInputSourceList(RESTDeviceUtils.INPUTSOURCE_CODE, RESTDeviceUtils.INPUTSOURCE_NAME));
               successList.add(timeConfCamel);
            }
         }

         resource.setSuccessList(successList);
         resource.setFailList(failList);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceReqServiceResource getCurrentStatusTime(V2CommonIds body) throws Exception {
      List deviceIds = body.getIds();
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var15) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var7 = deviceIds.iterator();

      while(var7.hasNext()) {
         String deviceId = (String)var7.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      String sessionId = UUID.randomUUID().toString();

      String deviceId;
      for(Iterator var19 = deviceIds.iterator(); var19.hasNext(); this.confManager.reqGetTimeFromDevice(deviceId, sessionId)) {
         deviceId = (String)var19.next();

         try {
            Device device = this.deviceDao.getDeviceMinInfo(deviceId);
            String deviceModelCode = device.getDevice_model_code();
            String modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
            int var13 = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
         } catch (NullPointerException var14) {
            this.logger.error("[REST_v2.0][DEVICE TIME SERVICE][getCurrentStatusTime] This ID is not exist : " + deviceId);
            failList.add(deviceId);
         }
      }

      resource.setRequestId(sessionId);
      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2CommonBulkResultResource getCurrentStatusTimeResult(V2DeviceReqServiceConf body) throws Exception {
      List deviceIds = body.getDeviceIds();
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      String modelKind;
      while(var4.hasNext()) {
         modelKind = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, modelKind);
         } catch (Exception var23) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      String requestId = body.getRequestId();
      boolean flag = false;
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var11 = deviceIds.iterator();

      while(var11.hasNext()) {
         String deviceId = (String)var11.next();

         try {
            if (body.getStep() != null && body.getStep() == 4) {
               this.confManager.reqGetTimeFromDevice(deviceId, requestId);
               failList.add(deviceId);
            } else {
               Device device = this.deviceDao.getDeviceMinInfo(deviceId);
               if (device == null) {
                  this.logger.error("[REST_v2.0][DEVICE SERVICE][getGeneralInfo] " + deviceId);
                  failList.add(deviceId);
               } else {
                  String deviceModelCode = device.getDevice_model_code();
                  modelKind = MDCTimeStrUtils.getModelKind(deviceModelCode);
                  int timerCnt = MDCTimeStrUtils.getTimerCnt(deviceModelCode);
                  DeviceTimeConf info = this.confManager.getTimeResultSet(deviceId, requestId, "GET_DEVICE_TIME_CONF");
                  if (info == null) {
                     this.logger.error("[REST_v2.0][DEVICE TIME SERVICE][getCurrentStatusTimeResult] " + ExceptionCode.SVC823[0] + " , " + ExceptionCode.SVC823[2]);
                     failList.add(deviceId);
                  } else {
                     boolean isSeparatableDevice = true;
                     if ("NEW".equalsIgnoreCase(modelKind)) {
                        info.fillTimeClock();
                        info.newfillTimerValues();
                        isSeparatableDevice = this.separatableDevice(info, timerCnt);
                     } else {
                        info = this.deviceConf.getDeviceOldTimeConf(deviceId);
                     }

                     V2DeviceTimeconfResource timeConfCamel = DeviceModelConverter.newConvertTimeinfoToCamelStyle(info);
                     timeConfCamel.setSeparatableDevice(isSeparatableDevice);
                     timeConfCamel.setDeviceId(deviceId);
                     timeConfCamel.setTimerCount(timerCnt);
                     timeConfCamel.setModelKind(modelKind);
                     List tmpHolidayList = this.deviceConf.getDeviceTimeHolidayConf(deviceId);
                     if (tmpHolidayList != null && tmpHolidayList.size() > 0) {
                        Iterator var19 = tmpHolidayList.iterator();

                        while(var19.hasNext()) {
                           DeviceTimeHolidayConf tmp = (DeviceTimeHolidayConf)var19.next();
                           DeviceTimeHolidayResource holiday = new DeviceTimeHolidayResource();
                           holiday.setDeviceId(tmp.getDevice_id());
                           holiday.setMonth1(tmp.getMonth1());
                           holiday.setMonth2(tmp.getMonth2());
                           holiday.setDay1(tmp.getDay1());
                           holiday.setDay2(tmp.getDay2());
                           holiday.setHolidaySeq(0);
                           timeConfCamel.addDeviceTimeHolidayResource(holiday);
                        }
                     }

                     timeConfCamel.setInputSourceList(RESTDeviceUtils.getInputSourceList(RESTDeviceUtils.INPUTSOURCE_CODE, RESTDeviceUtils.INPUTSOURCE_NAME));
                     successList.add(timeConfCamel);
                  }
               }
            }
         } catch (Exception var22) {
            this.logger.error("[REST_v2.0][DEVICE TIME SERVICE][getCurrentStatusTimeResult]", var22);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2CommonStatusResource reqSetTimeToDevice(String deviceId, HttpServletRequest request) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      V2CommonStatusResource resource = new V2CommonStatusResource();
      resource.setId(deviceId);
      DeviceConfManager confManager = null;
      DeviceTimeConf dtc = null;

      try {
         confManager = DeviceConfManagerImpl.getInstance();
         dtc = confManager.getTimeResultSet(deviceId, request.getSession().getId(), "SET_DEVICE_TIME_CONF");
      } catch (Exception var7) {
         this.logger.error("[REST_v2.0][DEVICE TIME SERVICE][reqSetTimeToDevice] " + RestExceptionCode.INTERNAL_SERVER_ERROR.getMessage(), var7);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR);
      }

      if (dtc != null && dtc.getTimer_clock() != null && dtc.getTimer_clock().equalsIgnoreCase("SUCCESS")) {
         resource.setStatus("success");
      } else {
         resource.setStatus("error");
      }

      return resource;
   }

   private V2DeviceTimeListResource writeData(String cnt, String productType, DeviceSystemInfoConf deviceSystemInfo, HttpServletRequest request) throws Exception {
      V2DeviceTimeListResource resource = new V2DeviceTimeListResource();
      resource.setCheck("");
      resource.setDeviceId(deviceSystemInfo.getDevice_id());
      resource.setDeviceType(deviceSystemInfo.getDevice_type());
      resource.setDeviceName(deviceSystemInfo.getDevice_name());
      resource.setDeviceModelName(deviceSystemInfo.getDevice_model_name());
      resource.setDeviceTypeVersion(deviceSystemInfo.getDevice_type_version());
      boolean power = DeviceUtils.isConnected(deviceSystemInfo.getDevice_id());
      if (power && deviceSystemInfo.getIs_child() && !DeviceUtils.isConnected(deviceSystemInfo.getDevice_id().split("_")[0])) {
         power = false;
      }

      resource.setPower(power);
      if (deviceSystemInfo.getError_flag() != null) {
         resource.setIsErrorFlag(Boolean.valueOf(deviceSystemInfo.getError_flag()));
      }

      String deviceId = deviceSystemInfo.getDevice_id();
      String device_model_code = null;
      String modelKind = null;
      int timerCnt = true;
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      device_model_code = device.getDevice_model_code();
      modelKind = MDCTimeStrUtils.getModelKind(device_model_code);
      int timerCnt = MDCTimeStrUtils.getTimerCnt(device_model_code);
      DeviceTimeConfManager dao = DeviceTimeConfManagerImpl.getInstance(productType);
      DeviceTimeConf info = null;
      if (modelKind.equalsIgnoreCase("NEW")) {
         info = dao.getDeviceNewTimeConf(deviceId, timerCnt);
      } else {
         info = dao.getDeviceOldTimeConf(deviceId);
      }

      Locale userLocale = SecurityUtils.getLocale();
      String strClock = null;
      String strTimer1 = "-";
      String strTimer2 = "-";
      String strTimer3 = "-";
      String strTimer4 = "-";
      String strTimer5 = "-";
      String strTimer6 = "-";
      String strTimer7 = "-";
      String strHolidayCnt = "-";
      String userDateFormat = SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat();
      if (info.getTimer_clock() != null && !info.getTimer_clock().equals("")) {
         strClock = MDCTimeStrUtils.newClockToDate(info.getTimer_clock(), userDateFormat);
      }

      if (info.getTimer_timer1() != null && !info.getTimer_timer1().equals("")) {
         strTimer1 = MDCTimeStrUtils.newTimerToDate(info.getTimer_timer1());
      }

      if (info.getTimer_timer2() != null && !info.getTimer_timer2().equals("")) {
         strTimer2 = MDCTimeStrUtils.newTimerToDate(info.getTimer_timer2());
      }

      if (info.getTimer_timer3() != null && !info.getTimer_timer3().equals("")) {
         strTimer3 = MDCTimeStrUtils.newTimerToDate(info.getTimer_timer3());
      }

      if (timerCnt == 7) {
         if (info.getTimer_timer4() != null && !info.getTimer_timer4().equals("")) {
            strTimer4 = MDCTimeStrUtils.newTimerToDate(info.getTimer_timer4());
         }

         if (info.getTimer_timer5() != null && !info.getTimer_timer5().equals("")) {
            strTimer5 = MDCTimeStrUtils.newTimerToDate(info.getTimer_timer5());
         }

         if (info.getTimer_timer6() != null && !info.getTimer_timer6().equals("")) {
            strTimer6 = MDCTimeStrUtils.newTimerToDate(info.getTimer_timer6());
         }

         if (info.getTimer_timer7() != null && !info.getTimer_timer7().equals("")) {
            strTimer7 = MDCTimeStrUtils.newTimerToDate(info.getTimer_timer7());
         }
      }

      if (info.getHoliday_cnt() != null) {
         strHolidayCnt = info.getHoliday_cnt().toString();
      }

      resource.setMdcUpdateTime(info.getMdc_update_time());
      if (strClock != null) {
         resource.setClockTime(strClock);
         SimpleDateFormat sdf = new SimpleDateFormat(userDateFormat);
         Date tempDate = sdf.parse(strClock);
         Timestamp time = new Timestamp(tempDate.getTime());
         resource.setCurrentTime(time);
      }

      resource.setTimerTimer1Table(strTimer1);
      resource.setTimerTimer2Table(strTimer2);
      resource.setTimerTimer3Table(strTimer3);
      resource.setTimerTimer4Table(strTimer4);
      resource.setTimerTimer5Table(strTimer5);
      resource.setTimerTimer6Table(strTimer6);
      resource.setTimerTimer7Table(strTimer7);
      resource.setTimerHoliday(strHolidayCnt);
      resource.setDeviceModelKind(modelKind);
      resource.setVwtId(deviceSystemInfo.getVwt_id());
      resource.setIconErrorSw(deviceSystemInfo.getIcon_error_sw());
      resource.setIconErrorHw(deviceSystemInfo.getIcon_error_hw());
      resource.setIconAlarm(deviceSystemInfo.getIcon_alarm());
      resource.setIconProcessContentDownload(deviceSystemInfo.getIcon_process_content_download());
      resource.setIconProcessLog(deviceSystemInfo.getIcon_process_log());
      resource.setIconProcessSwDownload(deviceSystemInfo.getIcon_process_sw_download());
      resource.setIconMemo(deviceSystemInfo.getIcon_memo());
      resource.setIconBackup(deviceSystemInfo.getIcon_backup());
      resource.setWebcam(deviceSystemInfo.getWebcam());
      resource.setTimerCount(MDCTimeStrUtils.getTimerCnt(device_model_code));
      resource.setHasChild(deviceSystemInfo.getHas_child());
      if (info.getChild_cnt() != null) {
         resource.setChildCount(deviceSystemInfo.getChild_cnt());
      } else {
         resource.setChildCount(0L);
      }

      if (deviceSystemInfo.getIs_child() != null) {
         resource.setIsChild(deviceSystemInfo.getIs_child());
      }

      resource.setDeviceModelCode(device_model_code);
      return resource;
   }

   public Boolean separatableDevice(DeviceTimeConf timeConf, int timeCnt) {
      Boolean result = true;
      String[] tmpTimerArr;
      if (timeConf.getTimer_timer1() != null) {
         tmpTimerArr = timeConf.getTimer_timer1().split(";");
         if (tmpTimerArr.length == 13) {
            result = false;
         }
      }

      if (timeConf.getTimer_timer2() != null) {
         tmpTimerArr = timeConf.getTimer_timer2().split(";");
         if (tmpTimerArr.length == 13) {
            result = false;
         }
      }

      if (timeConf.getTimer_timer3() != null) {
         tmpTimerArr = timeConf.getTimer_timer3().split(";");
         if (tmpTimerArr.length == 13) {
            result = false;
         }
      }

      if (timeCnt == 7) {
         if (timeConf.getTimer_timer4() != null) {
            tmpTimerArr = timeConf.getTimer_timer4().split(";");
            if (tmpTimerArr.length == 13) {
               result = false;
            }
         }

         if (timeConf.getTimer_timer5() != null) {
            tmpTimerArr = timeConf.getTimer_timer5().split(";");
            if (tmpTimerArr.length == 13) {
               result = false;
            }
         }

         if (timeConf.getTimer_timer6() != null) {
            tmpTimerArr = timeConf.getTimer_timer6().split(";");
            if (tmpTimerArr.length == 13) {
               result = false;
            }
         }

         if (timeConf.getTimer_timer7() != null) {
            tmpTimerArr = timeConf.getTimer_timer7().split(";");
            if (tmpTimerArr.length == 13) {
               result = false;
            }
         }
      }

      return result;
   }

   public String convertString(List list) {
      String result = "";
      if (!list.isEmpty() && list.size() > 0) {
         StringBuffer strBuf = new StringBuffer();

         for(int i = 0; i < list.size(); ++i) {
            strBuf.append(list.get(i));
            if (i < list.size() - 1) {
               strBuf.append(",");
            }
         }

         result = strBuf.toString();
      }

      return result;
   }
}
