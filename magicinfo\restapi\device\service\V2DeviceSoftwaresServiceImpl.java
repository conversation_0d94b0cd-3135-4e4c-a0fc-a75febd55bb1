package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.exception.CMSExceptionCode;
import com.samsung.common.export.PdfBuilder;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.SequenceDB;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.content.dao.DeleteFileDao;
import com.samsung.magicinfo.framework.content.dao.DeleteFileDaoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.log.entity.ServerLogEntity;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManager;
import com.samsung.magicinfo.framework.device.log.manager.DeviceLogManagerImpl;
import com.samsung.magicinfo.framework.device.software.dao.SoftwareDao;
import com.samsung.magicinfo.framework.device.software.entity.Software;
import com.samsung.magicinfo.framework.device.software.entity.SoftwareReservation;
import com.samsung.magicinfo.framework.device.software.entity.SoftwareType;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareFileExtractThread;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareFileZipThread;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManager;
import com.samsung.magicinfo.framework.device.software.manager.SoftwareManagerImpl;
import com.samsung.magicinfo.protocol.file.ChecksumCRC32;
import com.samsung.magicinfo.protocol.file.FileLoaderServlet;
import com.samsung.magicinfo.protocol.file.FileUploadCommonHelper;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteFail;
import com.samsung.magicinfo.restapi.common.model.V2CommonUpdateResult;
import com.samsung.magicinfo.restapi.device.model.V2DeviceModelNameResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSoftwareDeploy;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSoftwareDeployResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSoftwareItem;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSoftwareResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSwDeployInfoItem;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSwDeployInfoResource;
import com.samsung.magicinfo.restapi.device.model.V2ReservedSoftwareResource;
import com.samsung.magicinfo.restapi.device.model.V2SoftwareDeploymentStatus;
import com.samsung.magicinfo.restapi.device.model.V2SoftwareFileResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.service.statistics.DeviceStatisticsDownloadService;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import jodd.util.StringUtil;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.logging.log4j.Logger;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

@Service("V2DeviceSoftwaresService")
@Transactional
public class V2DeviceSoftwaresServiceImpl implements V2DeviceSoftwaresService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceSoftwaresServiceImpl.class);
   private DeviceStatisticsDownloadService downloadService = null;

   public V2DeviceSoftwaresServiceImpl() {
      super();
   }

   public void setDownloadService(DeviceStatisticsDownloadService downloadService) {
      this.downloadService = downloadService;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceSoftwareResource saveSw(String deviceType, String swName, String softwareType, String deviceModel, MultipartFile file, HttpServletRequest request) throws SQLException {
      String filePath = null;
      String thumbFilePath = null;
      V2DeviceSoftwareResource resource = new V2DeviceSoftwareResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      new LinkedHashMap();
      Locale var12 = SecurityUtils.getLocale();

      try {
         DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();
         new ServerLogEntity();
         HttpSession session = request.getSession();
         session.setMaxInactiveInterval(3600);
         deviceType = StrUtils.nvl(deviceType).equals("") ? "iPLAYER" : deviceType;
         swName = StrUtils.nvl(swName).equals("") ? "" : swName;
         softwareType = StrUtils.nvl(softwareType).equals("") ? "" : softwareType;
         if (StrUtils.nvl(deviceModel).equals("")) {
            deviceModel = "Default";
         }

         String fileName = file.getOriginalFilename();
         int pos = fileName.lastIndexOf(".");
         String ext = fileName.substring(pos + 1);
         if (!ext.equalsIgnoreCase("html") && !ext.equalsIgnoreCase("js") && !ext.equalsIgnoreCase("jsp")) {
            if (!file.getContentType().split("/")[1].equals("html") && !file.getContentType().split("/")[1].equals("javascript")) {
               Software software = new Software();
               if (swName.equals("")) {
                  swName = file.getOriginalFilename();
               }

               software.setSoftware_name(swName);
               software.setFile_name(file.getOriginalFilename());
               software.setFile_size(file.getSize());
               software.setDevice_type(deviceType);
               if ("LPLAYER".equalsIgnoreCase(deviceType)) {
                  software.setDevice_model_name(StrUtils.getLiteDeviceModelName(deviceModel));
               } else {
                  software.setDevice_model_name(deviceModel);
               }

               software.setIs_auto_update(false);
               software.setCreator_id(userContainer.getUser().getUser_id());
               software.setUpgrade_version("multi");
               String savedFileName = System.currentTimeMillis() + file.getOriginalFilename();
               String fileSavePath;
               if (softwareType.equalsIgnoreCase("customLogo")) {
                  fileSavePath = "sw.logo";
                  software.setSoftware_type("03");
               } else if (softwareType.equalsIgnoreCase("defaultContent")) {
                  fileSavePath = "sw.default_content";
                  software.setSoftware_type("04");
               } else if (softwareType.equalsIgnoreCase("wifiCertificate")) {
                  fileSavePath = "sw.wifi_certificate";
                  software.setSoftware_type("06");
               } else if (softwareType.equalsIgnoreCase("sslCertificate")) {
                  fileSavePath = "sw.ssl_certificate";
                  software.setSoftware_type("07");
               } else if (softwareType.equalsIgnoreCase("webApp")) {
                  fileSavePath = "sw.application";
                  software.setSoftware_type("05");
               } else {
                  fileSavePath = "sw.application";
                  software.setSoftware_type("");
               }

               boolean isFileSuccess = FileUploadCommonHelper.saveFileToDisk(savedFileName, file, fileSavePath);
               if (!isFileSuccess) {
                  throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_FILE_UPLOAD);
               } else {
                  software.setMime_type(ChecksumCRC32.getCRC32Value(savedFileName, fileSavePath));
                  software.setFile_path(FileUploadCommonHelper.getWebPath(fileSavePath) + "/" + savedFileName);
                  Long software_id = new Long((long)SequenceDB.getNextValue("MI_DMS_INFO_SOFTWARE"));
                  software.setSoftware_id(software_id);
                  String UPLOAD_HOME = "";
                  UPLOAD_HOME = CommonConfig.get("UPLOAD_HOME");
                  UPLOAD_HOME = UPLOAD_HOME.replace('/', File.separatorChar);
                  FileManagerImpl fileManager;
                  File swFile;
                  if ("03".equalsIgnoreCase(software.getSoftware_type())) {
                     fileManager = FileManagerImpl.getInstance();
                     filePath = FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.logo") + File.separator + savedFileName;
                     swFile = new File(filePath);
                     thumbFilePath = fileManager.createThumbnailFile(swFile, FileUploadCommonHelper.getAbsoluteDirectoryPath(fileSavePath), savedFileName);
                     (new StringBuilder()).append(FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.logo")).append(File.separator).append(thumbFilePath).toString();
                  } else if ("04".equalsIgnoreCase(software.getSoftware_type())) {
                     fileManager = FileManagerImpl.getInstance();
                     filePath = FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.default_content") + File.separator + savedFileName;
                     swFile = new File(filePath);
                     thumbFilePath = fileManager.createThumbnailFile(swFile, FileUploadCommonHelper.getAbsoluteDirectoryPath(fileSavePath), savedFileName);
                     (new StringBuilder()).append(FileUploadCommonHelper.getAbsoluteDirectoryPath("sw.default_content")).append(File.separator).append(thumbFilePath).toString();
                  } else {
                     SoftwareFileZipThread fileExtract;
                     Thread threadFileExtract;
                     if ("06".equalsIgnoreCase(software.getSoftware_type())) {
                        fileExtract = new SoftwareFileZipThread(UPLOAD_HOME, software.getFile_path(), savedFileName, file.getOriginalFilename(), fileSavePath, software_id);
                        threadFileExtract = new Thread(fileExtract);
                        threadFileExtract.start();
                        software.setStatus("zipping");
                     } else if ("07".equalsIgnoreCase(software.getSoftware_type())) {
                        fileExtract = new SoftwareFileZipThread(UPLOAD_HOME, software.getFile_path(), savedFileName, file.getOriginalFilename(), fileSavePath, software_id);
                        threadFileExtract = new Thread(fileExtract);
                        threadFileExtract.start();
                        software.setStatus("zipping");
                     } else {
                        filePath = UPLOAD_HOME + software.getFile_path();
                        String version = null;
                        if ("webApp".equalsIgnoreCase(softwareType) && "KIOSK".equalsIgnoreCase(deviceType)) {
                           version = DeviceUtils.getSoftwareVersionCustomFormat(filePath);
                        } else {
                           version = DeviceUtils.getSoftwareVersion(filePath);
                        }

                        if (version == null || version.equals("")) {
                           throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"The version information of file"});
                        }

                        software.setSoftware_version(version);
                        software.setFirmware_indicators(DeviceUtils.getFirmwareIndicators(version));
                        int count = DeviceUtils.getFileNumberfromZip(UPLOAD_HOME + software.getFile_path());
                        if ("webApp".equalsIgnoreCase(softwareType) && "KIOSK".equalsIgnoreCase(deviceType)) {
                           software.setStatus("done");
                        } else if (count == 2) {
                           Runnable fileExtract = new SoftwareFileExtractThread(UPLOAD_HOME, software.getFile_path(), savedFileName, fileSavePath, software_id);
                           Thread threadFileExtract = new Thread(fileExtract);
                           threadFileExtract.start();
                           software.setStatus("extract");
                           software.setUpgrade_version("single");
                        } else {
                           software.setStatus("done");
                        }
                     }
                  }

                  SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
                  boolean result = softwareDao.addDeviceSoftware(software, "P");
                  if (result) {
                     request.setAttribute("msg", "software_file_upload_success");
                     resource.setDeviceModelName(deviceModel);
                     resource.setDeviceType(deviceType);
                     resource.setSoftwareType(softwareType);
                     resource.setSoftwareName(swName);
                     return resource;
                  } else {
                     throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_FILE_UPLOAD);
                  }
               }
            } else {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_FILE_UPLOAD);
            }
         } else {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_FILE_UPLOAD);
         }
      } catch (Exception var30) {
         this.logger.error("Fail to add new software", var30);
         DeleteFileDao deleteFileDao = DeleteFileDaoImpl.getInstance();
         if (filePath != null) {
            deleteFileDao.insert(filePath);
         }

         if (thumbFilePath != null) {
            deleteFileDao.insert(thumbFilePath);
         }

         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_FILE_UPLOAD);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2CommonBulkResultResource deleteSw(List swIds, HttpServletRequest request) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      Locale locale = SecurityUtils.getLocale();
      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      new ServerLogEntity();
      DeviceLogManager logMgr = DeviceLogManagerImpl.getInstance();

      for(int i = 0; i < swIds.size(); ++i) {
         try {
            String softwareName = softwareDao.getSoftware(Long.parseLong((String)swIds.get(i))).getSoftware_name();
            boolean result = softwareDao.deleteSoftware(Long.parseLong((String)swIds.get(i)));
            if (!result) {
               V2CommonDeleteFail fail = new V2CommonDeleteFail();
               fail.setId((String)swIds.get(i));
               fail.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_DELETE_FAIL.getMessage());
               fail.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_DELETE_FAIL.getCode());
               failList.add(fail);
            } else {
               successList.add(swIds.get(i));
            }
         } catch (Exception var15) {
            this.logger.error("", var15);
            V2CommonDeleteFail fail = new V2CommonDeleteFail();
            fail.setId((String)swIds.get(i));
            fail.setReason(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_DELETE_FAIL.getMessage());
            fail.setReasonCode(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_DELETE_FAIL.getCode());
            failList.add(fail);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceSoftwareResource editSwSave(V2DeviceSoftwareItem body, String softwareId) throws Exception {
      V2DeviceSoftwareResource resource = new V2DeviceSoftwareResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      Software software = softwareDao.getSoftware(Long.parseLong(softwareId));
      if (software == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"device software information"});
      } else {
         software.setSoftware_name(body.getSoftwareName());
         if (body.getDeviceModelName() != null && !body.getDeviceModelName().equals("")) {
            software.setDevice_model_name(body.getDeviceModelName());
         }

         boolean result = softwareDao.setDeviceSoftware(software);
         if (!result) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_FILE_UPDATE);
         } else {
            resource.setSoftwareName(body.getSoftwareName());
            resource.setDeviceModelName(body.getDeviceModelName());
            resource.setSoftwareId(softwareId);
            return resource;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceSoftwareDeployResource editSwDeploy(String softwareId) throws Exception {
      V2DeviceSoftwareDeployResource resource = new V2DeviceSoftwareDeployResource();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      SoftwareReservation software = softwareDao.getReservationInfo(Long.parseLong(softwareId));
      if (software == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"device software information"});
      } else {
         List groupList = null;
         List groupNameList = new ArrayList();
         if ("GROUP".equalsIgnoreCase(software.getDeploy_type())) {
            groupList = softwareDao.getRsvGroupName(software.getSoftware_rsv_id());

            for(int i = 0; i < groupList.size(); ++i) {
               String temp = (String)((Map)groupList.get(i)).get("group_name");
               groupNameList.add(temp);
            }
         }

         resource.setSoftwareId(softwareId);
         resource.setDeviceModelName(software.getDevice_model_name());
         resource.setSoftwareType(software.getSoftware_type());
         resource.setSoftwareName(software.getSoftware_name());
         resource.setFileName(software.getFile_name());
         resource.setSoftwareVersion(software.getSoftware_version());
         resource.setMimeType(software.getCrcInfo());
         resource.setReservationDate(software.getReservation_date());
         resource.setDeployAppliedVer(software.getDeploy_applied_ver());
         resource.setDeviceType(software.getDevice_type());
         resource.setType(software.getType());
         resource.setGroupNameList(groupNameList);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceSoftwareDeployResource cancelSwDeploy(String swRsvId, HttpServletRequest request) throws Exception {
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2DeviceSoftwareDeployResource resource = new V2DeviceSoftwareDeployResource();
      SoftwareManager softwareMgr = SoftwareManagerImpl.getInstance();
      Locale var6 = SecurityUtils.getLocale();

      try {
         if (!softwareMgr.checkDeploiedSoftware(Long.valueOf(swRsvId))) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_DEPOLY_CHECK);
         } else {
            boolean result = softwareMgr.deleteReservation(Long.parseLong(swRsvId));
            if (result) {
               softwareMgr.updateReservationTypeBySwRsvId(Long.parseLong(swRsvId), 3L);
               resource.setSwReservationId(Long.valueOf(swRsvId));
               return resource;
            } else {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_JOB_CANCEL_FAIL);
            }
         }
      } catch (Exception var8) {
         this.logger.error("", var8);
         throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_RESERVATION_FAIL);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceSoftwareDeployResource saveSwDeploy(String swRsvId, String rsvDate) throws Exception {
      SoftwareManager softwareMgr = SoftwareManagerImpl.getInstance();
      long rsvType = 0L;
      V2DeviceSoftwareDeployResource resource = new V2DeviceSoftwareDeployResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      Timestamp serviceStartDate = null;
      if (rsvDate != null && !rsvDate.equals("")) {
         String creatorId = userContainer.getUser().getUser_id();
         if (rsvDate.equals("NOW")) {
            serviceStartDate = Timestamp.valueOf((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(Calendar.getInstance().getTime()));
            rsvType = 0L;
         } else {
            serviceStartDate = new Timestamp(Long.parseLong(rsvDate));
            rsvType = 1L;
            this.validateReservationTime(serviceStartDate);
         }

         SoftwareReservation reservation = new SoftwareReservation();
         reservation.setSoftware_rsv_id(Long.parseLong(swRsvId));
         reservation.setReservation_date(serviceStartDate);
         reservation.setCreator_id(creatorId);
         reservation.setType(rsvType);
         boolean result = softwareMgr.updateReservation(reservation);
         if (result) {
            resource.setSoftwareId(swRsvId);
            return resource;
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND);
         }
      } else {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"deploy start date"});
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceSoftwareResource publishPopup(String softwareId) throws Exception {
      V2DeviceSoftwareResource resource = new V2DeviceSoftwareResource();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      Software software = softwareDao.getSoftware(Long.parseLong(softwareId));
      if (software == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"device software information"});
      } else {
         Map map = new HashMap();
         map.put("deviceType", software.getDevice_type());
         map.put("firmwareIndicators", software.getFirmware_indicators());
         List verList = deviceDao.getAppVersionListBy(map);
         Date today = new Date();
         resource.setSoftwareId(softwareId);
         resource.setDeviceType(software.getDevice_type());
         resource.setSoftwareName(software.getSoftware_name());
         resource.setSoftwareType(software.getSoftware_type());
         resource.setDeviceModelName(software.getDevice_model_name());
         resource.setSoftwareVersion(software.getSoftware_version());
         resource.setSoftwareFileName(software.getFile_name());
         resource.setCrc(software.getMime_type());
         resource.setFirmwareIndicators(software.getFirmware_indicators());
         List deviceModelNameList = deviceDao.getDeviceModelNameListBy(map);
         resource.setDeviceModelNameList(deviceModelNameList);
         List list = new ArrayList();

         for(int i = 0; i < verList.size(); ++i) {
            if (verList.get(i) != null) {
               String temp = (String)((Map)verList.get(i)).get("VERSION");
               list.add(i, temp);
            }
         }

         resource.setVersionList(list);
         resource.setCurrentDate(today);
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2CommonUpdateResult publishSave(V2DeviceSoftwareDeploy body) throws Exception {
      String rsvDate = body.getRsvDate();
      String deployAppliedVer = body.getDeployAppliedVer();
      String appliedType = body.getAppliedType();
      List appliedGroupIds = body.getGroupIds();
      String appliedGroupId = ConvertUtil.convertListToStringWithSeparator(appliedGroupIds, ",");
      String msg = "";
      if ("GROUP".equalsIgnoreCase(appliedType)) {
         boolean checkFlag = false;
         if (appliedGroupIds != null) {
            Iterator var9 = appliedGroupIds.iterator();

            while(var9.hasNext()) {
               String groupId = (String)var9.next();

               try {
                  RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.parseLong(groupId));
               } catch (Exception var20) {
                  checkFlag = true;
                  break;
               }
            }
         } else {
            checkFlag = true;
         }

         if (checkFlag) {
            RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
         }
      }

      V2CommonUpdateResult resource = new V2CommonUpdateResult();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Timestamp serviceStartDate = null;
      SoftwareManager swMgr = SoftwareManagerImpl.getInstance();
      Long result = 0L;
      String softwareId = body.getSoftwareId();
      Software software = swMgr.getSoftware(Long.parseLong(softwareId));
      if (software == null) {
         this.logger.error("Failed to deploy software to devices. softwareId : " + softwareId);
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"device software information"});
      } else {
         if (rsvDate.equals("NOW")) {
            String timeStamp = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(Calendar.getInstance().getTime());
            software.setRsv_date(Timestamp.valueOf(timeStamp));
         } else {
            software.setType(1L);
            serviceStartDate = new Timestamp(Long.parseLong(rsvDate));
            this.validateReservationTime(serviceStartDate);
         }

         software.setDeploy_applied_ver(deployAppliedVer);
         software.setApplied_type(appliedType);
         if (serviceStartDate != null) {
            software.setRsv_date(serviceStartDate);
         }

         if ("DEVICE".equals(appliedType)) {
            if (null == body.getDeviceIds()) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FIRMWARE_FAIL_NO_TARGET);
            }

            software.setDeviceIds(body.getDeviceIds());
         } else if ("GROUP".equals(appliedType)) {
            software.setDevice_group(appliedGroupId);
         }

         UserContainer userContainer = SecurityUtils.getUserContainer();
         software.setSubscriber_id(userContainer.getUser().getUser_id());
         Object[] obj = swMgr.deploySoftwareToDevices(software, (String)null);
         if (obj != null && obj.length > 0) {
            result = (Long)obj[0];
         }

         HashMap errHash = new HashMap();
         if (obj != null && obj.length > 1) {
            errHash = (HashMap)obj[1];
         }

         boolean flag = result > 0L;
         if (!flag) {
            this.logger.error("Failed to deploy software to devices.(" + result + ") appliedGroupIds : " + appliedGroupIds);
            if (errHash.get("ALREADY_FIRMWARE") != null) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FIRMWARE_FAIL_ALREADY_FIRMWARE, new String[]{String.valueOf(errHash.get("ALREADY_FIRMWARE"))});
            } else if (errHash.get("OFFLINE_DEVICE") != null) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FIRMWARE_FAIL_OFFLINE_DEVICE, new String[]{String.valueOf(errHash.get("OFFLINE_DEVICE"))});
            } else if (errHash.get("NO_TARGET") != null) {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FIRMWARE_FAIL_NO_TARGET, new String[]{String.valueOf(errHash.get("NO_TARGET"))});
            } else {
               throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_FILE_DEPLOY);
            }
         } else {
            if (software.getDevice_group() != null) {
               successList = ConvertUtil.convertRepeatedDaysFromArrayToList(software.getDevice_group());
            } else if (software.getDeviceIds() != null) {
               successList = software.getDeviceIds();
            }

            resource.setSuccessList((List)successList);
            resource.setFailList(failList);
            return resource;
         }
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceSwDeployInfoResource swPublishDetail(String swReservationId, int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder) throws Exception {
      Locale locale = SecurityUtils.getLocale();
      V2DeviceSwDeployInfoResource resources = new V2DeviceSwDeployInfoResource();
      List list = new ArrayList();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      ListManager listMgr = new ListManager(softwareDao, "commonlist");
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sortColumn);
      condition.setOrder_dir(sortOrder);
      searchText = StrUtils.nvl(searchText);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      condition.setSrc_name(searchText);
      listMgr.addSearchInfo("condition", condition);
      listMgr.addSearchInfo("software_rsv_id", Long.valueOf(swReservationId));
      listMgr.setLstSize(Integer.valueOf(pageSize));
      listMgr.setSection("getReservedDeviceList");
      List reservationList = listMgr.V2dbexecute(startIndex, pageSize);
      PageManager pageMgr = listMgr.getPageManager();
      Integer powerOnCount = 0;

      int cntDevices;
      for(cntDevices = 0; cntDevices < reservationList.size(); ++cntDevices) {
         SoftwareReservation reservation = (SoftwareReservation)reservationList.get(cntDevices);
         V2DeviceSwDeployInfoItem resource = new V2DeviceSwDeployInfoItem();
         resource.setDeviceId(reservation.getDevice_id());
         resource.setDeviceName(reservation.getDevice_name());
         resource.setDeviceModelName(reservation.getDevice_model_name() != null ? reservation.getDevice_model_name() : "");
         resource.setGroupName(reservation.getGroup_name());
         resource.setSoftwareName(reservation.getSoftware_name());
         SimpleDateFormat sdf = new SimpleDateFormat(SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat());
         String start_date = "";
         String end_date = "";
         if (reservation.getService_start_date() != null) {
            start_date = sdf.format(reservation.getService_start_date());
            String[] date_array = start_date.split(" ");
            if (date_array.length == 2) {
               start_date = date_array[0] + "<br>" + date_array[1];
            }
         }

         resource.setServiceStartDate(start_date);
         if (reservation.getService_end_date() != null) {
            end_date = sdf.format(reservation.getService_end_date());
         }

         resource.setServiceEndDate(end_date);
         String application_version = "";
         String[] application_split;
         if (reservation.getApplication_version() != null) {
            application_version = reservation.getApplication_version();
            if (application_version.indexOf(";") > 0) {
               application_split = application_version.split(";");
               application_version = application_split[0];
            }
         }

         resource.setApplicationVersion(application_version);
         application_split = null;
         String progress = reservation.getProgress();
         if (progress != null && !progress.equals("")) {
            if (isStringDouble(progress)) {
               resource.setProgress(progress + '%');
            } else {
               resource.setProgress(progress);
            }
         } else {
            try {
               resource.setProgress("Unknown");
            } catch (Exception var30) {
               this.logger.error("", var30);
            }
         }

         String download_status = getDownloadStatus(reservation);

         try {
            resource.setDownloadStatus(download_status);
         } catch (Exception var29) {
            this.logger.error("", var29);
         }

         String fail_status = null;
         fail_status = reservation.GetFail_status();
         if (fail_status != null && !fail_status.equals("")) {
            resource.setFailStatus(fail_status);
         } else {
            resource.setFailStatus("");
         }

         resource.setRegistTime(reservation.getRegist_time());
         Boolean power = DeviceUtils.isConnected(reservation.getDevice_id());
         if (power) {
            powerOnCount = powerOnCount + 1;
         }

         resource.setPower(power);
         list.add(resource);
      }

      resources.setList(list);
      resources.setRecordsPowerOn(powerOnCount);
      cntDevices = softwareDao.getCntDownloadStatus(Long.valueOf(swReservationId), "reserved");
      int cntStandBy = softwareDao.getCntDownloadStatus(Long.valueOf(swReservationId), "stand_by");
      int cntDownloading = softwareDao.getCntDownloadStatus(Long.valueOf(swReservationId), "downloading");
      int cntFinished = softwareDao.getCntDownloadStatus(Long.valueOf(swReservationId), "download_finished");
      int cntUpgradeSuccess = softwareDao.getCntDownloadStatus(Long.valueOf(swReservationId), "upgrade_success");
      int cntUpgradeFail = softwareDao.getCntDownloadStatus(Long.valueOf(swReservationId), "upgrade_fail");
      resources.setCntDevices(cntDevices);
      resources.setCntStandBy(cntStandBy);
      resources.setCntDownloading(cntDownloading);
      resources.setCntFinished(cntFinished);
      resources.setCntUpgradeSuccess(cntUpgradeSuccess);
      resources.setCntUpgradeFail(cntUpgradeFail);
      return resources;
   }

   public V2DeviceSoftwareDeployResource swDeployRetry(String swReservationId, String rsvDate) throws Exception {
      V2DeviceSoftwareDeployResource resource = new V2DeviceSoftwareDeployResource();
      Long result = 0L;
      String msg = "";
      HashMap errHash = new HashMap();

      try {
         SoftwareManager swMgr = SoftwareManagerImpl.getInstance();
         SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
         SoftwareReservation reservation = softwareDao.getReservationInfo(Long.valueOf(swReservationId));
         if (reservation != null) {
            if (rsvDate.equals("NOW")) {
               reservation.setType(0L);
               reservation.setReservation_date(new Timestamp(System.currentTimeMillis() + 30000L));
            } else {
               reservation.setType(1L);
               reservation.setReservation_date(new Timestamp(Long.parseLong(rsvDate)));
            }

            Object[] obj = swMgr.retryDeploySoftware(reservation);
            if (obj != null && obj.length > 0) {
               result = (Long)obj[0];
            }

            if (obj != null && obj.length > 1) {
               errHash = (HashMap)obj[1];
            }
         }
      } catch (Exception var11) {
         this.logger.error("[SoftwareController] PUBLISH_RETRY fail! softwareRsvId : " + swReservationId);
         result = 0L;
      }

      boolean flag = result > 0L;
      if (!flag) {
         this.logger.error("Failed to deploy software to devices.(" + result + ")");
         if (errHash.get("ALREADY_FIRMWARE") != null) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FIRMWARE_FAIL_ALREADY_FIRMWARE, new String[]{String.valueOf(errHash.get("ALREADY_FIRMWARE"))});
         } else if (errHash.get("OFFLINE_DEVICE") != null) {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_FIRMWARE_FAIL_OFFLINE_DEVICE, new String[]{String.valueOf(errHash.get("OFFLINE_DEVICE"))});
         } else {
            throw new RestServiceException(RestExceptionCode.INTERNAL_SERVER_ERROR_SOFTWARE_FILE_DEPLOY);
         }
      } else {
         resource.setSwReservationId(Long.valueOf(swReservationId));
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceSoftwareResource editSw(String softwareId) throws Exception {
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      V2DeviceSoftwareResource resource = new V2DeviceSoftwareResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      Software software = softwareDao.getSoftware(Long.parseLong(softwareId));
      if (software == null) {
         throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"device software information"});
      } else {
         String deviceType = software.getDevice_type();
         List modelNameList = null;
         String organization = userContainer.getUser().getOrganization();
         if (!"iPLAYER".equalsIgnoreCase(deviceType) && !"APLAYER".equalsIgnoreCase(deviceType) && !"Default".equalsIgnoreCase(software.getDevice_model_name())) {
            modelNameList = deviceInfo.getConnectedDeviceModelNameListTypeS(deviceType, organization);
         }

         resource.setSoftwareId(softwareId);
         resource.setDeviceType(deviceType);
         resource.setSoftwareName(software.getSoftware_name());
         resource.setSoftwareType(software.getSoftware_type());
         if (!"03".equalsIgnoreCase(software.getSoftware_type()) && !"04".equalsIgnoreCase(software.getSoftware_type())) {
            resource.setSoftwareVersion(software.getSoftware_version());
            resource.setDeviceModelName(software.getDevice_model_name());
            resource.setCrc(software.getMime_type());
            List list = new ArrayList();
            if (modelNameList != null) {
               for(int i = 0; i < modelNameList.size(); ++i) {
                  String temp = (String)((Map)modelNameList.get(i)).get("device_model_name");
                  list.add(i, temp);
               }
            }

            resource.setDeviceModelNameList(list);
         }

         return resource;
      }
   }

   public SelectCondition setSoftwareType(SelectCondition selectCondition, String softwareType) {
      if ("LOGO".equalsIgnoreCase(softwareType)) {
         selectCondition.setType("LOGO");
      } else if ("DEFAULT".equalsIgnoreCase(softwareType)) {
         selectCondition.setType("DEFAULT");
      } else if ("CUSTOM".equalsIgnoreCase(softwareType)) {
         selectCondition.setType("CUSTOM");
      } else if (!"WIFI_CERTIFICATE".equalsIgnoreCase(softwareType) && !"WIFICERTIFICATE".equalsIgnoreCase(softwareType)) {
         if (!"SSL_CERTIFICATE".equalsIgnoreCase(softwareType) && !"SSLCERTIFICATE".equalsIgnoreCase(softwareType)) {
            selectCondition.setType("APPLICATION");
         } else {
            selectCondition.setType("07");
         }
      } else {
         selectCondition.setType("06");
      }

      return selectCondition;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2PageResource getRegisteredSoftwareList(int startIndex, int pageSize, String softwareType, String searchText, String sortColumn, String sortOrder) throws Exception {
      String sort_name = "create_date";
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = sort_name;
      }

      String order_dir = "desc";
      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = order_dir;
      }

      if (StrUtils.nvl(searchText).equals("")) {
         searchText = "";
      }

      Locale locale = SecurityUtils.getLocale();
      String userDateTimeFormat = SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat();
      SelectCondition selectCondition = new SelectCondition();
      selectCondition.setSort_name(sortColumn);
      selectCondition.setOrder_dir(sortOrder);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      selectCondition.setSrc_name(searchText);
      selectCondition = this.setSoftwareType(selectCondition, softwareType);
      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      SoftwareDao dao = new SoftwareDao();
      PagedListInfo result = dao.getRegisteredSoftwareList(startIndex, pageSize, condition);
      List swList = result.getPagedResultList();
      List list = new ArrayList();

      for(int i = 0; i < swList.size(); ++i) {
         Software software = (Software)swList.get(i);
         V2SoftwareFileResource dataItem = new V2SoftwareFileResource();
         dataItem.setDeviceName(software.getDevice_model_name());
         dataItem.setDeviceType(software.getDevice_type());
         dataItem.setFileName(software.getFile_name());
         dataItem.setSoftwareId(software.getSoftware_id());
         dataItem.setSoftwareName(software.getSoftware_name());
         dataItem.setSoftwareType(software.getSoftware_type());
         dataItem.setCrcInfo(StrUtils.nvl(software.getMime_type()));
         dataItem.setSoftwareVersion(software.getSoftware_version());
         dataItem.setFileSize(software.getFile_size());
         dataItem.setCreatorId(software.getCreator_id());
         dataItem.setFilePath(software.getFile_path());
         dataItem.setStatus(software.getStatus());
         dataItem.setCreateDate(software.getCreate_date());
         if (!"03".equalsIgnoreCase(software.getSoftware_type()) && !"04".equalsIgnoreCase(software.getSoftware_type())) {
            if ("06".equalsIgnoreCase(software.getSoftware_type())) {
               dataItem.setDetail("WIFI Certificate");
               dataItem.setSoftwareType(SoftwareType.WIFI_CERTIFICATE.getType());
            } else if ("07".equalsIgnoreCase(software.getSoftware_type())) {
               dataItem.setDetail("SSL Certificate");
               dataItem.setSoftwareType(SoftwareType.SSL_CERTIFICATE.getType());
            } else {
               dataItem.setDeviceModelName(software.getDevice_model_name());
            }
         } else {
            String ext = null;
            String fileType = null;
            if (software.getFile_name() != null) {
               ext = software.getFile_name().substring(software.getFile_name().lastIndexOf(".") + 1);
               byte var23 = -1;
               switch(ext.hashCode()) {
               case 97669:
                  if (ext.equals("bmp")) {
                     var23 = 0;
                  }
                  break;
               case 102340:
                  if (ext.equals("gif")) {
                     var23 = 1;
                  }
                  break;
               case 105441:
                  if (ext.equals("jpg")) {
                     var23 = 2;
                  }
                  break;
               case 111145:
                  if (ext.equals("png")) {
                     var23 = 4;
                  }
                  break;
               case 3268712:
                  if (ext.equals("jpeg")) {
                     var23 = 3;
                  }
               }

               switch(var23) {
               case 0:
               case 1:
               case 2:
               case 3:
               case 4:
                  fileType = "Image";
                  break;
               default:
                  fileType = "Video";
               }
            }

            dataItem.setDetail(fileType);
            dataItem.setFileSize(software.getFile_size());
            dataItem.setContent("");
         }

         list.add(dataItem);
      }

      V2PageResource newResource = V2PageResource.createPageResource(list, result, pageSize);
      newResource.setStartIndex(startIndex);
      return newResource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2SoftwareDeploymentStatus getReservedSoftwareDeploymentStatusDetail(String reservationId, String status, int startIndex, int pageSize) throws Exception {
      String src_name = this.getSourceName(status);
      String order_dir = "ASC";
      String sort_name = "device_name";
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      Locale locale = SecurityUtils.getLocale();
      SelectCondition selectCondition = new SelectCondition();
      selectCondition.setSort_name(sort_name);
      selectCondition.setOrder_dir(order_dir);
      selectCondition.setSrc_name(src_name);
      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      condition.put("software_rsv_id", Long.valueOf(reservationId));
      SoftwareDao dao = new SoftwareDao();
      PagedListInfo info = dao.getReservedDeviceListByOrganization(startIndex, pageSize, condition, true);
      List reservationList = info.getPagedResultList();
      V2SoftwareDeploymentStatus deploymentStatus = new V2SoftwareDeploymentStatus();
      int cntDevices = softwareDao.getCntDownloadStatusByOrganization(Long.valueOf(reservationId), "reserved");
      int cntStandBy = softwareDao.getCntDownloadStatusByOrganization(Long.valueOf(reservationId), "stand_by");
      int cntDownloading = softwareDao.getCntDownloadStatusByOrganization(Long.valueOf(reservationId), "downloading");
      int cntFinished = softwareDao.getCntDownloadStatusByOrganization(Long.valueOf(reservationId), "download_finished");
      int cntUpgradeSuccess = softwareDao.getCntDownloadStatusByOrganization(Long.valueOf(reservationId), "upgrade_success");
      int cntUpgradeFail = softwareDao.getCntDownloadStatusByOrganization(Long.valueOf(reservationId), "upgrade_fail");
      deploymentStatus.setCountOfReserved(cntDevices);
      deploymentStatus.setCountOfStandBy(cntStandBy);
      deploymentStatus.setCountOfDownloading(cntDownloading);
      deploymentStatus.setCountOfDownloadFinished(cntFinished);
      deploymentStatus.setCountOfUpgradeSuccess(cntUpgradeSuccess);
      deploymentStatus.setCountOfUpgradeFail(cntUpgradeFail);
      List resources = new ArrayList();

      for(int i = 0; i < reservationList.size(); ++i) {
         SoftwareReservation reservation = (SoftwareReservation)reservationList.get(i);
         SimpleDateFormat sdf = new SimpleDateFormat(SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat());
         Timestamp start_date = null;
         Timestamp end_date = null;
         String application_version = "";
         if (reservation.getApplication_version() != null) {
            application_version = reservation.getApplication_version();
            if (application_version.indexOf(";") > 0) {
               String[] application_split = application_version.split(";");
               application_version = application_split[0];
            }
         }

         String download_status = softwareDao.getDownloadStatus(reservation);
         String progress = null;
         if (reservation.getProgress() != null && !reservation.getProgress().equals("")) {
            if (isStringDouble(reservation.getProgress())) {
               progress = reservation.getProgress();
            } else {
               progress = reservation.getProgress();
            }
         } else if ("upgrade_success".equalsIgnoreCase(download_status)) {
            progress = "100";
         } else {
            progress = "Unknown";
         }

         String fail_status = null;
         if ("UPGRADE_FAIL".equalsIgnoreCase(download_status) && reservation.GetFail_status() != null && !reservation.GetFail_status().equals("")) {
            fail_status = reservation.GetFail_status();
         } else {
            fail_status = "";
         }

         String regist_time = "-";
         if (reservation.getRegist_time() != null) {
            regist_time = sdf.format(reservation.getRegist_time());
         }

         Boolean power = DeviceUtils.isConnected(reservation.getDevice_id());
         V2ReservedSoftwareResource resource = new V2ReservedSoftwareResource();
         resource.setDeviceId(reservation.getDevice_id());
         resource.setDeviceName(reservation.getDevice_name());
         resource.setDeviceModelName(reservation.getDevice_model_name() != null ? reservation.getDevice_model_name() : "");
         resource.setDeviceType(reservation.getDevice_type());
         resource.setGroupName(reservation.getGroup_name());
         resource.setSoftwareName(reservation.getSoftware_name());
         resource.setServiceStartDate(reservation.getService_start_date());
         resource.setServiceEndDate(reservation.getService_end_date());
         resource.setApplicationVersion(application_version);
         resource.setProgress(progress);
         resource.setDownloadStatus(download_status);
         resource.setFailStatus(fail_status);
         resource.setRegistTime(regist_time);
         resource.setPowerOn(power);
         resources.add(resource);
      }

      deploymentStatus.setReservedDevices(resources);
      return deploymentStatus;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2PageResource getReservedSoftwareDeploymentStatusList(String softwareType, int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder) throws Exception {
      Locale locale = SecurityUtils.getLocale();
      String userDateTimeFormat = SecurityUtils.getUserDateFormat() + " " + SecurityUtils.getUserTimeFormat();
      new SimpleDateFormat(userDateTimeFormat);
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      SelectCondition selectCondition = new SelectCondition();
      selectCondition.setSort_name(sortColumn);
      selectCondition.setOrder_dir(sortOrder);
      selectCondition.setSrc_name(searchText);
      selectCondition = this.setSoftwareType(selectCondition, softwareType);
      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      SoftwareDao dao = new SoftwareDao();
      PagedListInfo info = dao.getSoftwareReservationList(startIndex, pageSize, condition);
      List reservationList = info.getPagedResultList();
      List list = new ArrayList();
      Iterator var20 = reservationList.iterator();

      while(var20.hasNext()) {
         SoftwareReservation reservation = (SoftwareReservation)var20.next();
         reservationList.indexOf(reservation);
         V2ReservedSoftwareResource resource = new V2ReservedSoftwareResource();
         resource.setSoftwareReservationId(reservation.getSoftware_rsv_id());
         resource.setSoftwareName(StrUtils.nvl(reservation.getSoftware_name()));
         resource.setSoftwareVersion(StrUtils.nvl(reservation.getSoftware_version()));
         resource.setSoftwareType(StrUtils.nvl(reservation.getSoftware_type()));
         resource.setDeviceModelName(StrUtils.nvl(reservation.getDevice_model_name()));
         resource.setDeviceType(reservation.getDevice_type());
         resource.setCreateDate(reservation.getCreate_date());
         if (reservation.getType() == 1L) {
            resource.setReservationDate(reservation.getReservation_date());
         }

         if ("TYPE".equalsIgnoreCase(reservation.getDeploy_type())) {
            resource.setDeployType("Device Type");
         } else {
            resource.setDeployType(reservation.getDeploy_type());
         }

         resource.setSubscriberId(reservation.getSubscriber_id());
         resource.setDownloadStatus(softwareDao.getReservationDownloadStatus(reservation));
         resource.setCountOfAllState(reservation.getCnt_all_state());
         resource.setCountOfFinishedState(reservation.getCnt_finish_state());
         resource.setCountOfUpgradeFailedState(reservation.getCnt_upgrade_fail_state());
         resource.setCountOfDownloadingState(reservation.getCnt_all_state() - reservation.getCnt_finish_state() - reservation.getCnt_upgrade_fail_state());
         list.add(resource);
      }

      V2PageResource newResource = V2PageResource.createPageResource(list, info, pageSize);
      newResource.setStartIndex(startIndex);
      return newResource;
   }

   private String getSourceName(String status) {
      String srcName = "ALL";
      if (status.equalsIgnoreCase("ALL")) {
         srcName = "";
      } else if (status.equalsIgnoreCase("DOWNLOADING")) {
         srcName = "downloading";
      } else if (status.equalsIgnoreCase("DOWNLOAD_FINISHED")) {
         srcName = "download_finished";
      } else if (status.equalsIgnoreCase("UPGRADE_SUCCESS")) {
         srcName = "upgrade_success";
      } else if (status.equalsIgnoreCase("UPGRADE_FAIL")) {
         srcName = "upgrade_fail";
      }

      return srcName;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ModelAndView softwareExport(String exportType, String softwareType, String searchText, String sortColumn, String sortOrder, HttpServletResponse response, String localeData) {
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "create_date";
      }

      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "asc";
      }

      if (StrUtils.nvl(searchText).equals("")) {
         searchText = "";
      }

      if (StrUtils.nvl(exportType).equals("")) {
         exportType = "EXCEL";
      }

      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sortColumn);
      condition.setOrder_dir(sortOrder);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      condition.setSrc_name(searchText.toUpperCase());
      if ("LOGO".equalsIgnoreCase(softwareType)) {
         condition.setType("LOGO");
      } else if (SoftwareType.WIFI_CERTIFICATE.getType().equalsIgnoreCase(softwareType)) {
         condition.setType(SoftwareType.WIFI_CERTIFICATE.getType());
      } else if (SoftwareType.SSL_CERTIFICATE.getType().equalsIgnoreCase(softwareType)) {
         condition.setType(SoftwareType.SSL_CERTIFICATE.getType());
      } else {
         condition.setType("APPLICATION");
      }

      Map map = new HashMap();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      String fileExtension = "xls";
      if (exportType.toUpperCase().equals("PDF")) {
         fileExtension = "pdf";
      }

      Timestamp nowTime = new Timestamp(System.currentTimeMillis());
      Map dataMap = new HashMap();
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String strSWName = rms.getMessage("COM_TEXT_SOFTWARE_NAME_P", (Object[])null, locale);
      rms.getMessage("MIS_SID_PREMIUM_LOGO_NAME", (Object[])null, locale);
      String strVersion = rms.getMessage("TABLE_SOFTWARE_VERSION_P", (Object[])null, locale);
      String strFileName = rms.getMessage("TEXT_FILE_NAME_P", (Object[])null, locale);
      String strFilePath = rms.getMessage("MIS_SID_20_STORAGE_PATH", (Object[])null, locale);
      String strFileSize = rms.getMessage("ADMIN_CONTENT_REGISTRATION_CONTENTS_LIST_JSP_FILESIZE", (Object[])null, locale);
      String strSWType = rms.getMessage("TEXT_SOFTWARE_TYPE_P", (Object[])null, locale);
      String strMimeType = rms.getMessage("TEXT_CRC_INFO_P", (Object[])null, locale);
      String strCreateDate = rms.getMessage("TEXT_LAST_MODIFIED_DATE_P", (Object[])null, locale);
      String strCreateId = rms.getMessage("TABLE_AUTHOR_P", (Object[])null, locale);
      String strIsAutoUpdate = rms.getMessage("TEXT_AUTO_UPDATE_P", (Object[])null, locale);
      String strApplication = rms.getMessage("TEXT_APPLICATION_P", (Object[])null, locale);
      String strFirmware = rms.getMessage("COM_TEXT_FIRMWARE_P", (Object[])null, locale);
      String strDirver = rms.getMessage("TEXT_DRIVER_P", (Object[])null, locale);
      String strWebApp = rms.getMessage("MIS_SID_WEB_APP", (Object[])null, locale);
      String strDeviceType = rms.getMessage("MIS_TEXT_DEVICE_TYPE_P", (Object[])null, locale);
      map.put("condition", condition);
      String strApply = rms.getMessage("BUTTON_APPLY", (Object[])null, locale);
      String strNotApply = rms.getMessage("TEXT_NOT_APPLY_P", (Object[])null, locale);
      String fileName = "SoftwareList_" + nowTime.toString() + "." + fileExtension;
      String sheetName = "Software";
      String[] columnNames = new String[]{"device_type", "software_name", "software_version", "create_date", "str_auto_update", "software_type", "file_name", "file_path", "file_size", "mime_type", "creator_id"};
      String[] fieldNames = new String[]{strDeviceType, strSWName, strVersion, strCreateDate, strIsAutoUpdate, strSWType, strFileName, strFilePath, strFileSize, strMimeType, strCreateId};

      try {
         List resultList = softwareDao.getSoftwareListToExport(map);
         int dataListSize = false;
         Object[] dataList = null;
         if (resultList != null) {
            int dataListSize = resultList.size();
            dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               Software software = (Software)resultList.get(index);
               if (software.getIs_auto_update()) {
                  software.setStr_auto_update(strApply);
               } else if (!software.getIs_auto_update()) {
                  software.setStr_auto_update(strNotApply);
               }

               if (software.getSoftware_type() != null) {
                  if (software.getSoftware_type().equalsIgnoreCase("02")) {
                     software.setSoftware_type(strApplication);
                  } else if (software.getSoftware_type().equalsIgnoreCase("00")) {
                     software.setSoftware_type(strDirver);
                  } else if (software.getSoftware_type().equalsIgnoreCase("01")) {
                     software.setSoftware_type(strFirmware);
                  } else if (software.getSoftware_type().equalsIgnoreCase("05")) {
                     software.setSoftware_type(strWebApp);
                  } else {
                     software.setSoftware_type(strFirmware);
                  }
               }

               dataList[index] = software;
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      } catch (SQLException var43) {
         this.logger.error("", var43);
      }

      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public ModelAndView softwareDepolyExport(String exportType, String softwareType, String searchText, String sortColumn, String sortOrder, HttpServletResponse response, String localeData) {
      if (StrUtils.nvl(sortColumn).equals("")) {
         sortColumn = "create_date";
      }

      if (StrUtils.nvl(sortOrder).equals("")) {
         sortOrder = "asc";
      }

      if (StrUtils.nvl(searchText).equals("")) {
         searchText = "";
      }

      if (StrUtils.nvl(exportType).equals("")) {
         exportType = "EXCEL";
      }

      SelectCondition condition = new SelectCondition();
      condition.setSort_name(sortColumn);
      condition.setOrder_dir(sortOrder);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      condition.setSrc_name(searchText.toUpperCase());
      if ("LOGO".equalsIgnoreCase(softwareType)) {
         condition.setType("LOGO");
      } else {
         condition.setType("APPLICATION");
      }

      Map map = new HashMap();
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      String fileExtension = "xls";
      if (exportType.toUpperCase().equals("PDF")) {
         fileExtension = "pdf";
      }

      Timestamp nowTime = new Timestamp(System.currentTimeMillis());
      Map dataMap = new HashMap();
      if (StrUtils.nvl(localeData).equals("")) {
         String userLocale = SecurityUtils.getUserContainer().getUser().getLocale();
         if (userLocale != null && !userLocale.equalsIgnoreCase("")) {
            localeData = userLocale;
         } else {
            localeData = "en";
         }
      }

      Locale locale = new Locale(localeData);
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String strSWName = rms.getMessage("COM_TEXT_SOFTWARE_NAME_P", (Object[])null, locale);
      String strVersion = rms.getMessage("TABLE_SOFTWARE_VERSION_P", (Object[])null, locale);
      String strFileName = rms.getMessage("TEXT_FILE_NAME_P", (Object[])null, locale);
      String strRsvId = rms.getMessage("TABLE_RSV_ID_P", (Object[])null, locale);
      String strSCRId = rms.getMessage("TABLE_SUBSCRIBER_P", (Object[])null, locale);
      String strRsvDate = rms.getMessage("DID_ADMIN_DEVICE_REGDATE", (Object[])null, locale);
      String strSVCStartDate = rms.getMessage("TABLE_RESERVATION_DATE_P", (Object[])null, locale);
      String strTotalCnt = rms.getMessage("LIST_TITLE_TOTAL", (Object[])null, locale);
      String strFinishCnt = rms.getMessage("MSG_FINISHED", (Object[])null, locale);
      String strType = rms.getMessage("TABLE_TYPE_P", (Object[])null, locale);
      String strModel = rms.getMessage("ADMIN_DEVICEEVENT_ALARMRULE_ASSIGNRULE_CONTENTS_MODEL", (Object[])null, locale);
      String strGroup = rms.getMessage("TEXT_GROUP_P", (Object[])null, locale);
      String strDeviceType = rms.getMessage("MIS_TEXT_DEVICE_TYPE_P", (Object[])null, locale);
      String fileName = "SoftwareReservationList_" + nowTime.toString() + "." + fileExtension;
      String sheetName = "SoftwareReservation";
      String[] columnNames = new String[]{"software_rsv_id", "device_type", "software_name", "software_version", "create_date", "reservation_date", "deploy_type", "subscriber_id", "cnt_finish_state", "cnt_all_state", "file_name"};
      String[] fieldNames = new String[]{strRsvId, strDeviceType, strSWName, strVersion, strRsvDate, strSVCStartDate, strType, strSCRId, strFinishCnt, strTotalCnt, strFileName};
      map.put("condition", condition);

      try {
         List resultList = softwareDao.getReservedDeviceList(map);
         int dataListSize = false;
         Object[] dataList = null;
         if (resultList != null) {
            int dataListSize = resultList.size();
            dataList = new Object[dataListSize];

            for(int index = 0; index < dataListSize; ++index) {
               SoftwareReservation reservation = (SoftwareReservation)resultList.get(index);
               if (reservation.getDeploy_type() != null) {
                  if (reservation.getDeploy_type().equalsIgnoreCase("MODEL")) {
                     reservation.setDeploy_type(strModel);
                  } else if (reservation.getDeploy_type().equalsIgnoreCase("GROUP")) {
                     reservation.setDeploy_type(strGroup);
                  }
               }

               dataList[index] = reservation;
            }
         }

         dataMap.put("fileName", fileName);
         dataMap.put("sheetName", sheetName);
         dataMap.put("columnNames", columnNames);
         dataMap.put("fieldNames", fieldNames);
         dataMap.put("dataList", dataList);
      } catch (SQLException var38) {
         this.logger.error("", var38);
      }

      if (exportType.equalsIgnoreCase("PDF")) {
         PdfBuilder pdfView = new PdfBuilder();
         return new ModelAndView(pdfView, dataMap);
      } else {
         this.downloadService = new DeviceStatisticsDownloadService();
         this.downloadService.downloadExcelFile(dataMap, response);
         return null;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public void softwareDownload(String param_filepath, HttpServletRequest request, HttpServletResponse response) throws Exception {
      BufferedOutputStream os = null;
      FileInputStream fileIS = null;
      FileChannel fileChannel = null;

      try {
         String filepath;
         try {
            request.setCharacterEncoding("UTF-8");
            BufferedReader br = request.getReader();
            filepath = br.readLine();
            String topPath = CommonConfig.get("UPLOAD_HOME");
            if (filepath == null || filepath.equals("")) {
               filepath = StrUtils.nvl(param_filepath);
            }

            filepath = filepath.replace("\\", "/");
            String[] tmp = filepath.split("/");
            topPath = topPath.replace("\\", "/");
            String realname = filepath;
            if (tmp != null && tmp.length > 1) {
               String var10000 = tmp[tmp.length - 2];
               realname = tmp[tmp.length - 1];
            }

            URLEncoder.encode(realname, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(realname, "UTF-8") + ";");
            response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
            int dotIdx = realname.lastIndexOf(".");
            String extension = realname.substring(dotIdx + 1, realname.length());
            String contentType = FileLoaderServlet.getContentType(extension.toLowerCase());
            response.setContentType(contentType);
            os = new BufferedOutputStream(response.getOutputStream());
            String fullpath = topPath + "/" + filepath;
            fullpath = SecurityUtils.directoryTraversalChecker(fullpath, request.getRemoteAddr());
            File m_file = SecurityUtils.getSafeFile(fullpath);
            fileIS = new FileInputStream(m_file);
            fileChannel = fileIS.getChannel();
            String fileoffset = "0";
            long fileOffsetLong = Long.parseLong(fileoffset);
            int binaryRead;
            if (m_file.length() > 0L) {
               for(ByteBuffer buf = ByteBuffer.allocate(1024); (binaryRead = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)binaryRead) {
                  buf.flip();
                  os.write(buf.array(), 0, binaryRead);
                  buf.clear();
               }
            }

            os.close();
            fileChannel.close();
            fileIS.close();
         } catch (FileNotFoundException var30) {
            filepath = request.getParameter("id");
            if (!StringUtil.isEmpty(filepath)) {
               ContentInfo contentInfo = ContentInfoImpl.getInstance();
               contentInfo.deleteFileInfoIfNoExistFile(filepath);
            }
         } catch (Exception var31) {
            Exception e = var31;

            try {
               if (!(e instanceof ClientAbortException)) {
                  response.sendError(609, CMSExceptionCode.APP609[2]);
                  this.logger.error("", e);
               }
            } catch (Exception var29) {
               this.logger.error(var29);
            }
         }
      } finally {
         if (os != null) {
            os.close();
         }

         if (fileChannel != null) {
            fileChannel.close();
         }

         if (fileIS != null) {
            fileIS.close();
         }

      }

   }

   public static boolean isStringDouble(String s) {
      try {
         Double.parseDouble(s);
         return true;
      } catch (NumberFormatException var2) {
         return false;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2SoftwareFileResource registeredDetail(String softwareId) throws Exception {
      SoftwareManager softwareDao = SoftwareManagerImpl.getInstance();
      new Software();
      Software software = softwareDao.getSoftware(Long.parseLong(softwareId));
      V2SoftwareFileResource resource = new V2SoftwareFileResource();
      resource.setDeviceName(software.getDevice_model_name());
      resource.setDeviceType(software.getDevice_type());
      resource.setFileName(software.getFile_name());
      resource.setSoftwareId(software.getSoftware_id());
      resource.setSoftwareName(software.getSoftware_name());
      resource.setSoftwareType(software.getSoftware_type());
      resource.setCrcInfo(StrUtils.nvl(software.getMime_type()));
      resource.setSoftwareVersion(software.getSoftware_version());
      resource.setFileSize(software.getFile_size());
      resource.setCreatorId(software.getCreator_id());
      resource.setFilePath(software.getFile_path());
      resource.setStatus(software.getStatus());
      resource.setCreateDate(software.getCreate_date());
      if (!"03".equalsIgnoreCase(software.getSoftware_type()) && !"04".equalsIgnoreCase(software.getSoftware_type())) {
         if (software.getDevice_type().equals("iPLAYER") && software.getDevice_model_name().equals("Default")) {
            resource.setDeviceModelName("All Devices");
         } else {
            resource.setDeviceModelName(software.getDevice_model_name());
         }
      } else {
         String ext = null;
         String fileType = null;
         if (software.getFile_name() != null) {
            ext = software.getFile_name().substring(software.getFile_name().lastIndexOf(".") + 1);
            byte var8 = -1;
            switch(ext.hashCode()) {
            case 97669:
               if (ext.equals("bmp")) {
                  var8 = 0;
               }
               break;
            case 102340:
               if (ext.equals("gif")) {
                  var8 = 1;
               }
               break;
            case 105441:
               if (ext.equals("jpg")) {
                  var8 = 2;
               }
               break;
            case 111145:
               if (ext.equals("png")) {
                  var8 = 4;
               }
               break;
            case 3268712:
               if (ext.equals("jpeg")) {
                  var8 = 3;
               }
            }

            switch(var8) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
               fileType = "IMAGE";
               break;
            default:
               fileType = "MOVIE";
            }
         }

         resource.setDetail(fileType);
         resource.setFileSize(software.getFile_size());
         resource.setContent("");
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceModelNameResource getDeviceNameListByDeviceType(String deviceType) throws Exception {
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String organization = userContainer.getUser().getOrganization();
      List modelNameList = deviceInfo.getConnectedDeviceModelNameListTypeS(deviceType, organization);
      V2DeviceModelNameResource list = new V2DeviceModelNameResource();
      String temp = "";
      List resource = new ArrayList();

      for(int i = 0; i < modelNameList.size(); ++i) {
         temp = (String)((Map)modelNameList.get(i)).get("device_model_name");
         resource.add(i, temp);
      }

      list.setDeviceModelName(resource);
      return list;
   }

   public static String getDownloadStatus(SoftwareReservation reservation) {
      String download_status = "Standby";
      if (reservation != null && reservation.getDownload_status() != null && !reservation.getDownload_status().equals("")) {
         String var2 = reservation.getDownload_status();
         byte var3 = -1;
         switch(var2.hashCode()) {
         case -1337333919:
            if (var2.equals("upgrade_fail")) {
               var3 = 1;
            }
            break;
         case -1211129254:
            if (var2.equals("downloading")) {
               var3 = 2;
            }
            break;
         case -1178622752:
            if (var2.equals("upgrade_success")) {
               var3 = 0;
            }
            break;
         case -859291063:
            if (var2.equals("download_finished")) {
               var3 = 3;
            }
         }

         switch(var3) {
         case 0:
            return "Upgrade Success";
         case 1:
            return "Upgrade Fail";
         case 2:
            return "Downloading";
         case 3:
            return "Download Completed";
         }
      }

      return download_status;
   }

   private void validateReservationTime(Timestamp serviceStartDate) {
      if (Calendar.getInstance().getTime().after(serviceStartDate)) {
         Map details = new HashMap();
         Date current = new Date();
         details.put("timeZone", (new SimpleDateFormat("Z")).format(current));
         details.put("timestamp", current.getTime());
         throw new RestServiceException(RestExceptionCode.FAIL_TO_DEPLOY_SOFTWARE_FOR_WRONG_TIME, details);
      }
   }
}
