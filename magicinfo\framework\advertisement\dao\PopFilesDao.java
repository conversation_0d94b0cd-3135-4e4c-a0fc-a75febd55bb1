package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class PopFilesDao extends SqlSessionBaseDao {
   Logger logger = LoggingManagerV2.getLogger(PopFilesDao.class);

   public PopFilesDao() {
      super();
   }

   public boolean checkDuplicationByDB(String log_date, String device_id, String pop_file_type) throws SQLException {
      Long cnt = null;

      try {
         cnt = ((PopFilesDaoMapper)this.getMapper()).checkDuplicationByDB(device_id, Timestamp.valueOf(log_date + " 00:00:00"), pop_file_type);
      } catch (SQLException var6) {
         this.logger.error(var6);
      }

      return cnt.intValue() < 1;
   }

   public PopInfoEntity getPopFilesHourInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type) throws SQLException {
      return ((PopFilesDaoMapper)this.getMapper()).getPopFilesHourInfo(deviceId, logDate, contentId, fileId, pop_file_type);
   }

   public void insertPopFilesHourInfo(String deviceId, String contentId, String fileId, String pop_file_type, List popInfoEntityList) throws SQLException {
      ((PopFilesDaoMapper)this.getMapper()).insertPopFilesHourInfo(deviceId, contentId, fileId, pop_file_type, popInfoEntityList);
   }

   public int updatePopFilesHourInfo(String deviceId, String contentId, String fileId, String pop_file_type, PopInfoEntity popInfoEntity) throws SQLException {
      return ((PopFilesDaoMapper)this.getMapper()).updatePopFilesHourInfo(deviceId, contentId, fileId, pop_file_type, popInfoEntity);
   }

   public PopInfoEntity getPopFilesDayInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type) throws SQLException {
      return ((PopFilesDaoMapper)this.getMapper()).getPopFilesDayInfo(deviceId, logDate, contentId, fileId, pop_file_type);
   }

   public void insertPopFilesDayInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type, PopInfoEntity popInfoEntity, int dow) throws SQLException {
      ((PopFilesDaoMapper)this.getMapper()).insertPopFilesDayInfo(deviceId, logDate, contentId, fileId, pop_file_type, popInfoEntity, dow);
   }

   public int updatePopFilesDayInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type, PopInfoEntity popInfoEntity) throws SQLException {
      return ((PopFilesDaoMapper)this.getMapper()).updatePopFilesDayInfo(deviceId, logDate, contentId, fileId, pop_file_type, popInfoEntity);
   }

   public PopInfoEntity getPopFilesMonthInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type) throws SQLException {
      return ((PopFilesDaoMapper)this.getMapper()).getPopFilesMonthInfo(deviceId, logDate, contentId, fileId, pop_file_type);
   }

   public void insertPopFilesMonthInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type, PopInfoEntity popInfoEntity, int quarter) throws SQLException {
      ((PopFilesDaoMapper)this.getMapper()).insertPopFilesMonthInfo(deviceId, logDate, contentId, fileId, pop_file_type, popInfoEntity, quarter);
   }

   public int updatePopFilesMonthInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type, PopInfoEntity popInfoEntity) throws SQLException {
      return ((PopFilesDaoMapper)this.getMapper()).updatePopFilesMonthInfo(deviceId, logDate, contentId, fileId, pop_file_type, popInfoEntity);
   }

   public PopInfoEntity getPopFilesYearInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type) throws SQLException {
      return ((PopFilesDaoMapper)this.getMapper()).getPopFilesYearInfo(deviceId, logDate, contentId, fileId, pop_file_type);
   }

   public void insertPopFilesYearInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type, PopInfoEntity popInfoEntity, String name) throws SQLException {
      ((PopFilesDaoMapper)this.getMapper()).insertPopFilesYearInfo(deviceId, logDate, contentId, fileId, pop_file_type, popInfoEntity, name);
   }

   public int updatePopFilesYearInfo(String deviceId, Timestamp logDate, String contentId, String fileId, String pop_file_type, PopInfoEntity popInfoEntity, String name) throws SQLException {
      return ((PopFilesDaoMapper)this.getMapper()).updatePopFilesYearInfo(deviceId, logDate, contentId, fileId, pop_file_type, popInfoEntity, name);
   }

   public boolean insertPopFileErrInfo(String deviceId, String contentId, Timestamp startDate, Timestamp endDate, String errType, int duration, int threshold) throws SQLException {
      return ((PopFilesDaoMapper)this.getMapper()).insertPopFileErrInfo(deviceId, contentId, startDate, endDate, errType, duration, threshold);
   }
}
