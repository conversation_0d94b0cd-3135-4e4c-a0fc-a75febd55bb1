package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.restapi.device.model.V2DeletedDevice;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReplaceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceVwlLayoutResource;
import java.sql.SQLException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface V2DeviceVWLService {
   ResponseBody cancelVwlgroup(String var1, HttpServletRequest var2) throws SQLException;

   ResponseBody uploadVwl(HttpServletRequest var1) throws SQLException;

   ResponseBody renameVwl(String var1, String var2, HttpServletRequest var3) throws SQLException;

   ResponseBody deleteVwl(String var1, HttpServletRequest var2) throws Exception;

   V2DeviceReplaceResource checkReplaceDevice(String var1, String var2, String var3, String var4, List var5, List var6) throws Exception;

   String vwtManagement(String var1, HttpServletResponse var2) throws Exception;

   V2DeviceVwlLayoutResource showSignageLayout(String var1) throws Exception;

   V2DeletedDevice signageDisplaySetting(String var1, String var2) throws Exception;

   List signageSlaveInfo(String var1) throws Exception;
}
