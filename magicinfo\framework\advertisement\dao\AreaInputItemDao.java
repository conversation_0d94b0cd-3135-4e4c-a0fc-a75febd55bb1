package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.logger.LoggingManagerV2;
import java.sql.Connection;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class AreaInputItemDao {
   Logger logger = LoggingManagerV2.getLogger(AreaInputItemDao.class);

   public AreaInputItemDao() {
      super();
   }

   public AreaInputItemDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAdType() {
      throw new UnsupportedOperationException("AreaInputItemDao#getAreaAdType is not supported anymore: The table MI_AD_AREA_AD_TYPE_ID_INFO does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAgeType() {
      throw new UnsupportedOperationException("AreaInputItemDao#getAreaAgeType is not supported anymore: The table MI_AD_AREA_AGE_TYPE_ID_INFO does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaSexType() {
      throw new UnsupportedOperationException("AreaInputItemDao#getAreaSexType is not supported anymore: The table MI_AD_AREA_SEX_TYPE_ID_INFO does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaTimeType() {
      throw new UnsupportedOperationException("AreaInputItemDao#getAreaTimeType is not supported anymore: The table MI_AD_AREA_TIME_TYPE_ID_INFO does not exists");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaType() {
      throw new UnsupportedOperationException("AreaInputItemDao#getAreaType is not supported anymore: The table MI_AD_AREA_TYPE_ID_INFO does not exists");
   }
}
