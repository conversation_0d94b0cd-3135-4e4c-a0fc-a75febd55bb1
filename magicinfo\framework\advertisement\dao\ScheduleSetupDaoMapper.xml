<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.samsung.magicinfo.framework.advertisement.dao.ScheduleSetupDaoMapper">
    <insert id="registerSchedule">
        INSERT INTO MI_CDS_INFO_SCHEDULE (PROGRAM_ID, SCREEN_INDEX, SCHEDULE_ID, START_DATE, STOP_DATE, START_TIME
        , DURATION, REPEAT_TIME, PRIORITY, CONTENT_ID, CONTENT_TYPE, FRAME_INDEX, SCHEDULE_TYPE, IN_EFFECT_TYPE
        , IN_EFFECT_DURATION, IN_EFFECT_DIRECTION, OUT_EFFECT_TYPE, OUT_EFFECT_DURATION, OUT_EFFECT_DIRECTION
        , REPEAT_TYPE, CREATE_DATE, MODIFY_DATE, USER_ID, CONTENT_DURATION)
        VALUES (#{program_id}, 0, #{schedule_id}, #{result_list.start_date}, #{result_list.end_date}, #{start_time}
        , #{duration}, #{result_list.play_repeat}, 1, #{result_list.content_id}, 'CONTENT', #{frame_index}
        , '00', '', 0, '', '', 0, '', 'daily',
        <include refid="utils.currentTimestamp"/>
        ,<include refid="utils.currentTimestamp"/>, #{user_id}, #{result_list.play_duration})
    </insert>

    <update id="setScheduleDeviceGroup">
        INSERT INTO MI_CDS_MAP_PROGRAM_DEVICE (PROGRAM_ID, DEVICE_GROUP_ID) VALUES (#{program_id}, #{group_id})
    </update>

    <select id="getFileName" resultType="java.util.Map">
        SELECT
            FILE_NAME
        FROM MI_CMS_INFO_FILE
        WHERE file_id = #{file_id}
    </select>

    <select id="getFileType" resultType="java.util.Map">
        SELECT
            MEDIA_TYPE
        FROM MI_CMS_INFO_CONTENT_VERSION
        WHERE MAIN_FILE_ID = #{file_id}
    </select>

</mapper>