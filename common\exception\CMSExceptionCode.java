package com.samsung.common.exception;

public class CMSExceptionCode {
   public static final String ERROR = "code";
   public static final String MESSAGE = "message";
   public static final String[] HTTP200 = new String[]{"200", "HTTP", "OK"};
   public static final String[] HTTP202 = new String[]{"202", "HTTP", "Accepted"};
   public static final String[] HTTP204 = new String[]{"204", "HTTP", "No Content"};
   public static final String[] HTTP400 = new String[]{"400", "HTTP", "Bad Request"};
   public static final String[] HTTP403 = new String[]{"403", "HTTP", "Forbidden"};
   public static final String[] HTTP404 = new String[]{"404", "HTTP", "Not Found"};
   public static final String[] HTTP405 = new String[]{"405", "HTTP", "Method Not Allowed"};
   public static final String[] HTTP500 = new String[]{"500", "HTTP", "Internal Server Error"};
   public static final String[] HTTP501 = new String[]{"501", "HTTP", "Not Implemented"};
   public static final String[] HTTP503 = new String[]{"503", "HTTP", "Service Unavailable"};
   public static final String[] SWS501 = new String[]{"501", "SWS", "SOAP 1.2 Syntax  Error"};
   public static final String[] SWS502 = new String[]{"502", "SWS", "SOAP Version Mismatch"};
   public static final String[] SWS503 = new String[]{"503", "SWS", "Cannot process Mandatory Element"};
   public static final String[] SWS504 = new String[]{"504", "SWS", "Bad Encoding Error"};
   public static final String[] SWS505 = new String[]{"505", "SWS", "WS-Addressing Error"};
   public static final String[] SWS506 = new String[]{"506", "SWS", "WS-Eventing"};
   public static final String[] SWS507 = new String[]{"507", "SWS", "WS-Security Auth Error"};
   public static final String[] SWS508 = new String[]{"508", "SWS", "WS-Security Encryption Error"};
   public static final String[] SWS509 = new String[]{"509", "SWS", "WS-Security Digital Signature Error"};
   public static final String[] SWS510 = new String[]{"510", "SWS", "Attachment Error"};
   public static final String[] SWS512 = new String[]{"512", "SWS", "Invalid Action"};
   public static final String[] SWS513 = new String[]{"513", "SWS", "\"setTriggerCycle\" method is not supported in normal binding mode."};
   public static final String[] SWS514 = new String[]{"514", "SWS", "Unsupported Service Name"};
   public static final String[] SWS515 = new String[]{"515", "SWS", "Not valid XML"};
   public static final String[] SWS516 = new String[]{"516", "SWS", "Not supported operation"};
   public static final String[] SWS599 = new String[]{"599", "SWS", "Unknown exception from user defined web service."};
   public static final String[] APP601 = new String[]{"601", "Application", "Login failed."};
   public static final String[] APP602 = new String[]{"602", "Application", "Content ID is null."};
   public static final String[] APP603 = new String[]{"603", "Application", "Internal Server Error"};
   public static final String[] APP604 = new String[]{"604", "Application", "File ID is null."};
   public static final String[] APP605 = new String[]{"605", "Application", "Hash value is null."};
   public static final String[] APP606 = new String[]{"606", "Application", "File is not exist in server."};
   public static final String[] APP607 = new String[]{"607", "Application", "File is exist."};
   public static final String[] APP608 = new String[]{"608", "Application", "File path is null."};
   public static final String[] APP609 = new String[]{"609", "Application", "Client aborted downloading file."};
   public static final String[] APP610 = new String[]{"610", "Application", "The user does not have authorized to read content."};
   public static final String[] APP611 = new String[]{"611", "Application", "The user does not have authorized to write content."};
   public static final String[] APP612 = new String[]{"612", "Application", "Fail to get file information."};
   public static final String[] APP613 = new String[]{"613", "Application", "Fail to get file."};
   public static final String[] APP614 = new String[]{"614", "Application", "Cannot read CSD file."};
   public static final String[] APP615 = new String[]{"615", "Application", "Cannot read CSD file."};
   public static final String[] APP620 = new String[]{"620", "Application", "DB Connection Error"};
   public static final String[] APP699 = new String[]{"699", "Application", "Fail upload file (Unknow Exception)."};

   public CMSExceptionCode() {
      super();
   }
}
