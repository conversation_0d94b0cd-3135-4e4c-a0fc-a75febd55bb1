package com.samsung.common.export;

import com.lowagie.text.Chunk;
import com.lowagie.text.Document;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.Paragraph;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.statistics.entity.content.NewContentFrequencyEntity;
import java.io.ByteArrayOutputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.web.servlet.view.document.AbstractPdfView;

public class PdfPopBuilder extends AbstractPdfView {
   public PdfPopBuilder() {
      super();
   }

   public void buildPdfDocument(Map map, Document doc, PdfWriter pdfWriter, HttpServletRequest request, HttpServletResponse response) {
      Logger logger = LoggingManagerV2.getLogger(PdfBuilder.class);
      BaseFont baseFont = null;
      Font koreanFont = null;
      Font koreanBoldFont = null;

      try {
         baseFont = BaseFont.createFont("HYGoThic-Medium", "UniKS-UCS2-H", false);
         koreanFont = new Font(baseFont, 10.0F, 0);
         koreanBoldFont = new Font(baseFont, 10.0F, 1);
         ByteArrayOutputStream baos = new ByteArrayOutputStream();
         PdfWriter.getInstance(doc, baos);
         doc.open();
         String[] field_name = (String[])((String[])map.get("fieldNames"));
         int size = 4;
         String[] periodDate = (String[])((String[])map.get("ReportDateRange"));
         String generateDate = (String)map.get("ReportGenerated");
         doc.add(new Paragraph("Detail Report: "));
         doc.add(new Paragraph("Report Date Range: " + periodDate[0] + " ~ " + periodDate[1]));
         doc.add(new Paragraph("Report Generated: " + generateDate));
         float[] t = new float[size];

         for(int i = 0; i < size; ++i) {
            t[i] = 3.0F;
         }

         SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
         String cell_value_conntentName = field_name[0].toString();
         String cell_value_first = field_name[1].toString();
         String cell_value_last = field_name[2].toString();
         String cell_value_deviceName = field_name[3].toString();
         String cell_value_count = field_name[4].toString();
         String cell_value_duration = field_name[5].toString();
         String cell_value_total = field_name[6].toString();
         List data = (List)map.get("dataList");
         String[] columnNames = (String[])((String[])map.get("columnNames"));
         int column_size = columnNames.length;
         int length = data.size();
         if (length == 0) {
            String nodataString = (String)map.get("noData");
            doc.add(Chunk.NEWLINE);
            doc.add(new Paragraph(nodataString, koreanBoldFont));
         }

         for(int i = 0; i < length; ++i) {
            String firstString = "-";
            String lastString = "-";
            int count = false;
            int duration = false;
            int totalCount = 0;
            int totalDuration = 0;
            PdfPTable table = new PdfPTable(t);
            table.setWidthPercentage(100.0F);
            table.setKeepTogether(true);
            Map popDetailData = (Map)data.get(i);
            List deviceData = (List)popDetailData.get("data");
            int deviceDataSize = deviceData.size();
            String contentName = (String)popDetailData.get("content_name");
            String thumb_dir = (String)popDetailData.get("thumb_dir");
            Image png = Image.getInstance(thumb_dir);
            png.setAlignment(0);
            png.scaleAbsolute(146.0F, 109.0F);
            Timestamp first;
            if (popDetailData.get("first") != null) {
               first = (Timestamp)popDetailData.get("first");
               firstString = dateFormat.format(first);
            }

            if (popDetailData.get("first") != null) {
               first = (Timestamp)popDetailData.get("first");
               Timestamp last = (Timestamp)popDetailData.get("last");
               lastString = dateFormat.format(last);
            }

            try {
               table.addCell(new Paragraph(cell_value_conntentName, koreanBoldFont));
               table.addCell(new Paragraph(cell_value_first, koreanBoldFont));
               table.addCell(new Paragraph(cell_value_last, koreanBoldFont));
               PdfPCell cell = new PdfPCell(png);
               cell.setRowspan(deviceDataSize + 4);
               table.addCell(cell);
               table.addCell(new Paragraph(contentName, koreanFont));
               table.addCell(new Paragraph(firstString, koreanFont));
               table.addCell(new Paragraph(lastString, koreanFont));
            } catch (Exception var46) {
               logger.error("", var46);
            }

            if (deviceDataSize > 0) {
               try {
                  table.addCell(new Paragraph(cell_value_deviceName, koreanBoldFont));
                  table.addCell(new Paragraph(cell_value_count, koreanBoldFont));
                  table.addCell(new Paragraph(cell_value_duration + "(s)", koreanBoldFont));

                  for(int j = 0; j < deviceDataSize; ++j) {
                     int count = ((NewContentFrequencyEntity)deviceData.get(j)).getPlay_count();
                     int duration = ((NewContentFrequencyEntity)deviceData.get(j)).getDuration();
                     totalCount += count;
                     totalDuration += duration;
                     table.addCell(new Paragraph(((NewContentFrequencyEntity)deviceData.get(j)).getDevice_name(), koreanFont));
                     table.addCell(new Paragraph(Integer.toString(count), koreanFont));
                     table.addCell(new Paragraph(Integer.toString(duration), koreanFont));
                  }

                  table.addCell(new Paragraph(cell_value_total, koreanBoldFont));
                  table.addCell(new Paragraph(Integer.toString(totalCount), koreanFont));
                  table.addCell(new Paragraph(Integer.toString(totalDuration), koreanFont));
               } catch (Exception var47) {
                  logger.error("", var47);
               }
            }

            doc.add(Chunk.NEWLINE);
            doc.add(table);
         }

         doc.close();
         response.setHeader("Expires", "0");
         response.setHeader("Cache-Control", "must-revalidate, post-check=0, pre-check=0");
         response.setHeader("Pragma", "public");
         response.setHeader("Content-Type", "application/pdf");
         response.setHeader("Content-Disposition", "attachment; filename=\"" + map.get("fileName").toString() + "\"");
         response.setHeader("Content-Description", "JSP Generated Data");
         response.setHeader("Content-Transfer-Encoding", "binary;");
         response.setContentLength(baos.size());
         ServletOutputStream out = response.getOutputStream();
         baos.writeTo(out);
         out.flush();
      } catch (Exception var48) {
         logger.error("Error in " + this.getClass().getName() + "\n" + var48);
      }

   }
}
