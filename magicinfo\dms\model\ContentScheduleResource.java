package com.samsung.magicinfo.dms.model;

import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class ContentScheduleResource {
   @KPI
   @ApiModelProperty(
      example = "SPLAYER",
      required = true
   )
   String deviceType;
   @ApiModelProperty(
      example = "2.0",
      required = true
   )
   float deviceTypeVersion;
   @KPI
   @ApiModelProperty(
      example = "false"
   )
   boolean syncPlay;
   @KPI
   @ApiModelProperty(
      example = "false"
   )
   boolean vwlType;
   @ApiModelProperty(
      example = "2",
      required = true
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[ContentScheduleResource][scheduleGroupId]Only number is available."
   )
   String scheduleGroupId;
   @ApiModelProperty(
      example = ""
   )
   String deviceGroupIds;
   @ApiModelProperty(
      example = "Name"
   )
   @Size(
      max = 100,
      message = "[ContentScheduleResource][scheduleName]max size is 100."
   )
   String scheduleName;
   @ApiModelProperty(
      example = ""
   )
   String description;
   @ApiModelProperty(
      example = "false"
   )
   @Pattern(
      regexp = "true|false",
      message = "[ContentScheduleResource][deployReserve] Only true, false are available."
   )
   String deployReserve;
   Map deloyReserveResource;
   @ApiModelProperty(
      example = "0"
   )
   String contentSyncOn;
   @ApiModelProperty(
      example = "0"
   )
   String resume;
   @ApiModelProperty(
      example = ""
   )
   String backgroundMusic;
   @ApiModelProperty(
      example = ""
   )
   String contentMute;
   List itemList;
   String ipAddress;
   @ApiModelProperty(
      example = "0"
   )
   String priority;

   public ContentScheduleResource() {
      super();
   }

   public List getItemList() {
      return this.itemList;
   }

   public void setItemList(List itemList) {
      this.itemList = itemList;
   }

   public String getDeviceType() {
      return this.deviceType;
   }

   public void setDeviceType(String deviceType) {
      this.deviceType = deviceType;
   }

   public float getDeviceTypeVersion() {
      return this.deviceTypeVersion;
   }

   public void setDeviceTypeVersion(float deviceTypeVersion) {
      this.deviceTypeVersion = deviceTypeVersion;
   }

   public boolean isSyncPlay() {
      return this.syncPlay;
   }

   public void setSyncPlay(boolean syncPlay) {
      this.syncPlay = syncPlay;
   }

   public boolean isVwlType() {
      return this.vwlType;
   }

   public void setVwlType(boolean vwlType) {
      this.vwlType = vwlType;
   }

   public String getScheduleGroupId() {
      return this.scheduleGroupId;
   }

   public void setScheduleGroupId(String scheduleGroupId) {
      this.scheduleGroupId = scheduleGroupId;
   }

   public String getDeviceGroupIds() {
      return this.deviceGroupIds;
   }

   public void setDeviceGroupIds(String deviceGroupIds) {
      this.deviceGroupIds = deviceGroupIds;
   }

   public String getScheduleName() {
      return this.scheduleName;
   }

   public void setScheduleName(String scheduleName) {
      this.scheduleName = scheduleName;
   }

   public String getDescription() {
      return this.description;
   }

   public void setDescription(String description) {
      this.description = description;
   }

   public String getDeployReserve() {
      return this.deployReserve;
   }

   public void setDeployReserve(String deployReserve) {
      this.deployReserve = deployReserve;
   }

   public Map getDeployReserveResource() {
      return this.deloyReserveResource;
   }

   public void setDeployReserveResource(Map deloyReserveResource) {
      this.deloyReserveResource = deloyReserveResource;
   }

   public String getContentSyncOn() {
      return this.contentSyncOn;
   }

   public void setContentSyncOn(String contentSyncOn) {
      this.contentSyncOn = contentSyncOn;
   }

   public String getResume() {
      return this.resume;
   }

   public void setResume(String resume) {
      this.resume = resume;
   }

   public String getContentMute() {
      return this.contentMute;
   }

   public void setContentMute(String contentMute) {
      this.contentMute = contentMute;
   }

   public String getBackgroundMusic() {
      return this.backgroundMusic;
   }

   public void setBackgroundMusic(String backgroundMusic) {
      this.backgroundMusic = backgroundMusic;
   }

   public String getIpAddress() {
      return this.ipAddress;
   }

   public void setIpAddress(String ipAddress) {
      this.ipAddress = ipAddress;
   }

   public String getPriority() {
      return this.priority;
   }

   public void setPriority(String priority) {
      this.priority = priority;
   }
}
