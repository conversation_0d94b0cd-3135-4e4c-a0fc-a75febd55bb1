package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.magicinfo.framework.advertisement.entity.FaceInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.FaceKey;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import org.apache.ibatis.annotations.Param;

public interface FaceHumanBasedDaoMapper {
   FaceInfoEntity getPopMonthInfo(@Param("device_id") String var1, @Param("key") FaceKey var2, @Param("month") Timestamp var3) throws SQLException;

   FaceInfoEntity getPopYearInfo(@Param("device_id") String var1, @Param("key") FaceKey var2, @Param("year") Timestamp var3) throws SQLException;

   void addPopDayInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("day") Timestamp var3, @Param("dow") int var4) throws SQLException;

   void addPopHourInfoList(@Param("deviceId") String var1, @Param("key") FaceKey var2, @Param("list") ArrayList var3) throws SQLException;

   void addPopMonthInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("month") Timestamp var3, @Param("quarter") int var4) throws SQLException;

   void addPopYearInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("timestamp") Timestamp var3) throws SQLException;

   void setPopMonthInfo(@Param("device_id") String var1, @Param("key") FaceKey var2, @Param("duration") int var3, @Param("playcount") int var4, @Param("month") Timestamp var5) throws SQLException;

   void setPopYearInfo(@Param("device_id") String var1, @Param("key") FaceKey var2, @Param("duration") int var3, @Param("playcount") int var4, @Param("year") Timestamp var5) throws SQLException;

   Long checkDuplicationByDB(@Param("device_id") String var1, @Param("timestamp") Timestamp var2) throws SQLException;
}
