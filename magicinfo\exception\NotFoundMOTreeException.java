package com.samsung.magicinfo.exception;

import com.samsung.common.exception.BasicException;
import com.samsung.common.exception.ExceptionCode;

public class NotFoundMOTreeException extends BasicException {
   private static final long serialVersionUID = -6620844348472107836L;

   public NotFoundMOTreeException() {
      super(ExceptionCode.SRM607[0], ExceptionCode.SRM607[1], ExceptionCode.SRM607[2]);
   }

   public NotFoundMOTreeException(String reason) {
      super(ExceptionCode.SRM607[0], ExceptionCode.SRM607[1], reason);
   }

   public NotFoundMOTreeException(Throwable cause) {
      super(ExceptionCode.SRM607[0], ExceptionCode.SRM607[1], cause.toString());
   }

   public NotFoundMOTreeException(String code, String reason) {
      super(code, reason);
   }

   public NotFoundMOTreeException(String reason, Throwable cause) {
      super(reason, cause);
   }

   public NotFoundMOTreeException(String code, String subCode, String reason) {
      super(code, subCode, reason);
   }
}
