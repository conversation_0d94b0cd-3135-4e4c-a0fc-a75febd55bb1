package com.samsung.common.constants;

import java.util.HashMap;
import java.util.Map;

public class GroupConstants {
   public static final String ROOT_GROUP = "Root Group";
   public static final int ROOT_GROUP_ID = 0;
   public static final int ROOT_GROUP_PARENT_ID = -1;
   public static final int MINIMUM_ORG_REQ_IN_MULTI_ORG_GROUP = 2;
   public static final int NON_APPROVAL_GROUP_ID = 999999;
   public static final String DEFAULT_GROUP_NAME = "default";
   public static final int TEMP_GROUP_ID = -2;

   public GroupConstants() {
      super();
   }

   public static Map getConstantsMap() {
      Map result = new HashMap();
      result.put("ROOT_GROUP", "Root Group");
      result.put("ROOT_GROUP_ID", 0);
      result.put("ROOT_GROUP_PARENT_ID", -1);
      result.put("NON_APPROVAL_GROUP_ID", 999999);
      result.put("DEFAULT_GROUP_NAME", "default");
      result.put("TEMP_GROUP_ID", -2);
      return result;
   }
}
