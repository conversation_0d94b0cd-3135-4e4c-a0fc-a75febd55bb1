package com.samsung.magicinfo.cms.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.cms.service.ContentGroupService;
import com.samsung.magicinfo.framework.content.entity.Group;
import com.samsung.magicinfo.mvc.handler.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import java.util.Map;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "Content Management System Rest api",
   description = "Operations pertaining to content in Content Management System",
   tags = {"Content API Group"}
)
@RestController
@RequestMapping({"/restapi/v1.0/cms/contents/groups"})
public class ContentGroupController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private ContentGroupService contentGroupService;

   public ContentGroupController() {
      super();
   }

   @ApiOperation(
      value = "get default content list",
      notes = "get default content list.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listDefaultGroup() throws Exception {
      this.logger.info("[REST][CONTENT GROUP][listDefaultGroup] get content default group");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentGroupService.listDefaultGroup();
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][CONTENT GROUP][listDefaultGroup] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT GROUP][listDefaultGroup] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         this.logger.error("[REST][CONTENT GROUP][listDefaultGroup] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var3.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][CONTENT GROUP][listDefaultGroup] Exception is occured. " + var4.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get content group information",
      notes = "get selected group information by id.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{groupId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getGroup(@PathVariable String groupId) throws Exception {
      this.logger.info("[REST][CONTENT GROUP][getGroup][" + groupId + "] get group information by groupId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentGroupService.getGroup(groupId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][CONTENT GROUP][getGroup][" + groupId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT GROUP][getGroup][" + groupId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][CONTENT GROUP][getGroup][" + groupId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][CONTENT GROUP][getGroup][" + groupId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get child group",
      notes = "get child group included to selected group.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{groupId}/child"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listChildGroup(@PathVariable String groupId) throws Exception {
      this.logger.info("[REST][CONTENT GROUP][listChildGroup][" + groupId + "] get child group by groupId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentGroupService.listChildGroup(groupId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][CONTENT GROUP][listChildGroup][" + groupId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT GROUP][listChildGroup][" + groupId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         this.logger.error("[REST][CONTENT GROUP][listChildGroup][" + groupId + "] access denied.");
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         this.logger.error("[REST][CONTENT GROUP][listChildGroup][" + groupId + "] Exception is occured. " + var5.toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiVersion({1.0D})
   @ApiOperation(
      value = "create new group",
      notes = "create new content group.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{groupId}"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity createGroup(@PathVariable Long groupId, @RequestParam String name) throws Exception {
      Map resultMap = null;

      try {
         Group group = new Group();
         group.setP_group_id(groupId);
         group.setGroup_name(name);
         resultMap = this.contentGroupService.createGroup(group);
         return new ResponseEntity((ResponseBody)resultMap, (HttpStatus)resultMap.get("HttpStatus"));
      } catch (AccessDeniedException var5) {
         return new ResponseEntity((ResponseBody)resultMap, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         return new ResponseEntity((ResponseBody)resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }
}
