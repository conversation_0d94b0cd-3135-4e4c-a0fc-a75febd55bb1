package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

public class PlaylistUtils {
   protected static Logger logger = LoggingManagerV2.getLogger(PlaylistUtils.class);
   static ContentInfo cInfo = ContentInfoImpl.getInstance();

   public PlaylistUtils() {
      super();
   }

   public static String getContentPlayTime(Content content) throws SQLException {
      String playTime = content.getPlay_time();
      if (StringUtils.isEmpty(playTime) && "DLK".equals(content.getMedia_type())) {
         String tmpPlayTime = cInfo.getPlayTimeOfLftByDlk(content.getContent_id());
         if (tmpPlayTime != null) {
            playTime = tmpPlayTime;
            if (!content.getMedia_type().equalsIgnoreCase("DLK") && !cInfo.setContentPlayTime(content.getContent_id(), tmpPlayTime)) {
               logger.error("[DataRepair FAIL] " + content.getContent_name() + "[" + content.getContent_id() + "] playTime [" + tmpPlayTime + "]");
            }
         }
      }

      return playTime;
   }

   public static boolean updateLastModifiedDate(String playlistId) throws SQLException, ConfigException {
      PlaylistInfo playlistInfo = PlaylistInfoImpl.getInstance();
      return playlistInfo.setPlaylistModifiedDate(playlistId) > 0;
   }
}
