package com.samsung.magicinfo.framework.advertisement.entity.schedule;

public class ContentListEntity {
   private String user_id = null;
   private String schedule_id = null;
   private int area_ad_type_id = -1;
   private String area_ad_type_name = null;
   private int schedule_time_id = -1;
   private String start_date = null;
   private String end_date = null;
   private String start_hour = null;
   private String start_minute = null;
   private String end_hour = null;
   private String end_minute = null;
   private String content_id;
   private String main_file_id = null;
   private String thumb_nail_id = null;
   private String thumb_nail_name = null;
   private int play_duration = 0;
   private int play_repeat = 0;
   private int play_time = 0;

   public ContentListEntity() {
      super();
   }

   public String getUser_id() {
      return this.user_id;
   }

   public void setUser_id(String user_id) {
      this.user_id = user_id;
   }

   public String getSchedule_id() {
      return this.schedule_id;
   }

   public void setSchedule_id(String schedule_id) {
      this.schedule_id = schedule_id;
   }

   public int getSchedule_time_id() {
      return this.schedule_time_id;
   }

   public void setSchedule_time_id(int schedule_time_id) {
      this.schedule_time_id = schedule_time_id;
   }

   public int getArea_ad_type_id() {
      return this.area_ad_type_id;
   }

   public void setArea_ad_type_id(int area_ad_type_id) {
      this.area_ad_type_id = area_ad_type_id;
   }

   public String getArea_ad_type_name() {
      return this.area_ad_type_name;
   }

   public void setArea_ad_type_name(String area_ad_type_name) {
      this.area_ad_type_name = area_ad_type_name;
   }

   public String getStart_date() {
      return this.start_date;
   }

   public void setStart_date(String start_date) {
      this.start_date = start_date;
   }

   public String getEnd_date() {
      return this.end_date;
   }

   public void setEnd_date(String end_date) {
      this.end_date = end_date;
   }

   public String getStart_hour() {
      return this.start_hour;
   }

   public void setStart_hour(String start_hour) {
      this.start_hour = start_hour;
   }

   public String getStart_minute() {
      return this.start_minute;
   }

   public void setStart_minute(String start_minute) {
      this.start_minute = start_minute;
   }

   public String getEnd_hour() {
      return this.end_hour;
   }

   public void setEnd_hour(String end_hour) {
      this.end_hour = end_hour;
   }

   public String getEnd_minute() {
      return this.end_minute;
   }

   public void setEnd_minute(String end_minute) {
      this.end_minute = end_minute;
   }

   public String getContent_id() {
      return this.content_id;
   }

   public void setContent_id(String content_id) {
      this.content_id = content_id;
   }

   public String getMain_file_id() {
      return this.main_file_id;
   }

   public void setMain_file_id(String main_file_id) {
      this.main_file_id = main_file_id;
   }

   public String getThumb_nail_id() {
      return this.thumb_nail_id;
   }

   public void setThumb_nail_id(String thumb_nail_id) {
      this.thumb_nail_id = thumb_nail_id;
   }

   public String getThumb_nail_name() {
      return this.thumb_nail_name;
   }

   public void setThumb_nail_name(String thumb_nail_name) {
      this.thumb_nail_name = thumb_nail_name;
   }

   public int getPlay_duration() {
      return this.play_duration;
   }

   public void setPlay_duration(int play_duration) {
      this.play_duration = play_duration;
   }

   public int getPlay_repeat() {
      return this.play_repeat;
   }

   public void setPlay_repeat(int play_repeat) {
      this.play_repeat = play_repeat;
   }

   public int getPlay_time() {
      return this.play_time;
   }

   public void setPlay_time(int play_time) {
      this.play_time = play_time;
   }
}
