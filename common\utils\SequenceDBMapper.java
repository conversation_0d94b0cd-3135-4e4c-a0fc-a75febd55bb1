package com.samsung.common.utils;

import java.sql.SQLException;
import org.apache.ibatis.annotations.Param;

public interface SequenceDBMapper {
   int getCurrentValue(@Param("sequenceName") String var1) throws SQLException;

   void createSequence(@Param("sequenceName") String var1, @Param("initialValue") int var2) throws SQLException;

   int incrementValue(@Param("sequenceName") String var1, @Param("incrementBy") int var2) throws SQLException;
}
