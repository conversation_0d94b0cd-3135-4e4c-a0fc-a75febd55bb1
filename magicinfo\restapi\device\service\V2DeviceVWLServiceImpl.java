package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceDisplayConf;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceDisplayConfManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayout;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlLayoutMonitor;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.DOMVwtParserImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManager;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwlLayoutManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwtXmlParser;
import com.samsung.magicinfo.framework.device.vwlLayout.manager.VwtXmlParserInfo;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleAdminDao;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.protocol.file.FileUploadCommonHelper;
import com.samsung.magicinfo.protocol.servicemanager.ServiceDispatcher;
import com.samsung.magicinfo.protocol.servicemanager.WSRMServiceDispatcher;
import com.samsung.magicinfo.restapi.common.model.V2CommonDeleteFail;
import com.samsung.magicinfo.restapi.device.model.V2DeletedDevice;
import com.samsung.magicinfo.restapi.device.model.V2DeviceChildVwlLayoutItem;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReplaceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSignageSlaveResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceVwlLayoutResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import java.io.File;
import java.io.PrintWriter;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;
import org.json.JSONObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

@Service("V2DeviceVWLService")
@Transactional
public class V2DeviceVWLServiceImpl implements V2DeviceVWLService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceVWLServiceImpl.class);

   public V2DeviceVWLServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody cancelVwlgroup(String groupId, HttpServletRequest request) throws SQLException {
      ResponseBody responseBody = new ResponseBody();

      try {
         LinkedHashMap resultMap = new LinkedHashMap();
         Locale locale = SecurityUtils.getLocale();
         this.cancelVwllayoutGroup(request, groupId);
         Long longGroupId = Long.parseLong(groupId);
         if (longGroupId != null) {
            DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
            String defaultProgramId = deviceGroupDao.getDefaultProgramId(longGroupId);
            ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
            schInfo.setDefaultProgramId(longGroupId, defaultProgramId);
            ScheduleInfoDAO dao = new ScheduleInfoDAO();
            ScheduleAdminDao adminDao = new ScheduleAdminDao();
            List listPrograms = new ArrayList();
            ProgramEntity program = dao.getProgram(defaultProgramId);
            program.setDevice_group_ids(groupId.toString());
            listPrograms.add(program);
            if (listPrograms != null && listPrograms.size() > 0) {
               adminDao.deployDefaultPrograms(listPrograms);
            }
         }

         resultMap.put("status", "success");
         responseBody.setItems((new JSONObject(resultMap)).toString());
         responseBody.setStatus("Success");
         return responseBody;
      } catch (Exception var14) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var14.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody uploadVwl(HttpServletRequest request) throws SQLException {
      ResponseBody responseBody = new ResponseBody();
      LinkedHashMap resultMap = new LinkedHashMap();

      try {
         String tempFilePath = "VWT TEMP";
         MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest)request;
         MultipartFile vwtFile = multipartRequest.getFile("vwlLayoutFile");
         String fileName = vwtFile.getOriginalFilename();
         boolean isTempFileSuccess = FileUploadCommonHelper.saveFileToDisk_UTF8(fileName, vwtFile, tempFilePath);
         if (isTempFileSuccess) {
            VwtXmlParserInfo domVwtXmlParser = new DOMVwtParserImpl();
            VwtXmlParser vwtXmlParser = new VwtXmlParser(domVwtXmlParser);
            String vwtId = vwtXmlParser.getTempVwtParsing(fileName, SecurityUtils.getLoginUserId());
            String realFilePath = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + vwtId + File.separator + fileName;
            String realFolderPath = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + vwtId;
            File realFile = SecurityUtils.getSafeFile(realFilePath);
            File realFolder = SecurityUtils.getSafeFile(realFolderPath);
            String filePath = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + "temp" + File.separator + fileName;
            String folderPath = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + "temp" + File.separator;
            File tempFile = SecurityUtils.getSafeFile(filePath);
            File tempFolder = SecurityUtils.getSafeFile(folderPath);
            tempFile.renameTo(realFile);
            tempFolder.renameTo(realFolder);
            tempFile.delete();
            tempFolder.delete();
         }

         resultMap.put("status", "success");
         responseBody.setItems((new JSONObject(resultMap)).toString());
         responseBody.setStatus("Success");
         return responseBody;
      } catch (Exception var20) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var20.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody renameVwl(String vwlLayoutId, String vwlLayoutName, HttpServletRequest request) throws SQLException {
      ResponseBody responseBody = new ResponseBody();
      LinkedHashMap resultMap = new LinkedHashMap();

      try {
         VwlLayoutManager vwlLayoutInfo = VwlLayoutManagerImpl.getInstance();
         vwlLayoutId = StrUtils.nvl(vwlLayoutId).equals("") ? "" : vwlLayoutId;
         String reName = StrUtils.nvl(vwlLayoutName).equals("") ? "" : vwlLayoutName;
         VwlLayout vwlLayout = vwlLayoutInfo.getVwlLayoutInfo(vwlLayoutId);
         String old_vwllayout_name = vwlLayout.getVwt_file_name();
         String file_folder = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + vwlLayoutId + File.separator;
         int index = reName.lastIndexOf(".");
         String old_file_path;
         if (index == -1) {
            reName = reName + ".VWL";
         } else {
            old_file_path = reName.substring(index + 1);
            if (!old_file_path.equalsIgnoreCase("VWL")) {
               reName = reName + ".VWL";
            }
         }

         old_file_path = file_folder + old_vwllayout_name;
         String new_file_path = file_folder + reName;
         File oldFile = SecurityUtils.getSafeFile(old_file_path);
         File newFile = SecurityUtils.getSafeFile(new_file_path);
         oldFile.renameTo(newFile);
         vwlLayoutInfo.renameVwlLayoutFile(vwlLayoutId, reName);
         resultMap.put("status", "success");
         responseBody.setItems((new JSONObject(resultMap)).toString());
         responseBody.setStatus("Success");
         return responseBody;
      } catch (Exception var16) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var16.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public ResponseBody deleteVwl(String vwlLayoutIds, HttpServletRequest request) throws SQLException, Exception {
      ResponseBody responseBody = new ResponseBody();
      Locale locale = SecurityUtils.getLocale();
      LinkedHashMap resultMap = new LinkedHashMap();
      VwlLayoutManager vwlLayoutInfo = VwlLayoutManagerImpl.getInstance();
      vwlLayoutIds = StrUtils.nvl(vwlLayoutIds).equals("") ? "" : vwlLayoutIds;
      boolean delResult = vwlLayoutInfo.deleteVwlLayoutInfoWithFile(vwlLayoutIds.split(","));
      if (delResult) {
         resultMap.put("status", "success");
         responseBody.setItems((new JSONObject(resultMap)).toString());
         responseBody.setStatus("Success");
         return responseBody;
      } else {
         throw new RestServiceException(RestExceptionCode.BAD_REQUEST_VWL_MAPPED_GROUP_NOT_DELETE);
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority', 'Device Write Authority')")
   public V2DeviceReplaceResource checkReplaceDevice(String faultId, String asState, String groupId, String modelName, List deviceIds, List selDeviceIds) throws SQLException, Exception {
      groupId = StrUtils.nvl(groupId).equals("") ? "" : groupId;
      modelName = StrUtils.nvl(modelName).equals("") ? "" : modelName;
      if (!SecurityUtils.checkDeviceApprovalPermission()) {
         Iterator var7;
         String selDeviceId;
         if (deviceIds != null && !deviceIds.isEmpty()) {
            var7 = deviceIds.iterator();

            while(var7.hasNext()) {
               selDeviceId = (String)var7.next();
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, selDeviceId);
            }
         }

         if (selDeviceIds != null && !selDeviceIds.isEmpty()) {
            var7 = selDeviceIds.iterator();

            while(var7.hasNext()) {
               selDeviceId = (String)var7.next();
               RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, selDeviceId);
            }
         }

         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.parseLong(groupId));
      }

      V2DeviceReplaceResource resource = new V2DeviceReplaceResource();
      DeviceGroupInfo groupDao = DeviceGroupInfoImpl.getInstance();
      List chkInfo = new ArrayList();
      boolean checkMinusPriority = true;
      if (deviceIds != null && !deviceIds.isEmpty()) {
         chkInfo = deviceIds;
      } else if (selDeviceIds != null) {
         chkInfo = selDeviceIds;
      }

      long start = System.currentTimeMillis();
      String failDeviceId = null;

      for(int i = 0; i < ((List)chkInfo).size(); ++i) {
         checkMinusPriority = DeviceUtils.checkMinusPriority(groupId, (String)((List)chkInfo).get(i));
         if (!checkMinusPriority) {
            failDeviceId = (String)((List)chkInfo).get(i);
            break;
         }
      }

      long end = System.currentTimeMillis();
      NumberFormat formatter = new DecimalFormat("#0.00000");
      this.logger.info("Execution time is " + formatter.format((double)(end - start) / 1000.0D) + " seconds");
      if (!checkMinusPriority) {
         RestExceptionCode reason = RestExceptionCode.BAD_REQUEST_DEVICE_TYPE_GROUP_TYPE_DIFFERENT;
         resource.setReason(reason.getMessage());
         resource.setReasonCode(reason.getCode());
         resource.setGroupId(groupId);
         resource.setDeviceId(failDeviceId);
         resource.setResult("failure");
         return resource;
      } else {
         if (groupDao.isVwlGroup(groupId)) {
            resource.setIsVwlGroup(true);
            if (StringUtils.isBlank(groupId)) {
               throw new RestServiceException(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID, new String[]{"groupId"});
            }

            boolean bFound = false;
            long gId = Long.parseLong(groupId);
            List modelList = groupDao.getDeviceModelName(gId);

            for(int i = 0; i < modelList.size(); ++i) {
               String deviceModelName = ((Map)modelList.get(i)).get("device_model_name").toString();
               if (deviceModelName.equals(modelName)) {
                  bFound = true;
                  break;
               }
            }

            if (bFound) {
               String groupName = groupDao.getGroupNameByGroupId(gId);
               resource.setGroupId(groupId);
               resource.setGroupName(groupName);
               resource.setHasModel(true);
            } else {
               resource.setHasModel(false);
            }

            resource.setResult("success");
         } else {
            long gId = Long.parseLong(groupId);
            String groupName = groupDao.getGroupNameByGroupId(gId);
            resource.setIsVwlGroup(false);
            resource.setGroupId(groupId);
            resource.setGroupName(groupName);
            resource.setResult("success");
         }

         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public String vwtManagement(String vwlLayoutId, HttpServletResponse response) throws Exception {
      String request_id = StrUtils.nvl(vwlLayoutId).equals("") ? "" : vwlLayoutId;
      String[] vwllayout_id = request_id.split(",");
      VwtXmlParserInfo domVwtXmlParser = new DOMVwtParserImpl();
      VwtXmlParser vwtXmlParser = new VwtXmlParser(domVwtXmlParser);
      VwlLayoutManager VWTInfo = VwlLayoutManagerImpl.getInstance();
      VwlLayout vwlLayout = VWTInfo.getVwlLayoutInfo(vwllayout_id[0]);
      String fileName = vwlLayout.getVwt_file_name();
      Boolean is_Linear = vwlLayout.getIs_linear();
      List vwlLayoutMonitors = vwtXmlParser.getVwtMonitorInfo(vwllayout_id[0], fileName);

      try {
         if (vwlLayoutMonitors != null && vwlLayoutMonitors.size() != 0) {
            response.setContentType("text/xml;charset=UTF-8");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
            response.addHeader("Cache-Control", "post-check=0, pre-check=0");
            response.setDateHeader("Expires", 0L);
            response.setStatus(200);
            PrintWriter writer = response.getWriter();
            Element company = new Element("root");
            Document document = new Document(company);
            String linear_text;
            if (is_Linear) {
               linear_text = "TRUE";
            } else {
               linear_text = "FALSE";
            }

            int width = 0;
            int height = 0;
            company.addContent((new Element("lienar")).setText(linear_text));
            company.addContent((new Element("x_count")).setText(String.valueOf(vwlLayout.getX_count())));
            company.addContent((new Element("y_count")).setText(String.valueOf(vwlLayout.getY_count())));
            company.addContent((new Element("vwt_id")).setText(vwllayout_id[0]));

            for(int i = 0; i < vwlLayoutMonitors.size(); ++i) {
               Element monitor = new Element("monitor");
               monitor.addContent((new Element("width")).addContent(String.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPanel_width())));
               monitor.addContent((new Element("height")).addContent(String.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPanel_height())));
               monitor.addContent((new Element("position_x")).addContent(String.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPosition_x())));
               monitor.addContent((new Element("position_y")).addContent(String.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPosition_y())));
               monitor.addContent((new Element("bezel_leftright")).addContent(String.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getBezel_leftright())));
               monitor.addContent((new Element("bezel_topbottom")).addContent(String.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getBezel_topbottom())));
               monitor.addContent((new Element("map_id")).addContent(String.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getMap_id())));
               monitor.addContent((new Element("fileName")).addContent(fileName));
               if (width <= Integer.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPosition_x() + Integer.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPanel_width()))) {
                  width = Integer.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPosition_x()) + Integer.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPanel_width());
               }

               if (height <= Integer.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPosition_y()) + Integer.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPanel_height())) {
                  height = Integer.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPosition_y()) + Integer.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(i)).getPanel_height());
               }

               document.getRootElement().addContent(monitor);
            }

            if (!is_Linear) {
               company.addContent((new Element("document_width")).setText(String.valueOf(width)));
               company.addContent((new Element("document_height")).setText(String.valueOf(height)));
            } else {
               company.addContent((new Element("document_width")).setText(String.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(0)).getPanel_width())));
               company.addContent((new Element("document_height")).setText(String.valueOf(((VwlLayoutMonitor)vwlLayoutMonitors.get(0)).getPanel_height())));
            }

            XMLOutputter xmlOutputer = new XMLOutputter();
            xmlOutputer.setFormat(Format.getPrettyFormat());
            xmlOutputer.output(document, writer);
            return null;
         } else {
            throw new RestServiceException(RestExceptionCode.DATA_NOT_FOUND_FORMAT, new String[]{"vwlLayoutMonitor list"});
         }
      } catch (Exception var20) {
         this.logger.error("", var20);
         return "error";
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeletedDevice signageDisplaySetting(String deviceId, String signageType) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      V2DeletedDevice resource = new V2DeletedDevice();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getDeviceMinInfo(deviceId);
      if (signageType != null && !signageType.equals("SIGNAGE_ID_SHOW") && !signageType.equals("SIGNAGE_ID_HIDE") && !signageType.equals("SIGNAGE_ID_AUTOSET") && !signageType.equals("SCAN_CHILD_INFO")) {
         this.logger.error("REQ Set AuthorCmd : Invalid signage type=" + signageType);
         resource.setDeviceId(deviceId);
         resource.setReason(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID.generateFormattedMessages("signageType"));
         resource.setReasonCode(RestExceptionCode.BAD_REQUEST_PARAMETER_INVALID.getCode());
         resource.setResult("failure");
         return resource;
      } else {
         HashMap params = new HashMap();
         params.put("device", device);
         if (signageType != null && !signageType.equals("")) {
            params.put("signage_cmd", signageType);
         }

         ServiceDispatcher dispatcher = WSRMServiceDispatcher.getInstance();
         dispatcher.startService("SET_SIGNAGE_CMD", params);
         resource.setDeviceId(deviceId);
         resource.setResult("sucess");
         return resource;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Write Authority')")
   public V2DeviceVwlLayoutResource showSignageLayout(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      DeviceDisplayConfManager displayConfDao = DeviceDisplayConfManagerImpl.getInstance();
      V2DeviceVwlLayoutResource resource = new V2DeviceVwlLayoutResource();
      Device parentDevice = deviceDao.getDevice(deviceId);
      long childCnt = parentDevice.getChild_cnt();
      List childDeviceIDList = DeviceUtils.getChildDeviceIdList(deviceId, childCnt);
      if (childDeviceIDList != null && childDeviceIDList.size() > 0) {
         DeviceDisplayConf childDevice = displayConfDao.getDeviceDisplayConf((String)childDeviceIDList.get(0));
         Device childDeviceInfo = deviceDao.getDevice((String)childDeviceIDList.get(0));
         int width = 498;
         int height = 896;
         int bezel_leftright = 12;
         int bezel_topbottom = 10;
         if (childDeviceInfo.getScreen_rotation() == 0L) {
            width = 896;
            height = 498;
            bezel_leftright = 10;
            bezel_topbottom = 12;
         }

         int mon_width = width + bezel_leftright * 2;
         int mon_height = height + bezel_topbottom * 2;
         String vwlLayout = childDevice.getVwl_layout();
         int x_count = 0;
         int y_count = 0;
         if (vwlLayout != null && vwlLayout.indexOf("x") >= 0) {
            if (vwlLayout.indexOf(";") >= 0) {
               vwlLayout = vwlLayout.split(";")[0];
            }

            x_count = Integer.valueOf(vwlLayout.split("x")[0]);
            y_count = Integer.valueOf(vwlLayout.split("x")[1]);
         }

         boolean isLinear = true;
         resource.setLinear(isLinear);
         resource.setxCount((long)x_count);
         resource.setyCount((long)y_count);
         List childLayoutList = new ArrayList();

         for(int i = 0; (long)i < childCnt; ++i) {
            V2DeviceChildVwlLayoutItem childResource = new V2DeviceChildVwlLayoutItem();
            childDevice = displayConfDao.getDeviceDisplayConf((String)childDeviceIDList.get(i));
            String xyPosition = childDevice.getVwl_position();
            int position_x = 0;
            int position_y = 0;
            String mapId = childDevice.getDevice_id().split("_")[1];
            if (isLinear && !"default".equalsIgnoreCase(xyPosition)) {
               position_x = (Integer.valueOf(xyPosition.split("\\.")[0]) - 1) * mon_width;
               position_y = (Integer.valueOf(xyPosition.split("\\.")[1]) - 1) * mon_height;
            }

            childResource.setWidth((long)width);
            childResource.setHeight((long)height);
            childResource.setPositionX(position_x);
            childResource.setPositionY(position_y);
            childResource.setBezelLeftright(bezel_leftright);
            childResource.setBezelTopbottom(bezel_topbottom);
            childResource.setMapId(mapId);
            childLayoutList.add(childResource);
         }

         resource.setDocumentWidth((long)width);
         resource.setDocumentHeight((long)height);
         resource.setChildLayoutList(childLayoutList);
         return resource;
      } else {
         V2CommonDeleteFail fail = new V2CommonDeleteFail();
         fail.setId(deviceId);
         fail.setReason(RestExceptionCode.DATA_NOT_FOUND_FORMAT.generateFormattedMessages("child device"));
         fail.setReasonCode(RestExceptionCode.DATA_NOT_FOUND_FORMAT.getCode());
         resource.setFailReson(fail);
         return resource;
      }
   }

   public List signageSlaveInfo(String deviceId) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
      List resources = new ArrayList();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      Device device = deviceDao.getMonitoringViewDevice(deviceId);
      List deviceIdList = DeviceUtils.getChildDeviceIdList(deviceId, device.getChild_cnt());
      long child_cnt = (long)deviceIdList.size();
      DeviceDisplayConfManager displayConfDao = DeviceDisplayConfManagerImpl.getInstance();
      this.canDrawVWLLayout(deviceId, device.getChild_cnt());
      MonitoringManager mgr = MonitoringManagerImpl.getInstance();
      Device childDevice = null;
      String childDeviceId = null;
      String parnetId = null;
      String childId = null;
      String panelStatus = null;
      String lastScannedTime = null;
      DeviceDisplayConf displayConf = null;
      if (deviceIdList != null && child_cnt > 0L) {
         for(int i = 0; (long)i < child_cnt; ++i) {
            V2DeviceSignageSlaveResource resource = new V2DeviceSignageSlaveResource();
            childDeviceId = (String)deviceIdList.get(i);
            childDevice = deviceDao.getDevice(childDeviceId);
            if (childDevice != null) {
               displayConf = displayConfDao.getDeviceDisplayConf(childDeviceId);
               if (childDevice.getDevice_id() != null && childDevice.getDevice_id().indexOf("_") > -1) {
                  parnetId = childDevice.getDevice_id().split("_")[0];
                  childId = childDevice.getDevice_id().split("_")[1];
               }

               if (displayConf.getVwl_position().equals("DEFAULT") || displayConf.getVwl_layout().equals("DEFAULT")) {
                  boolean var9 = false;
               }

               boolean isConnectedChild = mgr.isConnected(childDeviceId);
               if (isConnectedChild) {
                  if (displayConf.getBasic_panel_status() == 1L) {
                     panelStatus = "BUTTON_OFF";
                  } else {
                     panelStatus = "TEXT_ON_P";
                  }
               } else {
                  panelStatus = "-";
               }

               if (childDevice.getLast_connection_time() != null && childDevice.getLast_connection_time().getTime() != (new Timestamp(0L)).getTime()) {
                  lastScannedTime = childDevice.getLast_connection_time().toString();
               } else {
                  lastScannedTime = "MIS_SID_NOT_SCANNED_YET";
               }

               resource.setChildId(childId);
               resource.setConnected(isConnectedChild);
               resource.setPanelStatus(panelStatus);
               resource.setInputSource(this.getInputSource(displayConf.getBasic_source()));
               resource.setLastScannedTime(lastScannedTime);
               resources.add(resource);
            }
         }
      }

      return resources;
   }

   public boolean cancelVwllayoutGroup(HttpServletRequest request, String groupId) throws Exception, SQLException {
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      List deviceIdList = deviceGroupInfo.getChildDeviceIdList(Integer.parseInt(groupId), false);
      deviceGroupInfo.getVwlLayoutIdByGroupId(groupId);
      int loopSize = deviceIdList.size();
      String deviceIdListStr = "";

      for(int i = 0; i < loopSize; ++i) {
         if (i == 0) {
            deviceIdListStr = (String)deviceIdList.get(i);
         } else {
            deviceIdListStr = deviceIdListStr + "," + (String)deviceIdList.get(i);
         }
      }

      for(int i = 0; i < loopSize; ++i) {
         Device device = deviceInfo.getDevice((String)deviceIdList.get(i));
         device.setVwt_id("");
         deviceInfo.setDevice(device);
         deviceInfo.deleteVwtInfo((String)deviceIdList.get(i));
      }

      deviceGroupInfo.cancelVwlGroup(groupId);
      VwlLayoutManager VwlLayoutManager = VwlLayoutManagerImpl.getInstance();
      String file_path = CommonConfig.get("VWT_HOME").replace('/', File.separatorChar) + File.separator + "EMPTY_LAYOUT.VWL";
      Iterator var12 = deviceIdList.iterator();

      while(var12.hasNext()) {
         String deviceId = (String)var12.next();
         VwlLayoutManager.deployVwlLayout(deviceId, "REMOVE_LAYOUT", "0", file_path);
      }

      DeviceGroupInfo groupInfo = DeviceGroupInfoImpl.getInstance();
      groupInfo.updateCacheDeviceGroup();
      return true;
   }

   private boolean canDrawVWLLayout(String deviceId, Long child_cnt) throws SQLException {
      DeviceDisplayConfManager deviceDisplayConfDao = DeviceDisplayConfManagerImpl.getInstance();
      boolean layout_result = deviceDisplayConfDao.checkSameVWLLayout(deviceId);
      boolean position_result = deviceDisplayConfDao.checkDifferentVWLPosition(deviceId, child_cnt);
      return layout_result && position_result;
   }

   private String getInputSource(Long source) {
      String resultStr = "";
      String input_src = String.valueOf(source);
      byte var5 = -1;
      switch(input_src.hashCode()) {
      case 52:
         if (input_src.equals("4")) {
            var5 = 4;
         }
         break;
      case 56:
         if (input_src.equals("8")) {
            var5 = 5;
         }
         break;
      case 1569:
         if (input_src.equals("12")) {
            var5 = 3;
         }
         break;
      case 1570:
         if (input_src.equals("13")) {
            var5 = 22;
         }
         break;
      case 1571:
         if (input_src.equals("14")) {
            var5 = 23;
         }
         break;
      case 1598:
         if (input_src.equals("20")) {
            var5 = 0;
         }
         break;
      case 1602:
         if (input_src.equals("24")) {
            var5 = 2;
         }
         break;
      case 1629:
         if (input_src.equals("30")) {
            var5 = 1;
         }
         break;
      case 1630:
         if (input_src.equals("31")) {
            var5 = 21;
         }
         break;
      case 1631:
         if (input_src.equals("32")) {
            var5 = 6;
         }
         break;
      case 1632:
         if (input_src.equals("33")) {
            var5 = 9;
         }
         break;
      case 1633:
         if (input_src.equals("34")) {
            var5 = 13;
         }
         break;
      case 1634:
         if (input_src.equals("35")) {
            var5 = 10;
         }
         break;
      case 1635:
         if (input_src.equals("36")) {
            var5 = 14;
         }
         break;
      case 1636:
         if (input_src.equals("37")) {
            var5 = 17;
         }
         break;
      case 1637:
         if (input_src.equals("38")) {
            var5 = 18;
         }
         break;
      case 1668:
         if (input_src.equals("48")) {
            var5 = 19;
         }
         break;
      case 1669:
         if (input_src.equals("49")) {
            var5 = 11;
         }
         break;
      case 1691:
         if (input_src.equals("50")) {
            var5 = 15;
         }
         break;
      case 1692:
         if (input_src.equals("51")) {
            var5 = 12;
         }
         break;
      case 1693:
         if (input_src.equals("52")) {
            var5 = 16;
         }
         break;
      case 1726:
         if (input_src.equals("64")) {
            var5 = 20;
         }
         break;
      case 1784:
         if (input_src.equals("80")) {
            var5 = 8;
         }
         break;
      case 1789:
         if (input_src.equals("85")) {
            var5 = 24;
         }
         break;
      case 1821:
         if (input_src.equals("96")) {
            var5 = 7;
         }
         break;
      case 1822:
         if (input_src.equals("97")) {
            var5 = 25;
         }
      }

      switch(var5) {
      case 0:
         resultStr = resultStr + "PC";
         break;
      case 1:
         resultStr = resultStr + "BNC";
         break;
      case 2:
         resultStr = resultStr + "DVI";
         break;
      case 3:
         resultStr = resultStr + "<spring:message code='TEXT_AV_P'/>";
         break;
      case 4:
         resultStr = resultStr + "S-Video";
         break;
      case 5:
         resultStr = resultStr + "<spring:message code='TEXT_COMPONENT_P'/>";
         break;
      case 6:
         resultStr = resultStr + "MagicInfo";
         break;
      case 7:
         resultStr = resultStr + "MagicInfo-Lite/S";
         break;
      case 8:
         resultStr = resultStr + "Plug In Module";
         break;
      case 9:
         resultStr = resultStr + "HDMI1";
         break;
      case 10:
         resultStr = resultStr + "HDMI2";
         break;
      case 11:
         resultStr = resultStr + "HDMI3";
         break;
      case 12:
         resultStr = resultStr + "HDMI4";
         break;
      case 13:
         resultStr = resultStr + "HDMI1_PC";
         break;
      case 14:
         resultStr = resultStr + "HDMI2_PC";
         break;
      case 15:
         resultStr = resultStr + "HDMI3_PC";
         break;
      case 16:
         resultStr = resultStr + "HDMI4_PC";
         break;
      case 17:
         resultStr = resultStr + "Display Port";
         break;
      case 18:
         resultStr = resultStr + "Display Port2";
         break;
      case 19:
         resultStr = resultStr + "ATV";
         break;
      case 20:
         resultStr = resultStr + "DTV";
         break;
      case 21:
         resultStr = resultStr + "DVI_VIDEO";
         break;
      case 22:
         resultStr = resultStr + "AV2";
         break;
      case 23:
         resultStr = resultStr + "Ext";
         break;
      case 24:
         resultStr = resultStr + "HDBaseT";
         break;
      case 25:
         resultStr = resultStr + "WiFi";
      }

      return resultStr;
   }
}
