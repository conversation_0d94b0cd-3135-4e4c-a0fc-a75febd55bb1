package com.samsung.magicinfo.auth.security.state.impl;

import com.samsung.magicinfo.auth.security.AuthResource;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;
import com.samsung.magicinfo.auth.security.state.AuthState;
import com.samsung.magicinfo.auth.security.strategies.AuthStrategyContext;
import com.samsung.magicinfo.auth.security.strategies.impl.HotpAuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.impl.TotpAuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.model.AuthModel;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;

public class ValidAuthState implements AuthState {
   public ValidAuthState() {
      super();
   }

   public OTPAuthType auth(AuthResource resource) {
      AuthModel authModel = new AuthModel();
      authModel.setUserId(resource.getUsername());
      authModel.setUserAuthDevice(resource.getUserAuthDevice());
      AuthStrategyContext authContext = null;
      if (resource.getTotp() != null) {
         authModel.setOtp(resource.getTotp());
         authContext = new AuthStrategyContext(new TotpAuthStrategy());
      } else {
         if (resource.getHotp() == null) {
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_REQUIRED_OTP_VALUE);
         }

         authModel.setOtp(resource.getHotp());
         authContext = new AuthStrategyContext(new HotpAuthStrategy());
      }

      if (!authContext.valid(authModel)) {
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_USER_NOT_APPROVED);
      } else {
         return null;
      }
   }
}
