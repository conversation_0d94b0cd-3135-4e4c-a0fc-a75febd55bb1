package com.samsung.magicinfo.exception;

public class ExceptionConstants {
   public static final String Receiver = "Receiver";
   public static final String Sender = "Sender";
   public static final String SubCode_InvalidMOFormat = "Invalide MO Format Message";
   public static final String SubCode_Translate = "Translate Fail";
   public static final String SubCode_AddressInfo = "Invalide Address Information Message";
   public static final String SubCode_SRMConfig = "UnSupported SRM configuration Message";
   public static final String SubCode_NotSupportOperation = "Not Supported Operation Message";
   public static final String SubCode_Interfacing = "A Message is cannot Interfacing";
   public static final String Reason_Translate = "Invalid MO Messages give rise to Failure";
   public static final String Reason_AddressInfo = "Was not Suitable Addressing Information Header give rise to Failure";
   public static final String Reason_NotSupportOperation = "UnSupported Configuration Information give rise to Failure";
   public static final String Reason_Interfacing = "Calling RMSAppInterface give rise to Failure From Unsupported Message";

   public ExceptionConstants() {
      super();
   }
}
