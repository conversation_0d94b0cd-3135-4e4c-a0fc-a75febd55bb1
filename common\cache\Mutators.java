package com.samsung.common.cache;

public enum Mutators {
   MAP_ITEM_MERGER {
      public MutatorOperation operation() {
         return new MapItemMerger();
      }
   },
   FULL_MAP_MERGER {
      public MutatorOperation operation() {
         return new FullMapMerger();
      }
   },
   MAP_ITEM_REMOVER {
      public MutatorOperation operation() {
         return new MapItemRemover();
      }
   };

   private Mutators() {
   }

   public abstract MutatorOperation operation();

   // $FF: synthetic method
   Mutators(Object x2) {
      this();
   }
}
