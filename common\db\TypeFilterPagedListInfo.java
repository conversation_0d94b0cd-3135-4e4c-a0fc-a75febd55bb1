package com.samsung.common.db;

import java.util.List;

public class TypeFilterPagedListInfo extends PagedListInfo {
   List typeFilter = null;

   public TypeFilterPagedListInfo(List result, int totalCount, List typeFilter) {
      super(result, totalCount);
      this.typeFilter = typeFilter;
   }

   public List getTypeFilter() {
      return this.typeFilter;
   }

   public void setTypeFilter(List typeFilter) {
      this.typeFilter = typeFilter;
   }
}
