package com.samsung.magicinfo.cms.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

@JsonInclude(Include.NON_NULL)
public class SyncPlaylistItemResource {
   @ApiModelProperty(
      example = "00000000-0000-0000-0000-000000000000",
      required = true
   )
   @Pattern(
      regexp = "^[0-9A-Fa-f]{8}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{4}\\-[0-9A-Fa-f]{12}$",
      message = "[SyncPlaylistItemResource][contentId] Not UUID pattern."
   )
   private String contentId = "";
   @ApiModelProperty(
      example = "1",
      allowableValues = "range[1, infinity]"
   )
   @Min(
      value = 1L,
      message = "[SyncPlaylistItemResource][contentOrder] minimum value is 1."
   )
   private Long contentOrder = 0L;
   @ApiModelProperty(
      example = "00:00:00"
   )
   @Pattern(
      regexp = "^(?:(?:([01]?\\d|2[0-3]):)?([0-5]?\\d):)?([0-5]?\\d)$|",
      message = "[SyncPlaylistItemResource][contentDuration] Not hh:mm:ss pattern."
   )
   private String contentDuration = "";

   public SyncPlaylistItemResource() {
      super();
   }

   public String getContentId() {
      return this.contentId;
   }

   public void setContentId(String contentId) {
      this.contentId = contentId;
   }

   public Long getContentOrder() {
      return this.contentOrder;
   }

   public void setContentOrder(Long contentOrder) {
      this.contentOrder = contentOrder;
   }

   public String getContentDuration() {
      return this.contentDuration;
   }

   public void setContentDuration(String contentDuration) {
      this.contentDuration = contentDuration;
   }
}
