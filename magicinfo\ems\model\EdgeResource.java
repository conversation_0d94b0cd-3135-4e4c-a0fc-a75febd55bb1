package com.samsung.magicinfo.ems.model;

import io.swagger.annotations.ApiModelProperty;

public class EdgeResource {
   @ApiModelProperty(
      example = "hostName"
   )
   private String hostName;
   @ApiModelProperty(
      example = "***********"
   )
   private String ip;
   @ApiModelProperty(
      example = "7001"
   )
   private String webport;
   @ApiModelProperty(
      example = "21"
   )
   private String ftpport;
   @ApiModelProperty(
      example = "true"
   )
   private String private_mode;
   @ApiModelProperty(
      example = "***********"
   )
   private String private_ip;
   @ApiModelProperty(
      example = "7001"
   )
   private String private_webport;
   @ApiModelProperty(
      example = "21"
   )
   private String private_ftpport;
   @ApiModelProperty(
      example = "START"
   )
   private String downloadServerStatus;
   @ApiModelProperty(
      example = "30"
   )
   private String downloadSyncPeriod;
   @ApiModelProperty(
      example = "1GB"
   )
   private String usableDiskSpace;
   @ApiModelProperty(
      example = "password"
   )
   private String ftpPassword;
   @ApiModelProperty(
      example = "password"
   )
   private String region;

   public EdgeResource() {
      super();
   }

   public String getFtpPassword() {
      return this.ftpPassword;
   }

   public void setFtpPassword(String ftpPassword) {
      this.ftpPassword = ftpPassword;
   }

   public String getUsableDiskSpace() {
      return this.usableDiskSpace;
   }

   public void setUsableDiskSpace(String usableDiskSpace) {
      this.usableDiskSpace = usableDiskSpace;
   }

   public String getDownloadSyncPeriod() {
      return this.downloadSyncPeriod;
   }

   public void setDownloadSyncPeriod(String downloadSyncPeriod) {
      this.downloadSyncPeriod = downloadSyncPeriod;
   }

   public String getHostName() {
      return this.hostName;
   }

   public void setHostName(String hostName) {
      this.hostName = hostName;
   }

   public String getIp() {
      return this.ip;
   }

   public void setIp(String ip) {
      this.ip = ip;
   }

   public String getWebport() {
      return this.webport;
   }

   public void setWebport(String webport) {
      this.webport = webport;
   }

   public String getFtpport() {
      return this.ftpport;
   }

   public void setFtpport(String ftpport) {
      this.ftpport = ftpport;
   }

   public String getPrivate_mode() {
      return this.private_mode;
   }

   public void setPrivate_mode(String private_mode) {
      this.private_mode = private_mode;
   }

   public String getPrivate_ip() {
      return this.private_ip;
   }

   public void setPrivate_ip(String private_ip) {
      this.private_ip = private_ip;
   }

   public String getPrivate_webport() {
      return this.private_webport;
   }

   public void setPrivate_webport(String private_webport) {
      this.private_webport = private_webport;
   }

   public String getPrivate_ftpport() {
      return this.private_ftpport;
   }

   public void setPrivate_ftpport(String private_ftpport) {
      this.private_ftpport = private_ftpport;
   }

   public String getDownloadServerStatus() {
      return this.downloadServerStatus;
   }

   public void setDownloadServerStatus(String downloadServerStatus) {
      this.downloadServerStatus = downloadServerStatus;
   }

   public String getRegion() {
      return this.region;
   }

   public void setRegion(String region) {
      this.region = region;
   }
}
