package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SequenceDB;
import com.samsung.magicinfo.framework.advertisement.entity.PopInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.PopRawEntity;
import com.samsung.magicinfo.framework.advertisement.entity.PopSecondInfoRow;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;
import org.apache.commons.lang.Validate;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;

public class ProofOfPlayDao extends SqlSessionBaseDao {
   private Logger logger = LoggingManagerV2.getLogger(ProofOfPlayDao.class);

   public ProofOfPlayDao() {
      super();
   }

   public boolean insertContentPlayHistory(List play_history) {
      boolean result = false;
      String device_id = ((String[])play_history.get(0))[0];
      String log_date = ((String[])play_history.get(0))[1];
      SqlSession sqlSession = this.openNewSession(ExecutorType.BATCH, false);

      try {
         ContentInfo conInfo = ContentInfoImpl.getInstance(sqlSession);

         for(int i = 1; i < play_history.size(); ++i) {
            String[] data = (String[])play_history.get(i);
            if (data != null && data.length > 1) {
               String content_id = data[1];
               String time_range = data[0];
               String fileName = conInfo.getMainFileName(content_id);

               Timestamp start_time;
               try {
                  start_time = Timestamp.valueOf(log_date + " " + time_range);
               } catch (IllegalArgumentException var18) {
                  this.logger.error("[STATISTICS] IllegalArgumentException has been occured for log_date: " + log_date + " time_range : " + time_range + " index: " + i);
                  continue;
               }

               result = ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).insertContentPlayHistory(device_id, start_time, time_range, content_id, Integer.parseInt(data[2]), fileName);
            }
         }
      } catch (Exception var19) {
         result = false;
         sqlSession.rollback();
         this.logger.error("", var19);
      } finally {
         if (sqlSession != null) {
            sqlSession.close();
         }

      }

      return result;
   }

   public boolean insertNewContentPlayHistory(ArrayList play_history, String logDate, String device_id, String log_id) throws SQLException {
      Validate.notNull(device_id, "Content Play History is required");
      Validate.notNull(device_id, "Device ID is required");
      Validate.notNull(logDate, "Log date is required");
      SqlSession sqlSession = this.openNewSession(ExecutorType.BATCH, false);

      try {
         try {
            Map mapNewDailyInfoToDay = new HashMap();
            Map mapNewDailyInfoToMonth = new HashMap();
            Map mapNewDailyInfoToYear = new HashMap();
            boolean useHourTable = true;
            boolean useSecondTable = true;

            try {
               String popCollectionLevel = CommonConfig.get("pop.db.collection.level");
               if (popCollectionLevel != null) {
                  this.logger.error("[POP] statistics collection level: " + popCollectionLevel);
                  byte var13 = -1;
                  switch(popCollectionLevel.hashCode()) {
                  case 99228:
                     if (popCollectionLevel.equals("day")) {
                        var13 = 0;
                     }
                     break;
                  case 3208676:
                     if (popCollectionLevel.equals("hour")) {
                        var13 = 1;
                     }
                  }

                  switch(var13) {
                  case 0:
                     useHourTable = false;
                     useSecondTable = false;
                     break;
                  case 1:
                     useSecondTable = false;
                  }
               }
            } catch (ConfigException var27) {
               this.logger.error("", var27);
            }

            HashMap mapRawByContentId = this.addPopSecondInfoList(play_history, device_id, useSecondTable, sqlSession);
            Set contentIdSet = mapRawByContentId.keySet();
            Iterator contentIdIter = contentIdSet.iterator();

            while(contentIdIter.hasNext()) {
               String contentId = (String)contentIdIter.next();
               ArrayList popRawList = (ArrayList)mapRawByContentId.get(contentId);

               PopInfoEntity dailyPlayInfoByContentId;
               try {
                  dailyPlayInfoByContentId = this.addPopHourInfoList(device_id, logDate, contentId, this.getHourArray(popRawList), useHourTable, sqlSession);
               } catch (Exception var28) {
                  this.logger.error("[STATISTICS] - catched in addPopHourInfoList (device_id: " + device_id + " content_id: " + contentId + ")", var28);
                  boolean var21 = false;
                  return var21;
               }

               PopInfoEntity originalDayPopInfo = this.getPopDayInfo(contentId, device_id, logDate, sqlSession);
               if (originalDayPopInfo != null) {
                  originalDayPopInfo.addDuration(dailyPlayInfoByContentId.getDuration());
                  originalDayPopInfo.addPlayCount(dailyPlayInfoByContentId.getPlay_count());
                  this.setPopDayInfo(contentId, device_id, originalDayPopInfo.getDuration(), originalDayPopInfo.getPlay_count(), logDate, sqlSession);
               } else {
                  mapNewDailyInfoToDay.put(contentId, dailyPlayInfoByContentId);
               }

               PopInfoEntity originalMonthPopInfo = this.getPopMonthInfo(contentId, device_id, logDate, sqlSession);
               if (originalMonthPopInfo != null) {
                  originalMonthPopInfo.addDuration(dailyPlayInfoByContentId.getDuration());
                  originalMonthPopInfo.addPlayCount(dailyPlayInfoByContentId.getPlay_count());
                  this.setPopMonthInfo(contentId, device_id, originalMonthPopInfo.getDuration(), originalMonthPopInfo.getPlay_count(), logDate, sqlSession);
               } else {
                  mapNewDailyInfoToMonth.put(contentId, dailyPlayInfoByContentId);
               }

               PopInfoEntity originalYearPopInfo = this.getPopYearInfo(contentId, device_id, logDate, sqlSession);
               if (originalYearPopInfo != null) {
                  originalYearPopInfo.addDuration(dailyPlayInfoByContentId.getDuration());
                  originalYearPopInfo.addPlayCount(dailyPlayInfoByContentId.getPlay_count());
                  this.setPopYearInfo(contentId, device_id, originalYearPopInfo.getDuration(), originalYearPopInfo.getPlay_count(), logDate, sqlSession);
               } else {
                  mapNewDailyInfoToYear.put(contentId, dailyPlayInfoByContentId);
               }
            }

            if (mapNewDailyInfoToDay.size() != 0) {
               this.addPopDayInfoList(device_id, mapNewDailyInfoToDay, logDate, sqlSession);
            }

            if (mapNewDailyInfoToMonth.size() != 0) {
               this.addPopMonthInfoList(device_id, mapNewDailyInfoToMonth, logDate, sqlSession);
            }

            if (mapNewDailyInfoToYear.size() != 0) {
               this.addPopYearInfoList(device_id, mapNewDailyInfoToYear, logDate, sqlSession);
            }

            sqlSession.commit();
         } catch (Exception var29) {
            if (sqlSession != null) {
               sqlSession.rollback();
            }

            this.logger.error("[MagicInfo_ProofOfPlay] fail to insert row on DB exception e : " + var29.getMessage(), var29);
         }

         return true;
      } finally {
         if (sqlSession != null) {
            sqlSession.close();
         }

      }
   }

   public boolean checkDuplicationByDB(String log_date, String device_id) {
      Long cnt = null;

      try {
         cnt = ((ProofOfPlayDaoMapper)this.getMapper()).checkDuplicationByDB(Timestamp.valueOf(log_date + " 00:00:00"), device_id);
      } catch (SQLException var5) {
         this.logger.error(var5);
      }

      return cnt.intValue() < 1;
   }

   public Integer nextPopId() throws SQLException {
      return SequenceDB.getNextValue("MI_STATISTICS_CONTENT_SECOND");
   }

   private HashMap addPopSecondInfoList(ArrayList play_history, String device_id, boolean useSecondTable, SqlSession sqlSession) throws SQLException {
      HashMap mapRaw = new HashMap();
      int maxPopId = 0;
      List popSecondInfoRows = null;
      if (useSecondTable) {
         maxPopId = ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).getMaxPopId();
         popSecondInfoRows = new ArrayList();
      }

      try {
         Iterator var8 = play_history.iterator();

         while(var8.hasNext()) {
            String[] args = (String[])var8.next();
            PopRawEntity cbse = new PopRawEntity();
            cbse.setTime(Timestamp.valueOf(args[0]));
            cbse.setDuration(Integer.parseInt(args[1]));
            String content_id = args[2];
            ArrayList arrCbse;
            if ((arrCbse = (ArrayList)mapRaw.get(content_id)) == null) {
               ArrayList newArrCbse = new ArrayList();
               newArrCbse.add(cbse);
               mapRaw.put(content_id, newArrCbse);
            } else {
               arrCbse.add(cbse);
               mapRaw.put(content_id, arrCbse);
            }

            if (useSecondTable) {
               PopSecondInfoRow entityToSave = new PopSecondInfoRow(maxPopId, content_id, device_id, Timestamp.valueOf(args[0]), Integer.parseInt(args[1]));
               popSecondInfoRows.add(entityToSave);
               ++maxPopId;
            }
         }

         if (useSecondTable && popSecondInfoRows != null && popSecondInfoRows.size() > 0) {
            var8 = popSecondInfoRows.iterator();

            while(var8.hasNext()) {
               PopSecondInfoRow popSecondInfoRow = (PopSecondInfoRow)var8.next();
               ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).addPopSecondInfoList(popSecondInfoRow);
            }
         }

         return mapRaw;
      } catch (Exception var14) {
         this.logger.error("", var14);
         throw var14;
      }
   }

   private PopInfoEntity getPopHourInfo(String contentId, String device_id, String log_date, SqlSession sqlSession) throws SQLException {
      return ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).getPopHourInfo(device_id, contentId, Timestamp.valueOf(log_date));
   }

   private PopInfoEntity getPopDayInfo(String contentId, String device_id, String logDate, SqlSession sqlSession) throws SQLException {
      return ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).getPopDayInfo(device_id, contentId, this.dayAsTimestamp(logDate));
   }

   private PopInfoEntity getPopMonthInfo(String contentId, String device_id, String logDate, SqlSession sqlSession) throws SQLException {
      return ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).getPopMonthInfo(device_id, contentId, this.monthAsTimestamp(logDate));
   }

   private PopInfoEntity getPopYearInfo(String contentId, String device_id, String logDate, SqlSession sqlSession) throws SQLException {
      return ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).getPopYearInfo(device_id, contentId, this.yearAsTimestamp(logDate));
   }

   private void addPopDayInfoList(String deviceId, Map dailyPlayInfo, String logDate, SqlSession sqlSession) throws SQLException {
      Set entrySet = dailyPlayInfo.entrySet();
      Iterator var6 = entrySet.iterator();

      while(var6.hasNext()) {
         Entry entry = (Entry)var6.next();
         ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).addPopDayInfoList(deviceId == null ? "null" : deviceId, (PopInfoEntity)entry.getValue(), this.dayAsTimestamp(logDate), this.extractDayOfWeekFrom(logDate), (String)entry.getKey());
      }

   }

   private PopInfoEntity[] getHourArray(ArrayList popRawList) {
      PopInfoEntity[] arrHour = new PopInfoEntity[24];
      boolean[] accessFlag = new boolean[24];

      int hour;
      for(hour = 0; hour < 24; ++hour) {
         accessFlag[hour] = false;
      }

      Iterator var7 = popRawList.iterator();

      while(var7.hasNext()) {
         PopRawEntity aPopRawList = (PopRawEntity)var7.next();
         hour = aPopRawList.getTime().getHours();
         int duration = aPopRawList.getDuration();
         if (!accessFlag[hour]) {
            accessFlag[hour] = true;
            arrHour[hour] = new PopInfoEntity();
         }

         arrHour[hour].addDuration(duration);
         arrHour[hour].addPlayCount(1);
      }

      return arrHour;
   }

   private PopInfoEntity addPopHourInfoList(String deviceId, String logDate, String contentId, PopInfoEntity[] arrSumByHour, boolean useHourTable, SqlSession sqlSession) throws SQLException {
      PopInfoEntity dailyPlayInfo = new PopInfoEntity();
      String logDateStr = "";
      ArrayList popInfoEntityList = new ArrayList();

      try {
         for(int i = 0; i < 24; ++i) {
            if (arrSumByHour[i] != null) {
               if (useHourTable) {
                  if (i < 10) {
                     logDateStr = logDate + " 0" + i + ":00:00";
                  } else {
                     logDateStr = logDate + " " + i + ":00:00";
                  }

                  PopInfoEntity originalHourPopInfo = this.getPopHourInfo(contentId, deviceId, logDateStr, sqlSession);
                  if (originalHourPopInfo != null) {
                     originalHourPopInfo.addDuration(arrSumByHour[i].getDuration());
                     originalHourPopInfo.addPlayCount(arrSumByHour[i].getPlay_count());
                     this.setPopHourInfo(contentId, deviceId, logDateStr, originalHourPopInfo.getDuration(), originalHourPopInfo.getPlay_count(), sqlSession);
                  } else {
                     Timestamp log_date;
                     try {
                        log_date = Timestamp.valueOf(logDateStr);
                     } catch (IllegalArgumentException var14) {
                        this.logger.error("[STATISTICS] IllegalArgumentException has been occured for logDateStr: " + logDateStr + " index: " + i);
                        continue;
                     }

                     arrSumByHour[i].setLogDateStr(log_date);
                     popInfoEntityList.add(arrSumByHour[i]);
                  }
               }

               dailyPlayInfo.addPlayCount(arrSumByHour[i].getPlay_count());
               dailyPlayInfo.addDuration(arrSumByHour[i].getDuration());
            }
         }

         if (useHourTable && !popInfoEntityList.isEmpty()) {
            ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).addPopHourInfoList(deviceId == null ? "null" : deviceId, contentId, popInfoEntityList);
         }

         return dailyPlayInfo;
      } catch (Exception var15) {
         this.logger.error("[MagicInfo_ProofOfPlay] fail to addPopHourInfoList e : " + var15.getMessage());
         throw var15;
      }
   }

   private void addPopMonthInfoList(String deviceId, Map mapNewDailyInfo, String logDate, SqlSession sqlSession) throws SQLException {
      Set entrySet = mapNewDailyInfo.entrySet();
      Iterator var6 = entrySet.iterator();

      while(var6.hasNext()) {
         Entry entry = (Entry)var6.next();
         ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).addPopMonthInfoList(deviceId == null ? "null" : deviceId, (PopInfoEntity)entry.getValue(), this.monthAsTimestamp(logDate), this.extractQuarterOfYearFrom(logDate), (String)entry.getKey());
      }

   }

   private void addPopYearInfoList(String deviceId, Map mapNewDailyInfo, String logDate, SqlSession sqlSession) throws SQLException {
      Set entrySet = mapNewDailyInfo.entrySet();
      Iterator var6 = entrySet.iterator();

      while(var6.hasNext()) {
         Entry entry = (Entry)var6.next();
         ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).addPopYearInfoList(deviceId == null ? "null" : deviceId, (PopInfoEntity)entry.getValue(), this.yearAsTimestamp(logDate), (String)entry.getKey());
      }

   }

   private void setPopHourInfo(String contentId, String device_id, String log_date, int duration, int playcount, SqlSession sqlSession) throws SQLException {
      ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).setPopHourInfo(playcount, duration, Timestamp.valueOf(log_date), contentId, device_id);
   }

   private void setPopDayInfo(String contentId, String device_id, int duration, int playcount, String logDate, SqlSession sqlSession) throws SQLException {
      ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).setPopDayInfo(playcount, duration, this.dayAsTimestamp(logDate), contentId, device_id);
   }

   private void setPopMonthInfo(String contentId, String device_id, int duration, int playcount, String logDate, SqlSession sqlSession) throws SQLException {
      ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).setPopMonthInfo(playcount, duration, this.monthAsTimestamp(logDate), contentId, device_id);
   }

   private void setPopYearInfo(String contentId, String device_id, int duration, int playcount, String logDate, SqlSession sqlSession) throws SQLException {
      ((ProofOfPlayDaoMapper)this.getMapper(sqlSession)).setPopYearInfo(playcount, duration, this.yearAsTimestamp(logDate), contentId, device_id);
   }

   private int extractDayOfWeekFrom(String logDate) {
      int result = 0;

      try {
         DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         Date date = sdf.parse(logDate);
         Calendar cal = Calendar.getInstance();
         cal.setTime(date);
         result = cal.get(7) - 1;
      } catch (ParseException var6) {
         this.logger.warn("Couldn't parse log date: " + logDate, var6);
      }

      return result;
   }

   private int extractQuarterOfYearFrom(String logDate) {
      int result = 0;

      try {
         DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         Date date = sdf.parse(logDate);
         Calendar cal = Calendar.getInstance();
         cal.setTime(date);
         result = cal.get(2) / 3 + 1;
      } catch (ParseException var6) {
         this.logger.warn("Couldn't parse log date: " + logDate, var6);
      }

      return result;
   }

   private Timestamp dayAsTimestamp(String logDate) {
      return Timestamp.valueOf(logDate + " 00:00:00");
   }

   private Timestamp monthAsTimestamp(String logDate) {
      return Timestamp.valueOf(logDate.substring(0, 7) + "-01 00:00:00");
   }

   private Timestamp yearAsTimestamp(String logDate) {
      return Timestamp.valueOf(logDate.substring(0, 4) + "-01-01 00:00:00");
   }
}
