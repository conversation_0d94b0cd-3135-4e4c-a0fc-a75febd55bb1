package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.stream.FactoryConfigurationError;
import javax.xml.stream.XMLInputFactory;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.TransformerFactoryConfigurationError;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.SchemaFactoryConfigurationError;
import org.apache.logging.log4j.Logger;

public class DocumentUtils {
   static Logger logger = LoggingManagerV2.getLogger(DocumentUtils.class);

   public DocumentUtils() {
      super();
   }

   public static DocumentBuilderFactory getDocumentBuilderFactoryInstance() throws ParserConfigurationException {
      DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
      dbf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
      dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
      dbf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
      dbf.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
      dbf.setXIncludeAware(false);
      dbf.setExpandEntityReferences(false);
      return dbf;
   }

   public static TransformerFactory getTransformerFactoryInstance() throws TransformerFactoryConfigurationError {
      TransformerFactory transformerFactory = TransformerFactory.newInstance();

      try {
         transformerFactory.setAttribute("http://javax.xml.XMLConstants/property/accessExternalDTD", "");
         transformerFactory.setAttribute("http://javax.xml.XMLConstants/property/accessExternalStylesheet", "");
      } catch (Exception var2) {
         logger.error(var2.getMessage());
      }

      return transformerFactory;
   }

   public static XMLInputFactory getXMLInputFactoryInstance() throws FactoryConfigurationError {
      XMLInputFactory xmlInputFactory = XMLInputFactory.newInstance();

      try {
         xmlInputFactory.setProperty("javax.xml.stream.supportDTD", Boolean.FALSE);
         xmlInputFactory.setProperty("http://javax.xml.XMLConstants/property/accessExternalDTD", "");
         xmlInputFactory.setProperty("javax.xml.stream.isSupportingExternalEntities", Boolean.FALSE);
      } catch (Exception var2) {
         logger.error(var2.getMessage());
      }

      return xmlInputFactory;
   }

   public static SchemaFactory getSchemaFactoryInstance() throws SchemaFactoryConfigurationError {
      SchemaFactory schemaFactory = SchemaFactory.newInstance("http://www.w3.org/2001/XMLSchema");

      try {
         schemaFactory.setProperty("http://javax.xml.XMLConstants/property/accessExternalSchema", "");
         schemaFactory.setProperty("http://javax.xml.XMLConstants/property/accessExternalDTD", "");
      } catch (Exception var2) {
         logger.error(var2.getMessage());
      }

      return schemaFactory;
   }
}
