package com.samsung.magicinfo.dms.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.dms.model.ContentScheduleResource;
import com.samsung.magicinfo.dms.model.ScheduleFilter;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;

public interface ContentScheduleService {
   ResponseBody listDashboardScheduleInfo() throws SQLException;

   ResponseBody deleteContentSchedule(String var1, String var2);

   ResponseBody permanentlyDeleteContentSchedule(String var1, String var2);

   ResponseBody listContentSchedules(int var1, int var2, ScheduleFilter var3) throws Exception;

   ResponseBody getContentSchedule(String var1) throws SQLException, ConfigException;

   ResponseBody createContentSchedule(ContentScheduleResource var1) throws ConfigException, SQLException;

   ResponseBody editContentSchedule(String var1, ContentScheduleResource var2) throws ConfigException, SQLException;

   ResponseBody deployContentSchedule(String var1, ContentScheduleResource var2) throws ConfigException, SQLException;

   ResponseBody copyContentSchedule(String var1, ContentScheduleResource var2) throws ConfigException, SQLException;

   ResponseBody getPublishStatus(String var1) throws SQLException, ConfigException;

   ResponseBody editContentSchedulePriority(String var1, String var2, ContentScheduleResource var3) throws ConfigException, SQLException;
}
