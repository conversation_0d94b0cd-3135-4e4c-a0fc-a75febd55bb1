package com.samsung.magicinfo.exception;

import com.samsung.common.exception.BasicException;

public class InterfacingException extends BasicException {
   private static final long serialVersionUID = 60607840186396692L;

   public InterfacingException() {
      super();
   }

   public InterfacingException(String message) {
      super(message);
   }

   public InterfacingException(Throwable cause) {
      super(cause);
   }

   public InterfacingException(String message, Throwable cause) {
      super(message, cause);
   }

   public InterfacingException(String code, String subcode, String reason) {
      super(code, subcode, reason);
   }
}
