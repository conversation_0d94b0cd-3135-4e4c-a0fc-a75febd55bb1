package com.samsung.magicinfo.dms.model;

import io.swagger.annotations.ApiModel;

@ApiModel
public class ContentScheduleDashboardResource {
   public long runningCount;
   public long totalCount;
   public long reservedCount;
   public long notSetDeviceCount;

   public ContentScheduleDashboardResource() {
      super();
   }

   public long getRunningCount() {
      return this.runningCount;
   }

   public void setRunningCount(long runningCount) {
      this.runningCount = runningCount;
   }

   public long getTotalCount() {
      return this.totalCount;
   }

   public void setTotalCount(long totalCount) {
      this.totalCount = totalCount;
   }

   public long getReservedCount() {
      return this.reservedCount;
   }

   public void setReservedCount(long reservedCount) {
      this.reservedCount = reservedCount;
   }

   public long getNotSetDeviceCount() {
      return this.notSetDeviceCount;
   }

   public void setNotSetDeviceCount(long notSetDeviceCount) {
      this.notSetDeviceCount = notSetDeviceCount;
   }
}
