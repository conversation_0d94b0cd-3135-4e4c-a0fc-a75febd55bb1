package com.samsung.magicinfo.cms.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.cms.model.PlaylistFilter;
import com.samsung.magicinfo.cms.model.PlaylistResource;
import com.samsung.magicinfo.cms.model.SyncPlaylistResource;
import java.sql.SQLException;

public interface PlaylistService {
   ResponseBody getDashboardPlaylistInfo() throws SQLException;

   ResponseBody listAllPlaylist(int var1, int var2) throws Exception;

   ResponseBody listPlaylist(PlaylistFilter var1) throws Exception;

   ResponseBody getActivePlaylistInfo(String var1) throws SQLException;

   ResponseBody createPlaylist(PlaylistResource var1) throws SQLException;

   ResponseBody editPlaylist(String var1, PlaylistResource var2) throws SQLException;

   ResponseBody deletePlaylist(String var1, Boolean var2) throws SQLException;

   ResponseBody copyPlaylist(String var1, PlaylistResource var2) throws SQLException;

   ResponseBody permanentlyDeletePlaylist(String var1, Boolean var2) throws SQLException;

   ResponseBody createSyncPlaylist(SyncPlaylistResource var1) throws SQLException;

   ResponseBody updateSyncPlaylist(String var1, SyncPlaylistResource var2) throws SQLException;
}
