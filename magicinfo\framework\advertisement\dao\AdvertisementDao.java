package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.logger.LoggingManagerV2;
import java.sql.Connection;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class AdvertisementDao {
   Logger logger = LoggingManagerV2.getLogger(AdvertisementDao.class);

   public AdvertisementDao() {
      super();
   }

   public AdvertisementDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public List getCompareAreaList(String user_id) {
      throw new UnsupportedOperationException("The AdvertisementDao#getCompareAreaList method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getCompareAreaDetailList(String user_id, int compare_id) {
      throw new UnsupportedOperationException("The AdvertisementDao#getCompareAreaDetailList method is not supported anymore. The table MI_AD_AREA_COMPARE_DETAIL_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getCompareAreaDetailInfo(String user_id, int compare_id) {
      throw new UnsupportedOperationException("The AdvertisementDao#getCompareAreaDetailInfo method is not supported anymore. The table MI_AD_AREA_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getCompareAreaTempDetailInfo(String user_id) {
      throw new UnsupportedOperationException("The AdvertisementDao#getCompareAreaTempDetailInfo method is not supported anymore. The table MI_AD_AREA_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public int saveCompareList(String user_id, String title, String area_name) {
      throw new UnsupportedOperationException("The AdvertisementDao#saveCompareList method is not supported anymore. The table MI_AD_AREA_COMPARE_LIST does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getScheduleListInfo(String user_id_x) {
      throw new UnsupportedOperationException("The AdvertisementDao#getScheduleListInfo method is not supported anymore. The table MI_AD_SCHEDULE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean setScheduleStatusApproved(String schedule_id) {
      throw new UnsupportedOperationException("The AdvertisementDao#setScheduleStatusApproved method is not supported anymore. The table MI_AD_SCHEDULE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean setScheduleStatusRejected(String schedule_id, String rejected_reason) {
      throw new UnsupportedOperationException("The AdvertisementDao#setScheduleStatusApproved method is not supported anymore. The table MI_AD_SCHEDULE_INFO does not exists.");
   }
}
