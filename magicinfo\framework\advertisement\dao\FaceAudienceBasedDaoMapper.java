package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.magicinfo.framework.advertisement.entity.AudienceInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.AudienceKey;
import com.samsung.magicinfo.framework.advertisement.entity.FaceTrafficEntity;
import com.samsung.magicinfo.framework.advertisement.entity.TrafficKey;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FaceAudienceBasedDaoMapper {
   void addAudiencePopHourInfoList(@Param("deviceId") String var1, @Param("audienceKey") AudienceKey var2, @Param("entities") List var3) throws SQLException;

   void addAudiencePopDayInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("day") Timestamp var3, @Param("dow") int var4, @Param("woy") int var5) throws SQLException;

   void addAudiencePopMonthInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("month") Timestamp var3, @Param("quarter") int var4) throws SQLException;

   void addAudiencePopYearInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("startTime") Timestamp var3) throws SQLException;

   AudienceInfoEntity getAudiencePopDayInfo(@Param("device_id") String var1, @Param("audienceKey") AudienceKey var2, @Param("day") Timestamp var3) throws SQLException;

   void setAudiencePopDayInfo(@Param("entitie") AudienceInfoEntity var1, @Param("day") Timestamp var2, @Param("audienceKey") AudienceKey var3, @Param("device_id") String var4) throws SQLException;

   AudienceInfoEntity getAudiencePopMonthInfo(@Param("device_id") String var1, @Param("audienceKey") AudienceKey var2, @Param("month") Timestamp var3) throws SQLException;

   void setAudiencePopMonthInfo(@Param("entitie") AudienceInfoEntity var1, @Param("month") Timestamp var2, @Param("audienceKey") AudienceKey var3, @Param("device_id") String var4) throws SQLException;

   AudienceInfoEntity getAudiencePopYearInfo(@Param("device_id") String var1, @Param("audienceKey") AudienceKey var2, @Param("year") Timestamp var3) throws SQLException;

   void setAudiencePopYearInfo(@Param("entitie") AudienceInfoEntity var1, @Param("year") Timestamp var2, @Param("audienceKey") AudienceKey var3, @Param("device_id") String var4) throws SQLException;

   List getYesterdayListBy(@Param("deviceIdList") String[] var1, @Param("period") String var2, @Param("conditionMap") HashMap var3, @Param("unit") String var4, @Param("type") String var5) throws SQLException;

   List getWeekListBy(@Param("deviceIdList") String[] var1, @Param("startDate") Timestamp var2, @Param("endDate") Timestamp var3, @Param("unit") String var4, @Param("type") String var5) throws SQLException;

   List getMonthListByMonth(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getMonthListByDay(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getMonthListByHour(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getMonthListByDow(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2) throws SQLException;

   List getTrafficMonthListByDay(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getTrafficMonthListByWeek(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getTrafficMonthListByHour(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getQuarterListByQuarter(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2) throws SQLException;

   List getYearListByYear(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getYearListByQuarter(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getCustomList(@Param("startDate") Timestamp var1, @Param("endDate") Timestamp var2, @Param("deviceIdList") String[] var3, @Param("unit") String var4, @Param("type") String var5) throws SQLException;

   List getQuarterListByMonth(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2) throws SQLException;

   void addTrafficPopHourInfoList(@Param("deviceId") String var1, @Param("trafficKey") TrafficKey var2, @Param("entities") List var3) throws SQLException;

   void addTrafficPopDayInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("day") Timestamp var3, @Param("dow") int var4, @Param("woy") int var5) throws SQLException;

   FaceTrafficEntity getTrafficPopMonthInfo(@Param("deviceId") String var1, @Param("trafficKey") TrafficKey var2, @Param("month") Timestamp var3) throws SQLException;

   void setTrafficPopMonthInfo(@Param("totalCount") int var1, @Param("month") Timestamp var2, @Param("deviceId") String var3, @Param("contentId") String var4) throws SQLException;

   void addTrafficPopMonthInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("month") Timestamp var3, @Param("quarter") int var4) throws SQLException;

   FaceTrafficEntity getTrafficPopYearInfo(@Param("deviceId") String var1, @Param("trafficKey") TrafficKey var2, @Param("year") Timestamp var3) throws SQLException;

   void setTrafficPopYearInfo(@Param("totalCount") int var1, @Param("year") Timestamp var2, @Param("deviceId") String var3, @Param("contentId") String var4) throws SQLException;

   void addTrafficPopYearInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("year") Timestamp var3) throws SQLException;

   List getTrafficYesterdayListBy(@Param("deviceIdList") String[] var1, @Param("unit") String var2, @Param("type") String var3) throws SQLException;

   List getTrafficWeekListBy(@Param("deviceIdList") String[] var1, @Param("startDate") Timestamp var2, @Param("endDate") Timestamp var3, @Param("unit") String var4, @Param("type") String var5) throws SQLException;

   List getTrafficMonthListByDow(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2) throws SQLException;

   List getTrafficMonthListByMonth(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getTrafficQuarterListByQuarter(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2) throws SQLException;

   List getTrafficYearListByQuarter(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getTrafficYearListByYear(@Param("deviceIdList") String[] var1, @Param("isThis") boolean var2, @Param("type") String var3) throws SQLException;

   List getTrafficCustomList(@Param("startDate") Timestamp var1, @Param("endDate") Timestamp var2, @Param("deviceIdList") String[] var3, @Param("unit") String var4, @Param("type") String var5) throws SQLException;
}
