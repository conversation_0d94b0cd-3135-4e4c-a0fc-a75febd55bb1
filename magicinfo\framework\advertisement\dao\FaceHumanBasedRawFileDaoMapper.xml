<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.samsung.magicinfo.framework.advertisement.dao.FaceHumanBasedRawFileDaoMapper">

    <insert id="addWeeklyReport">
        INSERT INTO MI_STATISTICS_FACE_HB_DETAIL_REPORT (ROOT_GROUP_ID, THE_YEAR, THE_MONTH, WEEK_OF_YEAR
            , WEEK_OF_MONTH, FILE_NAME)
            VALUES (#{root_group_id}, #{year}, #{month}, #{week_of_year}, #{week_of_month}, #{weeklyCsvFileName})
    </insert>

    <insert id="addMonthlyReport">
        INSERT INTO MI_STATISTICS_FACE_HB_DETAIL_REPORT (ROOT_GROUP_ID, THE_YEAR, THE_MONTH, WEEK_OF_YEAR
            , WEEK_OF_MONTH, FILE_NAME)
            VALUES (#{root_group_id}, #{year}, #{month}, #{week_of_year}, '0', #{monthlyCsvFileName})
    </insert>

    <select id="isExistWeeklyReport" resultType="java.lang.Boolean">
        SELECT
            COUNT(FILE_NAME)
        FROM MI_STATISTICS_FACE_HB_DETAIL_REPORT
        WHERE FILE_NAME = #{weeklyCsvFileName}
    </select>

    <select id="isExistMonthlyReport" resultType="java.lang.Boolean">
        SELECT
            COUNT(FILE_NAME)
        FROM MI_STATISTICS_FACE_HB_DETAIL_REPORT
        WHERE FILE_NAME = #{monthlyCsvFileName}
    </select>

    <select id="getDetailReportByRootGroupId"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.PopCsvFileEntity">
        SELECT
            *
        FROM MI_STATISTICS_FACE_HB_DETAIL_REPORT
        WHERE ROOT_GROUP_ID = #{root_group_id}
    </select>

    <select id="getYearListByRootGroupId" resultType="java.util.Map">
        SELECT
            DISTINCT THE_YEAR
        FROM MI_STATISTICS_FACE_HB_DETAIL_REPORT
        WHERE ROOT_GROUP_ID = #{root_group_id}
    </select>

	<select id="getYearListByAmsRootGroupId" resultType="java.util.Map">
        SELECT
            DISTINCT THE_YEAR
        FROM MI_STATISTICS_FACE_TB_DETAIL_REPORT
        WHERE ROOT_GROUP_ID = #{root_group_id}
    </select>
    
    <select id="getMonthReportListByRootGroupId"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.PopCsvFileEntity">
        SELECT
            *
        FROM MI_STATISTICS_FACE_HB_DETAIL_REPORT
        WHERE THE_YEAR = #{year} AND ROOT_GROUP_ID = #{root_group_id}
              AND WEEK_OF_MONTH = '0'
        <include refid="orderByMonth"/>
    </select>
    
    <select id="getMonthReportListByAmsRootGroupId"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.PopCsvFileEntity">
        SELECT
            *
        FROM MI_STATISTICS_FACE_TB_DETAIL_REPORT
        WHERE THE_YEAR = #{year} AND ROOT_GROUP_ID = #{root_group_id}
              AND WEEK_OF_MONTH = '0'
        <include refid="orderByMonth"/>
    </select>
    
    <sql id="orderByMonth">
        ORDER BY CAST(NULLIF(THE_MONTH, '') AS INT)
    </sql>
    
    <sql id="orderByMonth" databaseId="mysql">
        ORDER BY CONVERT(NULLIF(THE_MONTH, ''), UNSIGNED)
    </sql>

    <select id="getWeekReportListByRootGroupId"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.PopCsvFileEntity">
        SELECT
            *
        FROM MI_STATISTICS_FACE_HB_DETAIL_REPORT
        WHERE THE_YEAR = #{year} AND ROOT_GROUP_ID = #{root_group_id}
              AND WEEK_OF_MONTH != '0'
        ORDER BY WEEK_OF_YEAR
    </select>

	<select id="getWeekReportListByAmsRootGroupId"
            resultType="com.samsung.magicinfo.framework.advertisement.entity.PopCsvFileEntity">
        SELECT
            *
        FROM MI_STATISTICS_FACE_TB_DETAIL_REPORT
        WHERE THE_YEAR = #{year} AND ROOT_GROUP_ID = #{root_group_id}
              AND WEEK_OF_MONTH != '0'
        ORDER BY WEEK_OF_YEAR
    </select>
	
</mapper>