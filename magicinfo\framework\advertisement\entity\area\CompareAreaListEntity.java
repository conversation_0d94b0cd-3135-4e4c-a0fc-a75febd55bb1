package com.samsung.magicinfo.framework.advertisement.entity.area;

import java.sql.Timestamp;

public class CompareAreaListEntity {
   private String user_id = null;
   private int compare_id = 0;
   private String compare_title = null;
   private String area_id = null;
   private String area_name = null;
   private Timestamp compare_time = null;

   public CompareAreaListEntity() {
      super();
   }

   public String getUser_id() {
      return this.user_id;
   }

   public void setUser_id(String user_id) {
      this.user_id = user_id;
   }

   public int getCompare_id() {
      return this.compare_id;
   }

   public void setCompare_id(int compare_id) {
      this.compare_id = compare_id;
   }

   public String getCompare_title() {
      return this.compare_title;
   }

   public void setCompare_title(String compare_title) {
      this.compare_title = compare_title;
   }

   public String getArea_id() {
      return this.area_id;
   }

   public void setArea_id(String area_id) {
      this.area_id = area_id;
   }

   public String getArea_name() {
      return this.area_name;
   }

   public void setArea_name(String area_name) {
      this.area_name = area_name;
   }

   public Timestamp getCompare_time() {
      return this.compare_time;
   }

   public void setCompare_time(Timestamp compare_time) {
      this.compare_time = compare_time;
   }
}
