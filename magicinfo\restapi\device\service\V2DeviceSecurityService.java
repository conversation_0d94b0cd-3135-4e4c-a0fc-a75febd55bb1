package com.samsung.magicinfo.restapi.device.service;

import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.rms.model.DeviceSecurityConfResource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface V2DeviceSecurityService {
   V2CommonBulkResultResource getSecurityInfo(V2CommonIds var1) throws Exception;

   V2PageResource filterDeviceSecurityList(V2DeviceFilter var1, HttpServletRequest var2, HttpServletResponse var3) throws Exception;

   V2DeviceReqServiceResource updateSecurityInfo(DeviceSecurityConfResource var1) throws Exception;

   V2DeviceReqServiceResource getCurrentStatusSecurity(V2CommonIds var1) throws Exception;

   V2CommonBulkResultResource getCurrentStatusSecurityResult(V2DeviceReqServiceConf var1) throws Exception;
}
