package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.logger.LoggingManagerV2;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.Logger;

public class ContentInfoDao {
   Logger logger = LoggingManagerV2.getLogger(ContentInfoDao.class);

   public ContentInfoDao() {
      super();
   }

   public ContentInfoDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public List getContentList(Map map) throws SQLException {
      throw new UnsupportedOperationException("ContentInfoDao#getContentList is not supported anymore: There is a error in query");
   }
}
