package com.samsung.magicinfo.dms.service;

import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.dms.model.ContentScheduleDashboardResource;
import com.samsung.magicinfo.dms.model.ContentScheduleItemResource;
import com.samsung.magicinfo.dms.model.ContentScheduleResource;
import com.samsung.magicinfo.dms.model.DeletedContentScheduleResource;
import com.samsung.magicinfo.dms.model.PublishStatusResource;
import com.samsung.magicinfo.dms.model.ScheduleFilter;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.ListEntity;
import com.samsung.magicinfo.framework.common.MessageSourceManager;
import com.samsung.magicinfo.framework.common.MessageSourceManagerImpl;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.dao.PlaylistDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.dashboard.entity.UserDashboardEntity;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManager;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManagerImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfo;
import com.samsung.magicinfo.framework.monitoring.manager.DownloadStatusInfoImpl;
import com.samsung.magicinfo.framework.playlist.manager.common.PlaylistInterface;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.AdScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.AdSlotEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ChannelEntity;
import com.samsung.magicinfo.framework.scheduler.entity.CommonProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ContentsScheduleEntity;
import com.samsung.magicinfo.framework.scheduler.entity.DetailDownloadContentEntity;
import com.samsung.magicinfo.framework.scheduler.entity.FrameEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramEntity;
import com.samsung.magicinfo.framework.scheduler.entity.ProgramGroup;
import com.samsung.magicinfo.framework.scheduler.entity.ScheduleAdminEntity;
import com.samsung.magicinfo.framework.scheduler.entity.SelectConditionScheduleAdmin;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleManager;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleManagerImpl;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleUtility;
import com.samsung.magicinfo.framework.scheduler.manager.common.ProgramGroupImpl;
import com.samsung.magicinfo.framework.scheduler.manager.common.ProgramGroupInterface;
import com.samsung.magicinfo.framework.scheduler.manager.common.ScheduleInterface;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import com.samsung.magicinfo.protocol.util.dao.ServerSetupDao;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("ContentScheduleService")
@Transactional
public class ContentScheduleServiceImpl implements ContentScheduleService {
   protected final Log logger = LogFactory.getLog(this.getClass());
   ScheduleInterface infoDao = DAOFactory.getScheduleInfoImpl("PREMIUM");
   ScheduleManager scheduleMgr = ScheduleManagerImpl.getInstance();
   Locale locale = null;
   DeviceGroupInfo deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
   final int PUBLISH_DETAIL_MODE = 1;
   final int PUBLISH_TOTAL_MODE = 2;
   ResponseBody responseBody = new ResponseBody();

   public ContentScheduleServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody listDashboardScheduleInfo() throws SQLException {
      ContentScheduleDashboardResource data = new ContentScheduleDashboardResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
      new HashMap();
      long runningCount = 0L;
      long totalCount = 0L;
      long reservedCount = 0L;
      long notSetDeviceCount = 0L;
      boolean useDashboard = false;
      DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
      List list = dashboardInfo.getDashboard(userId);
      Iterator var16 = list.iterator();

      while(var16.hasNext()) {
         UserDashboardEntity entity = (UserDashboardEntity)var16.next();
         if (entity.getDashboard_name().equals("scheduleInfoDashboard")) {
            useDashboard = true;
         }
      }

      runningCount = schInfo.getMapedScheduleCount();
      totalCount = schInfo.getAllScheduleCount();
      reservedCount = schInfo.getContentScheduleCntToday();
      notSetDeviceCount = schInfo.getNotMapedScheduleCount();
      data.setRunningCount(runningCount);
      data.setTotalCount(totalCount);
      data.setReservedCount(reservedCount);
      data.setNotSetDeviceCount(notSetDeviceCount);
      this.responseBody.setStatus("Success");
      this.responseBody.setItems(data);
      return this.responseBody;
   }

   @PreAuthorize("hasAnyAuthority( 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody deleteContentSchedule(String programId, String userId) {
      ResponseBody responseBody = new ResponseBody();
      DeletedContentScheduleResource result = new DeletedContentScheduleResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String[] programIdList = programId.split(",");
      ArrayList notDeletedList = new ArrayList();

      try {
         if (programIdList != null) {
            for(int i = 0; i < programIdList.length; ++i) {
               if (!this.infoDao.deleteSchedule(programIdList[i], userId)) {
                  notDeletedList.add(programIdList[i]);
               }
            }

            result.setStatus("success");
         }
      } catch (Exception var9) {
         this.logger.error(var9.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var9.getMessage());
         return responseBody;
      }

      result.setNotDeletedList(notDeletedList);
      responseBody.setStatus("Success");
      responseBody.setItems(result);
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority( 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody permanentlyDeleteContentSchedule(String programId, String userId) {
      ResponseBody responseBody = new ResponseBody();
      LinkedHashMap result = new LinkedHashMap();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String[] programIdList = programId.split(",");
      ArrayList notDeletedList = new ArrayList();

      try {
         if (programIdList != null) {
            for(int i = 0; i < programIdList.length; ++i) {
               if (!this.infoDao.deleteSchedulePerm(programIdList[i], userContainer.getUser().getUser_id(), SecurityUtils.getLoginUser().getUserIP())) {
                  notDeletedList.add(programIdList[i]);
               }
            }

            result.put("status", "success");
         }

         result.put("notDeletedList", notDeletedList);
      } catch (Exception var9) {
         this.logger.error(var9.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var9.getMessage());
         return responseBody;
      }

      result.put("notDeletedList", notDeletedList);
      responseBody.setStatus("Success");
      responseBody.setItems(result);
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody listContentSchedules(int startIndex, int pageSize, ScheduleFilter filter) throws Exception {
      ListEntity list = new ListEntity();
      Hashtable conditionMap = new Hashtable();
      SelectConditionScheduleAdmin conditionList = new SelectConditionScheduleAdmin();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      String contentExits = rms.getMessage("TEXT_EXIST_P", (Object[])null, this.locale);
      String contentDExists = rms.getMessage("TEXT_NOT_EXIST_P", (Object[])null, this.locale);
      String creatorMapped = rms.getMessage("text.default_map", (Object[])null, this.locale);
      String creatorNotMapped = rms.getMessage("TEXT_DEFAULT_NOT_MAP_P", (Object[])null, this.locale);
      String strDefault = rms.getMessage("TEXT_DEFAULT_SCHEDULE_P", (Object[])null, this.locale);
      String strRootGroup = null;
      strRootGroup = userContainer.getUser().getOrganization();
      int orderNumber = filter.getOrderNumber();
      String groupId = filter.getGroupId();
      String order_dir = filter.getSortOrder();
      String strSearchId = filter.getSearchId();
      String strGroup = filter.getGroupType();
      String sort_name = filter.getSortColumn();
      String strNameLike = filter.getSearchText();
      String strFrameCount = filter.getFrameCount();
      String endModifiedDate = filter.getEndModifiedDate();
      String startModifiedDate = filter.getStartModifiedDate();
      String searchContentIds = filter.getSearchContentIds();
      String searchDevGroupIds = filter.getSearchDevGroupIds();
      String scheduleType = filter.getScheduleType();
      String device_type = filter.getDeviceType();
      String productType = filter.getProductType();
      String selId = filter.getSelectId();
      String isSelect = filter.getIsSelect();
      String section = "";
      if (orderNumber == 0) {
         sort_name = "modify_date";
      } else if (orderNumber == 1) {
         sort_name = "program_name";
      } else if (orderNumber == 3) {
         sort_name = "device_type";
      } else if (orderNumber == 6) {
         sort_name = "publish_status";
      } else if (orderNumber == 7) {
         sort_name = "modify_date";
      }

      conditionList.setSearch_id(strSearchId);
      conditionList.setSort_name(sort_name);
      conditionList.setOrder_dir(order_dir);
      conditionList.setNameLike(strNameLike);
      conditionList.setGroupType(strGroup);
      conditionList.setFrameCount(strFrameCount);
      conditionList.setUserRootGroup(strRootGroup);
      conditionList.setDevice_type(device_type);
      conditionList.setSelId(selId);
      conditionList.setIsSelect(isSelect);
      conditionList.setSelect_content_ids(searchContentIds);
      conditionList.setSelect_devgroup_ids(searchDevGroupIds);
      conditionList.setStart_modified_date(startModifiedDate);
      conditionList.setEnd_modified_date(endModifiedDate);
      if (scheduleType != null && !scheduleType.equals("")) {
         conditionList.setProgram_type(scheduleType);
      }

      if (groupId != null && !groupId.equals("")) {
         String tempGroupIds = this.processGroupId(groupId);
         conditionList.setGroupType(tempGroupIds);
      }

      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         if (!strGroup.equals("TRASH") && userContainer.checkAuthority("Content Schedule Write")) {
            section = "getScheduleAdminInfo";
         } else {
            section = "getScheduleByUser";
            conditionMap.put("UserGroupId", userContainer.getUser().getGroup_id());
         }
      }

      List frameList = this.infoDao.getDistinctFrames(conditionList);
      String strFrameVal = null;
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         strFrameVal = this.getFrameVal(frameList);
      }

      PageManager pageMgr = null;
      ListManager listMgr = new ListManager(this.infoDao, "commonlist");
      List infoList = null;
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         conditionMap.put("condition", conditionList);
         infoList = listMgr.dbexecuteWithTypeFilterNotUsedRequest(startIndex, pageSize, conditionMap, section);
      }

      pageMgr = listMgr.getPageManager();
      list.setRecordsReturned(infoList.size());
      list.setTotalRecords(pageMgr.getTotalRowCount());
      list.setRecordsFiltered(pageMgr.getTotalRowCount());
      list.setTypeFilterList(listMgr.getTypeFilterListToString());
      list.setStartIndex(startIndex);
      if (sort_name != null && !sort_name.equals("")) {
         list.setSort(sort_name);
         list.setDir(order_dir);
      }

      list.setResults(pageMgr.getInfo().getPageSize());
      int size = infoList.size();
      List records = new ArrayList();
      if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         this.deviceGroupInfo = DeviceGroupInfoImpl.getInstance();
      }

      for(int i = 0; i < size; ++i) {
         CommonProgramEntity info = (CommonProgramEntity)infoList.get(i);
         HashMap data = new HashMap();
         data.put("checkbox", "");
         String contentMapping = "";
         String defaultMapping = "";
         String deviceType = "iPLAYER";
         Float deviceTypeVersion = 1.0F;
         if (info.getContent_id().equalsIgnoreCase("Y")) {
            contentMapping = contentExits;
         } else if (info.getContent_id().equalsIgnoreCase("N")) {
            contentMapping = contentDExists;
         }

         if (info.getCreator_group_attached() != null && info.getCreator_group_attached().equalsIgnoreCase("N")) {
            defaultMapping = creatorNotMapped;
         } else if (info.getCreator_group_attached() != null && info.getCreator_group_attached().equalsIgnoreCase("Y")) {
            defaultMapping = creatorMapped;
         }

         if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
            if (info.getDevice_type() != null && !info.getDevice_type().equalsIgnoreCase("")) {
               deviceType = info.getDevice_type();
               deviceTypeVersion = info.getDevice_type_version();
            }

            data.put("deviceType", deviceType);
            data.put("deviceTypeVersion", deviceTypeVersion);
         }

         data.put("programName", info.getProgram_name());
         data.put("programId", info.getProgram_id());
         data.put("programType", info.getProgram_type());
         if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
            DownloadStatusInfo downloadInfo = DownloadStatusInfoImpl.getInstacne();
            String publishStatus = "";
            if (info.getDevice_group_id() != null && !info.getDevice_group_id().equals("")) {
               Object[] rtn = downloadInfo.getDownloadStatusListByProgramId(info.getProgram_id());
               if (rtn != null && rtn[0] != null) {
                  Map resultMap;
                  switch((Integer)rtn[0]) {
                  case 0:
                     publishStatus = "published";
                     if (rtn[1] != null) {
                        resultMap = (Map)rtn[1];
                        if (resultMap.get("deviceCount") != null) {
                           data.put("publishTotalCount", (Integer)resultMap.get("deviceCount"));
                        }
                     }
                     break;
                  case 1:
                     publishStatus = "publishing";
                     if (rtn[1] != null) {
                        resultMap = (Map)rtn[1];
                        if (resultMap.get("deviceCount") != null) {
                           data.put("publishTotalCount", (Integer)resultMap.get("deviceCount"));
                        }

                        if (resultMap.get("completeCount") != null) {
                           data.put("publishCurrentCount", resultMap.get("completeCount"));
                        }
                     }
                     break;
                  case 2:
                     publishStatus = "waiting";
                  }
               }
            }

            data.put("publishStatus", publishStatus);
         }

         data.put("modifyDate", TimeUtil.getGMTTime(Timestamp.valueOf(info.getModify_date().toString())));
         data.put("isSync", info.getUse_sync_play());
         data.put("isVwl", info.getUse_multi_vwl());
         data.put("isAdSchedule", info.getUse_ad_schedule());
         if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
            boolean existUserIdByPermissions = DeviceUtils.isDeviceGroupAuth(userContainer.getUser().getRole_name(), userContainer.getUser().getUser_id());
            List deviceGroupList = null;
            if (existUserIdByPermissions && this.deviceGroupInfo != null) {
               deviceGroupList = this.deviceGroupInfo.getScheduleMappingDeviceGroupAuth(info.getProgram_id(), userContainer.getUser().getUser_id(), true);
            }

            if (existUserIdByPermissions && info.getDevice_group_name() != null && !info.getDevice_group_name().equals("")) {
               String groupNameByPermissions = "";
               DeviceGroup deviceGroup;
               if (deviceGroupList != null) {
                  for(Iterator var57 = deviceGroupList.iterator(); var57.hasNext(); groupNameByPermissions = groupNameByPermissions + deviceGroup.getGroup_name()) {
                     deviceGroup = (DeviceGroup)var57.next();
                     if (groupNameByPermissions.length() > 0) {
                        groupNameByPermissions = groupNameByPermissions + ",";
                     }
                  }
               }

               data.put("deviceGroupName", groupNameByPermissions);
            } else {
               data.put("deviceGroupName", StrUtils.nvl(info.getDevice_group_name()));
            }

            if (info.getDevice_group_id() != null && !info.getDevice_group_id().equals("")) {
               data.put("device_group_id", info.getDevice_group_id());
            }

            boolean onVWLMenu = this.deviceGroupInfo.isVWLLayoutGroup(info.getDevice_group_id());
            if (onVWLMenu) {
               data.put("onVWLMenu", true);
            } else {
               data.put("onVWLMenu", false);
            }

            int dCnt = 0;
            if (existUserIdByPermissions) {
               DeviceGroup deviceGroup;
               if (deviceGroupList != null) {
                  for(Iterator var60 = deviceGroupList.iterator(); var60.hasNext(); dCnt += this.deviceGroupInfo.getCntDeviceInDeviceGroup(deviceGroup.getGroup_id().intValue())) {
                     deviceGroup = (DeviceGroup)var60.next();
                  }
               }
            } else if (this.deviceGroupInfo != null && info.getDevice_group_id() != null && !info.getDevice_group_id().equals("")) {
               String[] arrGroupId = info.getDevice_group_id().split(",");

               for(int j = 0; j < arrGroupId.length; ++j) {
                  dCnt += this.deviceGroupInfo.getCntDeviceInDeviceGroup(Integer.parseInt(arrGroupId[j]));
               }
            }

            data.put("deviceCount", dCnt);
         }

         if ("default".equalsIgnoreCase(strGroup)) {
            data.put("groupName", StrUtils.nvl(strDefault));
            data.put("groupId", StrUtils.nvl(strDefault));
         } else {
            data.put("groupName", StrUtils.nvl(info.getGroup_name()));
            data.put("groupId", info.getGroup_id());
         }

         if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
            data.put("channelCount", info.getChannelCount());
            data.put("contentId", contentMapping);
            data.put("creatorMapped", defaultMapping);
            data.put("framevals", strFrameVal);
         }

         data.put("contentExist", contentMapping);
         data.put("deployTime", info.getDeploy_time());
         data.put("useSyncPlay", info.getUse_sync_play());
         if (info.getLastdeploy_date() != null) {
            data.put("lastdeployDate", info.getLastdeploy_date().toString());
         }

         data.put("creatorId", info.getUser_id());
         records.add(data);
      }

      list.setData(records);
      this.responseBody.setTotalCount(pageMgr.getTotalRowCount());
      this.responseBody.setStatus("Success");
      this.responseBody.setItems(list);
      return this.responseBody;
   }

   private String processGroupId(String groupId) {
      String result = "";
      ProgramGroupInterface programGroupDao = ProgramGroupImpl.getInstance();
      String[] groupIdList = groupId.split(",");

      for(int i = 0; i < groupIdList.length; ++i) {
         if (result.length() > 0) {
            result = result + ",";
         }

         if (groupIdList[i].contains("c")) {
            result = result + groupIdList[i].replace("c", "");

            try {
               List prgoramGroupList = programGroupDao.getChildGroupList(Integer.parseInt(groupIdList[i].replace("c", "")), true);
               ProgramGroup item;
               if (prgoramGroupList != null && prgoramGroupList.size() > 0) {
                  for(Iterator var7 = prgoramGroupList.iterator(); var7.hasNext(); result = result + item.getGroup_id().toString()) {
                     item = (ProgramGroup)var7.next();
                     if (result.length() > 0) {
                        result = result + ",";
                     }
                  }
               }
            } catch (SQLException var9) {
               this.logger.error("", var9);
            }
         } else {
            result = result + groupIdList[i];
         }
      }

      return result.toString();
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   private String getFrameVal(List frameList) {
      StringBuffer strFrameBuffer = new StringBuffer();
      int frameCount = false;
      int frameCount = frameList.size();

      for(int i = 0; i < frameCount; ++i) {
         if (i == 0) {
            strFrameBuffer.append(String.valueOf(((ScheduleAdminEntity)frameList.get(i)).getFrameCount()));
         } else {
            strFrameBuffer.append(":").append(String.valueOf(((ScheduleAdminEntity)frameList.get(i)).getFrameCount()));
         }
      }

      return strFrameBuffer.toString();
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public void getDetailPublishStatus(int mode, String strProgramId, String strDeviceGroupId, StringBuffer buffer, Map publishStatusMap) throws NumberFormatException, SQLException {
      String published = "published";
      String publishing = "publishing";
      String waiting = "waiting";
      String failed = "failed";
      int total = 0;
      int now = 0;
      ScheduleInfoDAO dao = new ScheduleInfoDAO();
      String[] groupIdList = strDeviceGroupId.split(",");
      String[] var14 = groupIdList;
      int var15 = groupIdList.length;

      for(int var16 = 0; var16 < var15; ++var16) {
         String groupId = var14[var16];
         if (groupId != null && groupId.length() > 0) {
            total += this.deviceGroupInfo.getCntDeviceInDeviceGroup(Integer.parseInt(groupId));
            List list = dao.getScheduleDetailPublishStatusList(strProgramId, Long.parseLong(groupId));
            int listSize = list.size();
            if (list != null && listSize > 0) {
               String oldDeviceId = ((DetailDownloadContentEntity)list.get(0)).getDevice_id();
               if (oldDeviceId != null) {
                  float deviceTotal = 0.0F;
                  float deviceNow = 0.0F;
                  int jj = false;

                  int jj;
                  for(jj = 0; jj < listSize; ++jj) {
                     DetailDownloadContentEntity detailDownloadContentEntity = (DetailDownloadContentEntity)list.get(jj);
                     if (detailDownloadContentEntity != null) {
                        if (detailDownloadContentEntity.getDevice_id() != null && !oldDeviceId.equals(detailDownloadContentEntity.getDevice_id())) {
                           if (mode == 1) {
                              buffer.append("<device>");
                              buffer.append("<device_group_id>" + ((DetailDownloadContentEntity)list.get(jj - 1)).getGroup_id() + "</device_group_id>");
                              buffer.append("<device_group_name>" + ((DetailDownloadContentEntity)list.get(jj - 1)).getGroup_name() + "</device_group_name>");
                              buffer.append("<device_name>" + ((DetailDownloadContentEntity)list.get(jj - 1)).getDevice_name() + "</device_name>");
                              buffer.append("<progress>" + (int)(deviceNow / deviceTotal * 100.0F) + "</progress>");
                              buffer.append("<status>");
                           }

                           if (deviceTotal > 0.0F) {
                              if (mode == 1) {
                                 if (deviceTotal == deviceNow) {
                                    buffer.append(published);
                                    ++now;
                                 } else if (deviceNow == 0.0F) {
                                    buffer.append(waiting);
                                 } else {
                                    buffer.append(publishing);
                                 }
                              } else if (mode == 2 && deviceTotal == deviceNow) {
                                 ++now;
                              }
                           }

                           if (mode == 1) {
                              buffer.append("</status>");
                              buffer.append("</device>");
                           }

                           oldDeviceId = detailDownloadContentEntity.getDevice_id();
                           deviceTotal = 0.0F;
                           deviceNow = 0.0F;
                        }

                        if (detailDownloadContentEntity.getProgress() != null && detailDownloadContentEntity.getProgress().equals("100 %")) {
                           ++deviceNow;
                        }

                        ++deviceTotal;
                     }
                  }

                  if (((DetailDownloadContentEntity)list.get(jj - 1)).getDevice_name() != null) {
                     if (mode == 1) {
                        buffer.append("<device>");
                        buffer.append("<device_group_id>" + ((DetailDownloadContentEntity)list.get(jj - 1)).getGroup_id() + "</device_group_id>");
                        buffer.append("<device_group_name>" + ((DetailDownloadContentEntity)list.get(jj - 1)).getGroup_name() + "</device_group_name>");
                        buffer.append("<device_name>" + ((DetailDownloadContentEntity)list.get(jj - 1)).getDevice_name() + "</device_name>");
                        buffer.append("<progress>" + (int)(deviceNow / deviceTotal * 100.0F) + "</progress>");
                        buffer.append("<status>");
                        if (deviceTotal > 0.0F) {
                           if (deviceTotal == deviceNow) {
                              buffer.append(published);
                              ++now;
                           } else if (deviceNow == 0.0F) {
                              buffer.append(waiting);
                           } else {
                              buffer.append(publishing);
                           }
                        }

                        buffer.append("</status>");
                        buffer.append("</device>");
                     } else if (mode == 2 && deviceTotal > 0.0F && deviceTotal == deviceNow) {
                        ++now;
                     }
                  }
               }
            } else {
               now = total;
               if (mode == 1 && total > 0) {
                  DeviceGroup deviceGroup = this.deviceGroupInfo.getGroup(Integer.parseInt(groupId));
                  List scheduleList = dao.getSchedulePublishStatusList(strProgramId, Long.parseLong(groupId));

                  for(Iterator var22 = scheduleList.iterator(); var22.hasNext(); buffer.append("</device>")) {
                     DetailDownloadContentEntity detailDownloadContentEntity = (DetailDownloadContentEntity)var22.next();
                     buffer.append("<device>");
                     buffer.append("<device_group_id>" + deviceGroup.getGroup_id() + "</device_group_id>");
                     buffer.append("<device_group_name>" + deviceGroup.getGroup_name() + "</device_group_name>");
                     buffer.append("<device_name>" + detailDownloadContentEntity.getDevice_name() + "</device_name>");
                     if ("SUCCESS".equalsIgnoreCase(detailDownloadContentEntity.getStatus())) {
                        buffer.append("<progress>100</progress>");
                        buffer.append("<status>" + published + "</status>");
                     } else if ("FAIL".equalsIgnoreCase(detailDownloadContentEntity.getStatus())) {
                        buffer.append("<progress>0</progress>");
                        buffer.append("<status>" + failed + "</status>");
                     } else {
                        buffer.append("<progress>0</progress>");
                        buffer.append("<status>" + waiting + "</status>");
                     }
                  }
               }
            }
         }
      }

      if (mode == 1) {
         buffer.append("<now>" + now + "</now>");
         buffer.append("<total>" + total + "</total>");
      } else if (mode == 2) {
         String status = "";
         if (total == now) {
            status = published;
         } else if (now == 0) {
            ScheduleInfoDAO scheduleDao = new ScheduleInfoDAO();
            if (scheduleDao.getCheckContentPublishCount(strProgramId) > 0) {
               status = publishing;
            } else {
               status = waiting;
            }
         } else {
            status = publishing;
         }

         publishStatusMap.put("status", status);
         publishStatusMap.put("now", now + "");
         publishStatusMap.put("total", total + "");
      }

   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority')")
   public ResponseBody getContentSchedule(String programId) throws SQLException, ConfigException {
      LinkedHashMap result = new LinkedHashMap();
      LinkedHashMap data = new LinkedHashMap();
      new LinkedHashMap();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      ProgramEntity program = null;

      try {
         this.scheduleMgr.loadProgram(userContainer.getUser().getGroup_id(), userContainer.getUser().getOrganization(), data, programId, "");
         program = this.scheduleMgr.getScheudle(programId);
      } catch (Exception var54) {
         this.logger.error("", var54);
      }

      PlaylistInterface pInfo = DAOFactory.getPlaylistInfoImpl("PREMIUM");
      List deviceTypeList = new ArrayList();
      List channelList = program.getChannelList();
      List contentScheduleList = null;
      SelectConditionScheduleAdmin conditionList = new SelectConditionScheduleAdmin();
      new ScheduleInfoDAO();
      MessageSourceManager messageMgr = MessageSourceManagerImpl.getInstance();
      ScheduleInterface info = DAOFactory.getScheduleInfoImpl("PREMIUM");
      CommonProgramEntity programEntity = info.getProgram(programId);
      if (programEntity == null) {
         this.responseBody.setStatus("Fail");
         this.responseBody.setErrorCode(ExceptionCode.RES905[0]);
         this.responseBody.setErrorMessage(ExceptionCode.RES905[2]);
         return this.responseBody;
      } else {
         Map deviceGroupMap = info.getDeviceGroupIdsAndName(programId);
         String mainFrameId = "";
         conditionList.setProgram_id(programId);
         List channelLists = new ArrayList();
         LinkedHashMap channel;
         int channelNo;
         int k;
         if (channelList != null && channelList.size() > 0) {
            for(Iterator var19 = channelList.iterator(); var19.hasNext(); channelLists.add(channel)) {
               ChannelEntity channelEntity = (ChannelEntity)var19.next();
               channel = new LinkedHashMap();
               channelNo = channelEntity.getChannel_no();
               channel.put("channelName", channelEntity.getChannel_name());
               channel.put("channelNo", channelNo);
               List frameList = channelEntity.getFrameList();
               if (frameList != null) {
                  List frameLists = new ArrayList();

                  for(int j = 0; j < frameList.size(); ++j) {
                     FrameEntity frame = (FrameEntity)frameList.get(j);
                     LinkedHashMap frameMap = new LinkedHashMap();
                     frameMap.put("frameId", frame.getFrame_id());
                     frameMap.put("frameName", frame.getFrame_name());
                     frameMap.put("frameIndex", frame.getFrame_index());
                     frameMap.put("isMainFrame", frame.getIs_main_frame());
                     frameMap.put("x", frame.getX());
                     frameMap.put("y", frame.getY());
                     frameMap.put("width", frame.getWidth());
                     frameMap.put("height", frame.getHeight());
                     frameMap.put("lineData", frame.getLine_data());
                     if ("Y".equalsIgnoreCase(frame.getIs_main_frame())) {
                        mainFrameId = frame.getFrame_id();
                     }

                     List newScheduleList = new ArrayList();
                     List scheduleList = frame.getScheduleList();
                     long maxIndex = 0L;
                     if (scheduleList != null) {
                        for(k = 0; k < scheduleList.size(); ++k) {
                           LinkedHashMap schedule = new LinkedHashMap();
                           String contentType = ((ContentsScheduleEntity)scheduleList.get(k)).getContent_type();
                           schedule.put("id", ((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id());
                           schedule.put("fileName", ((ContentsScheduleEntity)scheduleList.get(k)).getFile_name());
                           schedule.put("fileId", ((ContentsScheduleEntity)scheduleList.get(k)).getFile_id());
                           String thumbnailPath;
                           if (((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type() != null && !((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type().equals("00")) {
                              thumbnailPath = this.getInputSource(((ContentsScheduleEntity)scheduleList.get(k)).getHw_input_source());
                              schedule.put("title", thumbnailPath);
                              schedule.put("contentId", thumbnailPath);
                              schedule.put("contentType", "HW_IS");
                           } else {
                              schedule.put("contentType", contentType);
                              if (contentType.equals("PLAYLIST")) {
                                 Playlist playlist = pInfo.getPlaylistActiveVerInfo(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                 schedule.put("contentId", ((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                 schedule.put("title", playlist.getPlaylist_name());
                              } else {
                                 schedule.put("contentId", ((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                 schedule.put("title", ((ContentsScheduleEntity)scheduleList.get(k)).getContent_name());
                              }
                           }

                           if (ScheduleUtility.isSupportInputSourceRepeat(programEntity.getDevice_type(), programEntity.getDevice_type_version()) && this.checkInputSource(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id())) {
                              schedule.put("title", ((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                           }

                           schedule.put("safetyLock", ((ContentsScheduleEntity)scheduleList.get(k)).getSafetyLock());
                           thumbnailPath = "";
                           String tempThumbFileId = "";
                           String tempThumbFileName = "";
                           String contentId;
                           if (!"PLAYLIST".equalsIgnoreCase(contentType)) {
                              if (!"HW_IS".equalsIgnoreCase(contentType) && (((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type() == null || ((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_type().equals("00"))) {
                                 ContentInfo contentDao = ContentInfoImpl.getInstance();
                                 Content contentInfo = contentDao.getThumbInfoOfActiveVersion(((ContentsScheduleEntity)scheduleList.get(k)).getContent_id());
                                 tempThumbFileId = contentInfo.getThumb_file_id();
                                 tempThumbFileName = contentInfo.getThumb_file_name();
                                 thumbnailPath = "/servlet/ContentThumbnail?thumb_id=" + tempThumbFileId + "&thumb_filename=" + tempThumbFileName;
                              }
                           } else {
                              PlaylistDao playlistDao = new PlaylistDao();
                              contentId = ((ContentsScheduleEntity)scheduleList.get(k)).getContent_id();
                              ContentFile thumbContent = playlistDao.getThumbFileInfo(contentId);
                              if (thumbContent != null && thumbContent.getFile_id() != null && thumbContent.getFile_name() != null) {
                                 tempThumbFileId = thumbContent.getFile_id();
                                 tempThumbFileName = thumbContent.getFile_name();
                              } else {
                                 Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(contentId);
                                 List tmpList = pInfo.getContentListOfPlaylist(playlistInfo.getPlaylist_id(), playlistInfo.getVersion_id());
                                 if (tmpList != null && tmpList.size() > 0) {
                                    Content playlistContent = (Content)tmpList.get(0);
                                    if (playlistContent != null) {
                                       tempThumbFileName = playlistContent.getThumb_file_name();
                                       tempThumbFileId = playlistContent.getThumb_file_id();
                                    }
                                 }
                              }

                              thumbnailPath = "/servlet/ContentThumbnail?thumb_id=" + tempThumbFileId + "&thumb_filename=" + tempThumbFileName;
                           }

                           schedule.put("thumbnailPath", thumbnailPath);
                           schedule.put("repeatType", ((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type());
                           schedule.put("weekdays", ((ContentsScheduleEntity)scheduleList.get(k)).getWeekdays());
                           schedule.put("monthdays", ((ContentsScheduleEntity)scheduleList.get(k)).getMonthdays());
                           SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                           contentId = null;
                           Date endDate = null;

                           try {
                              Date date = sdf.parse(((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              endDate = new Date(date.getTime() + (long)(((ContentsScheduleEntity)scheduleList.get(k)).getDuration() * 1000));
                           } catch (ParseException var53) {
                              this.logger.error("", var53);
                           }

                           DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                           schedule.put("playerMode", ((ContentsScheduleEntity)scheduleList.get(k)).getPlayer_mode());
                           List rangesMapList = new ArrayList();
                           LinkedHashMap rangesMap = new LinkedHashMap();
                           DateFormat endTime = new SimpleDateFormat("HH:mm:ss");
                           String var45 = ((ContentsScheduleEntity)scheduleList.get(k)).getRepeat_type();
                           byte var46 = -1;
                           switch(var45.hashCode()) {
                           case -1361669285:
                              if (var45.equals("day_of_month")) {
                                 var46 = 3;
                              }
                              break;
                           case -43636807:
                              if (var45.equals("day_of_week")) {
                                 var46 = 2;
                              }
                              break;
                           case 3415681:
                              if (var45.equals("once")) {
                                 var46 = 0;
                              }
                              break;
                           case 95346201:
                              if (var45.equals("daily")) {
                                 var46 = 1;
                              }
                           }

                           String color;
                           switch(var46) {
                           case 0:
                              schedule.put("start", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              schedule.put("end", df.format(endDate));
                              break;
                           case 1:
                              schedule.put("start", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              schedule.put("end", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                              schedule.put("stopDate", ((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                              rangesMap.put("start", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                              rangesMap.put("end", ((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                              rangesMapList.add(rangesMap);
                              schedule.put("ranges", rangesMapList);
                              schedule.put("dow", "[0,1,2,3,4,5,6]");
                              break;
                           case 2:
                              color = ((ContentsScheduleEntity)scheduleList.get(k)).getWeekdays();
                              String[] weekdaysArray = color.split(",");
                              schedule.put("start", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              schedule.put("end", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                              schedule.put("stopDate", ((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                              rangesMap.put("start", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                              rangesMap.put("end", ((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                              rangesMapList.add(rangesMap);
                              StringBuffer dow = new StringBuffer();
                              dow.append("[");
                              int index = 0;

                              for(; index < weekdaysArray.length; ++index) {
                                 if (index > 0) {
                                    dow.append(",");
                                 }

                                 String var51 = weekdaysArray[index];
                                 byte var52 = -1;
                                 switch(var51.hashCode()) {
                                 case 101661:
                                    if (var51.equals("fri")) {
                                       var52 = 4;
                                    }
                                    break;
                                 case 108300:
                                    if (var51.equals("mon")) {
                                       var52 = 0;
                                    }
                                    break;
                                 case 113638:
                                    if (var51.equals("sat")) {
                                       var52 = 5;
                                    }
                                    break;
                                 case 114252:
                                    if (var51.equals("sun")) {
                                       var52 = 6;
                                    }
                                    break;
                                 case 114817:
                                    if (var51.equals("thu")) {
                                       var52 = 3;
                                    }
                                    break;
                                 case 115204:
                                    if (var51.equals("tue")) {
                                       var52 = 1;
                                    }
                                    break;
                                 case 117590:
                                    if (var51.equals("wed")) {
                                       var52 = 2;
                                    }
                                 }

                                 switch(var52) {
                                 case 0:
                                    dow.append("1");
                                    break;
                                 case 1:
                                    dow.append("2");
                                    break;
                                 case 2:
                                    dow.append("3");
                                    break;
                                 case 3:
                                    dow.append("4");
                                    break;
                                 case 4:
                                    dow.append("5");
                                    break;
                                 case 5:
                                    dow.append("6");
                                    break;
                                 case 6:
                                    dow.append("0");
                                 }
                              }

                              dow.append("]");
                              schedule.put("ranges", rangesMapList);
                              schedule.put("dow", dow);
                              break;
                           case 3:
                              String monthdays = ((ContentsScheduleEntity)scheduleList.get(k)).getMonthdays();
                              schedule.put("start", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + ((ContentsScheduleEntity)scheduleList.get(k)).getStart_time());
                              schedule.put("end", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " " + endTime.format(endDate));
                              schedule.put("stopDate", ((ContentsScheduleEntity)scheduleList.get(k)).getStop_date());
                              rangesMap.put("start", ((ContentsScheduleEntity)scheduleList.get(k)).getStart_date() + " 00:00:00");
                              rangesMap.put("end", ((ContentsScheduleEntity)scheduleList.get(k)).getStop_date() + " 23:59:59");
                              rangesMap.put("month", monthdays);
                              rangesMapList.add(rangesMap);
                              schedule.put("ranges", rangesMapList);
                              schedule.put("dow", "[0,1,2,3,4,5,6]");
                           }

                           long currentIndex = ((ContentsScheduleEntity)scheduleList.get(k)).getPriority();
                           schedule.put("index", currentIndex);
                           if (maxIndex < currentIndex) {
                              maxIndex = currentIndex;
                           }

                           schedule.put("scheduleId", ((ContentsScheduleEntity)scheduleList.get(k)).getSchedule_id());
                           color = this.getColor(k);
                           schedule.put("color", color);
                           schedule.put("size", ((ContentsScheduleEntity)scheduleList.get(k)).getFile_size());
                           schedule.put("allDay", false);
                           schedule.put("cifsSlideTime", ((ContentsScheduleEntity)scheduleList.get(k)).getSlide_transition_time());
                           newScheduleList.add(schedule);
                        }
                     }

                     frameMap.put("maxIndex", maxIndex);
                     frameMap.put("scheduleList", newScheduleList);
                     frameLists.add(frameMap);
                  }

                  channel.put("mainFrameId", mainFrameId);
                  channel.put("frameList", frameLists);
               }
            }
         }

         String deviceGroupIds = (String)deviceGroupMap.get("device_group_ids");
         result.put("isAdSchedule", false);
         result.put("canWriteContent", true);
         result.put("programId", programEntity.getProgram_id());
         data.put("deviceType", programEntity.getDevice_type());
         data.put("deviceTypeVersion", programEntity.getDevice_type_version());
         data.put("numberOfChannel", channelLists.size());
         data.put("deviceGroupId", deviceGroupIds);
         data.put("programName", programEntity.getProgram_name());
         data.put("channelList", channelLists);
         data.put("deviceGroupName", deviceGroupMap.get("group_names"));
         data.put("scheduleName", programEntity.getProgram_name());
         data.put("scheudleGroup", programEntity.getProgram_group_name());
         data.put("lastModified", programEntity.getModify_date());
         data.put("programGroupName", programEntity.getProgram_group_name());
         data.put("programGroupId", programEntity.getProgram_group_id());
         data.put("type", program.getProgram_type());
         if (programEntity.getUse_sync_play() != null && programEntity.getUse_sync_play().equals("Y")) {
            data.put("scheduleType", messageMgr.getMessageSource("MIS_SID_SYNC_PLAY", this.locale));
         } else if (programEntity.getUse_ad_schedule() != null && programEntity.getUse_ad_schedule().equals("Y")) {
            data.put("scheduleType", messageMgr.getMessageSource("COM_DID_LFD_ADVERTISEMENT", this.locale));
         } else {
            data.put("scheduleType", messageMgr.getMessageSource("COM_TEXT_GENERAL_P", this.locale));
         }

         deviceTypeList.add("SIG_CHILD");
         String[] groupIdList = deviceGroupIds.split(",");
         String[] var57 = groupIdList;
         channelNo = groupIdList.length;

         for(int var58 = 0; var58 < channelNo; ++var58) {
            String groupId = var57[var58];
            if (groupId != null && groupId.length() > 0) {
               DownloadStatusInfo downloadInfo = DownloadStatusInfoImpl.getInstacne();
               Object[] rtn = downloadInfo.getDownloadStatusListByProgramId(programId);
               if (rtn != null && rtn.length > 1) {
                  Map resultMap = (Map)rtn[1];
                  if (resultMap != null) {
                     float total = 0.0F;
                     float complete = 0.0F;
                     if (resultMap.get("deviceCount") != null) {
                        total = (float)(Integer)resultMap.get("deviceCount");
                        data.put("deviceUseCount", resultMap.get("deviceCount"));
                     }

                     if (resultMap.get("completeCount") != null) {
                        complete = (float)(Integer)resultMap.get("completeCount");
                     }

                     data.put("programStatus", (int)complete + "/" + (int)total);
                     if (total > 0.0F && complete > 0.0F) {
                        data.put("value", complete / total * 100.0F);
                     } else {
                        data.put("value", 0);
                     }

                     if (resultMap.get("deviceList") != null) {
                        List deviceList = (List)resultMap.get("deviceList");
                        if (deviceList != null && deviceList.size() > 0) {
                           List rtnDeviceList = downloadInfo.getProgressInfoByDeviceId(programId, deviceList);
                           if (rtnDeviceList != null && rtnDeviceList.size() > 0) {
                              for(k = 0; k < rtnDeviceList.size(); ++k) {
                                 Map map = (Map)rtnDeviceList.get(k);
                                 if (map.get("complete") != null) {
                                    int tempComplete = -1;
                                    if (map.get("complete").getClass().equals(Integer.TYPE)) {
                                       tempComplete = (Integer)map.get("complete");
                                    } else if (map.get("complete").getClass().equals(Long.TYPE)) {
                                       tempComplete = CommonUtils.safeLongToInt((Long)map.get("complete"));
                                    }

                                    if (tempComplete == 0 && !DeviceUtils.isConnected((String)map.get("device_id"))) {
                                       map.put("device_status", "NetworkError");
                                    }
                                 }
                              }

                              result.put("deviceStatusList", rtnDeviceList);
                           }
                        }
                     }
                  }
               }
            }
         }

         data.put("description", programEntity.getDescription());
         result.put("deviceType", programEntity.getDevice_type());
         result.put("data", data);
         this.responseBody.setStatus("Success");
         this.responseBody.setItems(result);
         return this.responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody createContentSchedule(ContentScheduleResource params) throws ConfigException, SQLException {
      ResponseBody responseBody = new ResponseBody();
      LinkedHashMap data = new LinkedHashMap();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String sessionId = "";
      ProgramEntity program = this.scheduleMgr.newScheudle(params.getDeviceType(), params.getDeviceTypeVersion(), sessionId, userContainer.getUser().getUser_id(), (String)null, this.locale);
      List channelList = program.getChannelList();

      try {
         ChannelEntity channel = (ChannelEntity)channelList.get(0);
         List frameList = channel.getFrameList();
         if (frameList != null && frameList.size() > 0) {
            FrameEntity frame = (FrameEntity)frameList.get(0);
            data.put("programId", program.getProgram_id());
            data.put("programName", program.getProgram_name());
            data.put("channelNo", String.valueOf(channel.getChannel_no()));
            data.put("channelName", channel.getChannel_name());
            data.put("frameId", frame.getFrame_id());
            data.put("frameName", frame.getFrame_name());
            data.put("sessionId", sessionId);
            data.put("syncPlay", params.isSyncPlay() + "");
            data.put("isVwl", params.isVwlType());
            data.put("deviceType", params.getDeviceType());
            data.put("deviceTypeVersion", String.valueOf(params.getDeviceTypeVersion()));
            data.put("scheudleDesText", "");
            String userOrganization = userContainer.getUser().getOrganization();
            ProgramGroupInterface programGroupDao = ProgramGroupImpl.getInstance();
            if ("ROOT".equalsIgnoreCase(userOrganization)) {
               List prgoramGroupList = programGroupDao.getChildGroupList(0, false);
               if (prgoramGroupList == null || prgoramGroupList.size() < 1) {
                  responseBody.setStatus("Fail");
                  return responseBody;
               }

               Long groupId = ((ProgramGroup)prgoramGroupList.get(0)).getGroup_id();
               String orgranizationName = ((ProgramGroup)prgoramGroupList.get(0)).getGroup_name();
               prgoramGroupList = programGroupDao.getChildGroupList(CommonUtils.safeLongToInt(groupId), false);
               data.put("programGroupId", String.valueOf(((ProgramGroup)prgoramGroupList.get(0)).getGroup_id()));
               data.put("programGroupName", orgranizationName + " - " + ((ProgramGroup)prgoramGroupList.get(0)).getGroup_name());
            } else {
               int groupId = userContainer.getUser().getRoot_group_id().intValue();

               try {
                  groupId = programGroupDao.getProgramGroupForOrg(userOrganization);
               } catch (Exception var17) {
                  this.logger.error("", var17);
               }

               List prgoramGroupList = programGroupDao.getChildGroupList(groupId, false);
               data.put("programGroupId", String.valueOf(((ProgramGroup)prgoramGroupList.get(0)).getGroup_id()));
               data.put("programGroupName", ((ProgramGroup)prgoramGroupList.get(0)).getGroup_name());
            }

            data.put("status", "success");
         }
      } catch (Exception var18) {
         this.logger.error(var18.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var18.getMessage());
         return responseBody;
      }

      this.addScheduleItem(program, params);

      try {
         String isSync = params.isSyncPlay() ? "true" : "false";
         String isVwl = params.isVwlType() ? "true" : "false";
         if (this.scheduleMgr.saveProgram(program.getProgram_id(), params.getScheduleName(), params.getScheduleGroupId(), params.getDeviceGroupIds(), params.getDescription(), params.getDeployReserve(), params.getDeployReserveResource(), params.getContentSyncOn(), sessionId, params.getBackgroundMusic(), isSync, isVwl, params.getContentMute(), "mobile", params.getResume())) {
            responseBody.setStatus("Success");
         } else {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage("do not save program!");
            this.logger.error("[MagicInfo_Schedule] Do not save program! ");
         }
      } catch (Exception var16) {
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var16.getMessage());
         this.logger.error("[MagicInfo_Schedule] deploy schedule error! " + var16.getMessage(), var16);
      }

      responseBody.setStatus("Success");
      responseBody.setItems(data);
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public LinkedHashMap delScheduleItem(ProgramEntity program, ContentScheduleResource params) {
      LinkedHashMap result = new LinkedHashMap();

      try {
         if (program != null) {
            List channelList = program.getChannelList();
            if (channelList != null) {
               for(int i = 0; i < channelList.size(); ++i) {
                  List frameList = ((ChannelEntity)channelList.get(i)).getFrameList();
                  if (frameList != null) {
                     for(int j = 0; j < frameList.size(); ++j) {
                        List scheduleList = ((FrameEntity)frameList.get(j)).getScheduleList();
                        if (scheduleList == null) {
                           result.put("status", "fail");
                           result.put("fail", "scheduleList null");
                        } else {
                           ((FrameEntity)((ChannelEntity)program.getChannelList().get(i)).getFrameList().get(j)).setScheduleList(new ArrayList());
                           this.scheduleMgr.updateSchedule(program, program.getProgram_id());
                        }
                     }
                  } else {
                     this.logger.info("[MagicInfo_Schedule] frameList null : programId = " + program.getProgram_id());
                     result.put("status", "fail");
                     result.put("fail", "frameList null");
                  }
               }
            } else {
               this.logger.info("[MagicInfo_Schedule] channelList null : programId = " + program.getProgram_id());
               result.put("status", "fail");
               result.put("fail", "channelList null");
            }
         } else {
            this.logger.info("[MagicInfo_Schedule] program is null");
            result.put("status", "fail");
            result.put("fail", "program null");
         }
      } catch (Exception var9) {
         result.put("status", "fail");
         result.put("message", var9.getMessage());
      }

      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public LinkedHashMap addScheduleItem(ProgramEntity program, ContentScheduleResource params) {
      LinkedHashMap result = new LinkedHashMap();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      List itemList = params.getItemList();
      if (itemList != null && itemList.size() > 0) {
         Iterator var6 = itemList.iterator();

         while(var6.hasNext()) {
            ContentScheduleItemResource contentScheduleItemResource = (ContentScheduleItemResource)var6.next();
            String contentName = contentScheduleItemResource.getContentName();
            int channelNo = contentScheduleItemResource.getChannelNo();
            String frameId = contentScheduleItemResource.getFrameId();
            String startDate = contentScheduleItemResource.getStartDate();
            String stopDate = contentScheduleItemResource.getStopDate();
            String startTime = contentScheduleItemResource.getStartTime();
            String duration = contentScheduleItemResource.getDuration();
            String contentId = contentScheduleItemResource.getContentId();
            String contentType = contentScheduleItemResource.getContentType();
            String repeatType = contentScheduleItemResource.getRepeatType();
            String weekdays = contentScheduleItemResource.getWeekdays();
            String monthday = contentScheduleItemResource.getMonthday();
            String scheduleType = contentScheduleItemResource.getScheduleType();
            String inputSource = contentScheduleItemResource.getInputSource();
            String isSync = contentScheduleItemResource.getIsSync();
            String cifsSlideTime = contentScheduleItemResource.getCifsSlideTime();
            String playerMode = contentScheduleItemResource.getPlayerMode();
            int priority = contentScheduleItemResource.getPriority();
            String safetyLock = contentScheduleItemResource.getSafetyLock();
            boolean check = false;
            long currentIndex = 0L;

            try {
               ContentsScheduleEntity content = new ContentsScheduleEntity();
               this.scheduleMgr.contentScheduleInit(program.getProgram_id(), content);
               content.setContent_name(contentName);
               content.setStart_date(startDate);
               content.setStop_date(stopDate);
               content.setStart_time(startTime);
               content.setDuration(Integer.valueOf(duration));
               content.setContent_id(contentId);
               content.setPlayer_mode(playerMode);
               currentIndex = program.getCurrentIndex();
               String mediaType = contentType;
               ++currentIndex;
               if (contentType.equals("HW_IS")) {
                  String deviceType = program.getDevice_type();
                  if (ScheduleUtility.isSupportInputSourceRepeat(deviceType, program.getDevice_type_version())) {
                     content.setContent_type("HW_IS");
                     content.setSchedule_type("00");
                  } else if (deviceType.equals("SPLAYER") || deviceType.equals("LPLAYER")) {
                     content.setContent_id("");
                     if (inputSource.equals("1000")) {
                        content.setSchedule_type("01");
                     } else {
                        content.setSchedule_type("03");
                     }

                     content.setHw_input_source(inputSource);
                     content.setContent_type("");
                     content.setPriority(0L);
                     content.setHw_AtvDtv("-1");
                     content.setHw_AirCable("-1");
                     content.setHw_MajorCH("0");
                     content.setHw_MinorCH("0");
                     content.setHw_Volume("-1");
                     content.setHw_sch_ch("-1");
                  }
               } else {
                  PlaylistDao playlistDao = new PlaylistDao();
                  if (contentType.equals("PLAYLIST")) {
                     ContentFile thumbContent = playlistDao.getThumbFileInfo(contentId);
                     content.setFile_size(playlistDao.getPlaylistActiveVerInfo(contentId).getTotal_size());
                     if (thumbContent != null && thumbContent.getFile_id() != null && thumbContent.getFile_name() != null) {
                        content.setFile_id(thumbContent.getFile_id());
                        content.setFile_name(thumbContent.getFile_name());
                     } else {
                        Playlist playlistInfo = playlistDao.getPlaylistActiveVerInfo(contentId);
                        if (playlistInfo != null) {
                           ContentInfo contentDao = ContentInfoImpl.getInstance();
                           Content contentInfo = contentDao.getThumbInfoOfActiveVersion(playlistInfo.getContent_id());
                           content.setFile_id(contentInfo.getThumb_file_id());
                           content.setFile_name(contentInfo.getThumb_file_name());
                        }
                     }
                  } else {
                     ContentDao contentDao = new ContentDao();
                     Map thumbContent = contentDao.getThumbFileInfoOfActiveVersion(contentId);
                     content.setFile_size(contentDao.getContentActiveVerInfo(contentId).getTotal_size());
                     content.setFile_id((String)thumbContent.get("file_id"));
                     content.setFile_name((String)thumbContent.get("file_name"));
                  }

                  if (contentType.equals("CIFS")) {
                     int slideTime = 0;

                     try {
                        if (cifsSlideTime != null && !cifsSlideTime.equals("")) {
                           slideTime = Integer.valueOf(cifsSlideTime);
                        }
                     } catch (Exception var39) {
                        slideTime = 0;
                     }

                     content.setSlide_transition_time(slideTime);
                  } else {
                     content.setSlide_transition_time(0);
                  }

                  content.setSchedule_type("00");
                  content.setContent_type(contentType);
               }

               if (isSync != null && isSync.equals("true")) {
                  content.setIsSync(true);
               }

               content.setRepeat_type(repeatType);
               content.setWeekdays(weekdays);
               content.setMonthdays(monthday);
               content.setPriority((long)priority);
               content.setSafetyLock(safetyLock);
               content.setChannel_no(channelNo);
               Date date = new Date();
               Timestamp timestamp = new Timestamp(date.getTime());
               content.setModify_date(timestamp);
               content.setCreate_date(timestamp);
               content.setUser_id(userContainer.getUser().getUser_id());
               if (program != null) {
                  List channelList = program.getChannelList();
                  if (channelList == null) {
                     result.put("status", "fail");
                     result.put("fail", "channelList null");
                  } else {
                     for(int i = 0; i < channelList.size(); ++i) {
                        if (((ChannelEntity)channelList.get(i)).getChannel_no() == Integer.valueOf(channelNo)) {
                           List frameList = ((ChannelEntity)channelList.get(i)).getFrameList();
                           if (frameList == null) {
                              result.put("status", "fail");
                              result.put("message", "frameList null");
                           } else {
                              for(int j = 0; j < frameList.size(); ++j) {
                                 if (((FrameEntity)frameList.get(j)).getFrame_id().equals(frameId) || frameId.equals("new")) {
                                    List scheduleList = ((FrameEntity)frameList.get(j)).getScheduleList();
                                    if (program.getDevice_type().equals("SPLAYER") && mediaType != null && (mediaType.equals("LFD") || mediaType.equals("LFT")) && !((FrameEntity)frameList.get(j)).getLine_data().equals("ZeroFrameOnly")) {
                                       result.put("status", "fail");
                                       result.put("message", "LFD content is not played on the multiFrame schedule.");
                                    } else {
                                       if (scheduleList == null) {
                                          scheduleList = new ArrayList();
                                       }

                                       if (!content.getSchedule_type().equals("03")) {
                                          content.setFrame_index(((FrameEntity)frameList.get(j)).getFrame_index());
                                       }

                                       ((List)scheduleList).add(content);
                                       ((FrameEntity)frameList.get(j)).setScheduleList((List)scheduleList);
                                       ((ChannelEntity)channelList.get(i)).setFrameList(frameList);
                                       program.setChannelList(channelList);
                                       program.setCurrentIndex(currentIndex);
                                       this.scheduleMgr.updateSchedule(program, program.getProgram_id());
                                       result.put("scheduleId", content.getSchedule_id());
                                       result.put("status", "success");
                                       check = true;
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            } catch (Exception var40) {
               this.logger.error("", var40);
               result.put("status", "fail");
               result.put("message", var40.getMessage());
            }
         }
      }

      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody editContentSchedule(String programId, ContentScheduleResource params) throws ConfigException, SQLException {
      ResponseBody responseBody = new ResponseBody();
      String sessionId = "";

      try {
         ProgramEntity program = this.scheduleMgr.getScheudle(programId);
         if (program == null) {
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage("[MagicInfo_Schedule] Do not get program! ");
            return responseBody;
         } else {
            this.delScheduleItem(program, params);
            this.addScheduleItem(program, params);
            if (this.scheduleMgr.updateProgram(programId, params.getScheduleName(), params.getScheduleGroupId(), params.getDeviceGroupIds(), params.getDescription(), params.getDeployReserve(), params.getDeployReserveResource(), params.getContentSyncOn(), sessionId, params.getBackgroundMusic(), params.getContentMute(), "mobile", params.getResume())) {
               responseBody.setStatus("Success");
               return responseBody;
            } else {
               responseBody.setStatus("Fail");
               responseBody.setErrorMessage("[MagicInfo_Schedule] Do not save program! ");
               return responseBody;
            }
         }
      } catch (Exception var7) {
         this.logger.error("", var7);
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage("[MagicInfo_Schedule] deploy schedule error! " + var7.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody deployContentSchedule(String programId, ContentScheduleResource params) throws ConfigException, SQLException {
      ResponseBody responseBody = new ResponseBody();
      new LinkedHashMap();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      ScheduleInterface infoDao = DAOFactory.getScheduleInfoImpl("PREMIUM");

      try {
         String strGroupId = params.getDeviceGroupIds();
         strGroupId = this.checkDeviceGroupAuth(programId, strGroupId, userContainer.getUser().getUser_id(), userContainer.getUser().getRole_name(), "PREMIUM");
         boolean deleted = infoDao.updateDeviceGroup(strGroupId, programId, userContainer.getUser().getUser_id(), "mobile");
         CommonProgramEntity program = infoDao.getProgram(programId);
         if (program != null) {
            Map m = infoDao.getDeviceGroupIdsAndName(programId);
            program.setDevice_group_ids((String)m.get("device_group_ids"));
            infoDao.reserveSchedule(program);
         }

         if (deleted) {
            responseBody.setStatus("Success");
            return responseBody;
         } else {
            responseBody.setStatus("Fail");
            return responseBody;
         }
      } catch (Exception var11) {
         this.logger.error(var11.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var11.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody copyContentSchedule(String programId, ContentScheduleResource params) throws ConfigException, SQLException {
      ResponseBody responseBody = new ResponseBody();
      String newProgramId = UUID.randomUUID().toString();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      String userId = userContainer.getUser().getUser_id();
      String sessionId = "";
      new LinkedHashMap();
      ProgramEntity program = null;

      try {
         program = this.scheduleMgr.getScheudle(programId);
         int i;
         if (program.getProgram_type().equals("LFD")) {
            program.setProgram_id(newProgramId);
            if (program.getChannelList() != null) {
               List channelList = program.getChannelList();

               for(int i = 0; i < channelList.size(); ++i) {
                  ((ChannelEntity)channelList.get(i)).setProgram_id(newProgramId);
                  if (((ChannelEntity)channelList.get(i)).getFrameList() != null) {
                     for(i = 0; i < ((ChannelEntity)channelList.get(i)).getFrameList().size(); ++i) {
                        String newFrameId = UUID.randomUUID().toString();
                        ((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(i)).setProgram_id(newProgramId);
                        ((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(i)).setFrame_id(newFrameId);
                        if (((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(i)).getScheduleList() != null) {
                           for(int k = 0; k < ((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(i)).getScheduleList().size(); ++k) {
                              String newScheduleId = UUID.randomUUID().toString();
                              ((ContentsScheduleEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(i)).getScheduleList().get(k)).setProgram_id(newProgramId);
                              ((ContentsScheduleEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(i)).getScheduleList().get(k)).setSchedule_id(newScheduleId);
                           }
                        }
                     }
                  }
               }
            }

            if (this.scheduleMgr.saveProgram(programId, params.getScheduleName(), params.getScheduleGroupId(), params.getDeviceGroupIds(), params.getDescription(), params.getDeployReserve(), params.getDeployReserveResource(), params.getContentSyncOn(), sessionId, params.getBackgroundMusic(), params.isSyncPlay() + "", params.isVwlType() + "", params.getContentMute(), "mobile", params.getResume())) {
               this.scheduleMgr.removeSchedule(programId);
               responseBody.setStatus("Success");
               return responseBody;
            } else {
               responseBody.setStatus("Fail");
               responseBody.setErrorMessage("[MagicInfo_Schedule] Do not save program! ");
               return responseBody;
            }
         } else if (!program.getProgram_type().equals("ADV")) {
            return responseBody;
         } else {
            this.logger.error("[GetAdScheduleController] save as new programId : " + newProgramId);
            (new StringBuilder()).append(sessionId).append(newProgramId).toString();
            program.setProgram_id(newProgramId);
            if (program.getChannelList() != null) {
               List channelList = program.getChannelList();

               for(i = 0; i < channelList.size(); ++i) {
                  ((ChannelEntity)channelList.get(i)).setProgram_id(newProgramId);
                  if (((ChannelEntity)channelList.get(i)).getFrameList() != null) {
                     for(int j = 0; j < ((ChannelEntity)channelList.get(i)).getFrameList().size(); ++j) {
                        String newFrameId = UUID.randomUUID().toString();
                        ((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).setProgram_id(newProgramId);
                        ((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).setFrame_id(newFrameId);
                        if (((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList() != null) {
                           for(int k = 0; k < ((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().size(); ++k) {
                              String newSlotId = UUID.randomUUID().toString();
                              ((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).setProgram_id(newProgramId);
                              ((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).setFrame_id(newFrameId);
                              ((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).setSlot_id(newSlotId);
                              if (((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList() != null) {
                                 for(int l = 0; l < ((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().size(); ++l) {
                                    String newScheduleId = UUID.randomUUID().toString();
                                    ((AdScheduleEntity)((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().get(l)).setSchedule_id(newScheduleId);
                                    ((AdScheduleEntity)((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().get(l)).setProgram_id(newProgramId);
                                    ((AdScheduleEntity)((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().get(l)).setSlot_id(newSlotId);
                                    ((AdScheduleEntity)((AdSlotEntity)((FrameEntity)((ChannelEntity)channelList.get(i)).getFrameList().get(j)).getSlotList().get(k)).getScheduleList().get(l)).setUser_id(userId);
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }

            if (this.scheduleMgr.saveAdProgram(programId, params.getScheduleName(), params.getScheduleGroupId(), params.getDeviceGroupIds(), params.getDescription(), params.getDeployReserve(), params.getDeployReserveResource(), sessionId, "mobile")) {
               this.scheduleMgr.removeSchedule(programId);
               responseBody.setStatus("Success");
               return responseBody;
            } else {
               responseBody.setStatus("Fail");
               responseBody.setErrorMessage("[MagicInfo_Schedule] Do not save program! ");
               return responseBody;
            }
         }
      } catch (Exception var19) {
         this.logger.error(var19.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage(var19.getMessage());
         return responseBody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Read Authority')")
   public ResponseBody getPublishStatus(String programId) throws SQLException, ConfigException {
      ResponseBody responseBody = new ResponseBody();
      PublishStatusResource result = new PublishStatusResource();
      new ArrayList();
      new LinkedHashMap();
      DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
      ScheduleInterface info = DAOFactory.getScheduleInfoImpl("PREMIUM");
      Map deviceGroupMap = info.getDeviceGroupIdsAndName(programId);
      String var9 = (String)deviceGroupMap.get("device_group_ids");

      try {
         DownloadStatusInfo downloadInfo = DownloadStatusInfoImpl.getInstacne();
         Object[] rtn = downloadInfo.getDownloadStatusListByProgramId(programId);
         if (rtn != null && rtn.length > 1) {
            Map resultMap = (Map)rtn[1];
            if (resultMap != null) {
               float total = 0.0F;
               float complete = 0.0F;
               if (resultMap.get("deviceCount") != null) {
                  total = (float)(Integer)resultMap.get("deviceCount");
               }

               if (resultMap.get("completeCount") != null) {
                  complete = (float)(Integer)resultMap.get("completeCount");
               }

               result.setProgramStatus((int)complete + "/" + (int)total);
               if (total > 0.0F && complete > 0.0F) {
                  result.setValue(complete / total * 100.0F);
               } else {
                  result.setValue(0.0F);
               }

               if (resultMap.get("deviceList") != null) {
                  List deviceList = (List)resultMap.get("deviceList");
                  if (deviceList != null && deviceList.size() > 0) {
                     List rtnDeviceList = downloadInfo.getProgressInfoByDeviceId(programId, deviceList);
                     if (rtnDeviceList != null && rtnDeviceList.size() > 0) {
                        for(int i = 0; i < rtnDeviceList.size(); ++i) {
                           Map map = (Map)rtnDeviceList.get(i);
                           if (map.get("complete") != null) {
                              int tempComplete = -1;
                              if (map.get("complete").getClass().equals(Integer.TYPE)) {
                                 tempComplete = (Integer)map.get("complete");
                              } else if (map.get("complete").getClass().equals(Long.TYPE)) {
                                 tempComplete = CommonUtils.safeLongToInt((Long)map.get("complete"));
                              }

                              if (tempComplete == 0 && !DeviceUtils.isConnected((String)map.get("device_id"))) {
                                 map.put("device_status", "NetworkError");
                              }
                           }
                        }

                        result.setDeviceStatusList(rtnDeviceList);
                     }
                  }
               }
            }
         }

         result.setStatus("SUCCESS");
      } catch (Exception var20) {
         this.logger.error(var20.getMessage().toString());
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage("[MagicInfo_ScheduleController] fail download status information! e : " + var20.getMessage());
         return responseBody;
      }

      responseBody.setStatus("Success");
      responseBody.setItems(result);
      return responseBody;
   }

   @PreAuthorize("hasAnyAuthority('Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody editContentSchedulePriority(String programId, String scheduleId, ContentScheduleResource params) throws ConfigException, SQLException {
      ResponseBody responseBody = new ResponseBody();
      if (params.getPriority() != null && !"".equals(params.getPriority())) {
         try {
            Long requestPriority = Long.parseLong(params.getPriority());
            ScheduleInfo schInfo = ScheduleInfoImpl.getInstance();
            Long maxPriority = schInfo.getMaxPriorityByProgramId(programId);
            Long minPriority = schInfo.getMinPriorityByProgramId(programId);
            if (maxPriority != null && minPriority != null) {
               if (requestPriority >= maxPriority) {
                  requestPriority = maxPriority;
               } else if (requestPriority <= minPriority) {
                  requestPriority = minPriority;
               }

               Long originalPriority = schInfo.getPriorityByScheduleId(programId, scheduleId);
               if (originalPriority == null) {
                  this.logger.error("[MagicInfo_Schedule][editContentSchedulePriority] Invalid parameter - schedule id ");
                  responseBody.setStatus("Fail");
                  responseBody.setErrorMessage("[MagicInfo_Schedule] Invalid parameter - schedule id ");
                  return responseBody;
               } else if (requestPriority != originalPriority) {
                  List scheduleMapList = schInfo.getScheduleIdAndPriorityByProgramId(programId);
                  if (scheduleMapList.isEmpty() || scheduleMapList.size() == 0) {
                     this.logger.error("[MagicInfo_Schedule][editContentSchedulePriority] Invalid parameter - program id ");
                     responseBody.setStatus("Fail");
                     responseBody.setErrorMessage("[MagicInfo_Schedule] Invalid parameter - program id ");
                     return responseBody;
                  } else {
                     Iterator var11 = scheduleMapList.iterator();

                     while(true) {
                        while(true) {
                           while(var11.hasNext()) {
                              Map scheduleItem = (Map)var11.next();
                              Long currentPriority = Long.parseLong(scheduleItem.get("priority").toString());
                              if (requestPriority > originalPriority) {
                                 if (originalPriority < currentPriority && currentPriority <= requestPriority) {
                                    scheduleItem.put("priority", "" + (currentPriority - 1L));
                                 } else if (originalPriority.equals(currentPriority)) {
                                    scheduleItem.put("priority", requestPriority.toString());
                                 }
                              } else if (requestPriority <= currentPriority && currentPriority < originalPriority) {
                                 scheduleItem.put("priority", "" + (currentPriority + 1L));
                              } else if (originalPriority.equals(currentPriority)) {
                                 scheduleItem.put("priority", requestPriority.toString());
                              }
                           }

                           if (!schInfo.updateSchedulePriorityByProgramId(programId, scheduleMapList)) {
                              this.logger.error("[MagicInfo_Schedule][editContentSchedulePriority] Failed to update priority value! ");
                              responseBody.setStatus("Fail");
                              responseBody.setErrorMessage("[MagicInfo_Schedule] Failed to update priority value! ");
                              return responseBody;
                           }

                           responseBody.setStatus("Success");
                           return responseBody;
                        }
                     }
                  }
               } else {
                  responseBody.setStatus("Success");
                  return responseBody;
               }
            } else {
               this.logger.error("[MagicInfo_Schedule][editContentSchedulePriority] Invalid parameter - program id ");
               responseBody.setStatus("Fail");
               responseBody.setErrorMessage("[MagicInfo_Schedule] Invalid parameter - program id ");
               return responseBody;
            }
         } catch (Exception var14) {
            this.logger.error(var14.getMessage());
            responseBody.setStatus("Fail");
            responseBody.setErrorMessage(var14.getMessage());
            return responseBody;
         }
      } else {
         this.logger.error("[MagicInfo_Schedule][editContentSchedulePriority] Priority value is empty! ");
         responseBody.setStatus("Fail");
         responseBody.setErrorMessage("[MagicInfo_Schedule] Priority value is empty! ");
         return responseBody;
      }
   }

   public String checkDeviceGroupAuth(String strProgId, String strGroupId, String userId, String roleName, String productType) throws SQLException {
      ServerSetupDao serverSetupDao = new ServerSetupDao();
      boolean permissions_func = serverSetupDao.checkPermissionsDevice();
      if (permissions_func && !roleName.equals("Administrator") && !roleName.equals("Server Administrator")) {
         String result = strGroupId;
         DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
         List deviceList = deviceGroupDao.getScheduleMappingDeviceGroupAuth(strProgId, userId, false);
         if (deviceList != null && deviceList.size() > 0) {
            if (strGroupId.length() > 0) {
               result = strGroupId + ",";
            }

            result = result + ((DeviceGroup)deviceList.get(0)).getGroup_id();

            for(int i = 1; i < deviceList.size(); ++i) {
               result = result + "," + ((DeviceGroup)deviceList.get(i)).getGroup_id();
            }
         }

         return result;
      } else {
         return strGroupId;
      }
   }

   public String getInputSource(String inputsource) {
      byte var3 = -1;
      switch(inputsource.hashCode()) {
      case 52:
         if (inputsource.equals("4")) {
            var3 = 4;
         }
         break;
      case 56:
         if (inputsource.equals("8")) {
            var3 = 5;
         }
         break;
      case 1569:
         if (inputsource.equals("12")) {
            var3 = 3;
         }
         break;
      case 1570:
         if (inputsource.equals("13")) {
            var3 = 21;
         }
         break;
      case 1571:
         if (inputsource.equals("14")) {
            var3 = 22;
         }
         break;
      case 1598:
         if (inputsource.equals("20")) {
            var3 = 0;
         }
         break;
      case 1602:
         if (inputsource.equals("24")) {
            var3 = 2;
         }
         break;
      case 1629:
         if (inputsource.equals("30")) {
            var3 = 1;
         }
         break;
      case 1630:
         if (inputsource.equals("31")) {
            var3 = 20;
         }
         break;
      case 1631:
         if (inputsource.equals("32")) {
            var3 = 6;
         }
         break;
      case 1632:
         if (inputsource.equals("33")) {
            var3 = 9;
         }
         break;
      case 1633:
         if (inputsource.equals("34")) {
            var3 = 13;
         }
         break;
      case 1634:
         if (inputsource.equals("35")) {
            var3 = 10;
         }
         break;
      case 1635:
         if (inputsource.equals("36")) {
            var3 = 14;
         }
         break;
      case 1636:
         if (inputsource.equals("37")) {
            var3 = 17;
         }
         break;
      case 1637:
         if (inputsource.equals("38")) {
            var3 = 18;
         }
         break;
      case 1668:
         if (inputsource.equals("48")) {
            var3 = 25;
         }
         break;
      case 1669:
         if (inputsource.equals("49")) {
            var3 = 11;
         }
         break;
      case 1691:
         if (inputsource.equals("50")) {
            var3 = 15;
         }
         break;
      case 1692:
         if (inputsource.equals("51")) {
            var3 = 12;
         }
         break;
      case 1693:
         if (inputsource.equals("52")) {
            var3 = 16;
         }
         break;
      case 1726:
         if (inputsource.equals("64")) {
            var3 = 19;
         }
         break;
      case 1784:
         if (inputsource.equals("80")) {
            var3 = 8;
         }
         break;
      case 1789:
         if (inputsource.equals("85")) {
            var3 = 23;
         }
         break;
      case 1821:
         if (inputsource.equals("96")) {
            var3 = 7;
         }
         break;
      case 1822:
         if (inputsource.equals("97")) {
            var3 = 24;
         }
         break;
      case 1507423:
         if (inputsource.equals("1000")) {
            var3 = 26;
         }
      }

      switch(var3) {
      case 0:
         return "PC";
      case 1:
         return "BNC";
      case 2:
         return "DVI";
      case 3:
         return "AV";
      case 4:
         return "S-Video";
      case 5:
         return "Component";
      case 6:
         return "MagicInfo";
      case 7:
         return "MagicInfo-S";
      case 8:
         return "PlugInModule";
      case 9:
         return "HDMI1";
      case 10:
         return "HDMI2";
      case 11:
         return "HDMI3";
      case 12:
         return "HDMI4";
      case 13:
         return "HDMI1_PC";
      case 14:
         return "HDMI2_PC";
      case 15:
         return "HDMI3_PC";
      case 16:
         return "HDMI4_PC";
      case 17:
         return "Display Port";
      case 18:
         return "Display Port2";
      case 19:
         return "DTV";
      case 20:
         return "DVI_VIDEO";
      case 21:
         return "AV2";
      case 22:
         return "Ext";
      case 23:
         return "HDBaseT";
      case 24:
         return "WiFi";
      case 25:
         return "ATV";
      case 26:
         return "PanelOff";
      default:
         return "";
      }
   }

   public String getColor(int index) {
      String[] colors = new String[]{"#80cbff", "#6ee6a9", "#ff92b1", "#b22222", "#ff8c00", "#7B68EE"};
      if (index >= colors.length) {
         index %= colors.length;
      }

      return colors[index];
   }

   public boolean checkInputSource(String inputsource) {
      byte var3 = -1;
      switch(inputsource.hashCode()) {
      case -1822785823:
         if (inputsource.equals("MagicInfo-S")) {
            var3 = 7;
         }
         break;
      case -912876477:
         if (inputsource.equals("DisplayPort")) {
            var3 = 17;
         }
         break;
      case -872551335:
         if (inputsource.equals("HDMI1_PC")) {
            var3 = 13;
         }
         break;
      case -872521544:
         if (inputsource.equals("HDMI2_PC")) {
            var3 = 14;
         }
         break;
      case -872491753:
         if (inputsource.equals("HDMI3_PC")) {
            var3 = 15;
         }
         break;
      case -872461962:
         if (inputsource.equals("HDMI4_PC")) {
            var3 = 16;
         }
         break;
      case -162790437:
         if (inputsource.equals("MagicInfo")) {
            var3 = 6;
         }
         break;
      case 2101:
         if (inputsource.equals("AV")) {
            var3 = 3;
         }
         break;
      case 2547:
         if (inputsource.equals("PC")) {
            var3 = 0;
         }
         break;
      case 65155:
         if (inputsource.equals("ATV")) {
            var3 = 19;
         }
         break;
      case 65181:
         if (inputsource.equals("AV2")) {
            var3 = 22;
         }
         break;
      case 65911:
         if (inputsource.equals("BNC")) {
            var3 = 1;
         }
         break;
      case 68038:
         if (inputsource.equals("DTV")) {
            var3 = 20;
         }
         break;
      case 68087:
         if (inputsource.equals("DVI")) {
            var3 = 2;
         }
         break;
      case 70145:
         if (inputsource.equals("Ext")) {
            var3 = 23;
         }
         break;
      case 2694997:
         if (inputsource.equals("WiFi")) {
            var3 = 25;
         }
         break;
      case 68595609:
         if (inputsource.equals("HDMI1")) {
            var3 = 9;
         }
         break;
      case 68595610:
         if (inputsource.equals("HDMI2")) {
            var3 = 10;
         }
         break;
      case 68595611:
         if (inputsource.equals("HDMI3")) {
            var3 = 11;
         }
         break;
      case 68595612:
         if (inputsource.equals("HDMI4")) {
            var3 = 12;
         }
         break;
      case 74707987:
         if (inputsource.equals("DVI_VIDEO")) {
            var3 = 21;
         }
         break;
      case 604060893:
         if (inputsource.equals("Component")) {
            var3 = 5;
         }
         break;
      case 1125196011:
         if (inputsource.equals("PanelOff")) {
            var3 = 26;
         }
         break;
      case 1486493703:
         if (inputsource.equals("HDBaseT")) {
            var3 = 24;
         }
         break;
      case 1760315327:
         if (inputsource.equals("PlugInModule")) {
            var3 = 8;
         }
         break;
      case 1765600335:
         if (inputsource.equals("DisplayPort2")) {
            var3 = 18;
         }
         break;
      case 2019323489:
         if (inputsource.equals("S-Video")) {
            var3 = 4;
         }
      }

      switch(var3) {
      case 0:
         return true;
      case 1:
         return true;
      case 2:
         return true;
      case 3:
         return true;
      case 4:
         return true;
      case 5:
         return true;
      case 6:
         return true;
      case 7:
         return true;
      case 8:
         return true;
      case 9:
         return true;
      case 10:
         return true;
      case 11:
         return true;
      case 12:
         return true;
      case 13:
         return true;
      case 14:
         return true;
      case 15:
         return true;
      case 16:
         return true;
      case 17:
         return true;
      case 18:
         return true;
      case 19:
         return true;
      case 20:
         return true;
      case 21:
         return true;
      case 22:
         return true;
      case 23:
         return true;
      case 24:
         return true;
      case 25:
         return true;
      case 26:
         return true;
      default:
         return false;
      }
   }
}
