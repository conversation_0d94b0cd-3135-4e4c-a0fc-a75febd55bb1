## generateLiteJavaVncViewer
##
<?xml version='1.0' encoding='UTF-8' ?>
<jnlp spec='1.0'
	codebase='${serverUrl}/servlet/'
	href='${JNLP_SERVLET}${jnlpFileName}'>
	<information>
		<title>JavaViewer</title>
		<vendor>MagicInfo</vendor>
	</information>
	<security>
		<all-permissions/>
	</security>
	<resources>
		<j2se version='1.6+' initial-heap-size="64m" max-heap-size="512m"/>
		<jar href='${serverUrl}/uploader/${urlToken}/javaViewer.jar' main='true' />
	</resources>
	<applet-desc name='JavaViewer' main-class='VncViewer' width='${browserWidth}' height='${browserHeight}'>
		<param name='HOST' value='ID:${deviceId}'/>
		<param name='PORT' value='5900'/>
		<param name='REPEATERHOST' value='${tunnelingIP}'/>
		<param name='REPEATERPORT' value='5901'/>
		<param name='Show Controls' value='No'/>
	</applet-desc>
</jnlp>

