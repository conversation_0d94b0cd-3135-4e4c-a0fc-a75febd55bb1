package com.samsung.magicinfo.ems.model;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Pattern;

public class EdgeDownloadFile {
   @ApiModelProperty(
      example = "a.jpg"
   )
   String fileName;
   @ApiModelProperty(
      example = "FTP"
   )
   String protocol;
   @ApiModelProperty(
      example = "00-00-00-00-00-00"
   )
   String deviceId;
   @ApiModelProperty(
      example = "START"
   )
   @Pattern(
      regexp = "START|FAULT|END",
      message = "START/FAULT/END are available."
   )
   String status;

   public EdgeDownloadFile() {
      super();
   }

   public String getFileName() {
      return this.fileName;
   }

   public void setFileName(String fileName) {
      this.fileName = fileName;
   }

   public String getProtocol() {
      return this.protocol;
   }

   public void setProtocol(String protocol) {
      this.protocol = protocol;
   }

   public String getDeviceId() {
      return this.deviceId;
   }

   public void setDeviceId(String deviceId) {
      this.deviceId = deviceId;
   }

   public String getStatus() {
      return this.status;
   }

   public void setStatus(String status) {
      this.status = status;
   }
}
