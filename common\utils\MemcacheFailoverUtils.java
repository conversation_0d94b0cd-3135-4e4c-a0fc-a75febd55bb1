package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import net.spy.memcached.CASResponse;
import org.apache.logging.log4j.Logger;

public class MemcacheFailoverUtils {
   static Logger logger = LoggingManagerV2.getLogger(MemcacheFailoverUtils.class);
   public static final int INITIAL_CNT = 0;
   public static int MAXIMUM_CNT = 10;

   public MemcacheFailoverUtils() {
      super();
   }

   public static boolean isOKCASResponse(CASResponse casResponse, int currentRetryCnt) {
      try {
         if (CommonConfig.get("cache.type").equalsIgnoreCase("jnlp")) {
            return true;
         }
      } catch (ConfigException var6) {
         return true;
      }

      if (casResponse == CASResponse.OK) {
         return true;
      } else {
         StackTraceElement[] stackTraceElementArray;
         if (currentRetryCnt < MAXIMUM_CNT) {
            logger.info("[MagicInfo_Cached] CASResponse failed in enqueue , retry... " + currentRetryCnt);

            try {
               stackTraceElementArray = Thread.currentThread().getStackTrace();
               logger.info("[MagicInfo_Cached] Retrying method name : " + stackTraceElementArray[2].getClassName() + "." + stackTraceElementArray[2].getMethodName() + "() line:" + stackTraceElementArray[2].getLineNumber());
            } catch (Exception var4) {
            }

            try {
               Thread.sleep(10L);
            } catch (InterruptedException var3) {
               logger.error("", var3);
            }

            return false;
         } else {
            logger.fatal("[MagicInfo_Cached] Critical exception during check and store data in memory");

            try {
               stackTraceElementArray = Thread.currentThread().getStackTrace();
               logger.fatal("[MagicInfo_Cached] Retrying method name : " + stackTraceElementArray[2].getClassName() + "." + stackTraceElementArray[2].getMethodName() + "() line:" + stackTraceElementArray[2].getLineNumber());
            } catch (Exception var5) {
               logger.error("", var5);
            }

            return true;
         }
      }
   }
}
