<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.samsung.common.utils.SequenceDBMapper">


	<select id="getCurrentValue" resultType="int">
		SELECT 
			SEQUENCE_VALUE 
		FROM 
			MI_SYSTEM_INFO_SEQUENCE 
		WHERE 
			SEQUENCE_NAME = #{sequenceName}
	</select>
	
	<insert id="createSequence">
		INSERT INTO 
			MI_SYSTEM_INFO_SEQUENCE(sequence_name, sequence_value)
		VALUES
			(#{sequenceName}, #{initialValue})
	</insert>
	
	<update id="incrementValue">
		UPDATE
			MI_SYSTEM_INFO_SEQUENCE
		SET
			sequence_value = sequence_value + #{incrementBy}
		WHERE
			sequence_name = #{sequenceName}
	</update>
	
	

</mapper>