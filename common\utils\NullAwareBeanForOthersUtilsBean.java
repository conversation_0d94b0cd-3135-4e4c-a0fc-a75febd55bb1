package com.samsung.common.utils;

import java.lang.reflect.InvocationTargetException;
import org.apache.commons.beanutils.BeanUtilsBean;

public class NullAwareBeanForOthersUtilsBean extends BeanUtilsBean {
   public NullAwareBeanForOthersUtilsBean() {
      super();
   }

   public void copyProperty(Object bean, String name, Object value) throws IllegalAccessException, InvocationTargetException {
      if (value != null) {
         if (!name.equalsIgnoreCase("serialVersionUID")) {
            if (!name.equalsIgnoreCase("mdc_update_time")) {
               super.copyProperty(bean, name, value);
            }
         }
      }
   }
}
