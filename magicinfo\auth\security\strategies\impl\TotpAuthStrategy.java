package com.samsung.magicinfo.auth.security.strategies.impl;

import com.samsung.common.cache.CacheFactory;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.auth.security.otp.OTPAuthType;
import com.samsung.magicinfo.auth.security.strategies.AuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.Strategy;
import com.samsung.magicinfo.auth.security.strategies.model.AuthModel;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserContractManager;
import com.samsung.magicinfo.framework.user.manager.UserContractManagerImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.warrenstrange.googleauth.GoogleAuthenticator;
import com.warrenstrange.googleauth.GoogleAuthenticatorKey;
import com.warrenstrange.googleauth.GoogleAuthenticatorQRGenerator;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.apache.logging.log4j.Logger;

@Strategy(
   type = AuthStrategy.class
)
public class TotpAuthStrategy implements AuthStrategy {
   static Logger logger = LoggingManagerV2.getLogger(TotpAuthStrategy.class);

   public TotpAuthStrategy() {
      super();
   }

   public static GoogleAuthenticator getInstance() {
      return TotpAuthStrategy.GoogleAuthSingleton.instance;
   }

   public AuthModel init(AuthModel authModel) {
      try {
         GoogleAuthenticator gAuth = getInstance();
         GoogleAuthenticatorKey key = gAuth.createCredentials();
         authModel.setSecretKey(key.getKey());
         OTPAuthType otpAuthType = new OTPAuthType();
         otpAuthType.setSecretKey(authModel.getSecretKey());
         String QRCode = GoogleAuthenticatorQRGenerator.getOtpAuthTotpURL("MagicINFO", authModel.getUserId(), key);
         otpAuthType.setQrCode(QRCode);
         authModel.setOtpAuthType(otpAuthType);
         CacheFactory.getCache().set("INIT_TOTP" + authModel.getUserId(), authModel);
         return authModel;
      } catch (Exception var6) {
         logger.error(var6.getMessage());
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOTP_INVALID);
      }
   }

   public AuthModel active(AuthModel authModel) {
      try {
         AuthModel initAuthModel = (AuthModel)CacheFactory.getCache().get("INIT_TOTP" + authModel.getUserId());
         GoogleAuthenticator gAuth = getInstance();
         if (!gAuth.authorize(initAuthModel.getSecretKey(), authModel.getOtp())) {
            Map details = new HashMap();
            UserContractManager userContractManager = UserContractManagerImpl.getInstance();
            if (userContractManager.isBlockLogin(initAuthModel.addLoginFailCount(), initAuthModel.getLastLoginDate(), "OTP_APP_WARNING")) {
               Date current = new Date();
               details.put("timeZone", (new SimpleDateFormat("Z")).format(current));
               details.put("timestamp", current.getTime());
               throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOTP_WARNING, details);
            } else {
               details.put("failCount", initAuthModel.getLoginFailCount() + "");
               details.put("retryCount", userContractManager.getMaxRetryCount());
               throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOTP_INVALID, details);
            }
         } else {
            UserInfo userInfo = UserInfoImpl.getInstance();
            userInfo.setUserSecretKey(authModel.getUserId(), initAuthModel.getSecretKey());
            CacheFactory.getCache().delete("INIT_TOTP" + authModel.getUserId());
            return null;
         }
      } catch (RestServiceException var7) {
         logger.error(var7.getMessage());
         throw var7;
      } catch (Exception var8) {
         logger.error(var8.getMessage());
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOTP_INVALID);
      }
   }

   public boolean valid(AuthModel authModel) {
      GoogleAuthenticator gAuth = getInstance();
      UserInfo userInfo = UserInfoImpl.getInstance();
      UserContractManager userContractManager = UserContractManagerImpl.getInstance();
      Map userFailInfoMap = null;

      HashMap details;
      try {
         userFailInfoMap = userContractManager.getUserFailInfo(authModel.getUserId(), "OTP_APP");
         if (!userContractManager.checkUserFail(authModel.getUserId(), "OTP_APP", userFailInfoMap)) {
            details = new HashMap();
            details.put("retryCount", userContractManager.getMaxRetryCount());
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOTP_LOCK, details);
         }

         User user = userInfo.getUserInfo(authModel.getUserId());
         authModel.setSecretKey(user.getSecret_key());
      } catch (RestServiceException var11) {
         throw var11;
      } catch (Exception var12) {
         logger.error(var12.getMessage());
         throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOTP_INVALID);
      }

      if (!gAuth.authorize(authModel.getSecretKey(), authModel.getOtp())) {
         try {
            userContractManager.addUserFailCount(authModel.getUserId(), "OTP_APP");
            userFailInfoMap = userContractManager.getUserFailInfo(authModel.getUserId(), "OTP_APP");
            if (!userContractManager.checkUserFail(authModel.getUserId(), "OTP_APP", userFailInfoMap)) {
               details = new HashMap();
               details.put("retryCount", userContractManager.getMaxRetryCount());
               throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOTP_LOCK, details);
            } else {
               details = new HashMap();
               if (userFailInfoMap.containsKey("login_count")) {
                  long count = (Long)userFailInfoMap.get("login_count");
                  details.put("failCount", count + "");
                  details.put("retryCount", userContractManager.getMaxRetryCount());
               }

               throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOTP_INVALID, details);
            }
         } catch (RestServiceException var9) {
            throw var9;
         } catch (Exception var10) {
            logger.error(var10.getMessage());
            throw new RestServiceException(RestExceptionCode.UNAUTHORIZED_TOTP_INVALID);
         }
      } else {
         return true;
      }
   }

   private static class GoogleAuthSingleton {
      private static final GoogleAuthenticator instance = new GoogleAuthenticator();

      private GoogleAuthSingleton() {
         super();
      }
   }
}
