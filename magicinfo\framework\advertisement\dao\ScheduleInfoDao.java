package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.schedule.ScheduleInfoEntity;
import java.sql.Connection;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class ScheduleInfoDao {
   Logger logger = LoggingManagerV2.getLogger(ScheduleInfoDao.class);

   public ScheduleInfoDao() {
      super();
   }

   public ScheduleInfoDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public List getMyScheduleList(String user_id) {
      throw new UnsupportedOperationException("The ScheduleInfoDao#getMyScheduleList method is not supported anymore. The table MI_AD_SCHEDULE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public ScheduleInfoEntity getScheduleListDetailInfo(String schedule_id) {
      throw new UnsupportedOperationException("The ScheduleInfoDao#getScheduleListDetailInfo method is not supported anymore. The table MI_AD_SCHEDULE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public ScheduleInfoEntity getApprovedScheduleListDetailInfo(String schedule_id) {
      throw new UnsupportedOperationException("The ScheduleInfoDao#getApprovedScheduleListDetailInfo method is not supported anymore. The table MI_AD_SCHEDULE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getMyApprovedScheduleList(String user_id) {
      throw new UnsupportedOperationException("The ScheduleInfoDao#getMyApprovedScheduleList method is not supported anymore. The table MI_AD_SCHEDULE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean setScheduleStatusToCancel(String user_id, String schedule_id) {
      throw new UnsupportedOperationException("The ScheduleInfoDao#setScheduleStatusToCancel method is not supported anymore. The table MI_AD_SCHEDULE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean setScheduleStatusToRequestCancel(String user_id, String schedule_id) {
      throw new UnsupportedOperationException("The ScheduleInfoDao#setScheduleStatusToRequestCancel method is not supported anymore. The table MI_AD_SCHEDULE_INFO does not exists.");
   }
}
