package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.RequestUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.job.dao.JobDAO;
import com.samsung.magicinfo.framework.device.job.entity.JobEntity;
import com.samsung.magicinfo.framework.device.job.manager.JobManager;
import com.samsung.magicinfo.framework.device.job.manager.JobManagerImpl;
import com.samsung.magicinfo.protocol.file.FileLoaderServlet;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonUpdateResult;
import com.samsung.magicinfo.restapi.device.model.V2JobScheduleEditorJnlpStringBuilder;
import com.samsung.magicinfo.restapi.device.model.V2JobScheduleSaveAsJnlpStringBuilder;
import com.samsung.magicinfo.restapi.device.model.V2JobScheduleUploaderJnlpStringBuilder;
import com.samsung.magicinfo.restapi.device.model.V2RemoteJobResource;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;
import org.apache.xml.security.utils.Base64;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2RemoteJobService")
@Transactional
public class V2RemoteJobServiceImpl implements V2RemoteJobService {
   protected Logger logger = LoggingManagerV2.getLogger(V2RemoteJobServiceImpl.class);

   public V2RemoteJobServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource getRemoteJobList(int startIndex, int pageSize, String searchText, String sortColumn, String sortOrder) throws Exception {
      if (StrUtils.isNull(sortColumn)) {
         sortColumn = "create_date";
      }

      if (StrUtils.isNull(sortOrder)) {
         sortOrder = "desc";
      }

      if (StrUtils.isNull(searchText)) {
         searchText = "";
      }

      SelectCondition selectCondition = new SelectCondition();
      selectCondition.setSort_name(ConvertUtil.snakeCase(sortColumn));
      selectCondition.setOrder_dir(sortOrder);
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      selectCondition.setSrc_name(searchText);
      Map condition = new HashMap();
      condition.put("condition", selectCondition);
      JobDAO jobDAO = new JobDAO();
      PagedListInfo pagedListInfo = jobDAO.getJobList(condition, startIndex, pageSize);
      List jobResourceList = new ArrayList();
      this.makeJobResourceList(jobResourceList, pagedListInfo);
      V2PageResource newResource = V2PageResource.createPageResource(jobResourceList, pagedListInfo, pageSize);
      newResource.setStartIndex(startIndex);
      return newResource;
   }

   private void makeJobResourceList(List jobResourceList, PagedListInfo pagedListInfo) {
      long currTime = System.currentTimeMillis();
      List jobList = pagedListInfo.getPagedResultList();
      Iterator var6 = jobList.iterator();

      while(var6.hasNext()) {
         JobEntity job = (JobEntity)var6.next();
         boolean startIsNull = true;
         boolean stopIsNull = true;
         Calendar start_time = Calendar.getInstance();
         Calendar stop_time = Calendar.getInstance();
         if (StringUtils.isNotBlank(job.getStart_time())) {
            String[] timeStr = job.getStart_time().split(":");
            startIsNull = this.setJobTime(startIsNull, start_time, timeStr, job.getStart_date());
            stopIsNull = this.setJobTime(stopIsNull, stop_time, timeStr, job.getStop_date());
         }

         V2RemoteJobResource jobResource = new V2RemoteJobResource();
         this.setJobResource(jobResource, currTime, job, startIsNull, stopIsNull, start_time, stop_time);
         jobResourceList.add(jobResource);
      }

   }

   private void setJobResource(V2RemoteJobResource resource, long currTime, JobEntity job, boolean startIsNull, boolean stopIsNull, Calendar start_time, Calendar stop_time) {
      resource.setJobId(job.getJob_id());
      resource.setJobName(StrUtils.nvl(job.getJob_name()));
      resource.setJobType(this.convertJobType(job));
      resource.setRepeatType(this.makeRepeatType(job, this.convertPreRepeatType(job)));
      resource.setStartDate(this.makeStartDate(job));
      resource.setStartTime(job.getStart_time());
      resource.setDeviceCount(job.getDevice_count());
      resource.setUserId(job.getUser_id());
      resource.setIsCanceled(this.getMessageStatus(currTime, job, startIsNull, stopIsNull, start_time, stop_time));
      resource.setCreateDate(job.getCreate_date());
   }

   private boolean setJobTime(boolean startIsNull, Calendar time, String[] timeStr, String date) {
      if (null != date && !"".equals(date)) {
         String[] startDateStr = date.split("-");
         time.clear();
         time.set(1, Integer.parseInt(startDateStr[0]));
         time.set(2, Integer.parseInt(startDateStr[1]) - 1);
         time.set(5, Integer.parseInt(startDateStr[2]));
         time.set(11, Integer.parseInt(timeStr[0]));
         time.set(12, Integer.parseInt(timeStr[1]));
         time.set(13, Integer.parseInt(timeStr[2]));
         return false;
      } else {
         return startIsNull;
      }
   }

   private String makeRepeatType(JobEntity job, String repeatType) {
      String jobRepeatType = job.getRepeat_type();
      return !"immediately".equals(jobRepeatType) && !"once".equals(jobRepeatType) && !"daily".equals(jobRepeatType) ? repeatType + "(" + StrUtils.nvl(job.getMonthdays()) + StrUtils.nvl(job.getWeekdays()) + ")" : repeatType;
   }

   private String makeStartDate(JobEntity job) {
      String jobRepeatType = job.getRepeat_type();
      return !"immediately".equals(jobRepeatType) && !"once".equals(jobRepeatType) ? job.getStart_date() + " ~ " + job.getStop_date() : job.getStart_date();
   }

   private String convertPreRepeatType(JobEntity job) {
      String jobRepeatType = job.getRepeat_type();
      if ("immediately".equals(jobRepeatType)) {
         return "Immediately";
      } else if ("once".equals(jobRepeatType)) {
         return "Once";
      } else if ("daily".equals(jobRepeatType)) {
         return "Daily";
      } else if ("day_of_month".equals(jobRepeatType)) {
         return "Monthly";
      } else {
         return "day_of_week".equals(jobRepeatType) ? "Weekly" : "";
      }
   }

   private String convertJobType(JobEntity job) {
      String jobType = job.getJob_type();
      if ("command".equals(jobType)) {
         return "Launch Command";
      } else if ("delete".equals(jobType)) {
         return "Delete File/Folder";
      } else if ("destroy".equals(jobType)) {
         return "Close Window";
      } else if ("download".equals(jobType)) {
         return "Get File";
      } else if ("log_download".equals(jobType)) {
         return "Log Get File";
      } else if ("killprocess".equals(jobType)) {
         return "Kill Process";
      } else if ("reboot".equals(jobType)) {
         return "Reboot";
      } else if ("restart".equals(jobType)) {
         return "Restart";
      } else if ("service".equals(jobType)) {
         return "Service Management";
      } else {
         return "upload".equals(jobType) ? "Send File" : "";
      }
   }

   private String getMessageStatus(long currTime, JobEntity job, boolean startIsNull, boolean stopIsNull, Calendar startTime, Calendar stopTime) {
      String state1 = "Reservation";
      String state3 = "Completed";
      String state4 = "Cancel";
      if (job.isIs_canceled()) {
         return "Cancel";
      } else if (startIsNull) {
         return "Completed";
      } else if (!stopIsNull) {
         return stopTime.getTimeInMillis() > currTime ? "Reservation" : "Completed";
      } else {
         return startTime.getTimeInMillis() > currTime ? "Reservation" : "Completed";
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2CommonUpdateResult cancelJobs(V2CommonIds jobIds) throws Exception {
      List successList = new ArrayList();
      List failList = new ArrayList();
      JobManager jobMgr = JobManagerImpl.getInstance();
      Iterator var5 = jobIds.getIds().iterator();

      while(var5.hasNext()) {
         String jobId = (String)var5.next();

         try {
            if (!jobMgr.setJobCanceled(jobId)) {
               this.logger.error("jobId :[" + jobId + " ] Remote job cancel failed.");
               failList.add(jobId);
            } else {
               successList.add(jobId);
            }
         } catch (Exception var8) {
            this.logger.error(var8);
            failList.add(jobId);
         }
      }

      V2CommonUpdateResult resource = new V2CommonUpdateResult();
      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public void downloadToAddRemoteControl(HttpServletRequest request, HttpServletResponse response) throws Exception {
      String sessionId = request.getSession().getId();
      String jnlpFileName = this.makeJnlpFileName("job", sessionId);
      String topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + "jnlp" + File.separator;
      topPath = topPath.replace("\\", "/");
      FileUtils.deletePreviousJnlp(topPath);
      SecurityUtils.getSafeFile(topPath).mkdirs();
      File jnlpFile = SecurityUtils.getSafeFile(topPath + jnlpFileName);
      jnlpFile.createNewFile();
      V2JobScheduleUploaderJnlpStringBuilder jnlpStringBuilder = (new V2JobScheduleUploaderJnlpStringBuilder()).addServerUrl(RequestUtils.getWebUrl(request)).addUrlToken(Base64.encode(SecurityUtils.issueToken().getBytes())).addJnlpFilename(jnlpFileName).addLocale(new Locale("en", "US")).addSessionId(sessionId).addUserId(SecurityUtils.getLoginUserId()).addToken(SecurityUtils.issueToken()).addUserOrganization(SecurityUtils.getUserContainer().getUser().getOrganization()).addIsRootOrganization(SecurityUtils.getUserContainer().getUser().getOrganization().equals("ROOT"));
      this.writeToPrintWriter(jnlpFile, jnlpStringBuilder.build());
      this.downloadResponse(topPath, jnlpFileName, request, response);
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public void downloadToEditRemoteControlSettings(String jobId, HttpServletRequest request, HttpServletResponse response) throws Exception {
      String sessionId = request.getSession().getId();
      jobId = StrUtils.isNull(jobId) ? "" : jobId;
      String jnlpFileName = this.makeJnlpFileName("job_edit", sessionId);
      String topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + "jnlp" + File.separator;
      FileUtils.deletePreviousJnlp(topPath);
      SecurityUtils.getSafeFile(topPath).mkdirs();
      File jnlpFile = SecurityUtils.getSafeFile(topPath + jnlpFileName);
      jnlpFile.createNewFile();
      V2JobScheduleEditorJnlpStringBuilder jnlpBuilder = (new V2JobScheduleEditorJnlpStringBuilder()).addServerUrl(RequestUtils.getWebUrl(request)).addUrlToken(Base64.encode(SecurityUtils.issueToken().getBytes())).addJnlpFilename(jnlpFileName).addLocale(request.getLocale()).addSessionId(sessionId).addUserId(SecurityUtils.getLoginUserId()).addToken(SecurityUtils.issueToken()).addUserOrganization(SecurityUtils.getUserContainer().getUser().getOrganization()).addIsRootOrganization(SecurityUtils.getUserContainer().getUser().getOrganization().equals("ROOT")).addJobId(jobId);
      this.writeToPrintWriter(jnlpFile, jnlpBuilder.build());
      request.setAttribute("jnlpFileName", jnlpFileName);
      this.downloadResponse(topPath, jnlpFileName, request, response);
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public void downloadToReuseRemoteControl(String jobId, HttpServletRequest request, HttpServletResponse response) throws Exception {
      jobId = StrUtils.isNull(jobId) ? "" : jobId;
      String jnlpFileName = this.makeJnlpFileName("job_saveas", SecurityUtils.getLoginUserId());
      String topPath = CommonConfig.get("CONTENTS_HOME") + File.separator + "jnlp" + File.separator;
      SecurityUtils.getSafeFile(topPath).mkdirs();
      File jnlpFile = SecurityUtils.getSafeFile(topPath + jnlpFileName);
      jnlpFile.createNewFile();
      V2JobScheduleSaveAsJnlpStringBuilder jnlpBuilder = (new V2JobScheduleSaveAsJnlpStringBuilder()).addServerUrl(RequestUtils.getWebUrl(request)).addUrlToken(Base64.encode(SecurityUtils.issueToken().getBytes())).addJnlpFilename(jnlpFileName).addLocale(request.getLocale()).addSessionId(request.getSession().getId()).addUserId(SecurityUtils.getLoginUserId()).addToken(SecurityUtils.issueToken()).addUserOrganization(SecurityUtils.getUserContainer().getUser().getOrganization()).addIsRootOrganization(SecurityUtils.getUserContainer().getUser().getOrganization().equals("ROOT")).addJobId(jobId);
      this.writeToPrintWriter(jnlpFile, jnlpBuilder.build());
      request.setAttribute("jnlpFileName", jnlpFileName);
      this.downloadResponse(topPath, jnlpFileName, request, response);
   }

   private void downloadResponse(String topPath, String jnlpFileName, HttpServletRequest request, HttpServletResponse response) throws Exception {
      URLEncoder.encode(jnlpFileName, "UTF-8");
      response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(jnlpFileName, "UTF-8") + ";");
      response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
      int dotIdx = jnlpFileName.lastIndexOf(".");
      String extension = jnlpFileName.substring(dotIdx + 1);
      String contentType = FileLoaderServlet.getContentType(extension.toLowerCase());
      response.setContentType(contentType);
      String fullpath = topPath + "/" + jnlpFileName;
      fullpath = SecurityUtils.directoryTraversalChecker(fullpath, request.getRemoteAddr());
      File safeFile = SecurityUtils.getSafeFile(fullpath);
      if (0L != safeFile.length()) {
         BufferedOutputStream bufferedOutputStream = null;
         FileInputStream fileIS = null;
         FileChannel fileChannel = null;

         try {
            long fileOffsetLong = 0L;
            ByteBuffer buf = ByteBuffer.allocate(1024);
            bufferedOutputStream = new BufferedOutputStream(response.getOutputStream());
            fileIS = new FileInputStream(safeFile);

            int binaryRead;
            for(fileChannel = fileIS.getChannel(); -1 != (binaryRead = fileChannel.read(buf, fileOffsetLong)); fileOffsetLong += (long)binaryRead) {
               buf.flip();
               bufferedOutputStream.write(buf.array(), 0, binaryRead);
               buf.clear();
            }
         } catch (IOException var20) {
            this.logger.error(var20);
         } finally {
            if (null != bufferedOutputStream) {
               bufferedOutputStream.close();
            }

            if (null != fileChannel) {
               fileChannel.close();
            }

            if (null != fileIS) {
               fileIS.close();
            }

         }

      }
   }

   private String makeJnlpFileName(String jobType, String id) {
      String dateFormat = "yyyyMMddHHmmss";
      String fileType = ".jnlp";
      return jobType + "_" + id + "_" + DateUtils.date2String(SecurityUtils.getLoginUser().getCreate_date(), "yyyyMMddHHmmss") + ".jnlp";
   }

   private void writeToPrintWriter(File file, String outString) throws IOException {
      FileWriter fileWriter = new FileWriter(file);
      PrintWriter printWriter = new PrintWriter(fileWriter);
      printWriter.write(outString);
      printWriter.close();
      fileWriter.close();
   }
}
