package com.samsung.magicinfo.ems.model;

import io.swagger.annotations.ApiModelProperty;

public class EnvironmentPriorityFilter {
   @ApiModelProperty(
      example = "1"
   )
   private int signInfoDashboard = 0;
   @ApiModelProperty(
      example = "2"
   )
   private int noticeInfoDashboard = 0;
   @ApiModelProperty(
      example = "3"
   )
   private int contentInfoDashboard = 0;
   @ApiModelProperty(
      example = "4"
   )
   private int playlistInfoDashboard = 0;
   @ApiModelProperty(
      example = "5"
   )
   private int scheduleInfoDashboard = 0;
   @ApiModelProperty(
      example = "6"
   )
   private int deviceInfoDashboard = 0;
   @ApiModelProperty(
      example = "7"
   )
   private int userInfoDashboard = 0;
   @ApiModelProperty(
      example = "8"
   )
   private int storageInfoDashboard = 0;
   @ApiModelProperty(
      example = "9"
   )
   private int downloadServerDashBoard = 0;
   @ApiModelProperty(
      example = "10"
   )
   private int rmServerDashBoard = 0;
   @ApiModelProperty(
      example = "11"
   )
   private int datalinkServerDashBoard = 0;

   public EnvironmentPriorityFilter() {
      super();
   }

   public int getSignInfoDashboard() {
      return this.signInfoDashboard;
   }

   public void setSignInfoDashboard(int signInfoDashboard) {
      this.signInfoDashboard = signInfoDashboard;
   }

   public int getNoticeInfoDashboard() {
      return this.noticeInfoDashboard;
   }

   public void setNoticeInfoDashboard(int noticeInfoDashboard) {
      this.noticeInfoDashboard = noticeInfoDashboard;
   }

   public int getContentInfoDashboard() {
      return this.contentInfoDashboard;
   }

   public void setContentInfoDashboard(int contentInfoDashboard) {
      this.contentInfoDashboard = contentInfoDashboard;
   }

   public int getPlaylistInfoDashboard() {
      return this.playlistInfoDashboard;
   }

   public void setPlaylistInfoDashboard(int playlistInfoDashboard) {
      this.playlistInfoDashboard = playlistInfoDashboard;
   }

   public int getScheduleInfoDashboard() {
      return this.scheduleInfoDashboard;
   }

   public void setScheduleInfoDashboard(int scheduleInfoDashboard) {
      this.scheduleInfoDashboard = scheduleInfoDashboard;
   }

   public int getDeviceInfoDashboard() {
      return this.deviceInfoDashboard;
   }

   public void setDeviceInfoDashboard(int deviceInfoDashboard) {
      this.deviceInfoDashboard = deviceInfoDashboard;
   }

   public int getUserInfoDashboard() {
      return this.userInfoDashboard;
   }

   public void setUserInfoDashboard(int userInfoDashboard) {
      this.userInfoDashboard = userInfoDashboard;
   }

   public int getStorageInfoDashboard() {
      return this.storageInfoDashboard;
   }

   public void setStorageInfoDashboard(int storageInfoDashboard) {
      this.storageInfoDashboard = storageInfoDashboard;
   }

   public int getDownloadServerDashBoard() {
      return this.downloadServerDashBoard;
   }

   public void setDownloadServerDashBoard(int downloadServerDashBoard) {
      this.downloadServerDashBoard = downloadServerDashBoard;
   }

   public int getRmServerDashBoard() {
      return this.rmServerDashBoard;
   }

   public void setRmServerDashBoard(int rmServerDashBoard) {
      this.rmServerDashBoard = rmServerDashBoard;
   }

   public int getDatalinkServerDashBoard() {
      return this.datalinkServerDashBoard;
   }

   public void setDatalinkServerDashBoard(int datalinkServerDashBoard) {
      this.datalinkServerDashBoard = datalinkServerDashBoard;
   }
}
