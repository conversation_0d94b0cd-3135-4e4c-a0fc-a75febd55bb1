package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;

public class DaoTools {
   private static final String DATETIME_PATTERN = "[1-2]{1}[0-9]{3}[-/][0-1][0-9][-/][0-3][0-9](\\s[0-9]{2}:[0-9]{2}(:[0-9]{2}(\\.[0-9]?[0-9]?[0-9]?)?)?)?";
   private static final String TABLE_COLUMN_NAME_PATTERN = "[A-Za-z][A-Za-z0-9_\\.]*";
   private static final String NUMERIC_PATTERN = "-?[0-9]+(\\.[0-9]+)?";
   static Logger logger = LoggingManagerV2.getLogger(DaoTools.class);

   public DaoTools() {
      super();
   }

   public static int offsetStartPost(int startPos) {
      return startPos > 0 ? startPos - 1 : 0;
   }

   public static String toUpperCaseNullSafe(String input) {
      return input != null ? input.toUpperCase() : null;
   }

   public static String safeTableOrColumnName(String name) {
      if (StringUtils.isBlank(name)) {
         return "";
      } else if (name.matches("[A-Za-z][A-Za-z0-9_\\.]*")) {
         return name.toUpperCase();
      } else {
         throw new IllegalArgumentException("Invalid table/column name: " + name);
      }
   }

   public static String safeSortOrder(String order) {
      if (StringUtils.isBlank(order)) {
         return "";
      } else if ("ASC".equalsIgnoreCase(order)) {
         return "ASC";
      } else if ("DESC".equalsIgnoreCase(order)) {
         return "DESC";
      } else {
         logger.warn("Invalid value for order: " + order);
         return "";
      }
   }

   public static String safeDateTimeString(String datetime) {
      if (datetime != null && !isNullTextOrEmpty(datetime)) {
         if (datetime.matches("[1-2]{1}[0-9]{3}[-/][0-1][0-9][-/][0-3][0-9](\\s[0-9]{2}:[0-9]{2}(:[0-9]{2}(\\.[0-9]?[0-9]?[0-9]?)?)?)?")) {
            return datetime;
         } else {
            throw new IllegalArgumentException("Invalid date/time format: " + datetime);
         }
      } else {
         return datetime;
      }
   }

   public static Object safeNumeric(Object value) {
      if (value != null && !isNullTextOrEmpty(value)) {
         String number = String.valueOf(value);
         if (number.matches("-?[0-9]+(\\.[0-9]+)?")) {
            return number;
         } else {
            throw new IllegalArgumentException("Invalid number format: " + number);
         }
      } else {
         return value;
      }
   }

   private static boolean isNullTextOrEmpty(Object value) {
      String stringValue = String.valueOf(value);
      return "null".equalsIgnoreCase(stringValue) || "".equals(stringValue);
   }

   public static Object safeBoolean(Object bool) {
      if (bool == null) {
         return null;
      } else {
         String boolToString = String.valueOf(bool);
         if (!"true".equalsIgnoreCase(boolToString) && !"false".equalsIgnoreCase(boolToString) && !"1".equals(boolToString) && !"0".equals(boolToString) && !"NULL".equals(boolToString)) {
            throw new IllegalArgumentException("Invalid boolean value: " + bool);
         } else {
            return bool;
         }
      }
   }
}
