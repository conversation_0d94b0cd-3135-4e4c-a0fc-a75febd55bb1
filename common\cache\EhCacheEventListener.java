package com.samsung.common.cache;

import akka.actor.ActorRef;
import akka.actor.ActorSystem;
import akka.actor.Props;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.common.EhCacheEventActor;
import net.sf.ehcache.Ehcache;
import net.sf.ehcache.Element;
import net.sf.ehcache.event.CacheEventListener;
import org.apache.logging.log4j.Logger;

public class EhCacheEventListener implements CacheEventListener {
   Logger logger = LoggingManagerV2.getLogger(EhCacheEventListener.class);
   private ActorRef hello = null;

   public EhCacheEventListener() {
      super();
      ActorSystem system = ActorSystem.create();
      this.hello = system.actorOf(Props.create(EhCacheEventActor.class, new Object[0]));
   }

   public void notifyElementRemoved(Ehcache cache, Element element) throws net.sf.ehcache.CacheException {
      this.hello.tell("removed:" + element.getObjectKey(), (ActorRef)null);
   }

   public void notifyElementPut(Ehcache cache, Element element) throws net.sf.ehcache.CacheException {
      this.hello.tell("put:" + element.getObjectKey(), (ActorRef)null);
   }

   public void notifyElementUpdated(Ehcache cache, Element element) throws net.sf.ehcache.CacheException {
      this.hello.tell("updated:" + element.getObjectKey(), (ActorRef)null);
   }

   public void notifyElementExpired(Ehcache cache, Element element) {
      this.hello.tell("expired:" + element.getObjectKey(), (ActorRef)null);
   }

   public void notifyElementEvicted(Ehcache cache, Element element) {
      this.hello.tell("evicted:" + element.getObjectKey(), (ActorRef)null);
   }

   public void notifyRemoveAll(Ehcache cache) {
   }

   public void dispose() {
   }

   public Object clone() throws CloneNotSupportedException {
      return null;
   }
}
