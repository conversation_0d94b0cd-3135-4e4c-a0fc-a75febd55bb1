package com.samsung.magicinfo.ems.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.ems.model.EditTagInfoFilter;
import com.samsung.magicinfo.ems.model.EnvironmentNoticeFilter;
import com.samsung.magicinfo.ems.model.EnvironmentPriorityFilter;
import com.samsung.magicinfo.ems.model.TagResource;
import java.sql.SQLException;
import java.util.List;

public interface EnvironmentService {
   ResponseBody listDashboardInfo();

   ResponseBody createDashboard(String var1);

   ResponseBody deleteDashboard(String var1);

   ResponseBody updateDashboardPriority(EnvironmentPriorityFilter var1);

   ResponseBody listDashboardStorageInfo();

   ResponseBody listDashboardNoticeInfo();

   ResponseBody listEditNoticeInfo(String var1);

   ResponseBody createNoticeInfo(EnvironmentNoticeFilter var1, boolean var2);

   ResponseBody deleteNoticeInfo(String var1);

   ResponseBody listDashboardLoginInfo();

   ResponseBody listLicense();

   ResponseBody listMenu() throws Exception;

   ResponseBody getTagListInfo(TagResource var1) throws SQLException;

   ResponseBody getCategoryInfo(String var1) throws SQLException;

   ResponseBody checkLicenseStatus() throws SQLException;

   ResponseBody getLicenseCheckDetail() throws SQLException;

   ResponseBody getServerSetupInfo() throws SQLException;

   ResponseBody getUsedDeviceTypeInfoByLicense() throws SQLException;

   ResponseBody getMenuItemCount() throws SQLException;

   ResponseBody setFtpAccount(String var1, String var2, String var3) throws Exception;

   ResponseBody createTag(List var1) throws SQLException;

   ResponseBody getTagInfo(String var1) throws SQLException;

   ResponseBody editTagInfo(String var1, EditTagInfoFilter var2) throws Exception;

   ResponseBody deleteTagInfo(String var1) throws Exception;
}
