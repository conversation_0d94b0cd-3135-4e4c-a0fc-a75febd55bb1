package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;

public class DBUtils {
   public static final String SYSDATE = "SYSDATE ";
   public static final String NOW = "NOW() ";
   public static final String GETDATE = "GETDATE() ";
   public static final String CURRENT_TIMESTAMP = "CURRENT_TIMESTAMP ";
   public static final String NVL = "NVL";
   public static final String IFNULL = "IFNULL";
   public static final String ISNULL = "ISNULL";
   public static final String COALESCE = "COALESCE";
   public static final String CEIL = "CEIL";
   public static final String CEILING = "CEILING";
   public static final String CONCAT_ORACLE = "||";
   public static final String CONCAT_MSSQL = "+";
   public static final String CONCAT_MYSQL = ",";
   public static final String CONCAT_POSTGRESQL = "||";
   public static final String SUBSTR = "SUBSTR";
   public static final String SUBSTRING = "SUBSTRING";
   public static final String LPAD = "LPAD";
   public static final String REPLICATE = "REPLICATE";
   public static final String CAST = "CAST";
   public static final String CAST_ORACLE = " AS NUMBER";
   public static final String CAST_MSSQL = " AS NUMERIC";
   public static final String CAST_POSTGRESQL = " AS NUMERIC";
   public static final String CAST_MYSQL = " AS SIGNED";
   private static final String TO_CHAR_DATE_FORMAT_ORACLE = " TO_CHAR";
   private static final String TO_CHAR_DATE_FORMAT_POSTGRESQL = " TO_CHAR";
   private static final String TO_CHAR_DATE_FORMAT_MYSQL = " DATE_FORMAT";
   private static final String TO_CHAR_DATE_FORMAT_MSSQL = " REPLACE(CONVERT(VARCHAR";
   private static final String TO_CHAR_ORACLE = " TO_CHAR";
   private static final String TO_CHAR_POSTGRESQL = " CAST";
   private static final String TO_CHAR_MYSQL = " CAST";
   private static final String TO_CHAR_MSSQL = " CAST";
   private static final String TO_NUMBER_ORACLE = " TO_NUMBER";
   private static final String TO_NUMBER_POSTGRESQL = " CAST";
   private static final String TO_NUMBER_MYSQL = " CAST";
   private static final String TO_NUMBER_MSSQL = " CAST";
   private static final String TO_UPPER_ORACLE = " UPPER";
   private static final String TO_UPPER_POSTGRESQL = " UPPER";
   private static final String TO_UPPER_MYSQL = " UPPER";
   private static final String TO_UPPER_MSSQL = " UPPER";
   private static final String[] DATE_FORMAT_TYPE = new String[]{"YYYY-MM-DD", "HH24:MI", "YYYYMM", "YYYY-MM-DD HH24:MI:SS", "YYYYMMDD", "YYYY/MM/DD", "YYYY", "YYYYQ", "Q", "MM", "DD", "HH24", "MI", "YYYY-MM-DD HH24:MI"};
   private static final String[] DATE_FORMAT_ORACLE = new String[]{" 'YYYY-MM-DD' ", " 'HH24:MI' ", " 'YYYYMM' ", " 'YYYY-MM-DD HH24:MI:SS' ", " 'YYYYMMDD' ", " 'YYYY/MM/DD' ", " 'YYYY' ", " 'YYYYQ' ", " 'Q' ", " 'MM' ", " 'DD' ", " 'HH24' ", " 'MI' ", " 'YYYY-MM-DD HH24:MI' "};
   private static final String[] DATE_FORMAT_POSTGRESQL = new String[]{" 'YYYY-MM-DD' ", " 'HH24:MI' ", " 'YYYYMM' ", " 'YYYY-MM-DD HH24:MI:SS' ", " 'YYYYMMDD' ", " 'YYYY/MM/DD' ", " 'YYYY' ", " 'YYYYQ' ", " 'Q' ", " 'MM' ", " 'DD' ", " 'HH24' ", " 'MI' ", " 'YYYY-MM-DD HH24:MI' "};
   private static final String[] DATE_FORMAT_MYSQL = new String[]{" '%Y-%m-%d' ", " '%H:%i' ", " '%Y%m' ", " '%Y-%m-%d %H:%i:%s' ", " '%Y%m%d' ", " '%Y/%m/%d' ", " '%Y' ", " '%Y' ", "", " '%m' ", " '%d' ", " '%H' ", " '%i' ", " '%Y-%m-%d %H:%i' "};
   private static final String[] DATE_FORMAT_MSSQL = new String[]{" 120),'','' ", " 114),'','' ", " 120),'-','' ", " 120),'','' ", " 120),'-','' ", " 111),'',''", " 120),'','' ", " 120),'','' ", "", " 110),'','' ", " 106),'','' ", " 108),'','' ", " 108),':','' ", " 120),'','' "};
   private static final String[] DATE_FORMAT_LENGTH = new String[]{"10", "5", "7", "19", "10", "10", "4", "4", "", "2", "2", "2", "5", "16"};
   private static final String DATE_FORMAT_MI_START = "SUBSTRING( ";
   private static final String DATE_FORMAT_MI_END = " , 3, 2 )";

   public DBUtils() {
      super();
   }

   public static String currDate() throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return "SYSDATE ";
      } else if (strVendor.equals("MYSQL")) {
         return "NOW() ";
      } else if (strVendor.equals("MSSQL")) {
         return "GETDATE() ";
      } else if (strVendor.equals("POSTGRESQL")) {
         return "CURRENT_TIMESTAMP ";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String ifNull() throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return "NVL";
      } else if (strVendor.equals("MYSQL")) {
         return "IFNULL";
      } else if (strVendor.equals("MSSQL")) {
         return "ISNULL";
      } else if (strVendor.equals("POSTGRESQL")) {
         return "COALESCE";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String ceilToGetPages(String countData, int rows) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return "CEIL(" + countData + "/" + rows + ")";
      } else if (strVendor.equals("MYSQL")) {
         return "CEIL(" + countData + "/" + rows + ")";
      } else if (strVendor.equals("MSSQL")) {
         return "CAST(CEILING(CAST(" + countData + " as Float)/" + rows + ") as Integer) ";
      } else if (strVendor.equals("POSTGRESQL")) {
         return "CAST(CEIL(CAST (" + countData + " AS FLOAT)/" + rows + ") as INTEGER)";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String ceilToGetPages(String countData, String rows) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return "CEIL(" + countData + "/" + rows + ")";
      } else if (strVendor.equals("MYSQL")) {
         return "CEIL(" + countData + "/" + rows + ")";
      } else if (strVendor.equals("MSSQL")) {
         return " CAST(CEILING(CAST(" + countData + " as Float)/" + rows + ") as Integer) ";
      } else if (strVendor.equals("POSTGRESQL")) {
         return "CEIL(CAST (" + countData + " AS FLOAT)/" + rows + ")";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String concat(String[] str) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      String result = "";
      String operator;
      if (strVendor.equals("ORACLE")) {
         operator = "||";
      } else if (strVendor.equals("MYSQL")) {
         operator = ",";
      } else if (strVendor.equals("MSSQL")) {
         operator = "+";
      } else {
         if (!strVendor.equals("POSTGRESQL")) {
            throw new SQLException("Unsupported DB Vendor.");
         }

         operator = "||";
      }

      for(int index = 0; index < str.length; ++index) {
         result = result + str[index];
         if (index < str.length - 1) {
            result = result + operator;
         }
      }

      if (strVendor.equals("MYSQL")) {
         result = "CONCAT(" + result + ")";
      }

      return result;
   }

   public static String substr(String origin, String index) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return "SUBSTR(" + origin + "," + index + ")";
      } else if (strVendor.equals("MYSQL")) {
         return "SUBSTR(" + origin + "," + index + ")";
      } else if (strVendor.equals("POSTGRESQL")) {
         return "SUBSTRING(" + origin + " FROM " + index + ")";
      } else if (strVendor.equals("MSSQL")) {
         return "SUBSTRING(" + origin + "," + index + ", LEN(" + origin + "))";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String lpad(String origin, String length, String insert) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return "LPAD(" + origin + "," + length + "," + insert + ")";
      } else if (strVendor.equals("MYSQL")) {
         return "LPAD(" + origin + "," + length + "," + insert + ")";
      } else if (strVendor.equals("POSTGRESQL")) {
         return "LPAD(" + origin + "," + length + "," + insert + ")";
      } else if (strVendor.equals("MSSQL")) {
         return "REPLICATE(" + insert + "," + length + " - LEN(CAST(" + origin + " AS VARCHAR))) + CAST(" + origin + " AS VARCHAR)";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String toNumber(String argString) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return "CAST(" + argString + " AS NUMBER" + ")";
      } else if (strVendor.equals("MYSQL")) {
         return "CAST(" + argString + " AS SIGNED" + ")";
      } else if (strVendor.equals("MSSQL")) {
         return "CAST(" + argString + " AS NUMERIC" + ")";
      } else if (strVendor.equals("POSTGRESQL")) {
         return "CAST(" + argString + " AS NUMERIC" + ")";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String getFrontRowQuery(String baseQuery, int rowCount) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return "SELECT * FROM ( " + baseQuery + ") WHERE ROWNUM <= " + rowCount;
      } else if (strVendor.equals("MYSQL")) {
         return baseQuery + " LIMIT 0, " + rowCount;
      } else if (strVendor.equals("POSTGRESQL")) {
         return baseQuery + " LIMIT " + rowCount;
      } else if (strVendor.equals("MSSQL")) {
         baseQuery = baseQuery.replaceFirst("[sS][eE][lL][eE][cC][tT]", "SELECT TOP " + rowCount + " ");
         return baseQuery;
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String getPagedQuery(String baseQuery, String orderbyClause, int pageSize, int curpage) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      int end = curpage * pageSize;
      int start = end - pageSize + 1;
      if (strVendor.equals("ORACLE")) {
         return " SELECT T2.* FROM  (SELECT T1.*, CEIL(ROWNUM/" + pageSize + " ) AS PAGE FROM ( " + baseQuery + " " + orderbyClause + " ) T1 ) T2  WHERE PAGE = " + curpage;
      } else if (strVendor.equals("MYSQL")) {
         return baseQuery + " " + orderbyClause + " LIMIT " + (start - 1) + ", " + pageSize + " ";
      } else if (strVendor.equals("POSTGRESQL")) {
         return baseQuery + " " + orderbyClause + " LIMIT " + pageSize + " OFFSET " + (start - 1) + " ";
      } else if (strVendor.equals("MSSQL")) {
         return " SELECT T2.* FROM ( SELECT T1.*, ROW_NUMBER() OVER ( " + orderbyClause + " ) AS RN FROM( " + baseQuery + " )T1)T2  WHERE T2.RN BETWEEN " + start + " AND " + end;
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String convertToDateFormat(String argDate, String argDateFormat) throws ConfigException, SQLException {
      int dateFormat = getDateFormatNumber(argDateFormat);
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return " TO_CHAR(" + argDate + " , " + DATE_FORMAT_ORACLE[dateFormat] + ") ";
      } else if (strVendor.equals("POSTGRESQL")) {
         return " TO_CHAR(" + argDate + " , " + DATE_FORMAT_POSTGRESQL[dateFormat] + ") ";
      } else {
         String sql;
         if (strVendor.equals("MYSQL")) {
            sql = null;
            if (argDateFormat.equalsIgnoreCase("Q")) {
               return getQuarter(argDate);
            } else {
               sql = " DATE_FORMAT(" + argDate + " , " + DATE_FORMAT_MYSQL[dateFormat] + ") ";
               return argDateFormat.equalsIgnoreCase("YYYYQ") ? " CONCAT(" + sql + ", " + getQuarter(argDate) + ") " : sql;
            }
         } else if (strVendor.equals("MSSQL")) {
            sql = null;
            if (argDateFormat.equalsIgnoreCase("Q")) {
               return getQuarter(argDate);
            } else {
               sql = " REPLACE(CONVERT(VARCHAR(" + DATE_FORMAT_LENGTH[dateFormat] + "), " + argDate + " , " + DATE_FORMAT_MSSQL[dateFormat] + ") ";
               if (argDateFormat.equals("MI")) {
                  return "SUBSTRING( " + sql + " , 3, 2 )";
               } else {
                  return argDateFormat.equalsIgnoreCase("YYYYQ") ? sql + " + CAST(CAST(CONVERT(VARCHAR(2), " + argDate + ", 110) AS INT)/3 + 1 AS VARCHAR)" : sql;
               }
            }
         } else {
            throw new SQLException("Unsupported DB Vendor.");
         }
      }
   }

   private static String getQuarter(String argDate) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("MYSQL")) {
         return " QUARTER(" + argDate + ") ";
      } else if (strVendor.equals("MSSQL")) {
         return " CAST(CAST(CONVERT(VARCHAR(2), " + argDate + " , 110) AS INT)/3 + 1 AS VARCHAR) ";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   private static int getDateFormatNumber(String argDateFormat) throws ConfigException, SQLException {
      for(int i = 0; i < DATE_FORMAT_TYPE.length; ++i) {
         if (argDateFormat.equalsIgnoreCase(DATE_FORMAT_TYPE[i])) {
            return i;
         }
      }

      throw new SQLException("Unsupported Date Format.");
   }

   public static String convertToChar(String argString) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return " TO_CHAR(" + argString + ") ";
      } else if (strVendor.equals("MYSQL")) {
         return " CAST(" + argString + " AS CHAR) ";
      } else if (strVendor.equals("POSTGRESQL")) {
         return " CAST(" + argString + " AS VARCHAR) ";
      } else if (strVendor.equals("MSSQL")) {
         return " CAST(" + argString + " AS VARCHAR) ";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String convertToNumber(String argString) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return " TO_NUMBER(" + argString + ") ";
      } else if (strVendor.equals("MYSQL")) {
         return " CAST(" + argString + " AS SIGNED) ";
      } else if (strVendor.equals("POSTGRESQL")) {
         return " CAST(" + argString + " AS NUMERIC) ";
      } else if (strVendor.equals("MSSQL")) {
         return " CAST(" + argString + " AS NUMERIC) ";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }

   public static String convertToUpper(String argString) throws ConfigException, SQLException {
      String strVendor = CommonConfig.get("wsrm.dbVendor").toUpperCase();
      if (strVendor.equals("ORACLE")) {
         return " UPPER(" + argString + ") ";
      } else if (strVendor.equals("MYSQL")) {
         return " UPPER(" + argString + ") ";
      } else if (strVendor.equals("POSTGRESQL")) {
         return " UPPER(" + argString + ") ";
      } else if (strVendor.equals("MSSQL")) {
         return " UPPER(" + argString + ") ";
      } else {
         throw new SQLException("Unsupported DB Vendor.");
      }
   }
}
