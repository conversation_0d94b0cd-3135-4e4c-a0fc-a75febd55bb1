package com.samsung.magicinfo.exception;

import com.samsung.common.exception.BasicException;

public class InvalidMOFormatException extends BasicException {
   private static final long serialVersionUID = -334976470046475235L;

   public InvalidMOFormatException() {
      super();
   }

   public InvalidMOFormatException(String message) {
      super(message);
   }

   public InvalidMOFormatException(Throwable cause) {
      super(cause);
   }

   public InvalidMOFormatException(String message, Throwable cause) {
      super(message, cause);
   }

   public InvalidMOFormatException(String code, String subcode, String reason) {
      super(code, subcode, reason);
   }
}
