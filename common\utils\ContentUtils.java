package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.constants.CommonDataConstants;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.PlaylistContent;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfo;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.ScheduleInfoDAO;
import com.samsung.magicinfo.framework.scheduler.entity.DownloadContentEntity;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfo;
import com.samsung.magicinfo.framework.scheduler.manager.ScheduleInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

public class ContentUtils {
   static Logger logger = LoggingManagerV2.getLogger(ContentUtils.class);
   public static List fexts = Arrays.asList("gif", "jpeg", "jpg", "png", "swf", "bmp", "asf", "avi", "mpeg", "mpg", "ts", "trp", "m2v", "m2p", "mp4", "m1v", "m4v", "m4t", "vob", "m2t", "tsp", "mov", "asx", "wmv", "tp", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "htm", "html", "pps", "ppsx", "pdf", "mp3", "ogg", "wav", "wma", "mp2", "ac3", "pcm", "lpcm", "flv", "wmf", "emf", "tif", "tiff", "mid", "mkv", "ra", "rm", "ram", "rmvb", "3gp", "svi", "m2ts", "divx", "mts", "vro", "zip", "xml", "wgt", "aisr");
   public static final List EXECUTABLE_EXTENSIONS = Arrays.asList("exe", "bat", "sh", "jsp", "jspx", "asp", "php", "mht", "ps1", "vbs", "dll", "php5", "pht", "phtml", "shtml", "asa", "asax", "swf", "xap");
   private static String contentsHome = "";
   private static String thumbnailHome = "";

   public ContentUtils() {
      super();
   }

   public static Long getPlayTimeStr(String play_time) {
      Long ret = ContentConstants.CONTENT_DURATION;
      int pos = play_time.indexOf(":");
      if (pos > 0) {
         int len = play_time.length();
         int pos2 = play_time.lastIndexOf(":");
         String hour = play_time.substring(0, pos);
         String minute = play_time.substring(pos + 1, pos2);
         String second = play_time.substring(pos2 + 1, len);
         ret = (long)(Integer.parseInt(hour) * 60 * 60 + Integer.parseInt(minute) * 60 + Integer.parseInt(second));
      }

      return ret;
   }

   public static boolean supportContentType(String fext) {
      return fexts.contains(fext.toLowerCase());
   }

   public static boolean filenameHasExecutableType(String filename) {
      String extension = FilenameUtils.getExtension(filename).toLowerCase();
      return EXECUTABLE_EXTENSIONS.contains(extension);
   }

   public static Long getPlayTimeStr_lite(String play_time) {
      Long ret = ContentConstants.LITE_CONTENT_DURATION;
      int pos = play_time.indexOf(":");
      if (pos > 0) {
         int len = play_time.length();
         int pos2 = play_time.lastIndexOf(":");
         String hour = play_time.substring(0, pos);
         String minute = play_time.substring(pos + 1, pos2);
         String second = play_time.substring(pos2 + 1, len);
         ret = (long)(Integer.parseInt(hour) * 60 * 60 + Integer.parseInt(minute) * 60 + Integer.parseInt(second));
      }

      return ret;
   }

   public static String getPlayTimeFormattedStr(Long play_time) {
      int hours = 0;
      int minutes = 0;
      int seconds = 0;
      if (play_time != null && play_time > 0L) {
         hours = (int)(play_time / 3600L);
         int tmp1 = (int)(play_time % 3600L);
         minutes = tmp1 / 60;
         seconds = tmp1 % 60;
      }

      return zeroPlus(hours) + ":" + zeroPlus(minutes) + ":" + zeroPlus(seconds);
   }

   public static String zeroPlus(int v) {
      return v < 10 ? "0" + v : String.valueOf(v);
   }

   public static boolean checkPlaylistContent(String mediaType) {
      boolean result = false;
      ArrayList exceptMediaTypeList = new ArrayList();
      exceptMediaTypeList.add("VWL");
      exceptMediaTypeList.add("LFT");
      exceptMediaTypeList.add("SOUND");
      if (!exceptMediaTypeList.contains(mediaType)) {
         result = true;
      }

      return result;
   }

   public static boolean checkVwlPlaylistContent(String mediaType) {
      boolean result = false;
      ArrayList exceptMediaTypeList = new ArrayList();
      exceptMediaTypeList.add("IMAGE");
      exceptMediaTypeList.add("MOVIE");
      exceptMediaTypeList.add("VWL");
      exceptMediaTypeList.add("LFT");
      exceptMediaTypeList.add("SOUND");
      if (!exceptMediaTypeList.contains(mediaType)) {
         result = true;
      }

      return result;
   }

   public static boolean nvl(Boolean bool) {
      return bool == null ? false : bool;
   }

   public static Content addNewContent(String newFileName, String contentId, String newContentFileId, Content dlkContent, UserContainer userContainer) {
      File file = SecurityUtils.getSafeFile(newFileName);
      long filesize = file.length();
      ContentInfoImpl contentInfo = ContentInfoImpl.getInstance();

      try {
         ContentFile contentFile = contentInfo.getMainFileInfo(contentId);
         contentFile.setFile_id(newContentFileId);
         if (userContainer != null) {
            contentFile.setCreator_id(userContainer.getUser().getUser_id());
         }

         dlkContent.setMain_file_id(newContentFileId);
         contentFile.setFile_size(filesize);
         contentFile.setHash_code(getHash(file));
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         String newDlkFilePath = CONTENTS_HOME + File.separator + newContentFileId;
         contentFile.setFile_path(newDlkFilePath);
         List fileList = new ArrayList();
         fileList.add(contentFile);
         dlkContent.setArr_file_list(fileList);
         String contentOrgCreatorId = contentInfo.getContentOrgCreatorId(contentId);
         if (contentOrgCreatorId != null) {
            dlkContent.setOrg_creator_id(contentOrgCreatorId);
         } else {
            dlkContent.setOrg_creator_id(dlkContent.getCreator_id());
         }

         if (userContainer != null) {
            dlkContent.setOrganization_id(userContainer.getUser().getRoot_group_id());
         }
      } catch (SQLException var14) {
         logger.error("", var14);
      } catch (ConfigException var15) {
         logger.error("", var15);
      } catch (Exception var16) {
         logger.error("", var16);
      }

      return dlkContent;
   }

   public static String getFolderIp(String ip) {
      String folderIp = ip.replace('.', '_');
      folderIp = folderIp.replace(':', '_');
      return folderIp;
   }

   public static List getAdContentList(String programId) {
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();

      List result;
      try {
         Calendar c = Calendar.getInstance();
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         String stopDate = sdf.format(c.getTime());
         result = scheduleInfo.getContentListInADScheduleWithStopDate(programId, stopDate);
      } catch (Exception var6) {
         logger.error("[MagicInfo_ContentUtils] fail get ad content list e : " + var6.getMessage(), var6);
         result = null;
      }

      return result;
   }

   public static List getContentList(String programId) {
      List result = null;
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();

      try {
         new Date();
         Calendar c = Calendar.getInstance();
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         String stopDate = sdf.format(c.getTime());
         result = scheduleInfo.getContentListInScheduleWithStopDate(programId, stopDate);
         if (result != null) {
            int index = 0;

            label99:
            while(true) {
               while(true) {
                  if (index >= ((List)result).size()) {
                     break label99;
                  }

                  String str = (String)((List)result).get(index);
                  if (str != null && !str.trim().equals("")) {
                     ++index;
                  } else {
                     ((List)result).remove(index);
                  }
               }
            }
         }

         List tagPlaylists = scheduleInfo.getTagPlaylistIdVersion(programId);
         if (tagPlaylists != null && tagPlaylists.size() > 0) {
            PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
            Iterator var9 = tagPlaylists.iterator();

            label84:
            while(true) {
               String playlistId;
               long versionId;
               List tagList;
               do {
                  do {
                     Map map;
                     do {
                        do {
                           if (!var9.hasNext()) {
                              return (List)result;
                           }

                           map = (Map)var9.next();
                        } while(map.get("playlist_id") == null);
                     } while(map.get("version_id") == null);

                     playlistId = (String)map.get("playlist_id");
                     versionId = (Long)map.get("version_id");
                     tagList = pInfo.getTagListWithExpiredDate(playlistId, versionId, stopDate);
                  } while(tagList == null);
               } while(tagList.size() <= 0);

               Iterator var15 = tagList.iterator();

               while(true) {
                  List contentIds;
                  do {
                     do {
                        if (!var15.hasNext()) {
                           continue label84;
                        }

                        PlaylistContent playlistContent = (PlaylistContent)var15.next();
                        contentIds = null;
                        if (playlistContent.getTag_type() == 1L) {
                           contentIds = pInfo.getTagConditionWithTagIdList(playlistId, versionId, playlistContent.getTag_id(), playlistContent.getNumber_str());
                        } else {
                           contentIds = pInfo.getTagConditionWithTagIdList(playlistId, versionId, playlistContent.getTag_id());
                        }
                     } while(contentIds == null);
                  } while(contentIds.size() <= 0);

                  if (result == null) {
                     result = new ArrayList();
                  }

                  Iterator var18 = contentIds.iterator();

                  while(var18.hasNext()) {
                     Content content = (Content)var18.next();
                     if (!((List)result).contains(content.getContent_id())) {
                        ((List)result).add(content.getContent_id());
                     }
                  }
               }
            }
         }
      } catch (Exception var20) {
         logger.error("[MagicInfo_ContentUtils] fail get content list e : " + var20.getMessage(), var20);
         result = null;
      }

      return (List)result;
   }

   public static List getContentList(String programId, String deviceId, Timestamp programListDeploy_Date) {
      List result = new ArrayList();
      ScheduleInfoDAO dao = new ScheduleInfoDAO();
      Map condition = new HashMap();
      condition.put("program_id", programId);
      condition.put("device_id", deviceId);
      SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
      if (programListDeploy_Date != null) {
         String currDateStr = sf.format(programListDeploy_Date);
         condition.put("stop_date", currDateStr);
      }

      try {
         PagedListInfo pageListInfo = dao.getDownloadContentPagedListForContentSchedule(0, 10000, condition);
         DownloadContentEntity downloadContentEntity = null;

         for(int i = 0; i < pageListInfo.getTotalRowCount(); ++i) {
            downloadContentEntity = (DownloadContentEntity)pageListInfo.getPagedResultList().get(i);
            result.add(downloadContentEntity.getContent_id());
         }
      } catch (SQLException var10) {
         logger.error("", var10);
      }

      if (result.size() == 0) {
         logger.error("publish status programId=" + programId + ",deviceId=" + deviceId + ",contentCount=0");
      }

      return result;
   }

   public static List getFrameContentList(String programId) {
      List result = null;
      ScheduleInfo scheduleInfo = ScheduleInfoImpl.getInstance();

      try {
         new Date();
         Calendar c = Calendar.getInstance();
         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
         sdf.format(c.getTime());
         result = scheduleInfo.getFrameContentList(programId);
         if (result != null) {
            int index = 0;

            while(true) {
               while(index < result.size()) {
                  String str = (String)result.get(0);
                  if (str != null && !str.trim().equals("")) {
                     ++index;
                  } else {
                     result.remove(index);
                  }
               }

               return result;
            }
         }
      } catch (Exception var9) {
         logger.error("[MagicInfo_ContentUtils] fail get content list e : " + var9.getMessage(), var9);
         result = null;
      }

      return result;
   }

   public static Map getContentDeviceTypeAndVersion(Content content) throws SQLException {
      ContentCodeInfo codeInfo = ContentCodeInfoImpl.getInstance();
      long minpriority = 9999L;
      HashMap result = new HashMap();
      List fileTypeList = null;
      String deviceType = "SPLAYER";
      float deviceTypeVersion = CommonDataConstants.TYPE_VERSION_1_0;
      fileTypeList = codeInfo.getFileTypeListByFileType(content.getMain_file_Extension(), content.getMedia_type());
      if (fileTypeList != null) {
         for(int idx = 0; idx < fileTypeList.size(); ++idx) {
            Map map = (Map)fileTypeList.get(idx);
            String tmpDeviceType = (String)map.get("device_type");
            float tmpDeviceTypeVersion = Float.parseFloat(map.get("device_type_version").toString());
            long tmpPriority = Long.parseLong(map.get("priority").toString());
            if (tmpPriority >= 0L && minpriority > tmpPriority) {
               minpriority = tmpPriority;
               deviceType = tmpDeviceType;
               deviceTypeVersion = tmpDeviceTypeVersion;
            }
         }
      }

      result.put("deviceType", deviceType);
      result.put("deviceTypeVersion", deviceTypeVersion);
      return result;
   }

   public static boolean deleteDirectoryRecursive(File filePath) {
      if (filePath == null) {
         return false;
      } else {
         if (filePath.exists()) {
            File[] files = filePath.listFiles();
            if (files == null) {
               return false;
            }

            for(int i = 0; i < files.length; ++i) {
               if (files[i] == null) {
                  return false;
               }

               if (files[i].isDirectory()) {
                  deleteDirectoryRecursive(files[i]);
               } else {
                  files[i].delete();
               }
            }
         }

         return filePath.delete();
      }
   }

   public static Long getContentDuration(Content content, int beforeOrder, String selEffectList, List contentList, String productType, String deviceType) {
      Long content_duration = 0L;
      if (content != null && content.getPlay_time() != null && !content.getPlay_time().equals("-") && !content.getPlay_time().equals("")) {
         if (productType.equalsIgnoreCase("PREMIUM")) {
            content_duration = getPlayTimeStr(content.getPlay_time());
         }

         return content_duration;
      } else {
         Map contentDurationMap = getContentDurationByEffectList(selEffectList);
         if (contentDurationMap.containsKey(beforeOrder + "")) {
            content_duration = Long.valueOf((String)contentDurationMap.get(beforeOrder + ""));
            return content_duration;
         } else {
            if (contentList != null) {
               Iterator var8 = contentList.iterator();

               while(var8.hasNext()) {
                  PlaylistContent playlistContent = (PlaylistContent)var8.next();
                  if (playlistContent.getContent_id().equalsIgnoreCase(content.getContent_id()) && playlistContent.getContent_order() == (long)beforeOrder) {
                     content_duration = playlistContent.getContent_duration();
                     return content_duration;
                  }
               }
            }

            content_duration = getDefaultContentDuration(productType, deviceType, content.getContent_duration());
            return content_duration;
         }
      }
   }

   private static Long getDefaultContentDuration(String productType, String deviceType, Long contentDuration) {
      if (contentDuration == 0L && CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
         if (deviceType.equalsIgnoreCase("iPLAYER")) {
            contentDuration = ContentConstants.CONTENT_DURATION;
         } else if (deviceType.equalsIgnoreCase("SPLAYER")) {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         } else if (!deviceType.equalsIgnoreCase("S2PLAYER") && !deviceType.equalsIgnoreCase("S3PLAYER")) {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         } else {
            contentDuration = ContentConstants.LITE_CONTENT_DURATION;
         }
      }

      return contentDuration;
   }

   private static Map getContentDurationByEffectList(String effectList) {
      Map map = new HashMap();
      if (effectList != null && !effectList.equalsIgnoreCase("")) {
         String[] arrEffectList = effectList.split(",");
         String[] var3 = arrEffectList;
         int var4 = arrEffectList.length;

         for(int var5 = 0; var5 < var4; ++var5) {
            String effect = var3[var5];
            String[] arrEffectItem = effect.split("↑");
            map.put(arrEffectItem[8], arrEffectItem[7]);
         }
      }

      return map;
   }

   private static boolean isContentApproval(String userId) {
      UserInfoImpl userInfo = UserInfoImpl.getInstance();

      try {
         User user = userInfo.getUserInfo(userId);
         return user.getContent_approver().equals('Y');
      } catch (SQLException var3) {
         logger.error("", var3);
         return false;
      }
   }

   public static int createWebContent(String userId, String deviceType, float deviceTypeVersion, String path, Map data, String contentType, String contentId, String contentName, String tempFileId) {
      try {
         List fileListToSave = new ArrayList();
         UserInfo uInfo = UserInfoImpl.getInstance();
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         String lfdFileId = null;
         String startPage = null;
         String url_address = null;
         String refreshInterval = "00:01:00";
         long fileSize = 0L;
         String configFilePath = null;
         String contentMetaData = "";
         Map adsContentSettingData = null;
         boolean updateMode = false;
         if (data != null) {
            if (data.get("mode") != null && data.get("mode").equals("UPDATE")) {
               updateMode = true;
            }

            if (data.get("lfdFileId") != null) {
               lfdFileId = (String)data.get("lfdFileId");
            }

            if (data.get("startPage") != null) {
               startPage = (String)data.get("startPage");
               lfdFileId = UUID.randomUUID().toString().toUpperCase();
            }

            if (data.get("refreshInterval") != null) {
               refreshInterval = (String)data.get("refreshInterval");
            }

            if (data.get("contentMetaData") != null) {
               contentMetaData = (String)data.get("contentMetaData");
            }

            if (data.get("fileSize") != null) {
               fileSize = (Long)data.get("fileSize");
            }

            if (data.get("webUrl") != null) {
               url_address = (String)data.get("webUrl");
            }

            if (data.get("configFilePath") != null) {
               configFilePath = (String)data.get("configFilePath");
               lfdFileId = UUID.randomUUID().toString().toUpperCase();
            }

            if (data.get("adsSetting") != null) {
               adsContentSettingData = (Map)data.get("adsSetting");
               adsContentSettingData.put("content_id", contentId);
               lfdFileId = UUID.randomUUID().toString().toUpperCase();
            }
         } else {
            lfdFileId = UUID.randomUUID().toString().toUpperCase();
         }

         String originalWidth = "";
         String originalHeight = "";
         String webelementName = "";
         String webUrl = "";
         String duration = "";
         String repeatPeriod = "";
         String repeatEnd = "";
         String elementType = "Web";
         String elementTypeId = "";
         String fileExtension = ".LFD";
         if (contentType.equals("URL")) {
            originalWidth = "1920";
            originalHeight = "1080";
            webelementName = "Web";
            if (url_address != null) {
               webUrl = url_address;
            }

            duration = refreshInterval + ".00";
            repeatPeriod = "00:00:10.00";
            repeatEnd = refreshInterval + ".00";
            elementTypeId = "909736CD-5688-49D5-89B0-1DEB6E65962D";
         } else if (contentType.equals("ADS")) {
            elementType = "ADS";
            originalWidth = "0";
            originalHeight = "0";
            webelementName = contentName;
            duration = "23:59:59.99";
            repeatPeriod = "23:59:59.99";
            repeatEnd = "23:59:59.99";
            elementTypeId = "909736CD-5688-49D5-89B0-1DEB6E65962D";
         } else if (contentType.equals("HTML")) {
            originalWidth = "0";
            originalHeight = "0";
            webelementName = contentName;
            duration = "23:59:59.99";
            repeatPeriod = "23:59:59.99";
            repeatEnd = "23:59:59.99";
            elementTypeId = "909736CD-5688-49D5-89B0-1DEB6E65962D";
         } else if (contentType.equals("SAPP")) {
            elementType = "SAPP";
            fileExtension = ".SAPP";
            webelementName = contentName;
            duration = "23:59:59.99";
            repeatPeriod = "23:59:59.99";
            repeatEnd = "23:59:59.99";
         }

         DocumentBuilderFactory docFactory = DocumentUtils.getDocumentBuilderFactoryInstance();
         DocumentBuilder docBuilder = docFactory.newDocumentBuilder();
         Document doc = docBuilder.newDocument();
         Element rootElement = doc.createElement("Content");
         rootElement.setAttribute("id", contentId);
         rootElement.setAttribute("version", "3.00");
         rootElement.setAttribute("packaged", "true");
         rootElement.setAttribute("datalinkTemplate", "false");
         doc.appendChild(rootElement);
         Element playerType = doc.createElement("PlayerType");
         playerType.appendChild(doc.createTextNode(deviceType));
         rootElement.appendChild(playerType);
         Element playerTypeVersion = doc.createElement("PlayerTypeVersion");
         playerTypeVersion.appendChild(doc.createTextNode(Float.toString(deviceTypeVersion)));
         rootElement.appendChild(playerTypeVersion);
         Element FileID = doc.createElement("FileID");
         FileID.appendChild(doc.createTextNode(lfdFileId));
         rootElement.appendChild(FileID);
         Element name = doc.createElement("Name");
         name.setAttribute("type", "String");
         Element keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(contentName));
         name.appendChild(keyFrame);
         rootElement.appendChild(name);
         Element displayWidth = doc.createElement("DisplayWidth");
         displayWidth.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1920"));
         displayWidth.appendChild(keyFrame);
         rootElement.appendChild(displayWidth);
         Element displayHeight = doc.createElement("DisplayHeight");
         displayHeight.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1080"));
         displayHeight.appendChild(keyFrame);
         rootElement.appendChild(displayHeight);
         Element displayPolicy = doc.createElement("DisplayPolicy");
         displayPolicy.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1"));
         displayPolicy.appendChild(keyFrame);
         rootElement.appendChild(displayPolicy);
         Element BGMusicFilePath = doc.createElement("BGMusicFilePath");
         BGMusicFilePath.setAttribute("type", "File");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         BGMusicFilePath.appendChild(keyFrame);
         rootElement.appendChild(BGMusicFilePath);
         Element eventItems = doc.createElement("EventItems");
         rootElement.appendChild(eventItems);
         Element page1 = doc.createElement("Page");
         page1.setAttribute("index", "0");
         page1.setAttribute("type", "Global");
         Element page1Name = doc.createElement("Name");
         page1Name.setAttribute("type", "String");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         page1Name.appendChild(keyFrame);
         page1.appendChild(page1Name);
         Element BGColor = doc.createElement("BGColor");
         BGColor.setAttribute("type", "Color");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("181;181;181"));
         BGColor.appendChild(keyFrame);
         page1.appendChild(BGColor);
         Element MotionPathPoints = doc.createElement("MotionPathPoints");
         MotionPathPoints.setAttribute("type", "String");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         MotionPathPoints.appendChild(keyFrame);
         page1.appendChild(MotionPathPoints);
         Element BGImageFilePath = doc.createElement("BGImageFilePath");
         BGImageFilePath.setAttribute("type", "File");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         BGImageFilePath.appendChild(keyFrame);
         page1.appendChild(BGImageFilePath);
         Element BGRatio = doc.createElement("BGRatio");
         BGRatio.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1"));
         BGRatio.appendChild(keyFrame);
         page1.appendChild(BGRatio);
         Element OriginalWidth = doc.createElement("OriginalWidth");
         OriginalWidth.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1920"));
         OriginalWidth.appendChild(keyFrame);
         page1.appendChild(OriginalWidth);
         Element OriginalHeight = doc.createElement("OriginalHeight");
         OriginalHeight.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1080"));
         OriginalHeight.appendChild(keyFrame);
         page1.appendChild(OriginalHeight);
         Element DataLinkClearAtFirstItem = doc.createElement("DataLinkClearAtFirstItem");
         DataLinkClearAtFirstItem.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("False"));
         DataLinkClearAtFirstItem.appendChild(keyFrame);
         page1.appendChild(DataLinkClearAtFirstItem);
         Element DataLinkPageSync = doc.createElement("DataLinkPageSync");
         DataLinkPageSync.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         DataLinkPageSync.appendChild(keyFrame);
         page1.appendChild(DataLinkPageSync);
         Element IsVisible = doc.createElement("IsVisible");
         IsVisible.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("True"));
         IsVisible.appendChild(keyFrame);
         page1.appendChild(IsVisible);
         Element DurationPolicy = doc.createElement("DurationPolicy");
         DurationPolicy.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         DurationPolicy.appendChild(keyFrame);
         page1.appendChild(DurationPolicy);
         Element Duration = doc.createElement("Duration");
         Duration.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode(refreshInterval + ".00"));
         Duration.appendChild(keyFrame);
         page1.appendChild(Duration);
         Element InterpolationType = doc.createElement("InterpolationType");
         InterpolationType.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1"));
         InterpolationType.appendChild(keyFrame);
         page1.appendChild(InterpolationType);
         Element EventEnable = doc.createElement("EventEnable");
         EventEnable.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("True"));
         EventEnable.appendChild(keyFrame);
         page1.appendChild(EventEnable);
         Element EffectInName = doc.createElement("EffectInName");
         EffectInName.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         EffectInName.appendChild(keyFrame);
         page1.appendChild(EffectInName);
         Element EffectInDuration = doc.createElement("EffectInDuration");
         EffectInDuration.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("00:00:00.00"));
         EffectInDuration.appendChild(keyFrame);
         page1.appendChild(EffectInDuration);
         Element EffectInDirection = doc.createElement("EffectInDirection");
         EffectInDirection.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         EffectInDirection.appendChild(keyFrame);
         page1.appendChild(EffectInDirection);
         Element EffectInRepeatPolicy = doc.createElement("EffectInRepeatPolicy");
         EffectInRepeatPolicy.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         EffectInRepeatPolicy.appendChild(keyFrame);
         page1.appendChild(EffectInRepeatPolicy);
         Element EffectInRepeatPeriod = doc.createElement("EffectInRepeatPeriod");
         EffectInRepeatPeriod.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("00:00:00.00"));
         EffectInRepeatPeriod.appendChild(keyFrame);
         page1.appendChild(EffectInRepeatPeriod);
         Element EffectInRepeatCount = doc.createElement("EffectInRepeatCount");
         EffectInRepeatCount.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1"));
         EffectInRepeatCount.appendChild(keyFrame);
         page1.appendChild(EffectInRepeatCount);
         Element EventItems = doc.createElement("EventItems");
         page1.appendChild(EventItems);
         rootElement.appendChild(page1);
         Element page2 = doc.createElement("Page");
         page2.setAttribute("index", "1");
         page2.setAttribute("type", "Local");
         Element page2Name = doc.createElement("Name");
         page2Name.setAttribute("type", "String");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection("Page 1"));
         page2Name.appendChild(keyFrame);
         page2.appendChild(page2Name);
         Element p2BGColor = doc.createElement("BGColor");
         p2BGColor.setAttribute("type", "Color");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("255;255;255"));
         p2BGColor.appendChild(keyFrame);
         page2.appendChild(p2BGColor);
         Element p2MotionPathPoints = doc.createElement("MotionPathPoints");
         p2MotionPathPoints.setAttribute("type", "String");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         p2MotionPathPoints.appendChild(keyFrame);
         page2.appendChild(p2MotionPathPoints);
         Element p2BGImageFilePath = doc.createElement("BGImageFilePath");
         p2BGImageFilePath.setAttribute("type", "File");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         p2BGImageFilePath.appendChild(keyFrame);
         page2.appendChild(p2BGImageFilePath);
         Element p2BGRatio = doc.createElement("BGRatio");
         p2BGRatio.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1"));
         p2BGRatio.appendChild(keyFrame);
         page2.appendChild(p2BGRatio);
         Element p2OrginalWidth = doc.createElement("OriginalWidth");
         p2OrginalWidth.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode(originalWidth));
         p2OrginalWidth.appendChild(keyFrame);
         page2.appendChild(p2OrginalWidth);
         Element p2OriginalHeight = doc.createElement("OriginalHeight");
         p2OriginalHeight.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode(originalHeight));
         p2OriginalHeight.appendChild(keyFrame);
         page2.appendChild(p2OriginalHeight);
         Element p2DataLinkClearAtFirstItem = doc.createElement("DataLinkClearAtFirstItem");
         p2DataLinkClearAtFirstItem.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("False"));
         p2DataLinkClearAtFirstItem.appendChild(keyFrame);
         page2.appendChild(p2DataLinkClearAtFirstItem);
         Element p2DataLinkPageSync = doc.createElement("DataLinkPageSync");
         p2DataLinkPageSync.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         p2DataLinkPageSync.appendChild(keyFrame);
         page2.appendChild(p2DataLinkPageSync);
         Element p2IsVisible = doc.createElement("IsVisible");
         p2IsVisible.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("True"));
         p2IsVisible.appendChild(keyFrame);
         page2.appendChild(p2IsVisible);
         Element p2DurationPolicy = doc.createElement("DurationPolicy");
         p2DurationPolicy.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         p2DurationPolicy.appendChild(keyFrame);
         page2.appendChild(p2DurationPolicy);
         Element p2Duration = doc.createElement("Duration");
         p2Duration.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode(refreshInterval + ".00"));
         p2Duration.appendChild(keyFrame);
         page2.appendChild(p2Duration);
         Element p2InterpolationType = doc.createElement("InterpolationType");
         p2InterpolationType.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1"));
         p2InterpolationType.appendChild(keyFrame);
         page2.appendChild(p2InterpolationType);
         Element p2EventEnable = doc.createElement("EventEnable");
         p2EventEnable.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("True"));
         p2EventEnable.appendChild(keyFrame);
         page2.appendChild(p2EventEnable);
         Element p2EffectInName = doc.createElement("EffectInName");
         p2EffectInName.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         p2EffectInName.appendChild(keyFrame);
         page2.appendChild(p2EffectInName);
         Element p2EffectInDuration = doc.createElement("EffectInDuration");
         p2EffectInDuration.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("00:00:00.00"));
         p2EffectInDuration.appendChild(keyFrame);
         page2.appendChild(p2EffectInDuration);
         Element p2EffectInDirection = doc.createElement("EffectInDirection");
         p2EffectInDirection.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         p2EffectInDirection.appendChild(keyFrame);
         page2.appendChild(p2EffectInDirection);
         Element p2EffectInRepeatPolicy = doc.createElement("EffectInRepeatPolicy");
         p2EffectInRepeatPolicy.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         p2EffectInRepeatPolicy.appendChild(keyFrame);
         page2.appendChild(p2EffectInRepeatPolicy);
         Element p2EffectInRepeatPeriod = doc.createElement("EffectInRepeatPeriod");
         p2EffectInRepeatPeriod.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("00:00:00.00"));
         p2EffectInRepeatPeriod.appendChild(keyFrame);
         page2.appendChild(p2EffectInRepeatPeriod);
         Element p2EffectInRepeatCount = doc.createElement("EffectInRepeatCount");
         p2EffectInRepeatCount.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1"));
         p2EffectInRepeatCount.appendChild(keyFrame);
         page2.appendChild(p2EffectInRepeatCount);
         Element p2EventItems = doc.createElement("EventItems");
         page2.appendChild(p2EventItems);
         Element element = doc.createElement("Element");
         element.setAttribute("type", elementType);
         element.setAttribute("typeid", elementTypeId);
         Element webElementName = doc.createElement("Name");
         webElementName.setAttribute("type", "String");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(webelementName));
         webElementName.appendChild(keyFrame);
         element.appendChild(webElementName);
         Element WebURL = doc.createElement("WebURL");
         WebURL.setAttribute("type", "String");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(webUrl));
         WebURL.appendChild(keyFrame);
         element.appendChild(WebURL);
         Element eleMotionPathPoints = doc.createElement("MotionPathPoints");
         eleMotionPathPoints.setAttribute("type", "String");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         eleMotionPathPoints.appendChild(keyFrame);
         element.appendChild(eleMotionPathPoints);
         Element WebAuthorizationID = doc.createElement("WebAuthorizationID");
         WebAuthorizationID.setAttribute("type", "String");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         WebAuthorizationID.appendChild(keyFrame);
         element.appendChild(WebAuthorizationID);
         Element Layer = doc.createElement("Layer");
         Layer.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         Layer.appendChild(keyFrame);
         element.appendChild(Layer);
         Element PinnedPointNameList = doc.createElement("PinnedPointNameList");
         PinnedPointNameList.setAttribute("type", "MultilineString");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         PinnedPointNameList.appendChild(keyFrame);
         element.appendChild(PinnedPointNameList);
         Element GlobalElement = doc.createElement("GlobalElement");
         GlobalElement.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("False"));
         GlobalElement.appendChild(keyFrame);
         element.appendChild(GlobalElement);
         Element PositionX = doc.createElement("PositionX");
         PositionX.setAttribute("type", "RealNumber");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         PositionX.appendChild(keyFrame);
         element.appendChild(PositionX);
         Element PositionY = doc.createElement("PositionY");
         PositionY.setAttribute("type", "RealNumber");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         PositionY.appendChild(keyFrame);
         element.appendChild(PositionY);
         Element Width = doc.createElement("Width");
         Width.setAttribute("type", "RealNumber");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1920"));
         Width.appendChild(keyFrame);
         element.appendChild(Width);
         Element Height = doc.createElement("Height");
         Height.setAttribute("type", "RealNumber");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1080"));
         Height.appendChild(keyFrame);
         element.appendChild(Height);
         Element FilePath = doc.createElement("FilePath");
         FilePath.setAttribute("type", "File");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         FilePath.appendChild(keyFrame);
         element.appendChild(FilePath);
         Element WebOutputMode = doc.createElement("WebOutputMode");
         WebOutputMode.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         WebOutputMode.appendChild(keyFrame);
         element.appendChild(WebOutputMode);
         Element WebRefresh = doc.createElement("WebRefresh");
         WebRefresh.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1"));
         WebRefresh.appendChild(keyFrame);
         element.appendChild(WebRefresh);
         Element WebRefreshNew = doc.createElement("WebRefreshNew");
         WebRefreshNew.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         if (refreshInterval != null) {
            keyFrame.appendChild(doc.createTextNode(refreshInterval + ".00"));
         } else {
            keyFrame.appendChild(doc.createTextNode("00:01:00.00"));
         }

         WebRefreshNew.appendChild(keyFrame);
         element.appendChild(WebRefreshNew);
         Element WebInputEnable = doc.createElement("WebInputEnable");
         WebInputEnable.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("False"));
         WebInputEnable.appendChild(keyFrame);
         element.appendChild(WebInputEnable);
         Element WebScrollShow = doc.createElement("WebScrollShow");
         WebScrollShow.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("False"));
         WebScrollShow.appendChild(keyFrame);
         element.appendChild(WebScrollShow);
         Element WebVScrollPos = doc.createElement("WebVScrollPos");
         WebVScrollPos.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         WebVScrollPos.appendChild(keyFrame);
         element.appendChild(WebVScrollPos);
         Element WebHScrollPos = doc.createElement("WebHScrollPos");
         WebHScrollPos.setAttribute("type", "Number");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         WebHScrollPos.appendChild(keyFrame);
         element.appendChild(WebHScrollPos);
         Element WebAuthorizationPassWd = doc.createElement("WebAuthorizationPassWd");
         WebAuthorizationPassWd.setAttribute("type", "String");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createCDATASection(""));
         WebAuthorizationPassWd.appendChild(keyFrame);
         element.appendChild(WebAuthorizationPassWd);
         Element eleIsVisible = doc.createElement("IsVisible");
         eleIsVisible.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("True"));
         eleIsVisible.appendChild(keyFrame);
         element.appendChild(eleIsVisible);
         Element GlobalDisplayPriority = doc.createElement("GlobalDisplayPriority");
         GlobalDisplayPriority.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         GlobalDisplayPriority.appendChild(keyFrame);
         element.appendChild(GlobalDisplayPriority);
         Element WindowMode = doc.createElement("WindowMode");
         WindowMode.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("True"));
         WindowMode.appendChild(keyFrame);
         element.appendChild(WindowMode);
         Element GlobalPlayPolicy = doc.createElement("GlobalPlayPolicy");
         GlobalPlayPolicy.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         GlobalPlayPolicy.appendChild(keyFrame);
         element.appendChild(GlobalPlayPolicy);
         Element eleDurationPolicy = doc.createElement("DurationPolicy");
         eleDurationPolicy.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("0"));
         eleDurationPolicy.appendChild(keyFrame);
         element.appendChild(eleDurationPolicy);
         Element Start = doc.createElement("Start");
         Start.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("00:00:00.00"));
         Start.appendChild(keyFrame);
         element.appendChild(Start);
         Element eleDuration = doc.createElement("Duration");
         eleDuration.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode(duration));
         eleDuration.appendChild(keyFrame);
         element.appendChild(eleDuration);
         Element RepeatPeriod = doc.createElement("RepeatPeriod");
         RepeatPeriod.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode(repeatPeriod));
         RepeatPeriod.appendChild(keyFrame);
         element.appendChild(RepeatPeriod);
         Element RepeatEnd = doc.createElement("RepeatEnd");
         RepeatEnd.setAttribute("type", "Time");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode(repeatEnd));
         RepeatEnd.appendChild(keyFrame);
         element.appendChild(RepeatEnd);
         Element eleInterpolationType = doc.createElement("InterpolationType");
         eleInterpolationType.setAttribute("type", "List");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("1"));
         eleInterpolationType.appendChild(keyFrame);
         element.appendChild(eleInterpolationType);
         Element eleEventEnable = doc.createElement("EventEnable");
         eleEventEnable.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("True"));
         eleEventEnable.appendChild(keyFrame);
         element.appendChild(eleEventEnable);
         Element AuthorAreaFix = doc.createElement("AuthorAreaFix");
         AuthorAreaFix.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("False"));
         AuthorAreaFix.appendChild(keyFrame);
         element.appendChild(AuthorAreaFix);
         Element DesignCanvasVisible = doc.createElement("DesignCanvasVisible");
         DesignCanvasVisible.setAttribute("type", "Boolean");
         keyFrame = doc.createElement("KeyFrame");
         keyFrame.setAttribute("time", "0");
         keyFrame.appendChild(doc.createTextNode("True"));
         DesignCanvasVisible.appendChild(keyFrame);
         element.appendChild(DesignCanvasVisible);
         Element elementEventItems = doc.createElement("EventItems");
         element.appendChild(elementEventItems);
         boolean contentsApprovalEnable;
         String movepath;
         File moveFile;
         File configFile;
         ContentFile cmsContentFile;
         if (contentType.equals("URL")) {
            page2.appendChild(element);
            rootElement.appendChild(page2);
         } else {
            String hashId;
            Element WebPackage;
            Element StartupPage;
            String hashId;
            ContentFile indexContentFile;
            Element FileItems;
            Element FileItem;
            if (!contentType.equals("HTML")) {
               ContentInfoImpl cmsDao;
               String wgtFileId;
               if (contentType.equals("ADS")) {
                  try {
                     cmsDao = ContentInfoImpl.getInstance();
                     String adsContentConfigFileId = (String)adsContentSettingData.get("adsContentConfigFileId");
                     ContentFile adsConfigFile = cmsDao.getFileInfo(adsContentConfigFileId);
                     fileListToSave.add(adsConfigFile);
                     wgtFileId = (String)adsContentSettingData.get("default_content_file_id");
                     ContentFile defaultContentFile = cmsDao.getFileInfo(wgtFileId);
                     fileListToSave.add(defaultContentFile);
                     page2.appendChild(element);
                     rootElement.appendChild(page2);
                     Element FileItems = doc.createElement("FileItems");
                     Element FileItem = doc.createElement("FileItem");
                     FileItem.setAttribute("FileID", adsConfigFile.getFile_id());
                     FileItem.setAttribute("FileHashValue", adsConfigFile.getHash_code());
                     FileItem.appendChild(doc.createCDATASection(adsConfigFile.getFile_name()));
                     FileItem.setAttribute("FileSize", String.valueOf(adsConfigFile.getFile_size()));
                     WebPackage = doc.createElement("FileItem");
                     WebPackage.setAttribute("FileID", wgtFileId);
                     WebPackage.setAttribute("FileHashValue", defaultContentFile.getHash_code());
                     WebPackage.appendChild(doc.createCDATASection(defaultContentFile.getFile_name()));
                     WebPackage.setAttribute("FileSize", String.valueOf(defaultContentFile.getFile_size()));
                     FileItems.appendChild(FileItem);
                     FileItems.appendChild(WebPackage);
                     rootElement.appendChild(FileItems);
                  } catch (Exception var188) {
                     return -1;
                  }
               } else if (contentType.equals("SAPP")) {
                  try {
                     cmsDao = ContentInfoImpl.getInstance();
                     path = SecurityUtils.directoryTraversalChecker(path, (String)null);
                     File wgtFile = new File(path);
                     boolean bExistFile = false;
                     wgtFileId = null;
                     configFilePath = SecurityUtils.directoryTraversalChecker(configFilePath, (String)null);
                     configFile = new File(configFilePath);
                     hashId = null;
                     hashId = null;
                     WebPackage = doc.createElement("SAppPackage");
                     WebPackage.setAttribute("type", "File");
                     keyFrame = doc.createElement("KeyFrame");
                     keyFrame.setAttribute("time", "0");
                     keyFrame.appendChild(doc.createCDATASection(wgtFile.getName()));
                     WebPackage.appendChild(keyFrame);
                     element.appendChild(WebPackage);
                     StartupPage = doc.createElement("SAppConfig");
                     StartupPage.setAttribute("type", "File");
                     keyFrame = doc.createElement("KeyFrame");
                     keyFrame.setAttribute("time", "0");
                     keyFrame.appendChild(doc.createCDATASection(configFile.getName()));
                     StartupPage.appendChild(keyFrame);
                     element.appendChild(StartupPage);
                     page2.appendChild(element);
                     rootElement.appendChild(page2);

                     try {
                        File[] arr = new File[]{wgtFile, configFile};
                        File[] var227 = arr;
                        int var230 = arr.length;

                        for(int var232 = 0; var232 < var230; ++var232) {
                           File file = var227[var232];
                           hashId = getHash(file);
                           String fileName = file.getName();
                           String fileId = UUID.randomUUID().toString().toUpperCase();
                           if (cmsDao.isExistFileByHash(fileName, file.length(), hashId)) {
                              fileId = cmsDao.getFileIDByHash(fileName, file.length(), hashId);
                           } else {
                              String movepath = SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + fileId + File.separator + fileName, (String)null);
                              File moveFile = new File(movepath);
                              moveFile.getParentFile().mkdir();
                              InputStream input = null;
                              FileOutputStream output = null;

                              try {
                                 input = new FileInputStream(file);
                                 output = new FileOutputStream(moveFile);
                                 byte[] buf = new byte[1024];

                                 int bytesRead;
                                 while((bytesRead = input.read(buf)) > 0) {
                                    output.write(buf, 0, bytesRead);
                                 }
                              } catch (Exception var189) {
                              } finally {
                                 input.close();
                                 output.close();
                              }
                           }

                           if (file.equals(wgtFile)) {
                              wgtFileId = fileId;
                           } else {
                              hashId = fileId;
                           }
                        }

                        cmsContentFile = new ContentFile();
                        cmsContentFile.setIs_streaming("N");
                        cmsContentFile.setFile_id(wgtFileId);
                        cmsContentFile.setHash_code(getHash(wgtFile));
                        cmsContentFile.setFile_type("MAIN");
                        cmsContentFile.setFile_name(wgtFile.getName());
                        cmsContentFile.setFile_size(wgtFile.length());
                        cmsContentFile.setCreator_id(userId);
                        cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + wgtFileId);
                        cmsContentFile.setRefresh_interval(refreshInterval);
                        fileListToSave.add(cmsContentFile);
                        indexContentFile = new ContentFile();
                        indexContentFile.setIs_streaming("N");
                        indexContentFile.setFile_id(hashId);
                        indexContentFile.setHash_code(getHash(configFile));
                        indexContentFile.setFile_type("MAIN");
                        indexContentFile.setFile_name(configFile.getName());
                        indexContentFile.setFile_size(configFile.length());
                        indexContentFile.setCreator_id(userId);
                        indexContentFile.setFile_path(CONTENTS_HOME + File.separator + hashId);
                        fileListToSave.add(indexContentFile);
                        FileItems = doc.createElement("FileItems");
                        FileItem = doc.createElement("FileItem");
                        FileItem.setAttribute("FileID", wgtFileId);
                        FileItem.setAttribute("FileHashValue", getHash(wgtFile));
                        FileItem.setAttribute("FileSize", String.valueOf(wgtFile.length()));
                        FileItem.appendChild(doc.createCDATASection(wgtFile.getName()));
                        Element FileItem2 = doc.createElement("FileItem");
                        FileItem2.setAttribute("FileID", hashId);
                        FileItem2.setAttribute("FileHashValue", getHash(configFile));
                        FileItem2.setAttribute("FileSize", String.valueOf(configFile.length()));
                        FileItem2.appendChild(doc.createCDATASection(configFile.getName()));
                        FileItems.appendChild(FileItem);
                        FileItems.appendChild(FileItem2);
                        rootElement.appendChild(FileItems);
                     } catch (Exception var191) {
                        return -1;
                     }
                  } catch (Exception var192) {
                     logger.error("", var192);
                  }
               }
            } else {
               contentsApprovalEnable = false;
               boolean useVariableTag = Pattern.compile("\\$\\(.*?\\)").matcher(startPage).find();
               if (useVariableTag) {
                  contentsApprovalEnable = true;
               } else {
                  label1381: {
                     FileInputStream inputFile = new FileInputStream(path);
                     ZipInputStream zis = new ZipInputStream(inputFile);

                     byte var130;
                     try {
                        if (startPage == null) {
                           byte var212 = -1;
                           return var212;
                        }

                        ZipEntry entry = zis.getNextEntry();

                        while(true) {
                           if (entry == null) {
                              break label1381;
                           }

                           if (!entry.isDirectory() && entry.getName() != null && entry.getName().equals(startPage)) {
                              contentsApprovalEnable = true;
                           }

                           zis.closeEntry();
                           entry = zis.getNextEntry();
                        }
                     } catch (IOException var197) {
                        logger.error("", var197);
                        var130 = 10;
                     } finally {
                        inputFile.close();
                        zis.close();
                     }

                     return var130;
                  }
               }

               try {
                  if (!contentsApprovalEnable) {
                     return 0;
                  }

                  ContentInfo cmsDao = ContentInfoImpl.getInstance();
                  path = SecurityUtils.directoryTraversalChecker(path, (String)null);
                  File zipFile = new File(path);
                  boolean bExistFile = false;
                  hashId = null;
                  hashId = UUID.randomUUID().toString().toUpperCase();
                  WebPackage = doc.createElement("WebPackage");
                  WebPackage.setAttribute("type", "File");
                  keyFrame = doc.createElement("KeyFrame");
                  keyFrame.setAttribute("time", "0");
                  keyFrame.appendChild(doc.createCDATASection(zipFile.getName()));
                  WebPackage.appendChild(keyFrame);
                  element.appendChild(WebPackage);
                  StartupPage = doc.createElement("StartupPage");
                  StartupPage.setAttribute("type", "String");
                  keyFrame = doc.createElement("KeyFrame");
                  keyFrame.setAttribute("time", "0");
                  keyFrame.appendChild(doc.createCDATASection(startPage));
                  StartupPage.appendChild(keyFrame);
                  element.appendChild(StartupPage);
                  Element UnzipPath = doc.createElement("UnzipPath");
                  UnzipPath.setAttribute("type", "String");
                  keyFrame = doc.createElement("KeyFrame");
                  keyFrame.setAttribute("time", "0");
                  keyFrame.appendChild(doc.createCDATASection(""));
                  UnzipPath.appendChild(keyFrame);
                  element.appendChild(UnzipPath);
                  page2.appendChild(element);
                  rootElement.appendChild(page2);

                  try {
                     hashId = getHash(zipFile);
                     String fileName = zipFile.getName();
                     if (cmsDao.isExistFileByHash(fileName, zipFile.length(), hashId)) {
                        hashId = cmsDao.getFileIDByHash(fileName, zipFile.length(), hashId);
                     } else {
                        movepath = SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + hashId + File.separator + fileName, (String)null);
                        moveFile = new File(movepath);
                        moveFile.getParentFile().mkdir();
                        InputStream input = null;
                        FileOutputStream output = null;

                        try {
                           input = new FileInputStream(zipFile);
                           output = new FileOutputStream(moveFile);
                           byte[] buf = new byte[1024];

                           int bytesRead;
                           while((bytesRead = input.read(buf)) > 0) {
                              output.write(buf, 0, bytesRead);
                           }
                        } catch (Exception var193) {
                        } finally {
                           input.close();
                           output.close();
                        }
                     }

                     indexContentFile = new ContentFile();
                     indexContentFile.setIs_streaming("N");
                     indexContentFile.setHtml_start_page(startPage);
                     indexContentFile.setFile_id(hashId);
                     indexContentFile.setHash_code(hashId);
                     indexContentFile.setFile_type("MAIN");
                     indexContentFile.setFile_name(fileName);
                     indexContentFile.setFile_size(zipFile.length());
                     indexContentFile.setCreator_id(userId);
                     indexContentFile.setFile_path(CONTENTS_HOME + File.separator + hashId);
                     indexContentFile.setRefresh_interval(refreshInterval);
                     fileListToSave.add(indexContentFile);
                     FileItems = doc.createElement("FileItems");
                     FileItem = doc.createElement("FileItem");
                     FileItem.setAttribute("FileID", hashId);
                     FileItem.setAttribute("FileHashValue", hashId);
                     FileItem.setAttribute("FileSize", String.valueOf(zipFile.length()));
                     FileItem.appendChild(doc.createCDATASection(fileName));
                     FileItems.appendChild(FileItem);
                     rootElement.appendChild(FileItems);
                  } catch (Exception var195) {
                     return -1;
                  }
               } catch (Exception var196) {
                  logger.error("", var196);
               }
            }
         }

         contentsApprovalEnable = false;

         try {
            long orgId = uInfo.getRootGroupIdByUserId(userId);
            ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
            Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
            contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         } catch (Exception var187) {
         }

         TransformerFactory transformerFactory = DocumentUtils.getTransformerFactoryInstance();
         Transformer transformer = transformerFactory.newTransformer();
         DOMSource source = new DOMSource(doc);
         if (contentType.equals("HTML") || contentType.equals("SAPP") || contentType.equals("ADS")) {
            path = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home" + File.separator + lfdFileId;
            configFile = new File(SecurityUtils.directoryTraversalChecker(path, (String)null));
            configFile.mkdir();
         }

         String contentPath = path + File.separator + contentName + fileExtension;
         contentPath = SecurityUtils.directoryTraversalChecker(contentPath, (String)null);
         StreamResult result = new StreamResult((new File(contentPath)).getPath());
         transformer.transform(source, result);
         ContentInfo cmsDao = ContentInfoImpl.getInstance();
         long orgCreatorId = cmsDao.getRootId(userId);
         String groupId = null;
         if (groupId == null || groupId.equals("") || groupId.equals("0") || groupId.equals("null")) {
            groupId = String.valueOf(orgCreatorId);
         }

         cmsContentFile = new ContentFile();
         movepath = SecurityUtils.directoryTraversalChecker(path + File.separator + contentName + fileExtension, (String)null);
         moveFile = new File(movepath);
         cmsContentFile.setIs_streaming("N");
         cmsContentFile.setFile_id(lfdFileId);
         cmsContentFile.setHash_code(getHash(moveFile));
         cmsContentFile.setFile_type("MAIN");
         cmsContentFile.setFile_name(contentName + fileExtension);
         cmsContentFile.setFile_size(moveFile.length());
         cmsContentFile.setCreator_id(userId);
         cmsContentFile.setFile_path(path);
         Content content = new Content();
         content.setDevice_type(deviceType);
         content.setDevice_type_version(deviceTypeVersion);
         content.setVersion_id(1L);
         content.setContent_id(contentId);
         content.setGroup_id(Long.valueOf(groupId));
         content.setShare_flag(1);
         content.setContent_meta_data(contentMetaData);
         content.setCreator_id(userId);
         content.setMedia_type(contentType);
         content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
         content.setResolution("1920 x 1080");
         if (fileSize > 0L) {
            content.setTotal_size(fileSize);
         } else {
            content.setTotal_size(moveFile.length());
         }

         content.setIs_active("Y");
         content.setOrg_creator_id(String.valueOf(orgCreatorId));
         if (contentType.equals("HTML") && startPage != null) {
            content.setHtml_start_page(startPage);
         } else if (contentType.equals("URL") && url_address != null) {
            content.setUrl_address(url_address);
         }

         content.setRefresh_interval(refreshInterval);
         if (contentType.equals("HTML")) {
            content.setThumb_file_id("HTML_THUMBNAIL");
            content.setThumb_file_name("HTML_THUMBNAIL.PNG");
         } else if (contentType.equals("URL")) {
            content.setThumb_file_id("URL_THUMBNAIL");
            content.setThumb_file_name("URL_THUMBNAIL.PNG");
         } else if (contentType.equals("SAPP")) {
            content.setThumb_file_id("SAPP_THUMBNAIL");
            content.setThumb_file_name("SAPP_THUMBNAIL.PNG");
         } else if (contentType.equals("ADS")) {
            content.setThumb_file_id("ADS_THUMBNAIL");
            content.setThumb_file_name("ADS_THUMBNAIL.PNG");
         }

         content.setContent_name(contentName);
         content.setMain_file_id(lfdFileId);
         content.setMain_file_Extension(contentType);
         if (contentsApprovalEnable) {
            if (content.getMain_file_Extension().equalsIgnoreCase("LFT")) {
               content.setApproval_status("APPROVED");
            } else {
               AbilityUtils abilityUtils = new AbilityUtils();
               if (abilityUtils.isContentApprovalAuthority(userId)) {
                  content.setApproval_status("APPROVED");
               } else {
                  content.setApproval_status("UNAPPROVED");
               }
            }
         } else {
            content.setApproval_status("APPROVED");
         }

         fileListToSave.add(cmsContentFile);
         content.setArr_file_list(fileListToSave);
         if (updateMode) {
            if (contentName != null && !contentName.equals("")) {
               content.setContent_name(contentName);
            }

            cmsDao.setContentInfo(contentId, contentName, content.getContent_meta_data(), content.getShare_flag());
            List list = content.getArr_file_list();
            List fileIdList = new ArrayList();
            if (list != null && list.size() > 0) {
               Iterator var243 = list.iterator();

               while(var243.hasNext()) {
                  ContentFile contentFile = (ContentFile)var243.next();
                  fileIdList.add(contentFile.getFile_id());
                  cmsDao.addFile(contentFile);
               }
            }

            long versionId = cmsDao.getMaxContentVersionId(contentId) + 1L;
            content.setVersion_id(versionId);
            cmsDao.addContentVersionInfo(content);
            if (fileIdList != null && fileIdList.size() > 0) {
               Iterator var246 = fileIdList.iterator();

               while(var246.hasNext()) {
                  String fileID = (String)var246.next();
                  cmsDao.addMapContentFile(contentId, versionId, fileID);
               }
            }

            cmsDao.setActiveVersionForUploader(contentId, versionId, true);
            if (content.getMedia_type().equals("ADS")) {
               adsContentSettingData.put("is_active", 'Y');
               adsContentSettingData.put("is_deleted", 'N');
               adsContentSettingData.put("version_id", versionId);
               cmsDao.addAdsContentVersionInfo(adsContentSettingData);
               cmsDao.setAdsContentActiveVersion(contentId, versionId);
            }
         } else {
            cmsDao.addContent(content);
            cmsDao.setActiveVersion(contentId, true);
            cmsDao.setContentInfo(content.getContent_id(), content.getContent_name(), content.getContent_meta_data(), content.getShare_flag());
            cmsDao.setContentGroup(content.getContent_id(), content.getGroup_id());
            if (content.getMedia_type().equals("ADS")) {
               adsContentSettingData.put("is_active", 'Y');
               adsContentSettingData.put("version_id", 1L);
               cmsDao.addAdsContentVersionInfo(adsContentSettingData);
               cmsDao.setAdsContentActiveVersion(contentId, 1L);
            }
         }

         return 1;
      } catch (Exception var199) {
         logger.error("", var199);
         return 0;
      }
   }

   private static void delete(String filePath) throws Exception {
      File file = new File(filePath);
      if (file.isDirectory()) {
         if (file.list().length == 0) {
            file.delete();
         } else {
            File[] files = file.listFiles();
            File[] var3 = files;
            int var4 = files.length;

            for(int var5 = 0; var5 < var4; ++var5) {
               File temp = var3[var5];
               delete(temp.getPath());
            }

            if (file.list().length == 0) {
               file.delete();
            }
         }
      } else {
         file.delete();
      }

   }

   private static void insertFile(String userId, String path, Document doc, Element SupportFileItems, List fileListToSave, String tempFileId) throws IOException {
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      String tempFolder = null;
      String contentHome = null;
      Object output = null;

      try {
         tempFolder = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "html_content_temp" + File.separator + tempFileId;
         contentHome = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      } catch (Exception var51) {
      }

      try {
         File file = new File(path);
         File[] listOfFolder = file.listFiles();
         if (listOfFolder != null && listOfFolder.length > 0) {
            File[] var12 = listOfFolder;
            int var13 = listOfFolder.length;

            for(int var14 = 0; var14 < var13; ++var14) {
               File tempFile = var12[var14];
               String fileName = tempFile.getName().replaceFirst("[.][^.]+$", "");
               if (tempFile != null && !fileName.equalsIgnoreCase("index")) {
                  if (tempFile.isDirectory()) {
                     insertFile(userId, tempFile.getAbsolutePath(), doc, SupportFileItems, fileListToSave, tempFileId);
                  } else {
                     boolean bExistFile = false;
                     String filePath = ".\\" + tempFile.getAbsolutePath().substring(tempFolder.length() + 1);
                     String hashId = null;
                     String fileId = UUID.randomUUID().toString().toUpperCase();

                     try {
                        hashId = getHash(tempFile);
                        if (cmsDao.isExistFileByHash(tempFile.getName(), tempFile.length(), hashId)) {
                           bExistFile = true;
                           fileId = cmsDao.getFileIDByHash(tempFile.getName(), tempFile.length(), hashId);
                        }
                     } catch (Exception var50) {
                     }

                     try {
                        Element FileItem = doc.createElement("FileItem");
                        Element PureFileItem = doc.createElement("PureFileItem");
                        PureFileItem.appendChild(doc.createCDATASection(tempFile.getName()));
                        FileItem.appendChild(PureFileItem);
                        Element RealFullPath = doc.createElement("RealFullPath");
                        RealFullPath.appendChild(doc.createCDATASection(filePath));
                        FileItem.appendChild(RealFullPath);
                        Element FileSize = doc.createElement("FileSize");
                        FileSize.appendChild(doc.createTextNode(Long.toString(tempFile.length())));
                        FileItem.appendChild(FileSize);
                        Element FileID = doc.createElement("FileID");
                        FileID.appendChild(doc.createTextNode(fileId));
                        FileItem.appendChild(FileID);
                        Element FileHashValue = doc.createElement("FileHashValue");
                        FileHashValue.appendChild(doc.createTextNode(hashId));
                        FileItem.appendChild(FileHashValue);
                        Element KeyPathLocation = doc.createElement("KeyPathLocation");
                        KeyPathLocation.appendChild(doc.createCDATASection(filePath));
                        FileItem.appendChild(KeyPathLocation);
                        SupportFileItems.appendChild(FileItem);
                     } catch (Exception var49) {
                        logger.error("", var49);
                     }

                     File moveFile = null;

                     try {
                        if (!bExistFile) {
                           moveFile = new File(SecurityUtils.directoryTraversalChecker(contentHome + File.separator + fileId + File.separator + tempFile.getName(), (String)null));
                           moveFile.getParentFile().mkdir();
                           InputStream input = null;
                           FileOutputStream output1 = null;

                           try {
                              input = new FileInputStream(tempFile);
                              output1 = new FileOutputStream(moveFile);
                              byte[] buf = new byte[1024];

                              int bytesRead;
                              while((bytesRead = input.read(buf)) > 0) {
                                 output1.write(buf, 0, bytesRead);
                              }
                           } catch (Exception var52) {
                           } finally {
                              input.close();
                              output1.close();
                           }
                        }
                     } catch (Exception var54) {
                        logger.error("", var54);
                     }

                     try {
                        if (!bExistFile && moveFile != null) {
                           ContentFile cmsContentFile = new ContentFile();
                           cmsContentFile.setIs_streaming("N");
                           cmsContentFile.setFile_id(fileId);
                           cmsContentFile.setHash_code(getHash(moveFile));
                           cmsContentFile.setFile_type("MAIN");
                           cmsContentFile.setFile_name(moveFile.getName());
                           cmsContentFile.setFile_size(moveFile.length());
                           cmsContentFile.setCreator_id(userId);
                           cmsContentFile.setFile_path(moveFile.getPath());
                           fileListToSave.add(cmsContentFile);
                        }
                     } catch (Exception var48) {
                        logger.error("", var48);
                     }
                  }
               }
            }
         }
      } catch (Exception var55) {
         if (output != null) {
            ((OutputStream)output).close();
         }
      } finally {
         if (output != null) {
            ((OutputStream)output).close();
         }

      }

   }

   public static void makeLFDForWebContent(String contentId) {
      File lfdFile = new File("path");

      try {
         String var2 = getHash(lfdFile);
      } catch (Exception var3) {
         logger.error("", var3);
      }

   }

   private static String getHash(File file) throws Exception {
      FileInputStream fileIS = null;

      String var24;
      try {
         StringBuffer hash = new StringBuffer("");
         int index = 0;
         ByteBuffer buf = ByteBuffer.allocate(1024);
         FileChannel fileChannel;
         if (!file.exists()) {
            fileChannel = null;
            return fileChannel;
         }

         fileIS = new FileInputStream(file);
         fileChannel = fileIS.getChannel();
         MessageDigest messageDigest = MessageDigest.getInstance("SHA1");

         int nread;
         for(long fileOffsetLong = 0L; (nread = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)nread) {
            buf.flip();
            messageDigest.update(buf);
            buf.clear();
         }

         byte[] digest = messageDigest.digest();
         StringBuffer tmpSB = new StringBuffer();
         byte[] var12 = digest;
         int var13 = digest.length;

         for(int var14 = 0; var14 < var13; ++var14) {
            byte b = var12[var14];
            StringBuffer str = new StringBuffer(Integer.toHexString(b & 255));
            if (str.length() == 1) {
               str = (new StringBuffer("0")).append(str);
            }

            if (index > 7 && index < 16) {
               tmpSB = tmpSB.append(str);
            } else {
               tmpSB = str.append(tmpSB);
            }

            if (index == 3 || index == 5 || index == 7 || index == 9 || index == 15) {
               hash.append(tmpSB).append("-");
               tmpSB = new StringBuffer();
            }

            ++index;
         }

         hash.append(tmpSB);
         if (!hash.equals("")) {
            var24 = hash.toString().toUpperCase();
            return var24;
         }

         var24 = null;
      } catch (IOException var21) {
         logger.error("", var21);
         throw new Exception();
      } catch (NoSuchAlgorithmException var22) {
         throw new Exception();
      } finally {
         fileIS.close();
      }

      return var24;
   }

   public static ContentFile createSfiFile(Map data, String userId, String contentId, String hashId, String fileId, String fileName) {
      try {
         ContentInfo cmsDao = ContentInfoImpl.getInstance();
         String sfiFileID = UUID.randomUUID().toString().toUpperCase();
         String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
         File sfiFile = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + sfiFileID + File.separator + fileName + ".sfi", (String)null));
         sfiFile.getParentFile().mkdir();
         long fileSize = 0L;
         fileSize = (Long)data.get("fileSize");
         StringBuffer sb = new StringBuffer("");
         sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n");
         sb.append("<Content id=\"").append(contentId).append("\">\r\n");
         sb.append("  <MainFileItem>\r\n");
         sb.append("    <FileItem type=\"File\">\r\n");
         sb.append("      <PureFileItem><![CDATA[").append(ChangeSpecialChar(fileName)).append("]]></PureFileItem>\r\n");
         sb.append("      <RealFullPath><![CDATA[").append(ChangeSpecialChar(fileName)).append("]]></RealFullPath>\r\n");
         sb.append("      <KeyPathLocation><![CDATA[").append(ChangeSpecialChar(fileName)).append("]]></KeyPathLocation>\r\n");
         sb.append("      <FileID><![CDATA[").append(fileId).append("]]></FileID>\r\n");
         sb.append("      <FileSize><![CDATA[" + fileSize + "]]></FileSize>\r\n");
         sb.append("      <FileHashValue><![CDATA[").append(hashId).append("]]></FileHashValue>\r\n");
         sb.append("    </FileItem>\r\n");
         sb.append("  </MainFileItem>\r\n");
         sb.append("  <SupportFileItems>\r\n");
         sb.append("  </SupportFileItems>\r\n");
         sb.append("</Content>\r\n");
         sfiFile.createNewFile();
         PrintWriter print = new PrintWriter(sfiFile, "UTF-8");
         print.write(sb.toString());
         print.close();
         ContentFile cmsSFIContentFile = new ContentFile();
         cmsSFIContentFile.setIs_streaming("N");
         cmsSFIContentFile.setFile_id(sfiFileID);
         cmsSFIContentFile.setHash_code(getHash(sfiFile));
         cmsSFIContentFile.setFile_type("SFI");
         cmsSFIContentFile.setFile_name(fileName + ".sfi");
         cmsSFIContentFile.setFile_size(sfiFile.length());
         cmsSFIContentFile.setCreator_id(userId);
         cmsSFIContentFile.setFile_path(sfiFile.getParentFile().getPath());
         cmsDao.addFile(cmsSFIContentFile);
         return cmsSFIContentFile;
      } catch (Exception var15) {
         logger.error("", var15);
         return null;
      }
   }

   public static String ChangeSpecialChar(String str) {
      return str.replaceAll("<", "\\\\");
   }

   public static String parseForLFD(Content content, String expression) throws ConfigException, SAXException, IOException, ParserConfigurationException, XPathExpressionException {
      String rtn = null;
      String CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      if (content != null) {
         InputSource is = new InputSource(new FileReader(CONTENTS_HOME + File.separator + content.getMain_file_id() + File.separator + content.getMain_file_name()));
         DocumentBuilderFactory dbf = DocumentUtils.getDocumentBuilderFactoryInstance();
         Document document = dbf.newDocumentBuilder().parse(is);
         XPath xpath = XPathFactory.newInstance().newXPath();
         Node node = (Node)xpath.evaluate(expression, document, XPathConstants.NODE);
         if (node != null) {
            rtn = node.getChildNodes().item(0).getTextContent();
         }
      }

      return rtn;
   }

   public static String getCifsLocalPath(String contentHome, String ipAddress, String cifsLoginId, String path, String userId) {
      if (path.charAt(0) != '/') {
         path = "/" + path;
      }

      String[] temp = path.split("/");
      String tempDir = "";
      if (temp != null && temp.length > 0) {
         for(int i = 0; i < temp.length; ++i) {
            tempDir = tempDir + temp[i];
            if (i < temp.length - 1) {
               tempDir = tempDir + "/";
            }
         }
      }

      return contentHome != null && !contentHome.equals("") ? contentHome + File.separator + "CIFS_" + getFolderIp(ipAddress) + '_' + cifsLoginId.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + '_' + tempDir.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + '_' + userId : "CIFS_" + getFolderIp(ipAddress) + '_' + cifsLoginId.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + '_' + tempDir.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + '_' + userId;
   }

   public static String getFtpLocalPath(String contentHome, String serverIp, String loginId, String directory, String userId) {
      return contentHome != null && !contentHome.equals("") ? contentHome + File.separator + "FTP_" + getFolderIp(serverIp) + '_' + loginId.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + '_' + directory.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + userId : "FTP_" + getFolderIp(serverIp) + '_' + loginId.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + '_' + directory.replace('/', '_').replace('\\', '_').replace('.', '_').replace(' ', '_') + userId;
   }

   public static void deleteThumbFiles(String contentID) {
      try {
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         List thumbList = contentInfo.getThumbFileList(contentID);

         for(int k = 0; k < thumbList.size(); ++k) {
            Map map = (Map)thumbList.get(k);
            String thumb_file_id = (String)map.get("THUMB_FILE_ID");
            if (contentInfo.isDeletableFile(thumb_file_id, contentID) && !thumb_file_id.endsWith("_THUMBNAIL")) {
               String thumb_file_name = contentInfo.getFileName((String)map.get("THUMB_FILE_ID"));
               String thumb_path = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar) + File.separator + thumb_file_id + File.separator;
               String thumb_folder = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar) + File.separator + thumb_file_id + File.separator;
               File delFile = SecurityUtils.getSafeFile(thumb_path + thumb_file_name);
               File delMediumFile = SecurityUtils.getSafeFile(thumb_path + thumb_file_name + "_MEDIUM.PNG");
               File delSmallFile = SecurityUtils.getSafeFile(thumb_path + thumb_file_name + "_SMALL.PNG");
               File delHDFile = SecurityUtils.getSafeFile(thumb_path + FilenameUtils.removeExtension(thumb_file_name) + "_HD.PNG");
               File delFolder = SecurityUtils.getSafeFile(thumb_folder);
               delFile.delete();
               delMediumFile.delete();
               delSmallFile.delete();
               if (delHDFile.exists()) {
                  delHDFile.delete();
               }

               delFolder.delete();
            }
         }
      } catch (Exception var14) {
         logger.error(var14);
      }

   }

   public static String getContentsHome() throws ConfigException {
      if (StringUtils.isBlank(contentsHome)) {
         StringBuilder sb = new StringBuilder();
         sb.append(CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar));
         sb.append(File.separatorChar);
         sb.append("contents_home");
         contentsHome = sb.toString();
      }

      return contentsHome;
   }

   public static String getThumbnailHome() throws ConfigException {
      if (StringUtils.isBlank(thumbnailHome)) {
         StringBuilder sb = new StringBuilder();
         sb.append(CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar));
         thumbnailHome = sb.toString();
      }

      return thumbnailHome;
   }

   public static boolean updateLastModifiedDate(String contentId) throws SQLException, ConfigException {
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      return contentInfo.setContentModifiedDate(contentId) > 0;
   }

   public static ContentFile createAdsContentConfigFile(Map adsContentConfigData) {
      if (adsContentConfigData == null) {
         return null;
      } else {
         String CONTENTS_HOME = null;

         try {
            CONTENTS_HOME = getContentsHome();
         } catch (ConfigException var33) {
            return null;
         }

         File cmsHome = SecurityUtils.getSafeFile(CONTENTS_HOME);
         if (!cmsHome.exists()) {
            cmsHome.mkdir();
         }

         File adsConfigFile = null;
         String adsConfigFileId = UUID.randomUUID().toString().toUpperCase();
         String adsConfigFileName = "adsConfig.json";
         String adsConfigFilePath = SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + adsConfigFileId + File.separator + adsConfigFileName, (String)null);
         adsConfigFile = new File(adsConfigFilePath);
         adsConfigFile.getParentFile().mkdir();
         JSONObject adsContentConfigJson = new JSONObject(adsContentConfigData);

         Throwable var9;
         try {
            FileWriter writer = new FileWriter(adsConfigFile, false);
            var9 = null;

            try {
               if (!adsConfigFile.exists()) {
                  adsConfigFile.createNewFile();
               }

               writer.write(adsContentConfigJson.toString());
               writer.flush();
            } catch (Throwable var34) {
               var9 = var34;
               throw var34;
            } finally {
               if (writer != null) {
                  if (var9 != null) {
                     try {
                        writer.close();
                     } catch (Throwable var29) {
                        var9.addSuppressed(var29);
                     }
                  } else {
                     writer.close();
                  }
               }

            }
         } catch (Exception var36) {
            logger.error(var36);

            try {
               delete(CONTENTS_HOME + File.separator + adsConfigFileId + File.separator);
            } catch (Exception var28) {
               logger.error(var28);
            }

            return null;
         }

         ContentInfo contentDao = ContentInfoImpl.getInstance();
         var9 = null;

         try {
            Long fileSize = SecurityUtils.getSafeFile(adsConfigFilePath).length();
            String fileHash = FileUtils.getHash(adsConfigFile);
            ContentFile adsContentConfigFile;
            if (contentDao.numberOfExistingFileByHash("adsConfig.json", fileSize, fileHash) > 0) {
               String fileId = contentDao.getFileIDByHash("adsConfig.json", fileSize, fileHash);
               adsContentConfigFile = contentDao.getFileInfo(fileId);

               try {
                  delete(CONTENTS_HOME + File.separator + adsConfigFileId + File.separator);
               } catch (Exception var31) {
                  logger.error(var31);
               }
            } else {
               try {
                  adsContentConfigFile = new ContentFile();
                  adsContentConfigFile.setIs_streaming("N");
                  adsContentConfigFile.setFile_type("MAIN");
                  adsContentConfigFile.setFile_id(adsConfigFileId);
                  adsContentConfigFile.setFile_path(adsConfigFile.getParentFile().getPath());
                  adsContentConfigFile.setCreator_id(SecurityUtils.getLoginUserId());
                  adsContentConfigFile.setHash_code(FileUtils.getHash(adsConfigFile));
                  adsContentConfigFile.setFile_name("adsConfig.json");
                  adsContentConfigFile.setFile_type("JSON");
                  adsContentConfigFile.setFile_size(SecurityUtils.getSafeFile(adsConfigFilePath).length());
                  contentDao.addFile(adsContentConfigFile);
               } catch (Exception var30) {
                  try {
                     delete(CONTENTS_HOME + File.separator + adsConfigFileId + File.separator);
                  } catch (Exception var27) {
                     logger.error(var27);
                  }

                  return null;
               }
            }

            return adsContentConfigFile;
         } catch (Exception var32) {
            try {
               delete(CONTENTS_HOME + File.separator + adsConfigFileId + File.separator);
            } catch (Exception var26) {
               logger.error(var26);
            }

            return null;
         }
      }
   }
}
