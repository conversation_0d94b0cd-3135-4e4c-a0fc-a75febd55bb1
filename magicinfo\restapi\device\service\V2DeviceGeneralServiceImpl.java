package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.ExternalSystemUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.device.deviceInfo.dao.DeviceDao;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGeneralConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceModel;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceInfoImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.device.vwlLayout.dao.VwlLayoutDao;
import com.samsung.magicinfo.framework.device.vwlLayout.entity.VwlDeviceLayout;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManager;
import com.samsung.magicinfo.framework.monitoring.manager.MonitoringManagerImpl;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManager;
import com.samsung.magicinfo.framework.setup.manager.SlmLicenseManagerImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGeneral;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGeneralResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.rms.model.DeviceGeneralConfResource;
import com.samsung.magicinfo.rms.util.DeviceModelConverter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.ibatis.session.SqlSession;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2DeviceGeneralService")
@Transactional
public class V2DeviceGeneralServiceImpl implements V2DeviceGeneralService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceApprovalServiceImpl.class);
   private final String PRODUCT_TYPE = "PREMIUM";
   private final String ALL_MDC = "ALL_MDC";
   private final Map supportMap = new HashMap();
   DeviceInfo deviceDao = DeviceInfoImpl.getInstance();
   DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
   MonitoringManager monMgr = MonitoringManagerImpl.getInstance();
   DeviceDao dao = new DeviceDao((SqlSession)null);

   public V2DeviceGeneralServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource getGeneralInfo(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var20) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonBulkResultResource resources = new V2CommonBulkResultResource();
      List SuccessList = new ArrayList();
      List failList = new ArrayList();
      DeviceInfo deviceInfo = DeviceInfoImpl.getInstance();
      Iterator var8 = deviceIds.getIds().iterator();

      while(true) {
         while(var8.hasNext()) {
            String deviceId = (String)var8.next();
            V2DeviceGeneralResource resource = new V2DeviceGeneralResource();
            DeviceGeneralConf generalInfo = this.deviceDao.getDeviceGeneralConf(deviceId, false);
            DeviceGeneralConf generalInfoFromDB = this.deviceDao.getDeviceGeneralConf(deviceId, true);
            generalInfo.setDisk_space_repository(generalInfoFromDB.getDisk_space_repository());
            generalInfo.setDisk_space_usage(generalInfoFromDB.getDisk_space_usage());
            generalInfo.setDisk_space_available(generalInfoFromDB.getDisk_space_available());
            if (generalInfo == null) {
               this.logger.error("[REST_v2.0][DEVICE SERVICE][getGeneralInfo] cannot get device general configuration : " + deviceId);
               failList.add(deviceId);
            } else {
               generalInfo.initParamsForJson();
               DeviceGeneralConfResource generalInfoCamel = DeviceModelConverter.newConvertGeneralInfoToCamelStyle(generalInfo);
               List tmpModelNameList = new ArrayList();
               List modelNameList = this.deviceDao.getModelNameListByDeviceId(deviceId);
               if (modelNameList != null) {
                  Iterator var15;
                  DeviceModel deviceModel;
                  if ("LPLAYER".equalsIgnoreCase(generalInfo.getDevice_type())) {
                     var15 = modelNameList.iterator();

                     while(var15.hasNext()) {
                        deviceModel = (DeviceModel)var15.next();
                        tmpModelNameList.add(StrUtils.getLiteDeviceModelName(deviceModel.getDevice_model_name()));
                     }
                  } else {
                     var15 = modelNameList.iterator();

                     while(var15.hasNext()) {
                        deviceModel = (DeviceModel)var15.next();
                        tmpModelNameList.add(deviceModel.getDevice_model_name());
                     }
                  }
               }

               String mapLocation = null;

               try {
                  mapLocation = this.dao.getMapLocationByDeviceId(deviceId);
               } catch (Exception var19) {
                  this.logger.error("[REST_v2.0][DEVICE SERVICE][getGeneralInfo] Map location does not exist.", var19);
               }

               generalInfoCamel.setMapLocation(mapLocation);
               generalInfoCamel.setModelNameList(tmpModelNameList);
               generalInfoCamel.setPower(this.monMgr.isConnected(deviceId));
               resource.setApprovedDate(generalInfo.getCreate_date().getTime());
               if (deviceInfo.getDeviceMemo(deviceId) != null) {
                  resource.setMemo(deviceInfo.getDeviceMemo(deviceId).getMemo());
               }

               resource.setRecommendPlay(deviceInfo.getRecommendPlayByDeviceId(deviceId));
               if (generalInfo.getVwt_id() != null && !generalInfo.getVwt_id().equals("")) {
                  VwlLayoutDao vwlLayoutDao = new VwlLayoutDao();

                  try {
                     VwlDeviceLayout vwlDeviceLayout = vwlLayoutDao.getVwlLayoutByDeviceIdAndVwtId(deviceId, generalInfo.getVwt_id());
                     resource.setVwlDeviceLayout(vwlDeviceLayout);
                  } catch (Exception var18) {
                     this.logger.error("[REST_v2.0][DEVICE SERVICE][getGeneralInfo] Exception while getting vwlLayout info", var18);
                  }
               }

               SlmLicenseManager slmlicenseMgr = SlmLicenseManagerImpl.getInstance();
               int licCnt = slmlicenseMgr.getLicenseCountByProductCode("010D0F");
               if (licCnt > 0) {
                  resource.setHasE2ELicense(true);
               } else {
                  resource.setHasE2ELicense(false);
               }

               resource.setGeneralConf(generalInfoCamel);
               SuccessList.add(resource);
            }
         }

         resources.setSuccessList(SuccessList);
         resources.setFailList(failList);
         return resources;
      }
   }

   @PreAuthorize("hasAnyAuthority('Device Control Authority')")
   public V2DeviceReqServiceResource updateGeneralInfo(V2DeviceGeneral param) throws Exception {
      boolean checkFlag = false;
      List deviceIds = param.getDeviceIds();
      Iterator var4 = deviceIds.iterator();

      String requestId;
      while(var4.hasNext()) {
         requestId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, requestId);
         } catch (Exception var23) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      Iterator var25 = deviceIds.iterator();

      String e2eEnable;
      while(var25.hasNext()) {
         e2eEnable = (String)var25.next();
         if (!DeviceUtils.isConnected(e2eEnable)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      requestId = UUID.randomUUID().toString();
      e2eEnable = StrUtils.nvl(CommonConfig.get("e2e.enable"));
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var9 = deviceIds.iterator();

      while(var9.hasNext()) {
         String deviceId = (String)var9.next();
         boolean flag = false;
         boolean sendMo = false;
         DeviceGeneralConf deviceGeneralConf = new DeviceGeneralConf();
         deviceGeneralConf.setDevice_id(deviceId);
         String deviceName = null;
         String deviceModelName = null;
         String location = null;
         String mapLocation = null;
         if (!StrUtils.nvl(param.getDeviceName()).equals("")) {
            deviceName = param.getDeviceName();
            deviceGeneralConf.setDevice_name(deviceName);
            sendMo = true;
            flag = true;
         }

         if (!StrUtils.nvl(param.getDeviceModelName()).equals("")) {
            if (param.getDeviceModelName().startsWith("LITE_")) {
               deviceModelName = param.getDeviceModelName().substring(5);
            } else {
               deviceModelName = param.getDeviceModelName();
            }

            deviceGeneralConf.setDevice_model_name(deviceModelName);
            flag = true;
         }

         if (param.getLocation() != null) {
            location = param.getLocation().trim();
            if (!e2eEnable.equalsIgnoreCase("true")) {
               deviceGeneralConf.setLocation(location);
               flag = true;
            } else {
               SlmLicenseManager slmLicenseManager = SlmLicenseManagerImpl.getInstance();
               boolean res = false;
               if (CommonConfig.get("e2e.license.system") != null && !CommonConfig.get("e2e.license.system").toUpperCase().equals(ExternalSystemUtils.SYSTEM_PBP)) {
                  res = slmLicenseManager.modifyDeviceLocationProcessForE2E_SLMDirect(deviceId, location);
               } else {
                  res = slmLicenseManager.modifyDeviceLocationProcessForE2E(deviceId, location);
               }

               if (res) {
                  deviceGeneralConf.setLocation(location);
                  flag = true;
               } else {
                  flag = false;
               }
            }
         }

         if (param.getMapLocation() != null) {
            mapLocation = param.getMapLocation().trim();
            deviceGeneralConf.setMap_location(mapLocation);
            flag = true;
         }

         if (!flag) {
            resource.setFailList(deviceIds);
            this.logger.info("The fix does not exist.");
         }

         try {
            if (flag) {
               this.deviceDao.setDeviceNameAndLocation(deviceId, deviceName, location, mapLocation, deviceModelName);
            }

            if (param.isRecommendPlay() != null) {
               boolean enable = param.isRecommendPlay();
               if (enable) {
                  SlmLicenseManager slmlicenseMgr = SlmLicenseManagerImpl.getInstance();
                  int licCnt = slmlicenseMgr.getLicenseCountByProductCode("010D0F");
                  int curCnt = this.deviceDao.getCntRecommendPlayDevice();
                  if (curCnt >= licCnt) {
                     this.logger.error("AnalyticsLicFail");
                     throw new RestServiceException(RestExceptionCode.BAD_REQUEST_LICENSE_QUANTITY_EXCEED);
                  }
               }

               this.deviceDao.setRecommendPlayByDeviceId(deviceId, enable);
            }

            if (sendMo) {
               this.confManager.reqSetGeneralToDevice(deviceGeneralConf, requestId);
            }

            if (flag) {
               successList.add(deviceId);
            }
         } catch (NullPointerException var22) {
            this.logger.error("[REST_v2.0][DEVICE SERVICE][updateGeneralInfo] " + deviceId + " , " + requestId);
            failList.add(deviceId);
         }

         if (param.isSendCleanStorageFlag()) {
            this.confManager.reqGetDevicePredefinedCmd(deviceId, "CLEANUP_STORAGE_DATA", requestId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      if (successList.size() > 0) {
         resource.setRequestId(requestId);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2DeviceReqServiceResource reqGetGeneral(V2CommonIds body) throws Exception {
      String sessionId = UUID.randomUUID().toString();
      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      List deviceIds = body.getIds();
      boolean checkFlag = false;
      Iterator var8 = deviceIds.iterator();

      String deviceId;
      while(var8.hasNext()) {
         deviceId = (String)var8.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var12) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      var8 = deviceIds.iterator();

      while(var8.hasNext()) {
         deviceId = (String)var8.next();

         try {
            if (DeviceUtils.isConnected(deviceId)) {
               this.confManager.reqGetGeneralFromDevice(deviceId, sessionId);
               successList.add(deviceId);
            } else {
               failList.add(deviceId);
            }
         } catch (Exception var11) {
            this.logger.error("", var11);
            failList.add(deviceId);
         }
      }

      resource.setRequestId(sessionId);
      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2CommonBulkResultResource getCurrentGeneralWithRequestId(V2DeviceReqServiceConf body) throws Exception {
      DeviceConfManager confMgr = DeviceConfManagerImpl.getInstance();
      List deviceIds = body.getDeviceIds();
      boolean checkFlag = false;
      Iterator var5 = deviceIds.iterator();

      while(var5.hasNext()) {
         String deviceId = (String)var5.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var12) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var7 = deviceIds.iterator();

      while(var7.hasNext()) {
         String deviceId = (String)var7.next();
         DeviceGeneralConf deviceGeneralConf = confMgr.getGeneralResultSet(deviceId, body.getRequestId());
         if (deviceGeneralConf == null) {
            failList.add(deviceId);
         } else {
            DeviceGeneralConf generalInfoFromDB = this.deviceDao.getDeviceGeneralConf(deviceId, true);
            deviceGeneralConf.setDevice_id(deviceId);
            if (generalInfoFromDB.getDisk_space_repository() != null) {
               deviceGeneralConf.setDisk_space_repository(generalInfoFromDB.getDisk_space_repository());
            }

            if (generalInfoFromDB.getApplication_version() != null) {
               deviceGeneralConf.setApplication_version(generalInfoFromDB.getApplication_version());
            }

            DeviceGeneralConfResource deviceGeneralConfResource = DeviceModelConverter.newConvertGeneralInfoToCamelStyle(deviceGeneralConf);
            successList.add(deviceGeneralConfResource);
         }
      }

      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   public String convertString(List list) {
      String result = "";
      if (!list.isEmpty() && list.size() > 0) {
         StringBuffer strBuf = new StringBuffer();

         for(int i = 0; i < list.size(); ++i) {
            strBuf.append(list.get(i));
            if (i < list.size() - 1) {
               strBuf.append(",");
            }
         }

         result = strBuf.toString();
      }

      return result;
   }
}
