package com.samsung.magicinfo.dms.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.CommonUtils;
import com.samsung.magicinfo.dms.model.ContentScheduleResource;
import com.samsung.magicinfo.dms.model.ScheduleFilter;
import com.samsung.magicinfo.dms.service.ContentScheduleService;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import java.sql.SQLException;
import javax.validation.Valid;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "Basic Schedules Management System Rest api",
   description = "Operations pertaining to schedules in Basic Schedules Management System",
   tags = {"Schedule API Group"}
)
@RestController
@RequestMapping({"/restapi/v1.0/dms/schedule/contents"})
public class ContentScheduleController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private ContentScheduleService contentScheduleService;

   public ContentScheduleController() {
      super();
   }

   @ApiOperation(
      value = "get schedule information at dashboard",
      notes = "get schedule information at dashboard(runningCount, totalCount, reservedCount, notSetDeviceCount).",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/dashboard"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listDashboardScheduleInfo() {
      this.logger.info("[REST][CONTENT SCHEDULE][listDashboardScheduleInfo] get content schedule information used in the dashboard");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentScheduleService.listDashboardScheduleInfo();
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT SCHEDULE][listDashboardScheduleInfo] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT SCHEDULE][listDashboardScheduleInfo] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var3) {
         responsebody.setErrorMessage(var3.getMessage());
         this.logger.error("[REST][CONTENT SCHEDULE][listDashboardScheduleInfo] access denied.");
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var4) {
         this.logger.error("[REST][CONTENT SCHEDULE][listDashboardScheduleInfo] Exception is occured. " + var4.toString());
         responsebody.setErrorMessage(var4.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "delete content schedule by programId",
      notes = "delete content schedule by programId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{programId}"},
      method = {RequestMethod.DELETE}
   )
   public ResponseEntity deleteContentSchedule(@PathVariable String programId, @RequestParam(value = "permanently",required = false) Boolean permanently) throws SQLException {
      this.logger.info("[REST][CONTENT SCHEDULE][deleteContentSchedule][" + programId + "] delete content schedule by programId");
      ResponseBody responsebody = new ResponseBody();

      try {
         String userId = "admin";
         if (permanently != null && permanently) {
            responsebody = this.contentScheduleService.permanentlyDeleteContentSchedule(programId, userId);
         } else {
            responsebody = this.contentScheduleService.deleteContentSchedule(programId, userId);
         }

         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT SCHEDULE][deleteContentSchedule][" + programId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT SCHEDULE][deleteContentSchedule][" + programId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var5) {
         responsebody.setErrorMessage(var5.getMessage());
         this.logger.error("[REST][CONTENT SCHEDULE][deleteContentSchedule][" + programId + "] access denied.");
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         this.logger.error("[REST][CONTENT SCHEDULE][deleteContentSchedule][" + programId + "] Exception is occured. " + var6.toString());
         responsebody.setErrorMessage(var6.getMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "get contentSchedule list all",
      notes = "get contentSchedule list.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listContentScheduleAll(@RequestParam(value = "startIndex",required = true) int startIndex, @RequestParam(value = "pageSize",required = true) int pageSize) throws Exception {
      this.logger.info("[REST][CONTENT SCHEDULE][listContentScheduleAll] get all content schedule list");
      ResponseBody responsebody = new ResponseBody();
      if (!CommonUtils.checkAvailable("schedule")) {
         return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
      } else {
         try {
            ScheduleFilter filter = new ScheduleFilter();
            responsebody = this.contentScheduleService.listContentSchedules(startIndex, pageSize, filter);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT SCHEDULE][listContentScheduleAll] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT SCHEDULE][listContentScheduleAll] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            responsebody.setErrorMessage(var5.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][listContentScheduleAll] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            this.logger.error("[REST][CONTENT SCHEDULE][listContentScheduleAll] Exception is occured. " + var6.toString());
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @KPI
   @ApiOperation(
      value = "get contentSchedule list by filter",
      notes = "get contentSchedule list by filter.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/filter"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity listContentScheduleByGroup(@Valid @RequestBody ScheduleFilter filter, BindingResult result) throws Exception {
      this.logger.info("[REST][CONTENT SCHEDULE][listContentScheduleByGroup] get content schedule list by filter");
      ResponseBody responsebody = new ResponseBody();
      if (!CommonUtils.checkAvailable("schedule")) {
         return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
      } else if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNPROCESSABLE_ENTITY);
      } else {
         try {
            responsebody = this.contentScheduleService.listContentSchedules(filter.getStartIndex(), filter.getPageSize(), filter);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT SCHEDULE][listContentScheduleByGroup] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT SCHEDULE][listContentScheduleByGroup] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            responsebody.setErrorMessage(var5.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][listContentScheduleByGroup] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            this.logger.error("[REST][CONTENT SCHEDULE][listContentScheduleByGroup] Exception is occured. " + var6.toString());
            responsebody.setErrorMessage(var6.getMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "get contentSchedule view when user clicked",
      notes = "get contentSchedule view.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{programId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getProgramInfo(@PathVariable String programId) throws Exception {
      this.logger.info("[REST][CONTENT SCHEDULE][getProgramInfo][" + programId + "] get content schedule detail information by programId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentScheduleService.getContentSchedule(programId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT SCHEDULE][getProgramInfo][" + programId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT SCHEDULE][getProgramInfo][" + programId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         responsebody.setErrorMessage(var4.getMessage());
         this.logger.error("[REST][CONTENT SCHEDULE][getProgramInfo][" + programId + "] access denied.");
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         responsebody.setErrorMessage(var5.getMessage());
         this.logger.error("[REST][CONTENT SCHEDULE][getProgramInfo][" + programId + "] Exception is occured. " + var5.toString());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @KPI
   @ApiOperation(
      value = "create the content schedule",
      notes = "create the content schedule.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.POST},
      produces = {"application/json"},
      consumes = {"application/json"}
   )
   public ResponseEntity createContentSchedule(@Valid @RequestBody ContentScheduleResource params, BindingResult result) throws SQLException, ConfigException {
      this.logger.info("[REST][CONTENT SCHEDULE][createContentSchedule] create the content schedule");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNPROCESSABLE_ENTITY);
      } else {
         try {
            responsebody.setApiVersion("1.0");
            responsebody = this.contentScheduleService.createContentSchedule(params);
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT SCHEDULE][createContentSchedule] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][CONTENT SCHEDULE][createContentSchedule] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            responsebody.setErrorMessage(var5.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][createContentSchedule] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][createContentSchedule] Exception is occured. " + var6.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @KPI
   @ApiOperation(
      value = "edit the content schedule by programId",
      notes = "edit the content schedule by programId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{programId}"},
      method = {RequestMethod.PUT},
      produces = {"application/json"},
      consumes = {"application/json"}
   )
   public ResponseEntity editContentSchedule(@PathVariable("programId") String programId, @Valid @RequestBody ContentScheduleResource params, BindingResult result) throws SQLException, ConfigException {
      this.logger.info("[REST][CONTENT SCHEDULE][editContentSchedule][" + programId + "] edit the content schedule by programId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNPROCESSABLE_ENTITY);
      } else {
         try {
            responsebody.setApiVersion("1.0");
            responsebody = this.contentScheduleService.editContentSchedule(programId, params);
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT SCHEDULE][editContentSchedule][" + programId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT SCHEDULE][editContentSchedule][" + programId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][editContentSchedule][" + programId + "] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            responsebody.setErrorMessage(var7.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][editContentSchedule][" + programId + "] Exception is occured. " + var7.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "deploy the content schedule by programId",
      notes = "deploy the content schedule by programId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{programId}/deploy"},
      method = {RequestMethod.PUT},
      produces = {"application/json"},
      consumes = {"application/json"}
   )
   public ResponseEntity deployContentSchedule(@PathVariable String programId, @Valid @RequestBody ContentScheduleResource params, BindingResult result) throws SQLException, ConfigException {
      this.logger.info("[REST][CONTENT SCHEDULE][deployContentSchedule][" + programId + "] deploy the content schedule by programId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNPROCESSABLE_ENTITY);
      } else {
         try {
            responsebody.setApiVersion("1.0");
            responsebody = this.contentScheduleService.deployContentSchedule(programId, params);
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT SCHEDULE][deployContentSchedule][" + programId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][CONTENT SCHEDULE][deployContentSchedule][" + programId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][deployContentSchedule][" + programId + "] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            responsebody.setErrorMessage(var7.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][deployContentSchedule][" + programId + "] Exception is occured. " + var7.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "copy the content schedule by programId",
      notes = "copy the content schedule by programId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{programId}/copy"},
      method = {RequestMethod.PUT},
      produces = {"application/json"},
      consumes = {"application/json"}
   )
   public ResponseEntity copyContentSchedule(@PathVariable String programId, @Valid @RequestBody ContentScheduleResource params, BindingResult result) throws SQLException, ConfigException {
      this.logger.info("[REST][CONTENT SCHEDULE][copyContentSchedule][" + programId + "] copy the content schedule by programId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNPROCESSABLE_ENTITY);
      } else {
         try {
            responsebody.setApiVersion("1.0");
            responsebody = this.contentScheduleService.copyContentSchedule(programId, params);
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT SCHEDULE][copyContentSchedule][" + programId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][CONTENT SCHEDULE][copyContentSchedule][" + programId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][copyContentSchedule][" + programId + "] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            responsebody.setErrorMessage(var7.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][copyContentSchedule][" + programId + "] Exception is occured. " + var7.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "check the content schedule publish status by programId",
      notes = "check the content schedule publish status by programId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{programId}/publishStatus"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getPublishStatus(@PathVariable String programId) throws Exception {
      this.logger.info("[REST][CONTENT SCHEDULE][getPublishStatus][" + programId + "] check the content schedule publish status by programId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.contentScheduleService.getPublishStatus(programId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equals("Success")) {
            this.logger.info("[REST][CONTENT SCHEDULE][getPublishStatus][" + programId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][CONTENT SCHEDULE][getPublishStatus][" + programId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         responsebody.setErrorMessage(var4.getMessage());
         this.logger.error("[REST][CONTENT SCHEDULE][getPublishStatus][" + programId + "] access denied.");
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         responsebody.setErrorMessage(var5.getMessage());
         this.logger.error("[REST][CONTENT SCHEDULE][getPublishStatus][" + programId + "] Exception is occured. " + var5.toString());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "edit the content schedule priority by programId and scheduleId",
      notes = "edit the content schedule priority by programId and scheduleId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{programId}/schedules/{scheduleId}/priority"},
      method = {RequestMethod.PUT},
      produces = {"application/json"},
      consumes = {"application/json"}
   )
   public ResponseEntity editContentSchedulePriority(@PathVariable("programId") String programId, @PathVariable("scheduleId") String scheduleId, @Valid @RequestBody ContentScheduleResource params, BindingResult result) throws SQLException, ConfigException {
      this.logger.info("[REST][CONTENT SCHEDULE][Priority][" + programId + "][" + scheduleId + "] edit the content schedule priority by programId and scheduleId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNPROCESSABLE_ENTITY);
      } else {
         try {
            responsebody.setApiVersion("1.0");
            responsebody = this.contentScheduleService.editContentSchedulePriority(programId, scheduleId, params);
            if (responsebody.getStatus().equals("Success")) {
               this.logger.info("[REST][CONTENT SCHEDULE][Priority][" + programId + "][" + scheduleId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][CONTENT SCHEDULE][Priority][" + programId + "][" + scheduleId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var7) {
            responsebody.setErrorMessage(var7.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][Priority][" + programId + "][" + scheduleId + "] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var8) {
            responsebody.setErrorMessage(var8.getMessage());
            this.logger.error("[REST][CONTENT SCHEDULE][Priority][" + programId + "][" + scheduleId + "] Exception is occured. " + var8.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }
}
