package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.db.mybatis.SqlSessionBaseDao;
import java.sql.SQLException;
import java.util.List;

public class FaceContentBasedRawFileDao extends SqlSessionBaseDao {
   public FaceContentBasedRawFileDao() {
      super();
   }

   private boolean isExistWeeklyReport(String weeklyCsvFileName) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).isExistWeeklyReport(weeklyCsvFileName);
   }

   private boolean isExistAbWeeklyReport(String weeklyCsvFileName) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).isExistAbWeeklyReport(weeklyCsvFileName);
   }

   private boolean isExistTbWeeklyReport(String weeklyCsvFileName) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).isExistTbWeeklyReport(weeklyCsvFileName);
   }

   private boolean isExistMonthlyReport(String monthlyCsvFileName) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).isExistMonthlyReport(monthlyCsvFileName);
   }

   private boolean isExistAbMonthlyReport(String monthlyCsvFileName) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).isExistAbMonthlyReport(monthlyCsvFileName);
   }

   private boolean isExistTbMonthlyReport(String monthlyCsvFileName) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).isExistTbMonthlyReport(monthlyCsvFileName);
   }

   public void addWeeklyReport(long root_group_id, int year, int month, int week_of_year, int week_of_month, String weeklyCsvFileName) throws SQLException {
      if (!this.isExistWeeklyReport(weeklyCsvFileName)) {
         ((FaceContentBasedRawFileDaoMapper)this.getMapper()).addWeeklyReport(root_group_id, year, month, week_of_year, week_of_month, weeklyCsvFileName);
      }

   }

   public void addAbWeeklyReport(long root_group_id, int year, int month, int week_of_year, int week_of_month, String weeklyCsvFileName) throws SQLException {
      if (!this.isExistAbWeeklyReport(weeklyCsvFileName)) {
         ((FaceContentBasedRawFileDaoMapper)this.getMapper()).addAbWeeklyReport(root_group_id, year, month, week_of_year, week_of_month, weeklyCsvFileName);
      }

   }

   public void addTbWeeklyReport(long root_group_id, int year, int month, int week_of_year, int week_of_month, String weeklyCsvFileName) throws SQLException {
      if (!this.isExistTbWeeklyReport(weeklyCsvFileName)) {
         ((FaceContentBasedRawFileDaoMapper)this.getMapper()).addTbWeeklyReport(root_group_id, year, month, week_of_year, week_of_month, weeklyCsvFileName);
      }

   }

   public void addMonthlyReport(long root_group_id, int year, int month, int week_of_year, String monthlyCsvFileName) throws SQLException {
      if (!this.isExistMonthlyReport(monthlyCsvFileName)) {
         ((FaceContentBasedRawFileDaoMapper)this.getMapper()).addMonthlyReport(root_group_id, year, month, week_of_year, monthlyCsvFileName);
      }

   }

   public void addAbMonthlyReport(long root_group_id, int year, int month, int week_of_year, String monthlyCsvFileName) throws SQLException {
      if (!this.isExistAbMonthlyReport(monthlyCsvFileName)) {
         ((FaceContentBasedRawFileDaoMapper)this.getMapper()).addAbMonthlyReport(root_group_id, year, month, week_of_year, monthlyCsvFileName);
      }

   }

   public void addTbMonthlyReport(long root_group_id, int year, int month, int week_of_year, String monthlyCsvFileName) throws SQLException {
      if (!this.isExistTbMonthlyReport(monthlyCsvFileName)) {
         ((FaceContentBasedRawFileDaoMapper)this.getMapper()).addTbMonthlyReport(root_group_id, year, month, week_of_year, monthlyCsvFileName);
      }

   }

   public List getDetailReportByRootGroupId(int root_group_id) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).getDetailReportByRootGroupId(root_group_id);
   }

   public List getYearListByRootGroupId(int root_group_id) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).getYearListByRootGroupId(root_group_id);
   }

   public List getYearListByAmsRootGroupId(int root_group_id) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).getYearListByAmsRootGroupId(root_group_id);
   }

   public List getMonthReportListByRootGroupId(String year, int root_group_id) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).getMonthReportListByRootGroupId(root_group_id, year);
   }

   public List getMonthReportListByAmsRootGroupId(String year, int root_group_id) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).getMonthReportListByAmsRootGroupId(root_group_id, year);
   }

   public List getWeekReportListByRootGroupId(String year, int root_group_id) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).getWeekReportListByRootGroupId(year, root_group_id);
   }

   public List getWeekReportListByAmsRootGroupId(String year, int root_group_id) throws SQLException {
      return ((FaceContentBasedRawFileDaoMapper)this.getMapper()).getWeekReportListByAmsRootGroupId(year, root_group_id);
   }
}
