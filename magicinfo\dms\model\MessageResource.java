package com.samsung.magicinfo.dms.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class MessageResource {
   private static final long serialVersionUID = 1686198042916410055L;
   Date date = new Date();
   Timestamp today;
   @JsonIgnore
   private String messageId;
   @JsonIgnore
   private long idx;
   @JsonIgnore
   private long version;
   @ApiModelProperty(
      example = "messageText1",
      required = true
   )
   private String messageText;
   @ApiModelProperty(
      example = "Arial",
      required = true
   )
   private String font;
   @ApiModelProperty(
      example = "4",
      required = true
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[MessageResource][size] Only number is available."
   )
   private String size;
   @ApiModelProperty(
      example = "N",
      required = true
   )
   @Pattern(
      regexp = "N|Y",
      message = "[MessageResource][bold] Only N, Y are available."
   )
   private String bold;
   @ApiModelProperty(
      example = "N",
      required = true
   )
   @Pattern(
      regexp = "N|Y",
      message = "[MessageResource][underline] Only N, Y are available."
   )
   private String underline;
   @ApiModelProperty(
      example = "N",
      required = true
   )
   @Pattern(
      regexp = "N|Y",
      message = "[MessageResource][italic] Only N, Y are available."
   )
   private String italic;
   @ApiModelProperty(
      example = "#000000",
      required = true
   )
   @Pattern(
      regexp = "^\\#[0-9]{6}$",
      message = "[MessageResource][bgColor] Pattern is wrong (#000000) "
   )
   private String color;
   @ApiModelProperty(
      example = "middle",
      required = true
   )
   @Pattern(
      regexp = "top|middle|bottom|left|right",
      message = "[MessageResource][position] Only top, middle, bottom, left, right are available."
   )
   private String position;
   @ApiModelProperty(
      example = "0",
      required = true
   )
   @Pattern(
      regexp = "^[0-9]*$",
      message = "[MessageResource][bgImage] Only number is available."
   )
   private String bgImage;
   @ApiModelProperty(
      example = "#111111"
   )
   @Pattern(
      regexp = "^\\#[0-9]{6}$",
      message = "[MessageResource][bgColor] Pattern is wrong (#000000) "
   )
   private String bgColor;
   @JsonIgnore
   private long bgTransparency;
   @ApiModelProperty(
      example = "none"
   )
   @Pattern(
      regexp = "none|up|down|left|right",
      message = "[MessageResource][direction] Only none, up, down, left, right are available."
   )
   private String direction;
   @ApiModelProperty(
      example = "normal"
   )
   @Pattern(
      regexp = "slow|normal|fast",
      message = "[MessageResource][speed] Only slow, normal, fast are available."
   )
   private String speed;
   @ApiModelProperty(
      example = "2016-01-01"
   )
   @Pattern(
      regexp = "^\\d{4}-(0[1-9]|1[0-2])-([0-2]\\d|3[01])$",
      message = "[PlaylistItemResource][startDate] Not yyyy-mm-dd pattern."
   )
   private String startDate;
   @ApiModelProperty(
      example = "2999-01-01"
   )
   @Pattern(
      regexp = "^\\d{4}-(0[1-9]|1[0-2])-([0-2]\\d|3[01])$",
      message = "[PlaylistItemResource][endDate] Not yyyy-mm-dd pattern."
   )
   private String endDate;
   @ApiModelProperty(
      example = "once"
   )
   @Pattern(
      regexp = "once|daily|day_of_week|day_of_month",
      message = "[MessageResource][repeatType] once, daily, day_of_week, day_of_month are available."
   )
   private String repeatType;
   private String weekdays;
   private String monthdays;
   @ApiModelProperty(
      example = "00:00:00"
   )
   private String startTime;
   private long duration;
   @ApiModelProperty(
      example = "admin",
      required = true
   )
   @Size(
      max = 20,
      message = "[MessageResource][userId] max size is 20."
   )
   private String userId;
   private Timestamp createDate;
   private String modifiedDate;
   @ApiModelProperty(
      example = "N"
   )
   @Pattern(
      regexp = "Y|N",
      message = "[MessageResource][isDeleted] Only Y, N are available."
   )
   private String isDeleted;

   public MessageResource() {
      super();
      this.today = new Timestamp(this.date.getTime());
      Date lm = new Date(System.currentTimeMillis());
      String today = (new SimpleDateFormat("yyyy-MM-dd")).format(lm);
      this.messageText = "";
      this.startDate = today;
      this.endDate = today;
      this.repeatType = "once";
      this.startTime = "00:00:00";
      this.duration = 0L;
      this.font = "Arial";
      this.size = "4";
      this.bold = "N";
      this.italic = "N";
      this.underline = "N";
      this.color = "#000000";
      this.bgImage = "0";
      this.bgColor = "";
      this.position = "middle";
      this.direction = "none";
      this.speed = "";
      this.bgTransparency = 0L;
      this.weekdays = "";
      this.monthdays = "";
   }

   public String getMessageId() {
      return this.messageId;
   }

   public void setMessageId(String messageId) {
      this.messageId = messageId;
   }

   public long getIdx() {
      return this.idx;
   }

   public void setIdx(long idx) {
      this.idx = idx;
   }

   public long getVersion() {
      return this.version;
   }

   public void setVersion(long version) {
      this.version = version;
   }

   public String getMessageText() {
      return this.messageText;
   }

   public void setMessageText(String messageText) {
      this.messageText = messageText;
   }

   public String getFont() {
      return this.font;
   }

   public void setFont(String font) {
      this.font = font;
   }

   public String getSize() {
      return this.size;
   }

   public void setSize(String size) {
      this.size = size;
   }

   public String getBold() {
      return this.bold;
   }

   public void setBold(String bold) {
      this.bold = bold;
   }

   public String getUnderline() {
      return this.underline;
   }

   public void setUnderline(String underline) {
      this.underline = underline;
   }

   public String getItalic() {
      return this.italic;
   }

   public void setItalic(String italic) {
      this.italic = italic;
   }

   public String getColor() {
      return this.color;
   }

   public void setColor(String color) {
      this.color = color;
   }

   public String getPosition() {
      return this.position;
   }

   public void setPosition(String position) {
      this.position = position;
   }

   public String getBgImage() {
      return this.bgImage;
   }

   public void setBgImage(String bgImage) {
      this.bgImage = bgImage;
   }

   public String getBgColor() {
      return this.bgColor;
   }

   public void setBgColor(String bgColor) {
      this.bgColor = bgColor;
   }

   public long getBgTransparency() {
      return this.bgTransparency;
   }

   public void setBgTransparency(long bgTransparency) {
      this.bgTransparency = bgTransparency;
   }

   public String getDirection() {
      return this.direction;
   }

   public void setDirection(String direction) {
      this.direction = direction;
   }

   public String getSpeed() {
      return this.speed;
   }

   public void setSpeed(String speed) {
      this.speed = speed;
   }

   public String getStartDate() {
      return this.startDate;
   }

   public void setStartDate(String startDate) {
      this.startDate = startDate;
   }

   public String getEndDate() {
      return this.endDate;
   }

   public void setEndDate(String endDate) {
      this.endDate = endDate;
   }

   public String getRepeatType() {
      return this.repeatType;
   }

   public void setRepeatType(String repeatType) {
      this.repeatType = repeatType;
   }

   public String getWeekdays() {
      return this.weekdays;
   }

   public void setWeekdays(String weekdays) {
      this.weekdays = weekdays;
   }

   public String getMonthdays() {
      return this.monthdays;
   }

   public void setMonthdays(String monthdays) {
      this.monthdays = monthdays;
   }

   public String getStartTime() {
      return this.startTime;
   }

   public void setStartTime(String startTime) {
      this.startTime = startTime;
   }

   public long getDuration() {
      return this.duration;
   }

   public void setDuration(long duration) {
      this.duration = duration;
   }

   public String getUserId() {
      return this.userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public Timestamp getCreateDate() {
      return this.createDate;
   }

   public void setCreateDate(Timestamp createDate) {
      this.createDate = createDate;
   }

   public String getModifiedDate() {
      return this.modifiedDate;
   }

   public void setModifiedDate(String modifiedDate) {
      this.modifiedDate = modifiedDate;
   }

   public String getIsDeleted() {
      return this.isDeleted;
   }

   public void setIsDeleted(String isDeleted) {
      this.isDeleted = isDeleted;
   }

   public void setToDefaultMessageEntity() {
      Date lm = new Date(System.currentTimeMillis());
      String today = (new SimpleDateFormat("yyyy-MM-dd")).format(lm);
      this.setMessageText("");
      this.setStartDate(today);
      this.setEndDate(today);
      this.setRepeatType("once");
      this.setStartTime("00:00:00");
      this.setDuration(0L);
      this.setFont("Arial");
      this.setSize("4");
      this.setBold("N");
      this.setItalic("N");
      this.setUnderline("N");
      this.setColor("#000000");
      this.setBgImage("0");
      this.setBgColor("");
      this.setPosition("middle");
      this.setDirection("none");
      this.setSpeed("");
      this.setBgTransparency(0L);
      this.setWeekdays("");
      this.setMonthdays("");
   }
}
