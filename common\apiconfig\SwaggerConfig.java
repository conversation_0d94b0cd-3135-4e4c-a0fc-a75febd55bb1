package com.samsung.common.apiconfig;

import com.fasterxml.classmate.ResolvedType;
import com.fasterxml.classmate.TypeResolver;
import com.google.common.collect.Lists;
import com.samsung.common.config.CommonConfig;
import com.samsung.magicinfo.auth.security.TokenUtils;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.restapi.exception.RestErrorInfo;
import io.swagger.annotations.Api;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.ResponseMessageBuilder;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ApiKey;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class SwaggerConfig {
   @Autowired
   TypeResolver typeResolver;

   public SwaggerConfig() {
      super();
   }

   @Bean
   public Docket restApi() throws ConfigException {
      boolean showDefault = false;
      if (CommonConfig.get("swagger.access.v1") != null && CommonConfig.get("swagger.access.v2") != null) {
         showDefault = !Boolean.parseBoolean(CommonConfig.get("swagger.access.v1")) && !Boolean.parseBoolean(CommonConfig.get("swagger.access.v2"));
      }

      return (new Docket(DocumentationType.SWAGGER_2)).select().paths(PathSelectors.none()).build().apiInfo((new ApiInfoBuilder()).version("").title("MagicInfo REST API Default Swagger Page").description("You are not allowed to access Swagger page for MagicInfo REST API. Please  contact Server Administrator !!!").build()).enable(showDefault);
   }

   @Bean
   public Docket restApi_v1() throws ConfigException {
      boolean isEnabledV1 = true;
      if (CommonConfig.get("swagger.access.v1") != null) {
         isEnabledV1 = Boolean.parseBoolean(CommonConfig.get("swagger.access.v1"));
      }

      return (new Docket(DocumentationType.SWAGGER_2)).groupName("REST API v1.0").select().apis(RequestHandlerSelectors.withClassAnnotation(Api.class)).paths(PathSelectors.ant("/restapi/v1.0/**")).build().useDefaultResponseMessages(false).globalResponseMessage(RequestMethod.GET, this.getResponseMessages(RequestMethod.GET)).globalResponseMessage(RequestMethod.POST, this.getResponseMessages(RequestMethod.POST)).globalResponseMessage(RequestMethod.PUT, this.getResponseMessages(RequestMethod.PUT)).globalResponseMessage(RequestMethod.DELETE, this.getResponseMessages(RequestMethod.DELETE)).apiInfo((new ApiInfoBuilder()).version("1.0").title("MagicInfo REST API").description("Documentation for MagicInfo REST API v1.0").build()).securitySchemes(Lists.newArrayList(new ApiKey[]{this.apiKey()})).additionalModels(this.typeResolver.resolve(RestErrorInfo.class, new Type[0]), new ResolvedType[0]).enable(isEnabledV1);
   }

   @Bean
   public Docket restApi_v2() throws ConfigException {
      boolean isEnabledV2 = true;
      if (CommonConfig.get("swagger.access.v2") != null) {
         isEnabledV2 = Boolean.parseBoolean(CommonConfig.get("swagger.access.v2"));
      }

      return (new Docket(DocumentationType.SWAGGER_2)).groupName("REST API v2.0").select().apis(RequestHandlerSelectors.withClassAnnotation(Api.class)).paths(PathSelectors.ant("/restapi/v2.0/**")).build().useDefaultResponseMessages(false).globalResponseMessage(RequestMethod.GET, this.getResponseMessages(RequestMethod.GET)).globalResponseMessage(RequestMethod.POST, this.getResponseMessages(RequestMethod.POST)).globalResponseMessage(RequestMethod.PUT, this.getResponseMessages(RequestMethod.PUT)).globalResponseMessage(RequestMethod.DELETE, this.getResponseMessages(RequestMethod.DELETE)).apiInfo((new ApiInfoBuilder()).version("2.0").title("MagicInfo REST API").description("Documentation for MagicInfo REST API v2.0").build()).securitySchemes(Lists.newArrayList(new ApiKey[]{this.apiKey()})).additionalModels(this.typeResolver.resolve(RestErrorInfo.class, new Type[0]), new ResolvedType[0]).enable(isEnabledV2);
   }

   private List getResponseMessages(RequestMethod method) {
      ArrayList messages = new ArrayList();
      messages.add((new ResponseMessageBuilder()).code(400).message("Bad Request").responseModel(new ModelRef("RestErrorInfo")).build());
      messages.add((new ResponseMessageBuilder()).code(401).message("Unauthorized").responseModel(new ModelRef("RestErrorInfo")).build());
      messages.add((new ResponseMessageBuilder()).code(403).message("Forbidden").responseModel(new ModelRef("RestErrorInfo")).build());
      messages.add((new ResponseMessageBuilder()).code(404).message("Not Found").responseModel(new ModelRef("RestErrorInfo")).build());
      messages.add((new ResponseMessageBuilder()).code(500).message("Internal Server Error").responseModel(new ModelRef("RestErrorInfo")).build());
      messages.add((new ResponseMessageBuilder()).code(503).message("Service Unavailable").responseModel(new ModelRef("RestErrorInfo")).build());
      if (method == RequestMethod.POST) {
         messages.add((new ResponseMessageBuilder()).code(201).message("Created").build());
      }

      return messages;
   }

   private ApiKey apiKey() {
      return new ApiKey("api_key", TokenUtils.tokenHeader, "header");
   }

   private ApiInfo apiInfo() {
      return new ApiInfo("MagicInfo REST API", "MagicInfo REST API", "1.0", "MagicInfo REST API", "", "", "");
   }
}
