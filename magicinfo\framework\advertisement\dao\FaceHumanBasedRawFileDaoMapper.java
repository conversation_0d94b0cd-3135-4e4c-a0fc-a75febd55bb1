package com.samsung.magicinfo.framework.advertisement.dao;

import java.sql.SQLException;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FaceHumanBasedRawFileDaoMapper {
   boolean isExistWeeklyReport(@Param("weeklyCsvFileName") String var1) throws SQLException;

   boolean isExistMonthlyReport(@Param("monthlyCsvFileName") String var1) throws SQLException;

   void addWeeklyReport(@Param("root_group_id") long var1, @Param("year") int var3, @Param("month") int var4, @Param("week_of_year") int var5, @Param("week_of_month") int var6, @Param("weeklyCsvFileName") String var7) throws SQLException;

   void addMonthlyReport(@Param("root_group_id") long var1, @Param("year") int var3, @Param("month") int var4, @Param("week_of_year") int var5, @Param("monthlyCsvFileName") String var6) throws SQLException;

   List getDetailReportByRootGroupId(@Param("root_group_id") int var1) throws SQLException;

   List getYearListByRootGroupId(@Param("root_group_id") int var1) throws SQLException;

   List getYearListByAmsRootGroupId(@Param("root_group_id") int var1) throws SQLException;

   List getMonthReportListByRootGroupId(@Param("root_group_id") int var1, @Param("year") String var2) throws SQLException;

   List getMonthReportListByAmsRootGroupId(@Param("root_group_id") int var1, @Param("year") String var2) throws SQLException;

   List getWeekReportListByRootGroupId(@Param("root_group_id") int var1, @Param("year") String var2) throws SQLException;

   List getWeekReportListByAmsRootGroupId(@Param("root_group_id") int var1, @Param("year") String var2) throws SQLException;
}
