package com.samsung.magicinfo.auth.security.strategies;

import com.samsung.magicinfo.auth.security.strategies.impl.HotpAuthStrategy;
import com.samsung.magicinfo.auth.security.strategies.impl.TotpAuthStrategy;

public class StrategyFactory {
   public StrategyFactory() {
      super();
   }

   public static AuthStrategy getInstance(String type) {
      if ("TOTP".equals(type)) {
         return new TotpAuthStrategy();
      } else {
         return "HOTP".equals(type) ? new HotpAuthStrategy() : null;
      }
   }
}
