package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.framework.advertisement.entity.AreaOutlineEntity;
import com.samsung.magicinfo.framework.advertisement.entity.areainfo.AreaAgeInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.areainfo.AreaAgeShareInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.areainfo.AreaSexInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.areainfo.AreaTimeInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.areainfo.AreaTimeShareInfoEntity;
import java.sql.Connection;
import java.util.List;
import org.apache.logging.log4j.Logger;

public class AreaInfoDao {
   Logger logger = LoggingManagerV2.getLogger(AreaInfoDao.class);

   public AreaInfoDao() {
      super();
   }

   public AreaInfoDao(Connection con) {
      super();
   }

   /** @deprecated */
   @Deprecated
   public AreaOutlineEntity getAreaOutlineInfo(String area_id) {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaOutlineInfo method is not supported anymore. The table MI_AD_AREA_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAgeInfo(String user_id, String area_id) {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaAgeInfo method is not supported anymore. The table MI_AD_AREA_AGE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAgeInfo(String area_id) {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaAgeInfo method is not supported anymore. The table MI_AD_AREA_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAgeStatusInfo(String area_id) {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaAgeInfo method is not supported anymore. The table MI_AD_AREA_AGE_STATUS does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaSexInfo(String area_id) {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaSexInfo method is not supported anymore. The table MI_AD_AREA_SEX_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaTimeInfo(String area_id) {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaTimeInfo method is not supported anymore. The table MI_AD_AREA_TIME_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaTimeStatusInfo(String area_id) {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaTimeStatusInfo method is not supported anymore. The table MI_AD_AREA_TIME_SHARE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean updateAreaInfo(AreaOutlineEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#updateAreaInfo method is not supported anymore. The table updateAreaInfo does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean insertAreaInfo(AreaOutlineEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#insertAreaInfo method is not supported anymore. The table MI_AD_AREA_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean updateAreaAgeInfo(String area_id, AreaAgeInfoEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#updateAreaAgeInfo method is not supported anymore. The table MI_AD_AREA_AGE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean insertAreaAgeShareInfo(AreaAgeShareInfoEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#insertAreaAgeShareInfo method is not supported anymore. The table MI_AD_AREA_AGE_STATUS does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean updateAreaAgeShareInfo(String area_id, AreaAgeShareInfoEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#updateAreaAgeShareInfo method is not supported anymore. The table MI_AD_AREA_AGE_STATUS does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean insertAreaSexInfo(AreaSexInfoEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#insertAreaSexInfo method is not supported anymore. The table MI_AD_AREA_SEX_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean updateAreaSexInfo(String area_id, AreaSexInfoEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#updateAreaSexInfo method is not supported anymore. The table MI_AD_AREA_SEX_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean updateAreaTimeInfo(String area_id, AreaTimeInfoEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#updateAreaTimeInfo method is not supported anymore. The table MI_AD_AREA_TIME_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean insertAreaTimeShareInfo(AreaTimeShareInfoEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#insertAreaTimeShareInfo method is not supported anymore. The table MI_AD_AREA_TIME_SHARE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public boolean updateAreaTimeShareInfo(String area_id, AreaTimeShareInfoEntity data) {
      throw new UnsupportedOperationException("The AreaInfoDao#updateAreaTimeShareInfo method is not supported anymore. The table MI_AD_AREA_TIME_SHARE_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaTypeList() {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaTypeList method is not supported anymore. The table MI_AD_AREA_TYPE_ID_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaTypeListData() {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaTypeListData method is not supported anymore. The table MI_AD_AREA_TYPE_ID_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAdTypeList() {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaAdTypeList method is not supported anymore. The table MI_AD_AREA_AD_TYPE_ID_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAdTypeListData() {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaAdTypeListData method is not supported anymore. The table MI_AD_AREA_AD_TYPE_ID_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaTimeList() {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaAdTypeListData method is not supported anymore. The table MI_AD_AREA_AD_TYPE_ID_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaSexList() {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaSexList method is not supported anymore. The table MI_AD_AREA_SEX_TYPE_ID_INFO does not exists.");
   }

   /** @deprecated */
   @Deprecated
   public List getAreaAgeList() {
      throw new UnsupportedOperationException("The AreaInfoDao#getAreaAgeList method is not supported anymore. The table MI_AD_AREA_AGE_TYPE_ID_INFO does not exists.");
   }
}
