package com.samsung.magicinfo.restapi.device.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.DeviceUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.common.utils.page.ListManager;
import com.samsung.common.utils.page.PageManager;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceSecurityConf;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.SelectCondition;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfo;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.DeviceGroupInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManager;
import com.samsung.magicinfo.framework.device.deviceInfo.manager.deviceConf.DeviceSecurityConfManagerImpl;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManager;
import com.samsung.magicinfo.framework.device.service.deviceConf.DeviceConfManagerImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSecurityListResource;
import com.samsung.magicinfo.restapi.utils.ConvertUtil;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.rms.model.DeviceSecurityConfResource;
import com.samsung.magicinfo.rms.util.DeviceModelConverter;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("V2DeviceSecurityService")
@Transactional
public class V2DeviceSecurityServiceImpl implements V2DeviceSecurityService {
   protected Logger logger = LoggingManagerV2.getLogger(V2DeviceSecurityServiceImpl.class);
   DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
   String productType = "PREMIUM";
   final String ALL_MDC = "ALL_MDC";
   final String REQUEST_ID = "requestId";

   public V2DeviceSecurityServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Device Security Authority')")
   public V2CommonBulkResultResource getSecurityInfo(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var12) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance("PREMIUM");
      Iterator var7 = deviceIds.getIds().iterator();

      while(var7.hasNext()) {
         String deviceId = (String)var7.next();

         try {
            DeviceSecurityConf deviceSecurityConf = securityDao.getDeviceSecurityConf(deviceId);
            DeviceSecurityConfResource securityInfoCamel = DeviceModelConverter.convertSecurityInfoToCamelStyle(deviceSecurityConf);
            successList.add(securityInfoCamel);
         } catch (NullPointerException var11) {
            this.logger.error("[REST_v2.0][DEVICE SECURITY SERVICE][getSecurityInfo] This ID is not exist : " + deviceId);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Read Authority')")
   public V2PageResource filterDeviceSecurityList(V2DeviceFilter filter, HttpServletRequest request, HttpServletResponse response) throws Exception {
      DeviceSecurityConfManager dao = DeviceSecurityConfManagerImpl.getInstance(this.productType);
      DeviceGroupInfo deviceGroupDao = DeviceGroupInfoImpl.getInstance();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      V2PageResource result = new V2PageResource();
      String groupId = StrUtils.nvl(filter.getGroupId());
      String filterExpirationDate;
      if (!groupId.equals("")) {
         RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, Long.valueOf(groupId));
      } else {
         UserInfo userInfo = UserInfoImpl.getInstance();
         long mngOrgId = userContainer.getUser().getRoot_group_id();
         if (userContainer.getUser().getIs_mu().equalsIgnoreCase("Y")) {
            mngOrgId = userInfo.getCurMngOrgId(userContainer.getUser().getUser_id());
         }

         filterExpirationDate = userInfo.getOrganGroupName(mngOrgId);
         groupId = String.valueOf(deviceGroupDao.getOrganGroupIdByName(filterExpirationDate));
      }

      String connectionStatus = StrUtils.nvl(filter.getConnectionStatus()).equals("") ? "device_status_view_all" : filter.getConnectionStatus();
      String disconnectPeriod = null;
      int lastindex = connectionStatus.lastIndexOf("_");
      if (connectionStatus.substring(0, lastindex).equals("device_status_view_disconnection")) {
         disconnectPeriod = connectionStatus.substring(lastindex + 1);
         connectionStatus = connectionStatus.substring(0, lastindex);
      }

      if (StrUtils.nvl(filter.getSortColumn()).equals("")) {
         filter.setSortColumn("device_name");
      }

      filterExpirationDate = "device_status_view_all";
      String customInputVal = "0";
      String groupIds = "";
      if (filter.getGroupIds() != null) {
         groupIds = this.convertString(filter.getGroupIds());
      }

      String tagIds = null;
      if (filter.getTagIds() != null) {
         tagIds = this.convertString(filter.getTagIds());
      }

      String alarmFilter = "";
      if (filter.getAlarmTypes() != null) {
         alarmFilter = this.convertString(filter.getAlarmTypes());
      }

      String functionFilter = "";
      if (filter.getFunctionTypes() != null) {
         functionFilter = this.convertString(filter.getFunctionTypes());
      }

      String searchText = StrUtils.nvl(filter.getSearchText()).equals("") ? "" : filter.getSearchText();
      searchText = searchText.replaceAll("\\[", "^[");
      searchText = searchText.replaceAll("]", "^]");
      searchText = searchText.replaceAll("%", "^%");
      String inputSources = "";
      if (filter.getInputSources() != null) {
         inputSources = this.convertString(filter.getInputSources());
      }

      boolean isRoot = false;
      Map res = deviceGroupDao.getDeviceOrganizationByGroupId(Integer.parseInt(groupId));
      if (res != null && res.get("GROUP_ID") != null && Long.parseLong(groupId) == (Long)res.get("GROUP_ID")) {
         isRoot = true;
      }

      if (StrUtils.nvl(filter.getSearchId()).equals("")) {
         String var10000 = "-1";
      } else {
         filter.getSearchId();
      }

      String deviceType = "";
      if (filter.getDeviceType() != null) {
         deviceType = this.convertString(filter.getDeviceType());
      }

      ListManager listMgr = new ListManager(dao, "commonlist");
      SelectCondition condition = new SelectCondition();
      condition.setSort_name(filter.getSortColumn());
      condition.setOrder_dir(filter.getSortOrder());
      condition.setGroup_id(Long.valueOf(groupId));
      condition.setSrc_name(searchText);
      condition.setIsRoot(isRoot);
      condition.setStatus_view_mode(connectionStatus);
      if (disconnectPeriod != null) {
         condition.setDisconn_period(disconnectPeriod);
      }

      condition.setExpiration_date(filterExpirationDate);
      condition.setCustom_input_val(customInputVal);
      condition.setFilter_group_ids(groupIds);
      condition.setRole_name(userContainer.getUser().getRole_name());
      condition.setUser_id(userContainer.getUser().getUser_id());
      condition.setTagFilter(tagIds);
      condition.setSourceFilter(inputSources);
      if (StringUtils.isNotBlank(alarmFilter)) {
         condition.setAlarmFiltersByString(alarmFilter);
      }

      if (StringUtils.isNotBlank(functionFilter)) {
         condition.setFunctionFiltersByString(functionFilter);
      }

      if (StringUtils.isNotBlank(searchText)) {
         condition.setCommonSearchKeyword(searchText);
      }

      if (deviceType != null && !deviceType.equals("")) {
         condition.setDevice_type(deviceType);
      }

      ArrayList sourceList;
      String[] list;
      int i;
      if (StringUtils.isNotBlank(condition.getSourceFilter())) {
         sourceList = new ArrayList();
         list = condition.getSourceFilter().split(",");
         String[] var28 = list;
         int var29 = list.length;

         for(i = 0; i < var29; ++i) {
            String tag = var28[i];
            sourceList.add(Long.parseLong(tag));
         }

         condition.setSourceFilterList(sourceList);
      }

      sourceList = null;
      list = null;
      listMgr.addSearchInfo("condition", condition);
      listMgr.setLstSize(Integer.valueOf(filter.getPageSize()));
      listMgr.setSection("getDeviceSecurityConfList");
      List list = listMgr.V2dbexecute(filter.getStartIndex(), filter.getPageSize());
      PageManager pageMgr = listMgr.getPageManager();
      result.setRecordsReturned(list.size());
      result.setRecordsTotal(pageMgr.getTotalRowCount());
      result.setRecordsFiltered(pageMgr.getTotalRowCount());
      result.setPageSize(pageMgr.getPageSize());
      if (filter.getSortColumn() != null && !filter.getSortColumn().equals("")) {
         result.setSortColumn(filter.getSortColumn());
         result.setSortOrder(filter.getSortOrder());
      }

      int totalChildCount = 0;
      List securityList = new ArrayList();

      for(i = 0; i < list.size(); ++i) {
         DeviceSecurityConf info = (DeviceSecurityConf)list.get(i);
         if (info != null) {
            V2DeviceSecurityListResource resource = this.writeData(this.productType, info, request);
            if (info.getHas_child() != null && info.getHas_child()) {
               for(int j = 0; (long)j < info.getChild_cnt(); ++j) {
                  DeviceSecurityConf childDevice = dao.getDeviceSecurityConf(info.getDevice_id() + "_" + (j + 1));
                  if (childDevice != null) {
                     resource = this.writeData(this.productType, childDevice, request);
                  }
               }

               totalChildCount = (int)((long)totalChildCount + info.getChild_cnt());
            }

            resource.setChildCount((long)totalChildCount);
            securityList.add(i, resource);
         }
      }

      result = V2PageResource.createPageResource(securityList, pageMgr);
      result.setStartIndex(filter.getStartIndex());
      return result;
   }

   @PreAuthorize("hasAnyAuthority('Device Security Authority')")
   public V2DeviceReqServiceResource updateSecurityInfo(DeviceSecurityConfResource param) throws Exception {
      boolean checkFlag = false;
      List deviceIds = param.getDeviceIds();
      Iterator var4 = deviceIds.iterator();

      while(var4.hasNext()) {
         String deviceId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var16) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      Iterator var18 = deviceIds.iterator();

      while(var18.hasNext()) {
         String deviceId = (String)var18.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds);
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      DeviceConfManager confManager = DeviceConfManagerImpl.getInstance();
      DeviceSecurityConf deviceSecurityConf = null;
      String requestId = UUID.randomUUID().toString();
      List successList = new ArrayList();
      List failList = new ArrayList();
      DeviceSecurityConfManager securityDao = null;
      securityDao = DeviceSecurityConfManagerImpl.getInstance("PREMIUM");
      Iterator var11 = param.getDeviceIds().iterator();

      while(var11.hasNext()) {
         String deviceId = (String)var11.next();
         deviceSecurityConf = DeviceModelConverter.convertSecurityConfToSnakeStyle(param);

         try {
            deviceSecurityConf.setDevice_id(deviceId);
            if (deviceSecurityConf.getScreen_monitoring_lock() != null && deviceSecurityConf.getScreen_monitoring_lock() == 1L) {
               deviceSecurityConf.setRemote_control_server_lock(1L);
            }

            try {
               if (deviceSecurityConf.getRemote_control_server_lock() != null) {
                  DeviceSecurityConf securityConf = new DeviceSecurityConf();
                  securityConf.setDevice_id(deviceSecurityConf.getDevice_id());
                  securityConf.setRemote_control_server_lock(deviceSecurityConf.getRemote_control_server_lock());
                  securityDao.setDeviceSecurityConf(deviceSecurityConf);
               }
            } catch (SQLException var14) {
               this.logger.error("", var14);
            }

            if (!this.onlySetRemoteControlServerLock(deviceSecurityConf)) {
               confManager.reqSetSecurityToDevice(deviceSecurityConf, requestId, "ALL_MDC");
            }

            successList.add(deviceId);
         } catch (Exception var15) {
            this.logger.error("[REST_v2.0][DEVICE SECURITY SERVICE][updateSecurityInfo] This ID is not exist : " + deviceId);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      if (successList.size() > 0 && !this.onlySetRemoteControlServerLock(deviceSecurityConf)) {
         confManager.reqGetSecurityFromDevice((String)param.getDeviceIds().get(0), requestId, "ALL_MDC");
         resource.setRequestId(requestId);
      }

      return resource;
   }

   boolean onlySetRemoteControlServerLock(DeviceSecurityConf deviceSecurityConf) {
      return deviceSecurityConf.getChild_cnt() == null && deviceSecurityConf.getConn_child_cnt() == null && deviceSecurityConf.getIs_child() == null && deviceSecurityConf.getHas_child() == null && deviceSecurityConf.getDevice_name() == null && deviceSecurityConf.getDevice_type() == null && deviceSecurityConf.getDevice_type_version() == null && deviceSecurityConf.getMnt_safety_lock() == null && deviceSecurityConf.getMisc_remocon() == null && deviceSecurityConf.getMisc_panel_lock() == null && deviceSecurityConf.getMisc_all_lock() == null && deviceSecurityConf.getTouch_control_lock() == null && deviceSecurityConf.getMisc_block_usb_port() == null && deviceSecurityConf.getMisc_block_network_connection() == null && deviceSecurityConf.getMisc_server_network_setting() == null && deviceSecurityConf.getMisc_white_list() == null && deviceSecurityConf.getIs_init_security() == null && deviceSecurityConf.getCapture_lock() == null && deviceSecurityConf.getBluetooth_lock() == null && deviceSecurityConf.getWifi_lock() == null && deviceSecurityConf.getSource_lock() == null && deviceSecurityConf.getScreen_monitoring_lock() == null && deviceSecurityConf.getRemote_control_server_lock() != null && deviceSecurityConf.getIcon_error_sw() == null && deviceSecurityConf.getIcon_error_hw() == null && deviceSecurityConf.getIcon_alarm() == null && deviceSecurityConf.getIcon_process_content_download() == null && deviceSecurityConf.getIcon_process_log() == null && deviceSecurityConf.getIcon_process_sw_download() == null && deviceSecurityConf.getIcon_memo() == null && deviceSecurityConf.getIcon_backup() == null;
   }

   @PreAuthorize("hasAnyAuthority('Device Security Authority')")
   public V2DeviceReqServiceResource getCurrentStatusSecurity(V2CommonIds deviceIds) throws Exception {
      boolean checkFlag = false;
      Iterator var3 = deviceIds.getIds().iterator();

      while(var3.hasNext()) {
         String deviceId = (String)var3.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, deviceId);
         } catch (Exception var11) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2DeviceReqServiceResource resource = new V2DeviceReqServiceResource();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var6 = deviceIds.getIds().iterator();

      while(var6.hasNext()) {
         String deviceId = (String)var6.next();
         if (!DeviceUtils.isConnected(deviceId)) {
            resource.setFailList(deviceIds.getIds());
            this.logger.error("The device is not connected.");
            return resource;
         }
      }

      String sessionId = UUID.randomUUID().toString();
      Iterator var15 = deviceIds.getIds().iterator();

      while(var15.hasNext()) {
         String deviceId = (String)var15.next();

         try {
            this.confManager.reqGetSecurityFromDevice(deviceId, sessionId, "ALL_MDC");
            successList.add(deviceId);
         } catch (Exception var10) {
            this.logger.error("", var10);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      if (successList.size() > 0) {
         resource.setRequestId(sessionId);
      }

      return resource;
   }

   @PreAuthorize("hasAnyAuthority('Device Security Authority')")
   public V2CommonBulkResultResource getCurrentStatusSecurityResult(V2DeviceReqServiceConf body) throws Exception {
      List deviceIds = body.getDeviceIds();
      boolean checkFlag = false;
      Iterator var4 = deviceIds.iterator();

      String requestId;
      while(var4.hasNext()) {
         requestId = (String)var4.next();

         try {
            RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE, requestId);
         } catch (Exception var15) {
            checkFlag = true;
            break;
         }
      }

      if (checkFlag) {
         RestAPIAuthorityCheckUtil.throwAuthException(RestAPIAuthorityCheckUtil.AuthorityCheckType.DEVICE);
      }

      V2CommonBulkResultResource resource = new V2CommonBulkResultResource();
      requestId = body.getRequestId();
      List successList = new ArrayList();
      List failList = new ArrayList();
      Iterator var8 = deviceIds.iterator();

      while(var8.hasNext()) {
         String deviceId = (String)var8.next();

         try {
            DeviceSecurityConf info = this.confManager.getSecurityResultSet(deviceId, requestId, "GET_DEVICE_SECURITY_CONF", "ALL_MDC");
            if (info == null) {
               this.logger.error("[REST_v2.0][DEVICE SECURITY SERVICE][getCurrentStatusSecurityResult] " + deviceId + " , " + requestId);
               failList.add(deviceId);
            } else {
               DeviceSecurityConfManager securityDao = DeviceSecurityConfManagerImpl.getInstance("PREMIUM");
               DeviceSecurityConf deviceSecurityConf = securityDao.getDeviceSecurityConf(deviceId);
               DeviceSecurityConfResource securityInfoCamel = DeviceModelConverter.convertSecurityInfoToCamelStyle(deviceSecurityConf);
               securityInfoCamel.setDeviceId(deviceId);
               successList.add(securityInfoCamel);
            }
         } catch (Exception var14) {
            this.logger.error("", var14);
            failList.add(deviceId);
         }
      }

      resource.setSuccessList(successList);
      resource.setFailList(failList);
      return resource;
   }

   private void writeJson(StringBuffer resultBuf, String i, String productType, DeviceSecurityConf info, HttpServletRequest request) {
      String strOn = "ON";
      String strOff = "OFF";

      try {
         if (Integer.parseInt(i) > 0) {
            resultBuf.append(",");
         }
      } catch (Exception var9) {
         resultBuf.append(",");
      }

      resultBuf.append("{\"check\":\"\",\"device_id\":\"" + info.getDevice_id() + "\",");
      if (info.getIs_child() != null && info.getIs_child()) {
         resultBuf.append("\"device_type\":\"" + info.getDevice_type() + "\",");
      } else {
         resultBuf.append("\"device_type\":\"" + info.getDevice_type() + "\",");
      }

      resultBuf.append("\"over_device_name\":\"" + info.getDevice_name() + "\",");
      resultBuf.append("\"device_type_version\":\"" + info.getDevice_type_version() + "\",");
      resultBuf.append("\"device_name\":\"" + info.getDevice_name() + "\",");
      boolean power = DeviceUtils.isConnected(info.getDevice_id());
      if (power && info.getIs_child() && !DeviceUtils.isConnected(info.getDevice_id().split("_")[0])) {
         power = false;
      }

      resultBuf.append("\"power\":\"" + power + "\",");
      resultBuf.append("\"mnt_safety_lock\":\"" + info.getMnt_safety_lock() + "\",");
      resultBuf.append("\"misc_remocon\":\"" + info.getMisc_remocon() + "\",");
      resultBuf.append("\"misc_panel_lock\":\"" + info.getMisc_panel_lock() + "\",");
      resultBuf.append("\"misc_all_lock\":\"" + info.getMisc_all_lock() + "\",");
      if (info.getMisc_block_usb_port() != null) {
         if (info.getMisc_block_usb_port() == 1L) {
            resultBuf.append("\"misc_block_usb_port\":\"" + strOn + "\",");
         } else {
            resultBuf.append("\"misc_block_usb_port\":\"" + strOff + "\",");
         }
      } else {
         resultBuf.append("\"misc_block_usb_port\":\"-\",");
      }

      if (info.getMisc_block_network_connection() != null) {
         if (info.getMisc_block_network_connection() == 1L) {
            resultBuf.append("\"misc_block_network_connection\":\"" + strOn + "\"}");
         } else {
            resultBuf.append("\"misc_block_network_connection\":\"" + strOff + "\"}");
         }
      } else {
         resultBuf.append("\"misc_block_network_connection\":\"-\"}");
      }

   }

   private V2DeviceSecurityListResource writeData(String productType, DeviceSecurityConf info, HttpServletRequest request) {
      V2DeviceSecurityListResource resource = new V2DeviceSecurityListResource();
      String strOn = "ON";
      String strOff = "OFF";
      ObjectMapper mapper = new ObjectMapper();
      mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
      mapper.configure(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES, false);

      try {
         String sourceLock = info.getSource_lock();
         List hashMaps = stringToSourceLockList(sourceLock);
         String jsonString = mapper.writeValueAsString(info);

         try {
            Map map = (Map)mapper.readValue(jsonString, new TypeReference() {
            });
            Map map = ConvertUtil.convertMap(map);
            map.put("sourceLock", (Object)null);
            resource = (V2DeviceSecurityListResource)mapper.convertValue(map, V2DeviceSecurityListResource.class);
            resource.setSourceLock(hashMaps);
            boolean power = DeviceUtils.isConnected(info.getDevice_id());
            if (power && info.getIs_child() && !DeviceUtils.isConnected(info.getDevice_id().split("_")[0])) {
               power = false;
            }

            resource.setPower(power);
            if (info.getMisc_remocon() != null) {
               if (info.getMisc_remocon() == 1L) {
                  resource.setMiscRemocon(0L);
               } else {
                  resource.setMiscRemocon(1L);
               }
            }

            if (info.getBluetooth_lock() != null) {
               resource.setBluetoothLock(info.getBluetooth_lock());
            }

            if (info.getWifi_lock() != null) {
               resource.setWifiLock(info.getWifi_lock());
            }

            if (info.getScreen_monitoring_lock() != null) {
               resource.setScreenMonitoringLock(info.getScreen_monitoring_lock());
            }

            if (info.getScreen_monitoring_lock() != null) {
               resource.setScreenMonitoringLock(info.getScreen_monitoring_lock());
            }

            if (info.getMisc_block_usb_port() != null) {
               if (info.getMisc_block_usb_port() == 1L) {
                  resource.setMiscBlockUsbPort(strOn);
               } else {
                  resource.setMiscBlockUsbPort(strOff);
               }
            } else {
               resource.setMiscBlockUsbPort("-");
            }

            if (info.getMisc_server_network_setting() != null && info.getMisc_server_network_setting() >= 0L) {
               resource.setMiscServerNetworkSetting(info.getMisc_server_network_setting());
            }

            if (info.getMisc_block_network_connection() != null) {
               if (info.getMisc_block_network_connection() == 1L) {
                  resource.setMiscBlockNetworkConnection(strOn);
               } else {
                  resource.setMiscBlockNetworkConnection(strOff);
               }
            } else {
               resource.setMiscBlockNetworkConnection("-");
            }
         } catch (IOException var13) {
            this.logger.error("", var13);
         }
      } catch (JsonProcessingException var14) {
         this.logger.error("", var14);
      }

      return resource;
   }

   private static List stringToSourceLockList(String source) {
      List sourceLockList = new ArrayList();
      if (null != source && !source.isEmpty()) {
         StringTokenizer tokenizer = new StringTokenizer(source, ";");

         while(tokenizer.hasMoreElements()) {
            StringTokenizer tokenizer1 = new StringTokenizer(tokenizer.nextToken(), ":");
            HashMap inputSource = new HashMap();
            String code = tokenizer1.nextToken();

            try {
               inputSource.put("code", Integer.parseInt(code));
               inputSource.put("value", Integer.parseInt(tokenizer1.nextToken()));
               inputSource.put("text", DeviceUtils.getInputSource(code));
            } catch (NumberFormatException var7) {
               continue;
            }

            sourceLockList.add(inputSource);
         }

         return sourceLockList;
      } else {
         return sourceLockList;
      }
   }

   public String convertString(List list) {
      String result = "";
      if (!list.isEmpty() && list.size() > 0) {
         StringBuffer strBuf = new StringBuffer();

         for(int i = 0; i < list.size(); ++i) {
            strBuf.append(list.get(i));
            if (i < list.size() - 1) {
               strBuf.append(",");
            }
         }

         result = strBuf.toString();
      }

      return result;
   }
}
