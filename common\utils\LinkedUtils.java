package com.samsung.common.utils;

import java.util.Map;

public class LinkedUtils {
   public LinkedUtils() {
      super();
   }

   public static String getContentEditURL(Map map) {
      StringBuffer url = new StringBuffer();
      url.append(map.get("contextPath") + "/content/contentList.htm?cmd=EDIT_CONTENT&list_type=LIST&selId=");
      url.append(map.get("contentId"));
      url.append("&group_type=ALL&group_id=0&search_id=-1&content_type=CONTENT&productType=PREMIUM&isSelectMenu=TRUE");
      return url.toString();
   }

   public static String getContentSelectURL(Map map) {
      StringBuffer url = new StringBuffer();
      url.append(map.get("contextPath") + "/content/contentList.htm?cmd=SELECT_CONTENT&list_type=LIST&selId=");
      url.append(map.get("contentId"));
      url.append("&group_type=ALL&group_id=0&search_id=-1&content_type=CONTENT&productType=PREMIUM&isSelectMenu=TRUE");
      return url.toString();
   }

   public static String getPlaylistEditURL(Map map) {
      StringBuffer url = new StringBuffer();
      url.append(map.get("contextPath") + "/content/playlistDetailView.htm?cmd=EDIT&list_type=LIST&selId=");
      url.append(map.get("plId"));
      url.append("&viewOnly=NO&group_type=ALL&group_id=0&productType=PREMIUM&search_id=-1&isSelectMenu=TRUE");
      return url.toString();
   }

   public static String getPlaylistSelectURL(Map map) {
      StringBuffer url = new StringBuffer();
      String groupType = "ALL";
      String isSelectMenu = "PREMIUM";
      if (map.containsKey("groupType")) {
         groupType = (String)map.get("groupType");
      }

      if (map.containsKey("isSelectMenu")) {
         isSelectMenu = (String)map.get("isSelectMenu");
      }

      url.append(map.get("contextPath") + "/content/playlistList.htm?cmd=LIST&group_type=" + groupType + "&productType=PREMIUM&selId=");
      url.append(map.get("plId"));
      url.append("&isSelect=TRUE&isSelectMenu=" + isSelectMenu);
      return url.toString();
   }

   public static String getScheduleEditURL(Map map) {
      StringBuffer url = new StringBuffer();
      url.append(map.get("contextPath") + "/schedule/program/programMain.htm?cmd=EDIT&progId=");
      url.append(map.get("programId"));
      url.append("&group_type=ALL&group_name=ALL&productType=PREMIUM&isSelectMenu=PREMIUM");
      return url.toString();
   }

   public static String getScheduleSelectURL(Map map) {
      StringBuffer url = new StringBuffer();
      String groupType = "ALL";
      String isSelectMenu = "PREMIUM";
      if (map.containsKey("groupType")) {
         groupType = (String)map.get("groupType");
      }

      if (map.containsKey("isSelectMenu")) {
         isSelectMenu = (String)map.get("isSelectMenu");
      }

      url.append(map.get("contextPath") + "/schedule/scheduleAdmin.htm?cmd=QUERY&group_type=" + groupType + "&productType=PREMIUM&selId=");
      url.append(map.get("programId"));
      url.append("&isSelect=TRUE&isSelectMenu=" + isSelectMenu);
      return url.toString();
   }

   public static String getEventScheduleEditURL(Map map) {
      StringBuffer url = new StringBuffer();
      url.append(map.get("contextPath") + "/schedule/eventList.htm?cmd=QUERY");
      url.append("&strGroupType=ALL&eventSchedule=EVENT");
      return url.toString();
   }

   public static String getEventScheduleSelectURL(Map map) {
      StringBuffer url = new StringBuffer();
      url.append(map.get("contextPath") + "/schedule/event/eventScheduleMain.htm?cmd=EDIT&group_type=ALL&group_name=ALL&productType=PREMIUM&progId=");
      url.append(map.get("programId"));
      url.append("&isSelect=TRUE&isSelectMenu=EVENTSCHEDULE");
      return url.toString();
   }

   public static String getLinkedURL(String productType, String menu, String urlType, Map map) {
      String url = null;
      if (productType.equalsIgnoreCase("PREMIUM") && menu.equalsIgnoreCase("SCHEDULE") && urlType.equalsIgnoreCase("SELECT")) {
         url = getScheduleSelectURL(map);
      } else if (productType.equalsIgnoreCase("PREMIUM") && menu.equalsIgnoreCase("SCHEDULE") && urlType.equalsIgnoreCase("EDIT")) {
         url = getScheduleEditURL(map);
      } else if (productType.equalsIgnoreCase("PREMIUM") && menu.equalsIgnoreCase("PLAYLIST") && urlType.equalsIgnoreCase("SELECT")) {
         url = getPlaylistSelectURL(map);
      } else if (productType.equalsIgnoreCase("PREMIUM") && menu.equalsIgnoreCase("PLAYLIST") && urlType.equalsIgnoreCase("EDIT")) {
         url = getPlaylistEditURL(map);
      }

      return url;
   }
}
