package com.samsung.common.utils;

import java.io.File;
import java.io.FilenameFilter;

public class ExtensionFileFilter implements FilenameFilter {
   private String[] m_extensions;

   public ExtensionFileFilter(String extension) {
      super();
      this.m_extensions = new String[]{extension};
   }

   public boolean accept(File dir, String name) {
      for(int i = 0; i < this.m_extensions.length; ++i) {
         if (name.endsWith(this.m_extensions[i])) {
            return true;
         }
      }

      return false;
   }
}
