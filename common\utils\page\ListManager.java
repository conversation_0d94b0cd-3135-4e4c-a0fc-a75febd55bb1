package com.samsung.common.utils.page;

import com.samsung.common.db.DBListExecuter;
import com.samsung.common.db.PagedListInfo;
import com.samsung.common.db.SearchParameter;
import com.samsung.common.db.TypeFilterPagedListInfo;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.StrUtils;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.Logger;

public class ListManager {
   private static Logger logger = LoggingManagerV2.getLogger(ListManager.class);
   private int totalRowCount;
   private int currPage;
   private Hashtable searchParam;
   private String currentURL;
   private String queryString;
   private String contextPath;
   private String type;
   private Hashtable linkParam;
   private String section;
   private DBListExecuter executer;
   private int lstSize;
   private List typeFilterList;

   public ListManager(HttpServletRequest request, DBListExecuter executer) {
      this(request, executer, (String)null);
   }

   public ListManager(HttpServletRequest request, DBListExecuter executer, String type) {
      super();
      this.currPage = 1;
      this.section = null;
      this.executer = executer;
      this.currentURL = request.getRequestURI();
      this.queryString = request.getQueryString();
      this.contextPath = request.getContextPath();
      if (type == null) {
         type = request.getParameter("type");
         type = type == null ? "default" : type;
      }

      this.type = type;

      try {
         String page = StrUtils.nvl(request.getParameter("page")).equals("") ? (String)request.getAttribute("page") : request.getParameter("page");
         this.currPage = Integer.parseInt(page);
      } catch (Exception var5) {
         this.currPage = 1;
      }

   }

   public ListManager(DBListExecuter executer, String type) throws Exception {
      super();
      this.currPage = 1;
      this.section = null;
      this.executer = executer;
      this.type = type;
   }

   public PageManager getPageManager() {
      return this.getPageManager(this.type);
   }

   public PageManager getPageManager(String type) {
      return this.getPageManager(type, this.totalRowCount);
   }

   public PageManager getPageManager(String type, int totalRowCount) {
      return this.createPageManager(type, totalRowCount);
   }

   public void addSearchInfo(String fieldName, Object value) {
      Class var3;
      if (this.searchParam == null) {
         var3 = ListManager.class;
         synchronized(ListManager.class) {
            if (this.searchParam == null) {
               this.searchParam = new Hashtable();
            }
         }
      }

      if (value != null) {
         var3 = ListManager.class;
         synchronized(ListManager.class) {
            this.searchParam.put(fieldName, value);
         }
      }

   }

   public void setId(String id) {
   }

   public void setSection(String section) {
      this.section = section;
   }

   public List dbexecute() throws Exception {
      int pageSize = this.lstSize;
      int startPos = (this.currPage - 1) * pageSize + 1;
      PagedListInfo info = this.executer.getPagedList(startPos, pageSize, this.searchParam, this.section);
      this.totalRowCount = info.getTotalRowCount();
      return info.getPagedResultList();
   }

   public List dbexecuteWithTypeFilter() throws Exception {
      int pageSize = this.lstSize;
      int startPos = (this.currPage - 1) * pageSize + 1;
      TypeFilterPagedListInfo info = (TypeFilterPagedListInfo)this.executer.getPagedList(startPos, pageSize, this.searchParam, this.section);
      this.setTypeFilterMapList(info.getTypeFilter());
      this.totalRowCount = info.getTotalRowCount();
      return info.getPagedResultList();
   }

   public List dbexecuteWithTypeFilterNotUsedRequest(int startPos, int pageSize, Map condition, String section) throws Exception {
      TypeFilterPagedListInfo info = (TypeFilterPagedListInfo)this.executer.getPagedList(startPos, pageSize, condition, section);
      this.setTypeFilterMapList(info.getTypeFilter());
      this.totalRowCount = info.getTotalRowCount();
      return info.getPagedResultList();
   }

   public List V2dbexecute(int startIndex, int pageSize) throws Exception {
      PagedListInfo info = this.executer.getPagedList(startIndex, pageSize, this.searchParam, this.section);
      this.totalRowCount = info.getTotalRowCount();
      return info.getPagedResultList();
   }

   public List V2dbexecuteWithTypeFilter(int startIndex, int pageSize) throws Exception {
      TypeFilterPagedListInfo info = (TypeFilterPagedListInfo)this.executer.getPagedList(startIndex, pageSize, this.searchParam, this.section);
      this.setTypeFilterMapList(info.getTypeFilter());
      this.totalRowCount = info.getTotalRowCount();
      return info.getPagedResultList();
   }

   protected PageManager createPageManager(String type, int totalRowCount) {
      int currPageNo = this.currPage;
      PageManager pageManager = new PageManager(type, totalRowCount, currPageNo);

      try {
         pageManager.setCurrentURL(this.currentURL);
         pageManager.setQueryString(this.queryString);
         pageManager.setContextPath(this.contextPath);
         pageManager.setPageSize(this.lstSize);
         Enumeration e = this.searchParam.keys();

         while(e.hasMoreElements()) {
            Object key = e.nextElement();
            Object value = this.searchParam.get(key);
            if (SearchParameter.class.isInstance(value)) {
               SearchParameter param = (SearchParameter)value;
               pageManager.setQueryString(key.toString(), param.getValue());
            }
         }
      } catch (Exception var9) {
         pageManager.setCurrentURL("list.do");
         logger.debug("사용자입력 검색조건 없음:" + var9.getMessage());
      }

      return pageManager;
   }

   public void addParamInfo(String fieldName, Object value) {
      if (this.linkParam == null) {
         this.linkParam = new Hashtable();
      }

      this.linkParam.put(fieldName, value);
   }

   public PageManager getParamPageManager() {
      return this.getParamPageManager(this.type);
   }

   public PageManager getParamPageManager(String type) {
      return this.getParamPageManager(type, this.totalRowCount);
   }

   public PageManager getParamPageManager(String type, int totalRowCount) {
      return this.createParamPageManager(type, totalRowCount);
   }

   protected PageManager createParamPageManager(String type, int totalRowCount) {
      int currPageNo = this.currPage;
      PageManager pageManager = new PageManager(type, totalRowCount, currPageNo);

      try {
         pageManager.setCurrentURL(this.currentURL);
         pageManager.setQueryString(this.queryString);
         pageManager.setContextPath(this.contextPath);
         Enumeration e = this.linkParam.keys();

         while(e.hasMoreElements()) {
            String key = (String)e.nextElement();
            String value = (String)this.linkParam.get(key);
            pageManager.setQueryString(key, value);
         }
      } catch (Exception var8) {
         pageManager.setCurrentURL("cmslist.do");
         logger.debug("사용자입력 검색조건 없음:" + var8.getMessage());
      }

      return pageManager;
   }

   public int getLstSize() {
      return this.lstSize;
   }

   public void setLstSize(int lstSize) {
      this.lstSize = lstSize;
   }

   public void setTotalRowCount(int totalRowCount) {
      this.totalRowCount = totalRowCount;
   }

   public void setTypeFilterMapList(List typeFilterMapList) {
      if (this.typeFilterList == null) {
         this.typeFilterList = new ArrayList();
      }

      Iterator var2 = typeFilterMapList.iterator();

      while(true) {
         while(var2.hasNext()) {
            Map typeFilterMap = (Map)var2.next();
            String device_type = (String)typeFilterMap.get("device_type");
            Float device_type_version = ((Double)typeFilterMap.get("device_type_version")).floatValue();
            if (device_type.equalsIgnoreCase("iPLAYER")) {
               if (!this.typeFilterList.contains("iPLAYER")) {
                  this.typeFilterList.add("iPLAYER");
               }
            } else if (device_type.equalsIgnoreCase("SPLAYER") && device_type_version == 1.0F) {
               this.typeFilterList.add("SPLAYER");
            } else if (device_type.equalsIgnoreCase("SPLAYER") && device_type_version == 2.0F) {
               this.typeFilterList.add("S2PLAYER");
            } else if (device_type.equalsIgnoreCase("SPLAYER") && device_type_version == 3.0F) {
               this.typeFilterList.add("S3PLAYER");
            } else if (device_type.equalsIgnoreCase("APLAYER") && device_type_version == 1.0F) {
               this.typeFilterList.add("APLAYER");
            } else if (device_type.equalsIgnoreCase("3rdPartyPLAYER") && !this.typeFilterList.contains("3rdPartyPLAYER")) {
               this.typeFilterList.add("3rdPartyPLAYER");
            }
         }

         return;
      }
   }

   public String getTypeFilterListToString() {
      if (this.typeFilterList == null) {
         return "";
      } else {
         StringBuffer sb = new StringBuffer();

         for(int i = 0; i < this.typeFilterList.size(); ++i) {
            if (i != 0) {
               sb.append(";");
            }

            String typeFilter = (String)this.typeFilterList.get(i);
            if (typeFilter != null) {
               sb.append(typeFilter);
            }
         }

         return sb.toString();
      }
   }
}
