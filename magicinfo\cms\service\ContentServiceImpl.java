package com.samsung.magicinfo.cms.service;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.exception.ExceptionCode;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.AbilityUtils;
import com.samsung.common.utils.CommonUtils;
import com.samsung.common.utils.ContentUtils;
import com.samsung.common.utils.DateUtils;
import com.samsung.common.utils.DeleteContentUtils;
import com.samsung.common.utils.FileUtils;
import com.samsung.common.utils.RoleUtils;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.auth.UserContainer;
import com.samsung.magicinfo.cms.model.ContentApproveResource;
import com.samsung.magicinfo.cms.model.ContentDeleteResource;
import com.samsung.magicinfo.cms.model.ContentFileMapResource;
import com.samsung.magicinfo.cms.model.ContentFileResource;
import com.samsung.magicinfo.cms.model.ContentFilter;
import com.samsung.magicinfo.cms.model.ContentRefEvent;
import com.samsung.magicinfo.cms.model.ContentRefPlaylist;
import com.samsung.magicinfo.cms.model.ContentRefSchedule;
import com.samsung.magicinfo.cms.model.ContentResource;
import com.samsung.magicinfo.cms.model.ContentTagMappingResource;
import com.samsung.magicinfo.cms.model.DashboardContentResource;
import com.samsung.magicinfo.cms.model.UrlContentSettingResource;
import com.samsung.magicinfo.framework.common.DAOFactory;
import com.samsung.magicinfo.framework.common.FileManager;
import com.samsung.magicinfo.framework.common.FileManagerImpl;
import com.samsung.magicinfo.framework.common.LeftMenuGroupTreeDao;
import com.samsung.magicinfo.framework.content.constants.ContentConstants;
import com.samsung.magicinfo.framework.content.dao.ContentDao;
import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.ContentFile;
import com.samsung.magicinfo.framework.content.entity.ContentLog;
import com.samsung.magicinfo.framework.content.entity.NotificationData;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfo;
import com.samsung.magicinfo.framework.content.manager.ContentCodeInfoImpl;
import com.samsung.magicinfo.framework.content.manager.ContentInfo;
import com.samsung.magicinfo.framework.content.manager.ContentInfoImpl;
import com.samsung.magicinfo.framework.content.manager.LogInfo;
import com.samsung.magicinfo.framework.content.manager.LogInfoImpl;
import com.samsung.magicinfo.framework.dashboard.entity.UserDashboardEntity;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManager;
import com.samsung.magicinfo.framework.dashboard.manager.DashboardManagerImpl;
import com.samsung.magicinfo.framework.role.manager.AbilityInfoImpl;
import com.samsung.magicinfo.framework.scheduler.dao.EventInfoDao;
import com.samsung.magicinfo.framework.scheduler.manager.common.ScheduleInterface;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfo;
import com.samsung.magicinfo.framework.setup.manager.CategoryInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfo;
import com.samsung.magicinfo.framework.setup.manager.ServerSetupInfoImpl;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.entity.User;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfo;
import com.samsung.magicinfo.framework.user.manager.UserGroupInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.util.MailUtil;
import com.samsung.magicinfo.protocol.util.TimeUtil;
import com.samsung.magicinfo.restapi.exception.RestExceptionCode;
import com.samsung.magicinfo.restapi.exception.RestServiceException;
import com.samsung.magicinfo.restapi.utils.RestAPIAuthorityCheckUtil;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.NotFoundException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.DefaultMultipartHttpServletRequest;

@Service("ContentService")
@Transactional
public class ContentServiceImpl implements ContentService {
   protected Logger logger = LoggingManagerV2.getLogger(ContentServiceImpl.class);
   ContentInfo cInfo = ContentInfoImpl.getInstance();
   ContentDao cmsDao = new ContentDao();
   ContentDao contentTreeDao = new ContentDao();
   LeftMenuGroupTreeDao treeDao = new LeftMenuGroupTreeDao();

   public ContentServiceImpl() {
      super();
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody getDashboardContentInfo() throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         UserContainer userContainer = SecurityUtils.getUserContainer();
         String userId = SecurityUtils.getLoginUserId();
         DashboardContentResource data = new DashboardContentResource();
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         ContentDao contentDao = new ContentDao();
         boolean useDashboard = false;
         DashboardManager dashboardInfo = DashboardManagerImpl.getInstance();
         List list = dashboardInfo.getDashboard(userId);
         Iterator var10 = list.iterator();

         while(var10.hasNext()) {
            UserDashboardEntity entity = (UserDashboardEntity)var10.next();
            if (entity.getDashboard_name().equals("contentInfoDashboard")) {
               useDashboard = true;
            }
         }

         Map condition = new HashMap();
         condition.put("listType", "ALL");
         condition.put("creatorID", userId);
         int totalCount = contentDao.getContentListCnt(condition);
         int usedCount = contentInfo.getUsedContentCount(Long.parseLong("0"));
         data.setTotalCount(totalCount);
         data.setUsedCount(usedCount);

         try {
            String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
            UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
            long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
            ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
            Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
            boolean contentsApprovalEnable = false;
            if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
               contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
            }

            if (contentsApprovalEnable) {
               int unapprovedCount = false;
               int unapprovedCount;
               if (RoleUtils.isServerAdminRole(userContainer.getUser()) || !RoleUtils.isServerAdminRole(userContainer.getUser()) && userContainer.getUser().getRoot_group_id() == 0L) {
                  unapprovedCount = contentInfo.getUnapprovedContentCnt();
               } else {
                  unapprovedCount = contentInfo.getUnapprovedContentCnt(userContainer.getUser().getRoot_group_id());
               }

               int rejectCount = contentInfo.getRejectCnt(userContainer.getUser().getRoot_group_id());
               data.setUnapprovedCount(unapprovedCount);
               data.setRejectCount(rejectCount);
            }
         } catch (Exception var22) {
            this.logger.error(var22.getMessage().toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var22.getMessage());
            return responsebody;
         }

         responsebody.setItems(data);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var23) {
         this.logger.error(var23.getMessage());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var23.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody getContentInfoList(String[] contentIds) throws SQLException {
      ResponseBody responsebody = new ResponseBody();
      int totCnt = 0;

      try {
         List resourceList = new ArrayList();
         if (contentIds != null && contentIds.length > 0) {
            String[] var5 = contentIds;
            int var6 = contentIds.length;

            for(int var7 = 0; var7 < var6; ++var7) {
               String contentId = var5[var7];
               ContentResource resource = this.getContentDetail(contentId);
               resourceList.add(resource);
               if (resource != null) {
                  ++totCnt;
               }
            }
         }

         responsebody.setTotalCount(totCnt);
         responsebody.setItems(resourceList);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var10) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var10.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody getActiveContentInfo(String contentId) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         ContentResource resource = this.getContentDetail(contentId);
         responsebody.setItems(resource);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var4) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody listAllContent(int startIndex, int pageSize) throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         String sortColumn = "last_modified_date";
         String sortOrder = "desc";
         String groupType = "ALL";
         String mediaType = "";
         String groupId = "";
         String searchText = "";
         String selId = "-1";
         String mediaTypeFilter = "";
         String endDate = "";
         String startDate = "";
         String userId = SecurityUtils.getLoginUserId();
         boolean canReadUnshared = false;
         boolean canEditOthers = false;

         try {
            canReadUnshared = this.isContentManageAuthority(userId);
         } catch (Exception var31) {
            this.logger.error(var31.getMessage().toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var31.getMessage());
            return responsebody;
         }

         Map conditionMap = new HashMap();
         conditionMap.put("sortColumn", sortColumn);
         conditionMap.put("sortOrder", sortOrder);
         conditionMap.put("searchText", searchText);
         conditionMap.put("mediaType", mediaType);
         conditionMap.put("isMain", true);
         conditionMap.put("content_type", "CONTENT");
         conditionMap.put("isSelectContent", (Object)null);
         conditionMap.put("creatorID", "admin");
         conditionMap.put("canReadUnshared", canReadUnshared);
         conditionMap.put("searchID", "-1");
         conditionMap.put("selId", selId);
         conditionMap.put("startDate", startDate);
         conditionMap.put("endDate", endDate);
         conditionMap.put("searchCreator", "");
         if (groupType.equalsIgnoreCase("USER") || groupType.equalsIgnoreCase("ORGAN")) {
            canEditOthers = this.cInfo.getCanEditOthers(userId, groupType, (HttpServletRequest)null);
            if (canEditOthers) {
               conditionMap.put("viewRange", "all");
            } else {
               conditionMap.put("viewRange", "shared");
            }
         }

         if (groupType.equalsIgnoreCase("USER")) {
            conditionMap.put("creatorID", userId);
         } else if (groupType.equalsIgnoreCase("GROUPED")) {
            userId = this.cInfo.getGroupInfo(Long.valueOf(groupId)).getCreator_id();
            conditionMap.put("creatorID", userId);
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
         } else if (groupType.equalsIgnoreCase("SUBMITTED")) {
            conditionMap.put("creatorID", userId);
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
         } else {
            conditionMap.put("listType", groupType);
            conditionMap.put("creatorID", userId);
         }

         if (mediaTypeFilter != null && !mediaTypeFilter.equals("")) {
            conditionMap.put("media_type_filter", mediaTypeFilter);
         }

         List contentList = this.cmsDao.getContentListPage(conditionMap, startIndex, pageSize);
         List list = new ArrayList();

         int totalItems;
         for(totalItems = 0; totalItems < contentList.size(); ++totalItems) {
            Content content = (Content)contentList.get(totalItems);
            String deviceType = null;
            float deviceTypeVersion = 1.0F;
            if (content.getDevice_type() != null && !content.getDevice_type().equals("")) {
               deviceType = content.getDevice_type();
               deviceTypeVersion = content.getDevice_type_version();
            } else {
               try {
                  Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(content);
                  deviceType = (String)typeMap.get("deviceType");
                  deviceTypeVersion = Float.parseFloat(typeMap.get("deviceTypeVersion").toString());
               } catch (SQLException var30) {
                  this.logger.error(var30.getMessage().toString());
                  responsebody.setStatus("Fail");
                  responsebody.setErrorMessage(var30.getMessage());
                  return responsebody;
               }
            }

            ContentResource resource = new ContentResource();
            resource.setContentId(content.getContent_id());
            resource.setContentName(content.getContent_name().replaceAll("<", "&lt"));
            resource.setDeviceType(deviceType);
            resource.setDeviceTypeVersion(deviceTypeVersion);
            resource.setMediaType(content.getMedia_type());
            resource.setResolution(content.getResolution());
            String[] convertToSeconds = content.getPlay_time().split(":");
            if (convertToSeconds.length == 1) {
               resource.setPlayTime(0L);
            } else {
               resource.setPlayTime(Long.valueOf(convertToSeconds[1]) * 60L + Long.valueOf(convertToSeconds[2]));
            }

            resource.setTotalSize(content.getTotal_size());
            String modifyDate = content.getLast_modified_date().toString();
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            try {
               Date date = sd.parse(modifyDate);
               modifyDate = DateUtils.date2String(date, "yyyy-MM-dd HH:mm:ss");
            } catch (ParseException var29) {
               this.logger.error("", var29);
               responsebody.setStatus("Fail");
               responsebody.setErrorMessage(var29.getMessage());
               return responsebody;
            }

            resource.setLastModifiedDate(modifyDate);
            resource.setShareFlag(content.getShare_flag());
            resource.setGroupName(content.getGroup_name());
            resource.setContentMetaData(content.getContent_meta_data());
            resource.setCreatorId(content.getCreator_id());
            resource.setThumbFileName(content.getThumb_file_name());
            resource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + content.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(content.getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
            resource.setVersionId(content.getVersion_id());
            resource.setApprovalStatus(content.getApproval_status().toLowerCase());
            resource.setApprovalOpinion(content.getApproval_opinion());
            list.add(resource);
         }

         totalItems = this.cmsDao.getContentListCnt(conditionMap);
         responsebody.setTotalCount(totalItems);
         responsebody.setItems(list);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var32) {
         this.logger.error(var32.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var32.getMessage());
         return responsebody;
      }
   }

   public boolean isContentManageAuthority(String userId) {
      AbilityInfoImpl abilityInfo = AbilityInfoImpl.getInstance();

      try {
         List abilityList = abilityInfo.getAllAbilityListByUserId(userId);
         Iterator it = abilityList.iterator();

         while(it.hasNext()) {
            Map abilityMap = (Map)it.next();
            String abilityValue = (String)abilityMap.get("ability_name");
            if (abilityValue.equalsIgnoreCase("Content Manage Authority")) {
               return true;
            }
         }
      } catch (SQLException var7) {
         this.logger.error("", var7);
      }

      return false;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority', 'Content Schedule Read Authority', 'Content Schedule Write Authority',  'Content Schedule Manage Authority')")
   public ResponseBody listContent(ContentFilter filter) throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         int startIndex = filter.getStartIndex();
         int pageSize = filter.getPageSize();
         String sortColumn = filter.getSortColumn();
         String sortOrder = filter.getSortOrder();
         String groupType = filter.getGroupType();
         String mediaType = filter.getMediaType();
         String groupId = filter.getGroupId();
         String searchText = filter.getSearchText();
         String selId = filter.getSelectId();
         String mediaTypeFilter = filter.getMediaTypeFilter();
         String endDate = filter.getEndDate();
         String startDate = filter.getStartDate();
         String filterDeviceType = filter.getDeviceType();
         String filterDeviceTypeVersion = filter.getDeviceTypeVersion();
         String category = filter.getCategory();
         String tag = filter.getTag();
         String[] tagFilter = null;
         String isMain = filter.getIsMain();
         String tagInputType = filter.getTagInputType();
         if (tag != null && !tag.equals("")) {
            tagFilter = tag.split(",");
         }

         String createdDateFrom = filter.getCreatedDateFrom();
         String createdDateTo = filter.getCreatedDateTo();
         if (tagInputType == null) {
            tagInputType = "ID";
         }

         String userId = SecurityUtils.getLoginUserId();
         boolean canReadUnshared = false;
         boolean canEditOthers = false;

         try {
            canReadUnshared = this.isContentManageAuthority(userId);
         } catch (Exception var48) {
            this.logger.error(var48.getMessage().toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var48.getMessage());
            return responsebody;
         }

         Map conditionMap = new HashMap();
         conditionMap.put("sortColumn", sortColumn);
         conditionMap.put("sortOrder", sortOrder);
         conditionMap.put("searchText", searchText);
         conditionMap.put("mediaType", mediaType);
         conditionMap.put("isMain", isMain);
         conditionMap.put("content_type", "CONTENT");
         conditionMap.put("isSelectContent", (Object)null);
         conditionMap.put("creatorID", "admin");
         conditionMap.put("canReadUnshared", canReadUnshared);
         conditionMap.put("searchID", "-1");
         conditionMap.put("startDate", startDate);
         conditionMap.put("endDate", endDate);
         conditionMap.put("searchCreator", "");
         conditionMap.put("category", category);
         conditionMap.put("tagFilter", tagFilter);
         conditionMap.put("tagInputType", tagInputType);
         conditionMap.put("createdDateFrom", createdDateFrom);
         conditionMap.put("createdDateTo", createdDateTo);
         if (groupType.equalsIgnoreCase("USER") || groupType.equalsIgnoreCase("ORGAN")) {
            canEditOthers = this.cInfo.getCanEditOthers(userId, groupType, (HttpServletRequest)null);
            if (canEditOthers) {
               conditionMap.put("viewRange", "all");
            } else {
               conditionMap.put("viewRange", "shared");
            }
         }

         if (groupType.equalsIgnoreCase("USER")) {
            conditionMap.put("creatorID", userId);
         } else if (groupType.equalsIgnoreCase("GROUPED")) {
            conditionMap.put("creatorID", userId);
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
         } else if (groupType.equalsIgnoreCase("SUBMITTED")) {
            conditionMap.put("creatorID", userId);
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
         } else {
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
            conditionMap.put("creatorID", userId);
         }

         if (mediaTypeFilter != null && !mediaTypeFilter.equals("")) {
            conditionMap.put("media_type_filter", mediaTypeFilter);
         }

         if (filterDeviceType != null && !filterDeviceType.equals("")) {
            conditionMap.put("deviceType", filterDeviceType);
         }

         if (filterDeviceTypeVersion != null && !filterDeviceTypeVersion.equals("")) {
            conditionMap.put("deviceTypeVersion", filterDeviceTypeVersion);
         }

         UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
         String orgName = SecurityUtils.getUserContainer().getUser().getOrganization();
         long orgId = userGroupInfo.getOrgGroupIdByName(orgName);
         ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
         Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
         boolean contentsApprovalEnable = false;
         if (infoMap != null && infoMap.get("CONTENTS_APPROVAL_ENABLE") != null) {
            contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         }

         if (isMain != null && isMain.equalsIgnoreCase("true")) {
            AbilityUtils ability = new AbilityUtils();
            if (contentsApprovalEnable && !ability.isContentApprovalAuthority()) {
               conditionMap.put("isContentApprove", true);
            }
         } else if (contentsApprovalEnable) {
            conditionMap.put("approval_status", "APPROVED");
         }

         List contentList = this.cmsDao.getContentListPage(conditionMap, startIndex, pageSize);
         List list = new ArrayList();

         int totalItems;
         for(totalItems = 0; totalItems < contentList.size(); ++totalItems) {
            Content content = (Content)contentList.get(totalItems);
            String deviceType = null;
            float deviceTypeVersion = 1.0F;
            if (content.getDevice_type() != null && !content.getDevice_type().equals("")) {
               deviceType = content.getDevice_type();
               deviceTypeVersion = content.getDevice_type_version();
            } else {
               try {
                  Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(content);
                  deviceType = (String)typeMap.get("deviceType");
                  deviceTypeVersion = Float.parseFloat(typeMap.get("deviceTypeVersion").toString());
               } catch (SQLException var47) {
                  this.logger.error(var47.getMessage().toString());
                  responsebody.setStatus("Fail");
                  responsebody.setErrorMessage(var47.getMessage());
                  return responsebody;
               }
            }

            ContentResource resource = new ContentResource();
            resource.setContentId(content.getContent_id());
            resource.setContentName(content.getContent_name().replaceAll("<", "&lt"));
            resource.setDeviceType(deviceType);
            resource.setDeviceTypeVersion(deviceTypeVersion);
            resource.setMediaType(content.getMedia_type());
            resource.setResolution(content.getResolution());
            String[] convertToSeconds = content.getPlay_time().split(":");
            if (convertToSeconds.length == 1) {
               resource.setPlayTime(0L);
            } else {
               resource.setPlayTime(Long.valueOf(convertToSeconds[1]) * 60L + Long.valueOf(convertToSeconds[2]));
            }

            resource.setTotalSize(content.getTotal_size());
            String modifyDate = content.getLast_modified_date().toString();
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            try {
               Date date = sd.parse(modifyDate);
               modifyDate = DateUtils.date2String(date, "yyyy-MM-dd HH:mm:ss");
            } catch (ParseException var46) {
               this.logger.error("", var46);
               responsebody.setStatus("Fail");
               responsebody.setErrorMessage(var46.getMessage());
               return responsebody;
            }

            resource.setLastModifiedDate(TimeUtil.getGMTTime(content.getLast_modified_date()));
            resource.setShareFlag(content.getShare_flag());
            resource.setGroupId(content.getGroup_id());
            resource.setGroupName(content.getGroup_name());
            resource.setContentMetaData(content.getContent_meta_data());
            resource.setCreatorId(content.getCreator_id());
            resource.setThumbFileName(content.getThumb_file_name());
            resource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + content.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(content.getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
            resource.setVersionId(content.getVersion_id());
            resource.setApprovalStatus(content.getApproval_status().toLowerCase());
            resource.setApprovalOpinion(content.getApproval_opinion());
            resource.setIsDeleted(content.getIs_deleted());
            list.add(resource);
         }

         totalItems = this.cmsDao.getContentListCnt(conditionMap);
         responsebody.setTotalCount(totalItems);
         responsebody.setItems(list);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var49) {
         this.logger.error(var49.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var49.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody listContentFile(ContentFilter filter) throws Exception {
      ResponseBody responsebody = new ResponseBody();

      try {
         int startIndex = filter.getStartIndex();
         int pageSize = filter.getPageSize();
         String sortColumn = filter.getSortColumn();
         String sortOrder = filter.getSortOrder();
         String groupType = filter.getGroupType();
         String mediaType = filter.getMediaType();
         String groupId = filter.getGroupId();
         String searchText = filter.getSearchText();
         String selId = filter.getSelectId();
         String mediaTypeFilter = filter.getMediaTypeFilter();
         String endDate = filter.getEndDate();
         String startDate = filter.getStartDate();
         String filterDeviceType = filter.getDeviceType();
         String filterDeviceTypeVersion = filter.getDeviceTypeVersion();
         String category = filter.getCategory();
         String tag = filter.getTag();
         String isMain = filter.getIsMain();
         String isThumbnail = filter.getIsThumbnail();
         String[] tagFilter = null;
         if (tag != null && !tag.equals("")) {
            tagFilter = tag.split(",");
         }

         String userId = SecurityUtils.getLoginUserId();
         boolean canReadUnshared = false;
         boolean canEditOthers = false;

         try {
            canReadUnshared = this.isContentManageAuthority(userId);
         } catch (Exception var35) {
            this.logger.error(var35.getMessage().toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var35.getMessage());
            return responsebody;
         }

         Map conditionMap = new HashMap();
         conditionMap.put("sortColumn", sortColumn);
         conditionMap.put("sortOrder", sortOrder);
         conditionMap.put("searchText", searchText);
         conditionMap.put("mediaType", mediaType);
         conditionMap.put("isMain", isMain);
         conditionMap.put("content_type", "CONTENT");
         conditionMap.put("isSelectContent", (Object)null);
         conditionMap.put("creatorID", "admin");
         conditionMap.put("canReadUnshared", canReadUnshared);
         conditionMap.put("searchID", "-1");
         conditionMap.put("selId", selId);
         conditionMap.put("startDate", startDate);
         conditionMap.put("endDate", endDate);
         conditionMap.put("searchCreator", "");
         conditionMap.put("category", category);
         conditionMap.put("tagFilter", tagFilter);
         if ("TRUE".equalsIgnoreCase(filter.getIsUsedContent())) {
            conditionMap.put("contentUsingStatusFilter", "used_content");
         } else if ("FALSE".equalsIgnoreCase(filter.getIsUsedContent())) {
            conditionMap.put("contentUsingStatusFilter", "unused_content");
         }

         if (groupType.equalsIgnoreCase("USER") || groupType.equalsIgnoreCase("ORGAN")) {
            canEditOthers = this.cInfo.getCanEditOthers(userId, groupType, (HttpServletRequest)null);
            if (canEditOthers) {
               conditionMap.put("viewRange", "all");
            } else {
               conditionMap.put("viewRange", "shared");
            }
         }

         if (groupType.equalsIgnoreCase("USER")) {
            conditionMap.put("creatorID", userId);
         } else if (groupType.equalsIgnoreCase("GROUPED")) {
            conditionMap.put("creatorID", userId);
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
         } else if (groupType.equalsIgnoreCase("SUBMITTED")) {
            conditionMap.put("creatorID", userId);
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
         } else {
            conditionMap.put("groupID", groupId);
            conditionMap.put("listType", groupType);
            conditionMap.put("creatorID", userId);
         }

         if (mediaTypeFilter != null && !mediaTypeFilter.equals("")) {
            conditionMap.put("media_type_filter", mediaTypeFilter);
         }

         if (filterDeviceType != null && !filterDeviceType.equals("")) {
            conditionMap.put("deviceType", filterDeviceType);
         }

         if (filterDeviceTypeVersion != null && !filterDeviceTypeVersion.equals("")) {
            conditionMap.put("deviceTypeVersion", filterDeviceTypeVersion);
         }

         List contentList = this.cmsDao.getContentListPage(conditionMap, startIndex, pageSize);
         List list = new ArrayList();

         int totalItems;
         for(totalItems = 0; totalItems < contentList.size(); ++totalItems) {
            Content content = (Content)contentList.get(totalItems);
            String deviceType = null;
            float deviceTypeVersion = 1.0F;
            if (content.getDevice_type() != null && !content.getDevice_type().equals("")) {
               deviceType = content.getDevice_type();
               deviceTypeVersion = content.getDevice_type_version();
            } else {
               try {
                  Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(content);
                  deviceType = (String)typeMap.get("deviceType");
                  deviceTypeVersion = Float.parseFloat(typeMap.get("deviceTypeVersion").toString());
               } catch (SQLException var34) {
                  this.logger.error(var34.getMessage().toString());
                  responsebody.setStatus("Fail");
                  responsebody.setErrorMessage(var34.getMessage());
                  return responsebody;
               }
            }

            String contentId = content.getContent_id();
            List contentFileList = null;
            if (isThumbnail != null && "FALSE".equalsIgnoreCase(isThumbnail)) {
               contentFileList = this.cmsDao.getActiveFileListByType(contentId, false);
            } else if (isThumbnail != null && "TRUE".equalsIgnoreCase(isThumbnail)) {
               contentFileList = this.cmsDao.getActiveFileListByType(contentId, true);
            } else {
               contentFileList = this.cmsDao.getActiveFileList(contentId);
            }

            list.addAll(contentFileList);
         }

         totalItems = this.cmsDao.getContentListCnt(conditionMap);
         responsebody.setTotalCount(totalItems);
         responsebody.setItems(list);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var36) {
         this.logger.error(var36.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var36.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody updateContentFile(String contentId, HttpServletRequest request) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         ContentInfo cmsDao = ContentInfoImpl.getInstance();
         UserInfo uInfo = UserInfoImpl.getInstance();
         String userId = SecurityUtils.getLoginUserId();

         long orgCreatorId;
         try {
            orgCreatorId = cmsDao.getRootId(userId);
            if (!cmsDao.isExistContentID(contentId)) {
               this.logger.error("[REST][CONTENT][updateContentFile] Content id is not exist! ");
               responsebody.setStatus("Fail");
               responsebody.setErrorMessage("[REST][CONTENT][updateContentFile] Content id is not exist! ");
               return responsebody;
            }
         } catch (SQLException var105) {
            this.logger.error(var105.getMessage());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var105.getMessage());
            return responsebody;
         }

         if (!ServletFileUpload.isMultipartContent(request)) {
            responsebody.setErrorCode(ExceptionCode.RES909[0]);
            responsebody.setErrorMessage(ExceptionCode.RES909[2]);
            return responsebody;
         } else {
            String CONTENTS_HOME = null;
            String THUMBNAIL_HOME = null;

            try {
               CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
               THUMBNAIL_HOME = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar);
            } catch (ConfigException var104) {
               this.logger.error(var104.getMessage());
               responsebody.setStatus("Fail");
               responsebody.setErrorMessage(var104.getMessage());
               return responsebody;
            }

            File cmsHome = SecurityUtils.getSafeFile(CONTENTS_HOME);
            if (!cmsHome.exists()) {
               cmsHome.mkdir();
            }

            ContentCodeInfo codeDao = ContentCodeInfoImpl.getInstance();
            FileManager fileManager = FileManagerImpl.getInstance();
            boolean bExistFile = false;

            try {
               DefaultMultipartHttpServletRequest multipartRequest = (DefaultMultipartHttpServletRequest)request;
               Map map = multipartRequest.getFileMap();
               MultipartFile multipartFile = (MultipartFile)map.get("file");
               if (multipartFile == null) {
                  this.logger.error("[REST][CONTENT][updateContentFile] Empty file info! ");
                  responsebody.setStatus("Fail");
                  responsebody.setErrorMessage("[REST][CONTENT][updateContentFile] Empty file info! ");
                  return responsebody;
               }

               synchronized(this) {
                  if (multipartFile.isEmpty()) {
                     this.logger.error("[REST][CONTENT][updateContentFile] Empty file info! ");
                     responsebody.setStatus("Fail");
                     responsebody.setErrorMessage("[REST][CONTENT][updateContentFile] Empty file info! ");
                     return responsebody;
                  }

                  String fileID = UUID.randomUUID().toString().toUpperCase();
                  String fileName = multipartFile.getOriginalFilename();
                  String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
                  String contentName = fileName.substring(0, fileName.lastIndexOf("."));
                  String mediaType = null;
                  System.out.println("start! contentName : " + contentName + " contentId : " + contentId);
                  mediaType = StrUtils.nvl(codeDao.getMediaTypeByFileType(fileExtension.toUpperCase()));
                  if (mediaType != null && mediaType.length() == 0) {
                     responsebody.setStatus("Fail");
                     responsebody.setErrorMessage("doNotSupportContentList : " + fileName);
                     return responsebody;
                  }

                  long fileSize = multipartFile.getSize();
                  File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + fileID + File.separator + fileName, (String)null));
                  file.getParentFile().mkdir();
                  InputStream is = multipartFile.getInputStream();
                  FileOutputStream os = new FileOutputStream(file.getAbsoluteFile());

                  try {
                     IOUtils.copy(is, os);
                  } catch (Exception var102) {
                     this.logger.error(var102);
                  } finally {
                     os.close();
                     is.close();
                  }

                  String hashId = this.getHash(file);
                  String strmUrl = null;
                  String thumbnailFileId = null;
                  List fileListToSave = new ArrayList();
                  boolean createThumbnail = true;
                  if (mediaType.equalsIgnoreCase("STRM")) {
                     BufferedReader br = null;

                     try {
                        char[] c = new char[(int)file.length()];
                        br = new BufferedReader(new FileReader(file));
                        br.read(c);
                        strmUrl = new String(c);
                     } catch (FileNotFoundException var99) {
                        this.logger.error("", var99);
                     } catch (Exception var100) {
                        this.logger.error("", var100);
                     } finally {
                        try {
                           br.close();
                        } catch (IOException var96) {
                           this.logger.error("", var96);
                        }

                     }
                  }

                  if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                     bExistFile = true;
                  }

                  try {
                     long versionId = cmsDao.getMaxContentVersionId(contentId) + 1L;
                     long existGroupId = cmsDao.getGroupId(contentId);
                     Content content = new Content();
                     String thumbFileId = null;
                     ContentFile cmsContentFile;
                     if (bExistFile) {
                        file.delete();
                        fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                        file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                        thumbFileId = cmsDao.getThumbIdByMainFileId(fileID);
                        if (thumbFileId != null) {
                           cmsContentFile = cmsDao.getFileInfo(thumbFileId);
                           content.setThumb_file_id(thumbFileId);
                           content.setThumb_file_name(cmsContentFile.getFile_name());
                           createThumbnail = false;
                        }
                     } else {
                        cmsContentFile = new ContentFile();
                        if (!fileExtension.equalsIgnoreCase("mpeg") && !fileExtension.equalsIgnoreCase("mpg") && !fileExtension.equalsIgnoreCase("wmv") && !fileExtension.equalsIgnoreCase("avi") && !fileExtension.equalsIgnoreCase("mov") && !fileExtension.equalsIgnoreCase("mp4") && !fileExtension.equalsIgnoreCase("asf")) {
                           cmsContentFile.setIs_streaming("N");
                        } else {
                           cmsContentFile.setIs_streaming("Y");
                        }

                        cmsContentFile.setFile_id(fileID);
                        cmsContentFile.setHash_code(hashId);
                        cmsContentFile.setFile_type("MAIN");
                        cmsContentFile.setFile_name(fileName);
                        cmsContentFile.setFile_size(fileSize);
                        cmsContentFile.setCreator_id(userId);
                        cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + fileID);
                        cmsDao.addFile(cmsContentFile);
                        fileListToSave.add(cmsContentFile);
                     }

                     content.setMain_file_id(fileID);
                     content.setMain_file_Extension(fileExtension.toUpperCase());
                     content.setContent_id(contentId);
                     content.setVersion_id(versionId);
                     content.setCreator_id(userId);
                     content.setMedia_type(mediaType);
                     content.setGroup_id(existGroupId);
                     content.setShare_flag(1);
                     content.setContent_meta_data("");
                     content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
                     content.setTotal_size(fileSize);
                     content.setIs_active("N");
                     content.setOrg_creator_id(String.valueOf(orgCreatorId));
                     String play_time;
                     if (content.getMedia_type().equalsIgnoreCase("SOUND")) {
                        content.setThumb_file_id("SOUND_THUMBNAIL");
                        content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
                        String[] fileMeta;
                        if (fileExtension.equals("MP3")) {
                           try {
                              fileMeta = fileManager.getFileMeta(file);
                              if (fileMeta[0] != null) {
                                 play_time = fileMeta[0];
                                 if (play_time.length() > 8) {
                                    content.setPlay_time(play_time.substring(0, 8));
                                    content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                 } else {
                                    content.setPlay_time(play_time);
                                 }
                              } else {
                                 play_time = fileManager.getMP3PlayTime(file);
                                 if (play_time.length() > 8) {
                                    content.setPlay_time(play_time.substring(0, 8));
                                    content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                 } else {
                                    content.setPlay_time(play_time);
                                 }
                              }
                           } catch (Exception var98) {
                              content.setPlay_time("");
                           }
                        } else {
                           fileMeta = fileManager.getFileMeta(file);
                           if (fileMeta[0] != null) {
                              play_time = fileMeta[0];
                              if (play_time.length() > 8) {
                                 content.setPlay_time(play_time.substring(0, 8));
                                 content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                              } else {
                                 content.setPlay_time(play_time);
                              }
                           } else {
                              content.setPlay_time("");
                           }
                        }
                     } else if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                        if (content.getMedia_type().equalsIgnoreCase("STRM")) {
                           if (strmUrl != null) {
                              cmsDao.updateUrlSetting(content.getContent_id(), content.getContent_name(), strmUrl);
                           }

                           content.setThumb_file_id("STRM_THUMBNAIL");
                           content.setThumb_file_name("STRM_THUMBNAIL.PNG");
                        } else if (content.getMedia_type().equalsIgnoreCase("OFFICE")) {
                           content.setThumb_file_id("OFFICE_THUMBNAIL");
                           content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
                           thumbnailFileId = "OFFICE_THUMBNAIL";
                        } else if (content.getMedia_type().equalsIgnoreCase("FLASH")) {
                           content.setThumb_file_id("FLASH_THUMBNAIL");
                           content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
                           thumbnailFileId = "FLASH_THUMBNAIL";
                        } else if (content.getMedia_type().equalsIgnoreCase("PDF")) {
                           content.setThumb_file_id("PDF_THUMBNAIL");
                           content.setThumb_file_name("PDF_THUMBNAIL.PNG");
                           thumbnailFileId = "PDF_THUMBNAIL";
                        } else {
                           content.setThumb_file_id("ETC_THUMBNAIL");
                           content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                           thumbnailFileId = "ETC_THUMBNAIL";
                        }
                     } else {
                        if (createThumbnail) {
                           thumbnailFileId = UUID.randomUUID().toString().toUpperCase();
                        } else {
                           thumbnailFileId = thumbFileId;
                        }

                        Map thumbnailMap = fileManager.createThumbnailFile(file, thumbnailFileId, content);
                        if (thumbnailMap != null && thumbnailMap.get("status") != null && thumbnailMap.get("status").equals("error")) {
                           throw new Exception("error create thumbnail");
                        }

                        if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                           content.setResolution((String)thumbnailMap.get("resolution"));
                        }

                        if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                           play_time = (String)thumbnailMap.get("playTime");
                           if (play_time.length() > 8) {
                              content.setPlay_time(play_time.substring(0, 8));
                              content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                           } else {
                              content.setPlay_time(play_time);
                           }
                        }

                        File thumbnail = null;
                        if (thumbnailMap != null && thumbnailMap.get("file") != null) {
                           thumbnail = (File)thumbnailMap.get("file");
                        }

                        String thumbHash = FileUtils.getHash(thumbnail);
                        if (thumbHash == null) {
                           content.setThumb_file_id("NOIMAGE_THUMBNAIL");
                           content.setThumb_file_name("NOIMAGE_THUMBNAIL.PNG");
                        } else {
                           if (cmsDao.isExistFileByHash(fileName + ".png", thumbnail.length(), thumbHash)) {
                              thumbnailFileId = cmsDao.getFileIDByHash(fileName + ".png", thumbnail.length(), thumbHash);
                           }

                           content.setThumb_file_id(thumbnailFileId);
                           content.setThumb_file_name(fileName + ".png");
                        }

                        boolean addThumbFile = true;
                        ContentFile cmsThumbFile = new ContentFile();
                        File fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
                        File thumbnailFileChk = new File(SecurityUtils.directoryTraversalChecker(THUMBNAIL_HOME + "/" + thumbnailFileId, (String)null));
                        if (thumbnailFileChk.exists()) {
                           addThumbFile = false;
                        }

                        if (addThumbFile) {
                           cmsThumbFile.setFile_id(thumbnailFileId);
                           cmsThumbFile.setHash_code(thumbHash);
                           cmsThumbFile.setFile_type("THUMBNAIL");
                           cmsThumbFile.setFile_name(fileName + ".png");
                           cmsThumbFile.setFile_size(thumbnail.length());
                           cmsThumbFile.setCreator_id("SYSTEM");
                           cmsThumbFile.setFile_path(CONTENTS_HOME + File.separator + thumbnailFileId);
                           cmsDao.addFile(cmsThumbFile);
                           if (cmsThumbFile != null) {
                              fileListToSave.add(cmsThumbFile);
                           }

                           content.setArr_file_list(fileListToSave);
                           if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                              content.setResolution((String)thumbnailMap.get("resolution"));
                           }

                           String filePath;
                           if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                              filePath = (String)thumbnailMap.get("playTime");
                              if (filePath.length() > 8) {
                                 content.setPlay_time(filePath.substring(0, 8));
                                 content.setPlay_time_milli(filePath.substring(9, filePath.length()));
                              } else {
                                 content.setPlay_time(filePath);
                              }
                           }

                           try {
                              if (!fileCmsHome.exists()) {
                                 boolean fSuccess = fileCmsHome.mkdir();
                                 if (!fSuccess) {
                                    this.logger.error("mkdir Fail");
                                 }
                              }

                              filePath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id();
                              File fileCmsFile = SecurityUtils.getSafeFile(filePath);
                              if (!fileCmsFile.exists()) {
                                 boolean fSuccess = fileCmsFile.mkdir();
                                 if (!fSuccess) {
                                    this.logger.error("mkdir Fail");
                                 }
                              }

                              String image_url = CONTENTS_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                              String thumb_url = THUMBNAIL_HOME + "/" + cmsThumbFile.getFile_id() + "/" + cmsThumbFile.getFile_name();
                              File checkFile = SecurityUtils.getSafeFile(thumb_url);
                              if (!checkFile.exists()) {
                                 File thumbFile = SecurityUtils.getSafeFile(image_url);
                                 if (thumbFile.exists()) {
                                    BufferedImage bufferedImage = ImageIO.read(thumbFile);
                                    if (bufferedImage == null) {
                                       this.logger.error("[REST][CONTENT][updateContentFile] null thumbnail image : " + thumbFile.getPath());
                                       throw new NullPointerException();
                                    }

                                    int orgWidth = bufferedImage.getWidth();
                                    int orgHeight = bufferedImage.getHeight();
                                    int smallWidth = 50;
                                    int smallHeight = 38;
                                    int mediumWidth = 165;
                                    int mediumHeight = 109;
                                    if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                                       mediumHeight = orgHeight * mediumWidth / orgWidth;
                                       if (mediumHeight % 2 != 0) {
                                          ++mediumHeight;
                                       }
                                    } else {
                                       mediumWidth = orgWidth * mediumHeight / orgHeight;
                                       if (mediumWidth % 2 != 0) {
                                          ++mediumWidth;
                                       }
                                    }

                                    if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                                       smallHeight = orgHeight * smallWidth / orgWidth;
                                       if (smallHeight % 2 != 0) {
                                          ++smallHeight;
                                       }
                                    } else {
                                       smallWidth = orgWidth * smallHeight / orgHeight;
                                       if (smallWidth % 2 != 0) {
                                          ++smallWidth;
                                       }
                                    }

                                    if (mediumWidth < 1) {
                                       mediumWidth = 1;
                                    }

                                    if (mediumHeight < 1) {
                                       mediumHeight = 1;
                                    }

                                    if (smallWidth < 1) {
                                       smallWidth = 1;
                                    }

                                    if (smallHeight < 1) {
                                       smallHeight = 1;
                                    }

                                    File thumb_File = SecurityUtils.getSafeFile(thumb_url);
                                    File thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                                    File thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                                    Map hdThumbnailMap = fileManager.createThumbnailFile(file, THUMBNAIL_HOME, thumbnailFileId, content, 1280, 720, "_HD.PNG");
                                    if (hdThumbnailMap != null && hdThumbnailMap.get("status") != null && hdThumbnailMap.get("status").equals("error")) {
                                       this.logger.error("HD Thumbnail create error.");
                                    }
                                 }
                              }
                           } catch (Exception var97) {
                              this.logger.error("[REST][CONTENT][updateContentFile] error create thumbnail");
                              throw new Exception("error create thumbnail!");
                           }
                        }
                     }

                     this.logger.info("[REST][CONTENT][updateContentFile] content update! contentId : " + contentId + " new version : " + versionId + " fileId : " + fileID);
                     cmsDao.addContentVersionInfo(content);
                     cmsDao.addMapContentFile(contentId, versionId, fileID);
                     if (thumbnailFileId == null) {
                        thumbnailFileId = "ETC_THUMBNAIL";
                     }

                     cmsDao.addMapContentFile(contentId, versionId, thumbnailFileId);
                     cmsDao.setActiveVersionForUploader(contentId, versionId, true);
                  } catch (Exception var106) {
                     this.logger.error("[REST][CONTENT][updateContentFile] fail update content error : " + var106.getMessage(), var106);
                  }
               }
            } catch (Exception var108) {
               this.logger.error(var108.getMessage());
               responsebody.setStatus("Fail");
               responsebody.setErrorMessage(var108.getMessage());
               return responsebody;
            }

            ContentResource resource = this.getContentDetail(contentId);
            responsebody.setItems(resource);
            responsebody.setStatus("Success");
            return responsebody;
         }
      } catch (Exception var109) {
         this.logger.error(var109.getMessage());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var109.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody deleteContent(String contentId) throws SQLException {
      ResponseBody responsebody = new ResponseBody();
      ContentDeleteResource resource = new ContentDeleteResource();
      String contentIds = contentId;
      new ContentLog();
      LogInfo logInfo = LogInfoImpl.getInstance();
      String userId = SecurityUtils.getLoginUserId();
      List deletedContentIds = new ArrayList();
      List refPlaylistList = new ArrayList();
      List refScheduleList = new ArrayList();
      List refEventList = new ArrayList();
      ArrayList contentNameList = new ArrayList();
      TagInfo tagInfo = TagInfoImpl.getInstance();
      CategoryInfoImpl categoryInfo = CategoryInfoImpl.getInstance();

      try {
         int cnt = 0;
         String undeletableContentIds = "";
         if (contentIds != "") {
            String[] contentIdList = contentIds.split(",");
            String sessionId = UUID.randomUUID().toString();
            List triggerTagList = new ArrayList();
            int i = 0;

            while(true) {
               if (i >= contentIdList.length) {
                  if (triggerTagList.size() > 0) {
                     tagInfo.setPlaylistTrigger(triggerTagList);
                  }
                  break;
               }

               if (!SecurityUtils.checkReadPermissionWithOrgAndId("Content", contentIdList[i])) {
                  throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_NOT_HAVE, new String[]{"content read"});
               }

               Content curContent = this.cInfo.getContentAndFileActiveVerInfo(contentIdList[i]);
               if (curContent != null && SecurityUtils.getLoginUser().getRoot_group_id() != 0L && SecurityUtils.getLoginUser().getRoot_group_id() != curContent.getOrganization_id()) {
                  throw new RestServiceException(RestExceptionCode.FORBIDDEN_PERMISSION_DENIED);
               }

               if (this.cInfo.isDeletableContent(contentIdList[i], sessionId)) {
                  if (this.cInfo.deleteContent(contentIdList[i], userId, sessionId) > 0) {
                     List tagList = tagInfo.getContentTagList(contentIdList[i]);
                     if (tagList != null && tagList.size() > 0) {
                        Iterator var23 = tagList.iterator();

                        while(var23.hasNext()) {
                           TagEntity tag = (TagEntity)var23.next();
                           if (!triggerTagList.contains((long)tag.getTag_id())) {
                              triggerTagList.add((long)tag.getTag_id());
                           }
                        }

                        tagInfo.deleteTagInfoFromContentId(contentIdList[i]);
                     }

                     categoryInfo.deleteCategoryFromContentId(contentIdList[i]);
                     List ftpList = this.cInfo.getFtpContentSettingByContentId(contentIdList[i]);
                     List cifsList = this.cInfo.getCifsContentSettingByContentId(contentIdList[i]);
                     if (ftpList != null && ftpList.size() > 0) {
                        this.cInfo.updateFtpSettingAsDeleted(contentIdList[i], "Y");
                     }

                     if (cifsList != null && cifsList.size() > 0) {
                        this.cInfo.updateCifsSettingAsDeleted(contentIdList[i], "Y");
                     }

                     deletedContentIds.add(contentIdList[i]);
                  }
               } else {
                  ++cnt;
                  if (undeletableContentIds != "") {
                     undeletableContentIds = undeletableContentIds + ",";
                  }

                  undeletableContentIds = undeletableContentIds + contentIdList[i];
                  contentNameList.add(StrUtils.cutCharLen(this.cInfo.getContentName(contentIdList[i]), 25));
                  this.setRefPlaylistList(contentIdList[i], refPlaylistList, "PREMIUM");
                  this.setRefScheduleList(contentIdList[i], refScheduleList, "PREMIUM");
                  this.setRefEventList(contentIdList[i], refEventList);
               }

               ++i;
            }
         }

         if (cnt > 0) {
            resource.setUndeletableContentIds(undeletableContentIds);
            resource.setContentNameList(contentNameList);
            resource.setRefPlaylists(refPlaylistList);
            resource.setRefSchedules(refScheduleList);
            resource.setRefEvents(refEventList);
            resource.setStatus("undelete");
         } else {
            resource.setDeletedContentIds(deletedContentIds);
            resource.setStatus("success");
         }

         responsebody.setItems(resource);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var25) {
         this.logger.error(var25.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var25.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody forceDeleteContent(String contentId) throws SQLException {
      ResponseBody responsebody = new ResponseBody();
      ContentDeleteResource resource = new ContentDeleteResource();
      UserContainer userContainer = SecurityUtils.getUserContainer();
      LogInfo logInfo = LogInfoImpl.getInstance();
      new ContentLog();
      String sessionId = UUID.randomUUID().toString();
      String content_list = contentId;
      String[] aval = null;
      ArrayList deletedContentIds = new ArrayList();

      try {
         if (content_list.length() > 0) {
            List triggerTagList = new ArrayList();
            TagInfo tagInfo = TagInfoImpl.getInstance();
            CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
            String ipAddress = "mobile";
            aval = content_list.split(",");

            for(int i = 0; i < aval.length; ++i) {
               DeleteContentUtils.checkContentFromPlaylist(aval[i], ipAddress);
               DeleteContentUtils.checkContentFromSchedule(aval[i], ipAddress);
               DeleteContentUtils.checkContentFromConvertTable(aval[i]);
               DeleteContentUtils.checkContentFromDLK(aval[i], ipAddress);
               DeleteContentUtils.checkContentFromEventCondition(aval[i]);
               if (this.cInfo.deleteContent(aval[i], userContainer.getUser().getUser_id(), sessionId) > 0) {
                  List tagList = tagInfo.getContentTagList(aval[i]);
                  if (tagList != null && tagList.size() > 0) {
                     Iterator var17 = tagList.iterator();

                     while(var17.hasNext()) {
                        TagEntity tag = (TagEntity)var17.next();
                        if (!triggerTagList.contains((long)tag.getTag_id())) {
                           triggerTagList.add((long)tag.getTag_id());
                        }
                     }

                     tagInfo.deleteTagInfoFromContentId(aval[i]);
                  }

                  categoryInfo.deleteCategoryFromContentId(aval[i]);
                  deletedContentIds.add(aval[i]);
               }
            }

            if (triggerTagList.size() > 0) {
               tagInfo.setPlaylistTrigger(triggerTagList);
            }
         }

         resource.setDeletedContentIds(deletedContentIds);
         resource.setStatus("success");
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var19) {
         this.logger.error(var19.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var19.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Manage Authority')")
   public ResponseBody approveContents(ContentApproveResource resource) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         String contentIds = resource.getContentId();
         String[] content = null;
         if (contentIds.length() > 0) {
            content = contentIds.split(",");
         }

         for(int i = 0; i < content.length; ++i) {
            this.cInfo.setApprovalStatus(content[i], resource.getStatus(), resource.getOpinion());
         }

         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var6) {
         this.logger.error(var6.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody uploadContent(String groupId, HttpServletRequest request) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         ContentInfo cmsDao = ContentInfoImpl.getInstance();
         UserInfo uInfo = UserInfoImpl.getInstance();
         String userId = SecurityUtils.getLoginUserId();
         boolean contentsApprovalEnable = false;

         long orgCreatorId;
         try {
            long orgId = uInfo.getRootGroupIdByUserId(userId);
            orgCreatorId = cmsDao.getRootId(userId);
            ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
            Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
            contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         } catch (SQLException var73) {
            this.logger.error(var73.getMessage().toString());
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var73.getMessage());
            return responsebody;
         }

         if (groupId == null || groupId.equals("") || groupId.equals("0") || groupId.equals("null")) {
            groupId = String.valueOf(orgCreatorId);
         }

         if (!ServletFileUpload.isMultipartContent(request)) {
            responsebody.setErrorCode(ExceptionCode.RES909[0]);
            responsebody.setErrorMessage(ExceptionCode.RES909[2]);
            return responsebody;
         } else {
            String CONTENTS_HOME = null;
            String THUMBNAIL_HOME = null;

            try {
               CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
               THUMBNAIL_HOME = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar);
            } catch (ConfigException var72) {
               this.logger.error(var72.getMessage().toString());
               responsebody.setStatus("Fail");
               responsebody.setErrorMessage(var72.getMessage());
               return responsebody;
            }

            File cmsHome = SecurityUtils.getSafeFile(CONTENTS_HOME);
            if (!cmsHome.exists()) {
               cmsHome.mkdir();
            }

            new ServletFileUpload(new DiskFileItemFactory());
            ContentCodeInfo codeDao = ContentCodeInfoImpl.getInstance();
            FileManager fileManager = FileManagerImpl.getInstance();
            boolean bExistFile = false;
            boolean bMustAddContent = false;
            ArrayList doNotSupportContentList = new ArrayList();

            try {
               DefaultMultipartHttpServletRequest multipartRequest = (DefaultMultipartHttpServletRequest)request;
               Map map = multipartRequest.getFileMap();
               MultipartFile multiitem = (MultipartFile)map.get("file");
               if (multiitem != null) {
                  synchronized(this) {
                     if (!multiitem.isEmpty()) {
                        String fileID = UUID.randomUUID().toString().toUpperCase();
                        String contentId = UUID.randomUUID().toString().toUpperCase();
                        String fileName = multiitem.getOriginalFilename();
                        String fext = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
                        String contentName = fileName.substring(0, fileName.lastIndexOf("."));
                        String mediaType = null;
                        System.out.println("start! contentName : " + contentName + " contentId : " + contentId);
                        mediaType = StrUtils.nvl(codeDao.getMediaTypeByFileType(fext.toUpperCase()));
                        if (mediaType == null || mediaType != null && mediaType.length() == 0) {
                           doNotSupportContentList.add(fileName);
                           responsebody.setStatus("Fail");
                           responsebody.setErrorMessage("doNotSupportContentList : " + fileName);
                           return responsebody;
                        }

                        long fileSize = multiitem.getSize();
                        File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + fileID + File.separator + fileName, (String)null));
                        file.getParentFile().mkdir();
                        InputStream is = multiitem.getInputStream();
                        FileOutputStream os = new FileOutputStream(file.getAbsoluteFile());

                        try {
                           int data = false;
                           IOUtils.copy(is, os);
                        } catch (Exception var70) {
                           this.logger.error(var70);
                        } finally {
                           os.close();
                           is.close();
                        }

                        String hashId = this.getHash(file);
                        ContentFile cmsContentFile = new ContentFile();
                        if (!fext.equalsIgnoreCase("mpeg") && !fext.equalsIgnoreCase("mpg") && !fext.equalsIgnoreCase("wmv") && !fext.equalsIgnoreCase("avi") && !fext.equalsIgnoreCase("mov") && !fext.equalsIgnoreCase("mp4") && !fext.equalsIgnoreCase("asf")) {
                           cmsContentFile.setIs_streaming("N");
                        } else {
                           cmsContentFile.setIs_streaming("Y");
                        }

                        if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                           bExistFile = true;
                        }

                        if (bExistFile) {
                           ContentFile existFile = cmsDao.getMainFileInfo(contentId);
                           if (existFile == null) {
                              existFile = cmsDao.getMainFileInfoOfTmpVer(contentId);
                           }

                           if (existFile != null) {
                              if (existFile.getFile_name().equals(cmsContentFile.getFile_name()) && existFile.getFile_size().equals(cmsContentFile.getFile_size()) && existFile.getHash_code().equalsIgnoreCase(cmsContentFile.getHash_code())) {
                                 bMustAddContent = false;
                              } else {
                                 bMustAddContent = true;
                              }
                           }
                        } else {
                           bMustAddContent = true;
                        }

                        if (bExistFile) {
                           file.delete();
                           fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                           file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                        }

                        cmsContentFile.setFile_id(fileID);
                        cmsContentFile.setHash_code(hashId);
                        cmsContentFile.setFile_type("MAIN");
                        cmsContentFile.setFile_name(fileName);
                        cmsContentFile.setFile_size(fileSize);
                        cmsContentFile.setCreator_id(userId);
                        cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + fileID);
                        Content content = new Content();
                        content.setVersion_id(1L);
                        content.setContent_id(contentId);
                        content.setGroup_id(Long.valueOf(groupId));
                        content.setShare_flag(1);
                        content.setContent_meta_data("");
                        content.setCreator_id(userId);
                        content.setMedia_type(mediaType);
                        content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
                        content.setTotal_size(fileSize);
                        content.setIs_active("Y");
                        content.setOrg_creator_id(String.valueOf(orgCreatorId));
                        ContentFile cmsThumbFile = null;
                        this.logger.error("[ContentFileUploadServlet] content name : " + content.getContent_name() + " content Media Type : " + content.getMedia_type());
                        if (content.getMedia_type().equalsIgnoreCase("LFD") || content.getMedia_type().equalsIgnoreCase("LFT") || content.getMedia_type().equalsIgnoreCase("VWL") || content.getMedia_type().equalsIgnoreCase("PROM")) {
                           doNotSupportContentList.add(contentName);
                           responsebody.setStatus("Fail");
                           responsebody.setErrorMessage("doNotSupportContentList : " + contentName);
                           return responsebody;
                        }

                        String filePath;
                        if (content.getMedia_type().equalsIgnoreCase("SOUND")) {
                           content.setThumb_file_id("SOUND_THUMBNAIL");
                           content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
                           String[] fileMeta;
                           String play_time;
                           if (fext.equals("MP3")) {
                              try {
                                 fileMeta = fileManager.getFileMeta(file);
                                 if (fileMeta[0] != null) {
                                    play_time = fileMeta[0];
                                    if (play_time.length() > 8) {
                                       content.setPlay_time(play_time.substring(0, 8));
                                       content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                    } else {
                                       content.setPlay_time(play_time);
                                    }
                                 } else {
                                    play_time = fileManager.getMP3PlayTime(file);
                                    if (play_time.length() > 8) {
                                       content.setPlay_time(play_time.substring(0, 8));
                                       content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                    } else {
                                       content.setPlay_time(play_time);
                                    }
                                 }
                              } catch (Exception var69) {
                                 content.setPlay_time("");
                              }
                           } else {
                              fileMeta = fileManager.getFileMeta(file);
                              if (fileMeta[0] != null) {
                                 play_time = fileMeta[0];
                                 if (play_time.length() > 8) {
                                    content.setPlay_time(play_time.substring(0, 8));
                                    content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                 } else {
                                    content.setPlay_time(play_time);
                                 }
                              } else {
                                 content.setPlay_time("");
                              }
                           }
                        } else if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                           if (content.getMedia_type().equalsIgnoreCase("OFFICE")) {
                              content.setThumb_file_id("OFFICE_THUMBNAIL");
                              content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
                           } else if (content.getMedia_type().equalsIgnoreCase("FLASH")) {
                              content.setThumb_file_id("FLASH_THUMBNAIL");
                              content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
                           } else if (content.getMedia_type().equalsIgnoreCase("PDF")) {
                              content.setThumb_file_id("PDF_THUMBNAIL");
                              content.setThumb_file_name("PDF_THUMBNAIL.PNG");
                           } else {
                              content.setThumb_file_id("ETC_THUMBNAIL");
                              content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                           }
                        } else {
                           cmsThumbFile = new ContentFile();
                           String thumbnailFileId = UUID.randomUUID().toString().toUpperCase();
                           Map thumbnailMap = fileManager.createThumbnailFile(file, thumbnailFileId, content);
                           if (thumbnailMap != null && thumbnailMap.get("status") != null && thumbnailMap.get("status").equals("error")) {
                              throw new Exception("error create thumbnail");
                           }

                           File thumbnail = null;
                           if (thumbnailMap != null && thumbnailMap.get("file") != null) {
                              thumbnail = (File)thumbnailMap.get("file");
                           }

                           filePath = this.getHash(thumbnail);
                           if (filePath == null) {
                              content.setThumb_file_id("NOIMAGE_THUMBNAIL");
                              content.setThumb_file_name("NOIMAGE_THUMBNAIL.PNG");
                           } else {
                              if (cmsDao.isExistFileByHash(fileName + ".png", thumbnail.length(), filePath)) {
                                 thumbnailFileId = cmsDao.getFileIDByHash(fileName + ".png", thumbnail.length(), filePath);
                              }

                              content.setThumb_file_id(thumbnailFileId);
                              content.setThumb_file_name(fileName + ".png");
                           }

                           cmsThumbFile.setFile_id(thumbnailFileId);
                           cmsThumbFile.setHash_code(filePath);
                           cmsThumbFile.setFile_type("THUMBNAIL");
                           cmsThumbFile.setFile_name(fileName + ".png");
                           cmsThumbFile.setFile_size(thumbnail.length());
                           cmsThumbFile.setCreator_id(userId);
                           cmsThumbFile.setFile_path(CONTENTS_HOME + File.separator + thumbnailFileId);
                           if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                              content.setResolution((String)thumbnailMap.get("resolution"));
                           }

                           if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                              String play_time = (String)thumbnailMap.get("playTime");
                              if (play_time.length() > 8) {
                                 content.setPlay_time(play_time.substring(0, 8));
                                 content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                              } else {
                                 content.setPlay_time(play_time);
                              }
                           }
                        }

                        if (contentName.length() > 55) {
                           contentName = contentName.substring(0, 55);
                        }

                        content.setContent_name(contentName);
                        content.setMain_file_id(fileID);
                        content.setMain_file_Extension(fext.toUpperCase());
                        if (contentsApprovalEnable) {
                           if (content.getMain_file_Extension().equalsIgnoreCase("LFT")) {
                              content.setApproval_status("APPROVED");
                           } else {
                              AbilityUtils abilityUtils = new AbilityUtils();
                              if (abilityUtils.isContentApprovalAuthority(userId)) {
                                 content.setApproval_status("APPROVED");
                              } else {
                                 content.setApproval_status("UNAPPROVED");
                              }
                           }
                        } else {
                           content.setApproval_status("APPROVED");
                        }

                        List fileListToSave = new ArrayList();
                        fileListToSave.add(cmsContentFile);
                        if (cmsThumbFile != null) {
                           fileListToSave.add(cmsThumbFile);
                        }

                        content.setArr_file_list(fileListToSave);
                        cmsDao.addContent(content);
                        if (!bExistFile) {
                           try {
                              File fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
                              if (!fileCmsHome.exists()) {
                                 boolean fSuccess = fileCmsHome.mkdir();
                                 if (!fSuccess) {
                                    this.logger.error("mkdir Fail");
                                 }
                              }

                              String storePath = StrUtils.nvl(request.getHeader("STORE_PATH"));
                              if (StrUtils.nvl(storePath).equals(".\\")) {
                                 storePath = "";
                              }

                              filePath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id() + StrUtils.nvl(storePath);
                              File fileCmsFile = SecurityUtils.getSafeFile(filePath);
                              if (!fileCmsFile.exists()) {
                                 boolean fSuccess = fileCmsFile.mkdir();
                                 if (!fSuccess) {
                                    this.logger.error("mkdir Fail");
                                 }
                              }

                              String image_url = CONTENTS_HOME + File.separator + content.getThumb_file_id() + File.separator + content.getThumb_file_name();
                              String thumb_url = THUMBNAIL_HOME + File.separator + content.getThumb_file_id() + File.separator + content.getThumb_file_name();
                              File checkFile = SecurityUtils.getSafeFile(thumb_url);
                              if (!checkFile.exists()) {
                                 File thumbFile = SecurityUtils.getSafeFile(image_url);
                                 if (thumbFile.exists()) {
                                    BufferedImage bufferedImage = ImageIO.read(thumbFile);
                                    if (bufferedImage == null) {
                                       throw new NullPointerException();
                                    }

                                    int orgWidth = bufferedImage.getWidth();
                                    int orgHeight = bufferedImage.getHeight();
                                    int smallWidth = 50;
                                    int smallHeight = 38;
                                    int mediumWidth = 165;
                                    int mediumHeight = 109;
                                    if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                                       mediumHeight = orgHeight * mediumWidth / orgWidth;
                                       if (mediumHeight % 2 != 0) {
                                          ++mediumHeight;
                                       }
                                    } else {
                                       mediumWidth = orgWidth * mediumHeight / orgHeight;
                                       if (mediumWidth % 2 != 0) {
                                          ++mediumWidth;
                                       }
                                    }

                                    if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                                       smallHeight = orgHeight * smallWidth / orgWidth;
                                       if (smallHeight % 2 != 0) {
                                          ++smallHeight;
                                       }
                                    } else {
                                       smallWidth = orgWidth * smallHeight / orgHeight;
                                       if (smallWidth % 2 != 0) {
                                          ++smallWidth;
                                       }
                                    }

                                    if (mediumWidth < 1) {
                                       mediumWidth = 1;
                                    }

                                    if (mediumHeight < 1) {
                                       mediumHeight = 1;
                                    }

                                    if (smallWidth < 1) {
                                       smallWidth = 1;
                                    }

                                    if (smallHeight < 1) {
                                       smallHeight = 1;
                                    }

                                    File thumb_File = SecurityUtils.getSafeFile(thumb_url);
                                    File thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                                    File thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                                    Map hdThumbnailMap = fileManager.createThumbnailFile(file, THUMBNAIL_HOME, content.getThumb_file_id(), content, 1280, 720, "_HD.PNG");
                                    if (hdThumbnailMap != null && hdThumbnailMap.get("status") != null && hdThumbnailMap.get("status").equals("error")) {
                                       this.logger.error("HD Thumbnail create error.");
                                    }
                                 }
                              }
                           } catch (Exception var68) {
                              throw new Exception("error create thumbnail!");
                           }
                        }

                        cmsDao.setActiveVersion(contentId, true);
                        cmsDao.setContentInfo(content.getContent_id(), content.getContent_name(), content.getContent_meta_data(), content.getShare_flag());
                        cmsDao.setContentGroup(content.getContent_id(), content.getGroup_id());
                        responsebody.setItems(content);
                        System.out.println("stop! contentName : " + contentName);
                     }
                  }
               }
            } catch (Exception var75) {
               this.logger.error(var75.getMessage().toString());
               responsebody.setStatus("Fail");
               responsebody.setErrorMessage(var75.getMessage());
               return responsebody;
            }

            responsebody.setStatus("Success");
            return responsebody;
         }
      } catch (Exception var76) {
         this.logger.error(var76.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var76.getMessage());
         return responsebody;
      }
   }

   public String getHash(File file) throws Exception {
      FileInputStream fileIS = null;

      String var31;
      try {
         StringBuffer hash = new StringBuffer("");
         int index = 0;
         ByteBuffer buf = ByteBuffer.allocate(1024);
         FileChannel fileChannel;
         if (!file.exists()) {
            System.out.println("null");
            fileChannel = null;
            return fileChannel;
         }

         fileIS = new FileInputStream(file);
         fileChannel = fileIS.getChannel();
         MessageDigest messageDigest = MessageDigest.getInstance("SHA1");

         int nread;
         for(long fileOffsetLong = 0L; (nread = fileChannel.read(buf, fileOffsetLong)) != -1; fileOffsetLong += (long)nread) {
            buf.flip();
            messageDigest.update(buf);
            buf.clear();
         }

         byte[] digest = messageDigest.digest();
         StringBuffer tmpSB = new StringBuffer();
         byte[] var13 = digest;
         int var14 = digest.length;

         for(int var15 = 0; var15 < var14; ++var15) {
            byte b = var13[var15];
            StringBuffer str = new StringBuffer(Integer.toHexString(b & 255));
            if (str.length() == 1) {
               str = (new StringBuffer("0")).append(str);
            }

            if (index > 7 && index < 16) {
               tmpSB = tmpSB.append(str);
            } else {
               tmpSB = str.append(tmpSB);
            }

            if (index == 3 || index == 5 || index == 7 || index == 9 || index == 15) {
               hash.append(tmpSB).append("-");
               tmpSB = new StringBuffer();
            }

            ++index;
         }

         hash.append(tmpSB);
         if (!hash.equals("")) {
            var31 = hash.toString().toUpperCase();
            return var31;
         }

         var31 = null;
      } catch (IOException var28) {
         this.logger.error("", var28);
         throw new Exception();
      } catch (NoSuchAlgorithmException var29) {
         throw new Exception();
      } finally {
         try {
            if (fileIS != null) {
               fileIS.close();
            }
         } catch (IOException var27) {
            this.logger.error("", var27);
         }

      }

      return var31;
   }

   public void setRefPlaylistList(String contentId, List refPlaylistList, String productType) {
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List pList = null;

      try {
         if (CommonUtils.isUsable(productType, new String[]{"PREMIUM"})) {
            pList = cInfo.getPlaylistListUsingContent(contentId);
         }

         if (pList != null && pList.size() != 0) {
            Map map = null;
            List playlists = new ArrayList();
            String playlist_name = "";
            String playlist_id = "";

            for(int j = 0; j < pList.size(); ++j) {
               ContentRefPlaylist refPlaylist = new ContentRefPlaylist();
               map = (Map)pList.get(j);
               playlist_id = (String)map.get("playlist_id");
               playlist_name = (String)map.get("playlist_name");
               String version = this.getPlaylistVersionStr(playlist_id, contentId);
               refPlaylist.setPlaylistId(playlist_id);
               refPlaylist.setPlaylistName(playlist_name + "(" + version + ")");
               playlists.add(refPlaylist);
            }

            refPlaylistList.add(playlists);
         }
      } catch (SQLException var13) {
         this.logger.error("", var13);
      }

   }

   public void setRefScheduleList(String contentId, List refScheduleList, String productType) {
      ScheduleInterface sInfo = DAOFactory.getScheduleInfoImpl(productType);

      try {
         List programList = sInfo.getProgramByContentId(contentId);
         if (programList != null && programList.size() != 0) {
            String programName = "";
            Map map = null;
            List refSchedules = new ArrayList();
            String scheduleId = "";
            String scheduleName = "";

            for(int j = 0; j < programList.size(); ++j) {
               ContentRefSchedule schedule = new ContentRefSchedule();
               map = (Map)programList.get(j);
               scheduleId = (String)map.get("program_id");
               scheduleName = (String)map.get("program_name");
               schedule.setScheduleId(scheduleId);
               schedule.setScheduleName(scheduleName);
               refSchedules.add(schedule);
            }

            refScheduleList.add(refSchedules);
         }
      } catch (SQLException var13) {
         this.logger.error("", var13);
      }

   }

   public void setRefEventList(String contentId, List refEventList) {
      EventInfoDao eventInfoDao = new EventInfoDao();

      try {
         List eventList = eventInfoDao.getEventIdListByContentId(contentId);
         if (eventList != null && eventList.size() != 0) {
            String eventName = "";
            String eventId = "";
            Map map = null;
            List refEvents = new ArrayList();

            for(int j = 0; j < eventList.size(); ++j) {
               ContentRefEvent refEvent = new ContentRefEvent();
               eventId = (String)((Map)eventList.get(j)).get("EVENT_ID");
               eventName = eventInfoDao.getEvent(eventId).getEvent_name();
               refEvent.setEventId(eventId);
               refEvent.setEventName(eventName);
               refEvents.add(refEvent);
            }

            refEventList.add(refEvents);
         }
      } catch (SQLException var11) {
         this.logger.error("", var11);
      }

   }

   public String getPlaylistVersionStr(String playlistId, String contentId) throws SQLException {
      String result = "";
      ContentInfo cInfo = ContentInfoImpl.getInstance();
      List pList = null;
      pList = cInfo.getPlaylistVersionListUsingContent(playlistId, contentId);
      Map map = null;
      if (pList != null) {
         for(int j = 0; j < pList.size(); ++j) {
            map = (Map)pList.get(j);
            if (j > 0) {
               result = result + ",";
            }

            result = result + map.get("version_id").toString();
         }
      }

      return result;
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody getContentFileInfo(String fileId) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         ContentInfoImpl cInfo = ContentInfoImpl.getInstance();

         try {
            ContentFile contentFile = cInfo.getContentFileInfoByFileId(fileId);
            List contentIdList = cInfo.getContentIdListByContentFileId(fileId);
            ContentFileMapResource resource = new ContentFileMapResource();
            resource.setContentIds(contentIdList);
            resource.setContentFile(contentFile);
            responsebody.setItems(resource);
         } catch (Exception var7) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(ExceptionCode.RES906[2]);
            return responsebody;
         }

         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var8) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var8.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Write Authority', 'Content Manage Authority')")
   public ResponseBody renameContent(String contentId, String newContentName) throws SQLException {
      ResponseBody responsebody = new ResponseBody();
      new ContentLog();
      LogInfo logInfo = LogInfoImpl.getInstance();
      String userId = SecurityUtils.getLoginUserId();
      User loginUser = SecurityUtils.getLoginUser();

      try {
         ContentInfo cInfo = ContentInfoImpl.getInstance();
         Content content = cInfo.getContentActiveVerInfo(contentId);
         AbilityUtils ability = new AbilityUtils();
         if (!loginUser.getRole_name().equals("Server Administrator")) {
            if (!ability.checkAuthority("Content Manage")) {
               if (!content.getCreator_id().equals(userId)) {
                  throw new AccessDeniedException("Access Denied. This content is not yours.");
               }
            } else {
               UserInfo userInfo = UserInfoImpl.getInstance();
               Long contentOrgId = userInfo.getRootGroupIdByUserId(cInfo.getGroupInfo(content.getGroup_id()).getCreator_id());
               if (!loginUser.getRoot_group_id().equals(contentOrgId)) {
                  throw new AccessDeniedException("Access Denied. This content is not your organization.");
               }
            }
         }

         cInfo.setContentInfo(contentId, newContentName, content.getContent_meta_data(), content.getShare_flag());
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var14) {
         this.logger.error(var14.getMessage().toString());
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var14.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public ResponseBody getTagMapping(String[] contentIds) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         ContentInfo cInfo = ContentInfoImpl.getInstance();
         TagInfoImpl tagInfo = TagInfoImpl.getInstance();

         try {
            ArrayList result = new ArrayList();
            if (contentIds != null && contentIds.length > 0) {
               String[] var6 = contentIds;
               int var7 = contentIds.length;

               for(int var8 = 0; var8 < var7; ++var8) {
                  String contentId = var6[var8];
                  ContentTagMappingResource data = new ContentTagMappingResource();
                  List tagList = tagInfo.getContentTagList(contentId);
                  List tagMapList = new ArrayList();
                  HashMap tagMap;
                  if (tagList != null && tagList.size() > 0) {
                     for(Iterator var13 = tagList.iterator(); var13.hasNext(); tagMapList.add(tagMap)) {
                        TagEntity tag = (TagEntity)var13.next();
                        tagMap = new HashMap();
                        tagMap.put("tagValue", tag.getTag_value());
                        tagMap.put("tagId", tag.getTag_id());
                        tagMap.put("tagName", tag.getTag_name());
                        tagMap.put("tagType", tag.getTag_type());
                        int cnt = tagInfo.getCntTagCondition(Long.valueOf((long)tag.getTag_id()));
                        if (cnt > 0) {
                           List tagConditions = tagInfo.getContentTagConditionList(contentId, Long.valueOf((long)tag.getTag_id()));
                           if (tagConditions != null && tagConditions.size() > 0) {
                              List tagConditionList = new ArrayList();
                              Iterator var19 = tagConditions.iterator();

                              while(var19.hasNext()) {
                                 Map condition = (Map)var19.next();
                                 Map conditionMap = new HashMap();
                                 conditionMap.put("tagConditionId", condition.get("tag_condition_id"));
                                 conditionMap.put("tagCondition", condition.get("tag_condition"));
                                 tagConditionList.add(conditionMap);
                              }

                              tagMap.put("conditionList", tagConditionList);
                           }
                        }
                     }
                  }

                  data.setContentId(contentId);
                  data.setTag(tagMapList);
                  result.add(data);
               }
            }

            responsebody.setItems(result);
         } catch (Exception var22) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(ExceptionCode.RES906[2]);
            return responsebody;
         }

         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var23) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var23.getMessage());
         return responsebody;
      }
   }

   public ResponseBody assignTags(String contentId, List tags) throws SQLException {
      ResponseBody responsebody = new ResponseBody();

      try {
         TagInfo tagInfo = TagInfoImpl.getInstance();
         ContentInfo contentInfo = ContentInfoImpl.getInstance();
         String[] tagList = new String[tags.size()];

         for(int i = 0; i < tags.size(); ++i) {
            tagList[i] = String.valueOf(((TagEntity)tags.get(i)).getTag_id());
         }

         List beforeDelTagIds = new ArrayList();
         List tagIds = tagInfo.getTagIdFromContentId(contentId);
         Content content = contentInfo.getContentActiveVerInfo(contentId);
         ContentLog log = new ContentLog();
         log.setContent_id(contentId);
         log.setContent_name(content.getContent_name());
         log.setVersion_id(content.getVersion_id());
         log.setMedia_type(content.getMedia_type());
         log.setThumb_file_id(content.getThumb_file_id());
         log.setThumb_file_name(content.getThumb_file_name());
         log.setCreator_id(content.getCreator_id());
         log.setUser_id(SecurityUtils.getUserContainer().getUser().getUser_id());
         long tagId;
         if (tagIds != null && tagIds.size() > 0) {
            Iterator var11 = tagIds.iterator();

            while(var11.hasNext()) {
               Map map = (Map)var11.next();
               tagId = (Long)map.get("tag_id");
               if (!beforeDelTagIds.contains(tagId)) {
                  beforeDelTagIds.add(tagId);
               }

               if (!Arrays.asList(tagList).contains(String.valueOf(tagId))) {
                  log.setTag_id(Long.valueOf(tagId));
                  log.setEvent_type("1");
                  tagInfo.addLogContentTag(log);
               }
            }
         }

         for(int i = 0; i < tags.size(); ++i) {
            TagEntity tag = (TagEntity)tags.get(i);
            tagId = (long)tag.getTag_id();
            long tagType = tag.getTag_type();
            String[] tagConditionList = tag.getTag_condition();
            if (tagType == 1L) {
               long conditionId = -4L;
               if (tagConditionList != null && tagConditionList.length > 0) {
                  String tagCondition = tagConditionList[0];
                  tagInfo.deleteCondition(Long.valueOf(tagId));
                  conditionId = tagInfo.addCondition(Long.valueOf(tagId), tagCondition);
                  tagInfo.setContentTagMapping(contentId, String.valueOf(tagId), String.valueOf(conditionId));
               }
            } else {
               String[] var18;
               int var19;
               int var20;
               String tagCondition;
               if (tagType == 2L) {
                  tagInfo.deleteBooleanCondition(Long.valueOf(tagId));
                  var18 = tagConditionList;
                  var19 = tagConditionList.length;

                  for(var20 = 0; var20 < var19; ++var20) {
                     tagCondition = var18[var20];
                     if (!this.isAssignedTag(contentId, tagId, tagCondition)) {
                        if (tagCondition != null && tagCondition.equals("true")) {
                           tagInfo.addBoolenCondition(Long.valueOf(tagId), -2L);
                           tagInfo.setContentTagMapping(contentId, String.valueOf(tagId), "-2");
                           break;
                        }

                        tagInfo.addBoolenCondition(Long.valueOf(tagId), -3L);
                        tagInfo.setContentTagMapping(contentId, String.valueOf(tagId), "-3");
                        break;
                     }
                  }
               } else if (tagConditionList != null && tagConditionList.length > 0) {
                  var18 = tagConditionList;
                  var19 = tagConditionList.length;

                  for(var20 = 0; var20 < var19; ++var20) {
                     tagCondition = var18[var20];
                     if (!this.isAssignedTag(contentId, tagId, tagCondition) && tagCondition != null && !tagCondition.equals("")) {
                        tagInfo.setContentTagMapping(contentId, String.valueOf(tagId), tagCondition);
                     }
                  }
               } else {
                  tagInfo.setContentTagMapping(contentId, String.valueOf(tagId), "-1");
               }
            }

            if (!beforeDelTagIds.contains(tagId)) {
               beforeDelTagIds.add(tagId);
            }
         }

         tagInfo.setPlaylistTrigger(beforeDelTagIds);
         ContentResource resource = this.getContentDetail(contentId);
         responsebody.setItems(resource);
         responsebody.setStatus("Success");
         return responsebody;
      } catch (Exception var22) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var22.getMessage());
         return responsebody;
      }
   }

   @PreAuthorize("hasAnyAuthority('Content Read Authority', 'Content Write Authority', 'Content Manage Authority')")
   public UrlContentSettingResource createUrlContent(UrlContentSettingResource resource) throws Exception {
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, resource.getContentId());
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.USER, resource.getUserId());
      if (!StrUtils.nvl(resource.getGroupId()).equals("")) {
         RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(resource.getGroupId()));
      }

      ContentInfo cInfo = ContentInfoImpl.getInstance();
      UserGroupInfo userGroupInfo = UserGroupInfoImpl.getInstance();
      UrlContentSettingResource newResource = new UrlContentSettingResource();
      String webContentName = resource.getUrlContentName();
      String webContentUrl = resource.getUrlAddress();
      String refreshInterval = resource.getRefreshInterval();
      String contentMetaData = resource.getContentMetaData();
      String contentId = UUID.randomUUID().toString().toUpperCase();
      String lfdFileId = UUID.randomUUID().toString().toUpperCase();
      ContentInfo contentInfo = ContentInfoImpl.getInstance();
      if (!StringUtils.isEmpty(webContentUrl)) {
         webContentUrl = webContentUrl.replaceAll("&amp;", "&");
      }

      Map data = new HashMap();
      data.put("webUrl", webContentUrl);
      data.put("lfdFileId", lfdFileId);
      data.put("contentMetaData", contentMetaData);
      data.put("refreshInterval", refreshInterval);
      String CONTENTS_HOME = null;

      try {
         CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
      } catch (ConfigException var22) {
      }

      File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + lfdFileId, (String)null));
      if (!file.exists()) {
         file.mkdir();
      }

      String deviceType = "";
      float deviceTypeVersion = 0.0F;
      if (ContentUtils.createWebContent(SecurityUtils.getLoginUserId(), deviceType, deviceTypeVersion, CONTENTS_HOME + File.separator + lfdFileId, data, "URL", contentId, webContentName, (String)null) == 1) {
         ArrayList contentNotiDataList = new ArrayList();

         Content content;
         try {
            content = cInfo.getContentActiveVerInfo(contentId);
            NotificationData notiData = new NotificationData();
            notiData.setName(content.getContent_name());
            notiData.setOrgId(content.getOrganization_id());
            notiData.setOrgName(userGroupInfo.getGroupNameByGroupId(content.getOrganization_id()));
            notiData.setUserName(this.getLoginUserId());
            contentNotiDataList.add(notiData);
            if (contentNotiDataList != null && contentNotiDataList.size() > 0) {
               MailUtil.sendContentEventMail(contentNotiDataList, "Add content");
            }
         } catch (Exception var21) {
            this.logger.error(var21);
         }

         content = cInfo.getContentAndFileActiveVerInfo(contentId);
         if (content != null) {
            newResource.setContentId(contentId);
            newResource.setType(content.getMedia_type());
            newResource.setGroupId(String.valueOf(content.getGroup_id()));
            newResource.setUserId(content.getCreator_id());
            newResource.setUrlContentName(content.getContent_name());
            newResource.setUrlAddress(content.getUrl_address());
            newResource.setContentMetaData(content.getContent_meta_data());
            newResource.setRefreshInterval(content.getRefresh_interval());
            if (content.getMedia_type().equalsIgnoreCase("STRM")) {
               List settings = contentInfo.getUrlContentSettingByContentId(contentId);
               if (settings.size() > 0) {
                  Map setting = (Map)settings.get(0);
                  newResource.setUrlAddress((String)setting.get("url"));
               }
            }
         }
      }

      return newResource;
   }

   public List uploadContentFile(MultipartFile multipartFile, String groupId, String updatedContentId, String contentType, String webContentName, String startPage, String refreshInterval, String mode, HttpServletRequest request) throws Exception {
      ContentInfo cmsDao = ContentInfoImpl.getInstance();
      UserInfo uInfo = UserInfoImpl.getInstance();
      RestAPIAuthorityCheckUtil.checkAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, updatedContentId);
      if (!StrUtils.nvl(groupId).equals("")) {
         RestAPIAuthorityCheckUtil.checkPostAuthority(RestAPIAuthorityCheckUtil.AuthorityCheckType.CONTENT, Long.parseLong(groupId));
      }

      String userId = SecurityUtils.getLoginUserId();
      if (webContentName != null) {
         webContentName = URLDecoder.decode(webContentName, "UTF-8");
      }

      boolean contentUpdate = false;
      if (updatedContentId != null && !updatedContentId.equals("")) {
         contentUpdate = true;
      }

      long orgCreatorId;
      try {
         orgCreatorId = cmsDao.getRootId(userId);
      } catch (SQLException var119) {
         throw new NotFoundException(var119.getMessage());
      }

      if (groupId == null || groupId.equals("") || groupId.equals("0") || groupId.equals("null")) {
         groupId = String.valueOf(orgCreatorId);
      }

      if (!ServletFileUpload.isMultipartContent(request)) {
         throw new IllegalArgumentException("This is not multipart content.");
      } else {
         String CONTENTS_HOME = null;
         String THUMBNAIL_HOME = null;

         try {
            CONTENTS_HOME = CommonConfig.get("CONTENTS_HOME").replace('/', File.separatorChar) + File.separatorChar + "contents_home";
            THUMBNAIL_HOME = CommonConfig.get("THUMBNAIL_HOME").replace('/', File.separatorChar);
         } catch (ConfigException var118) {
            throw new NotFoundException(var118.getMessage());
         }

         File cmsHome = SecurityUtils.getSafeFile(CONTENTS_HOME);
         if (!cmsHome.exists()) {
            cmsHome.mkdir();
         }

         new ServletFileUpload(new DiskFileItemFactory());
         List resources = new ArrayList();
         ContentCodeInfo codeDao = ContentCodeInfoImpl.getInstance();
         FileManager fileManager = FileManagerImpl.getInstance();
         boolean bExistFile = false;
         boolean bMustAddContent = false;
         List doNotSupportContentList = new ArrayList();
         boolean contentsApprovalEnable = false;

         try {
            long orgId = uInfo.getRootGroupIdByUserId(userId);
            ServerSetupInfo serverSetupDao = ServerSetupInfoImpl.getInstance();
            Map infoMap = serverSetupDao.getServerInfoByOrgId(orgId);
            contentsApprovalEnable = (Boolean)infoMap.get("CONTENTS_APPROVAL_ENABLE");
         } catch (Exception var117) {
            throw new NotFoundException(var117.getMessage());
         }

         try {
            if (multipartFile != null) {
               synchronized(this) {
                  if (!multipartFile.isEmpty()) {
                     String fileName = multipartFile.getOriginalFilename();
                     String fext = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
                     String contentName = fileName.substring(0, fileName.lastIndexOf("."));
                     String mediaType = null;
                     if (contentType == null || !contentType.equals("HTML") && !ContentConstants.getMediaTypeForAuthor().contains(contentType)) {
                        mediaType = StrUtils.nvl(codeDao.getMediaTypeByFileType(fext.toUpperCase()));
                     } else {
                        mediaType = contentType;
                     }

                     if (mediaType == null || mediaType.equals("") || !ContentUtils.supportContentType(fext)) {
                        throw new IllegalArgumentException("Not supported file type.");
                     }

                     long fileSize = multipartFile.getSize();
                     String fileID = UUID.randomUUID().toString().toUpperCase();
                     String contentId = null;
                     if (contentUpdate) {
                        contentId = updatedContentId;
                     } else {
                        contentId = UUID.randomUUID().toString().toUpperCase();
                     }

                     File file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separator + fileID + File.separator + fileName, (String)null));
                     file.getParentFile().mkdir();
                     InputStream is = multipartFile.getInputStream();
                     FileOutputStream os = new FileOutputStream(file.getAbsoluteFile());

                     try {
                        IOUtils.copy(is, os);
                     } catch (Exception var115) {
                        this.logger.error(var115);
                        throw new Exception(var115);
                     } finally {
                        os.close();
                        is.close();
                     }

                     String thumbnailFileId = null;
                     String hashId;
                     String strmUrl;
                     if (contentType != null && !contentType.isEmpty() && contentType.equals("HTML")) {
                        hashId = null;
                        strmUrl = null;
                        if (fext.equalsIgnoreCase("zip")) {
                           Map data = new HashMap();
                           data.put("startPage", startPage);
                           data.put("refreshInterval", refreshInterval);
                           data.put("fileSize", fileSize);
                           if (mode != null && mode.equalsIgnoreCase("update")) {
                              data.put("mode", "UPDATE");
                           }

                           int code = ContentUtils.createWebContent(userId, "", 0.0F, file.getPath(), data, contentType, contentId, webContentName, fileID);
                           if (code != 1) {
                              throw new Exception("Failed to create web contents.");
                           }

                           hashId = "success";
                           file.delete();
                           File parentFile = file.getParentFile();
                           parentFile.delete();
                           this.logger.info("[MagicInfo_UploadHtmlContent] delete temp Folder");
                           ContentFileResource resource = new ContentFileResource();
                           resource.setFileId(fileID);
                           resource.setContentId(contentId);
                           resources.add(resource);
                        }
                     } else {
                        hashId = FileUtils.getHash(file);
                        strmUrl = null;
                        String filePath;
                        File checkFile;
                        File thumb_File;
                        String image_url;
                        if (contentUpdate) {
                           List fileListToSave = new ArrayList();
                           boolean createThumbnail = true;
                           if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                              bExistFile = true;
                           }

                           if (mediaType.equalsIgnoreCase("STRM")) {
                              BufferedReader br = null;

                              try {
                                 char[] c = new char[(int)file.length()];
                                 br = new BufferedReader(new FileReader(file));
                                 br.read(c);
                                 strmUrl = new String(c);
                              } catch (FileNotFoundException var112) {
                                 this.logger.error("", var112);
                                 throw new FileNotFoundException(var112.getMessage());
                              } catch (Exception var113) {
                                 this.logger.error("", var113);
                                 throw new Exception(var113.getMessage());
                              } finally {
                                 try {
                                    br.close();
                                 } catch (IOException var108) {
                                    this.logger.error("", var108);
                                    throw new IOException(var108.getMessage());
                                 }
                              }
                           }

                           if (bExistFile) {
                              file.delete();
                              fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                              file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                           } else {
                              ContentFile cmsContentFile = new ContentFile();
                              if (!fext.equalsIgnoreCase("mpeg") && !fext.equalsIgnoreCase("mpg") && !fext.equalsIgnoreCase("wmv") && !fext.equalsIgnoreCase("avi") && !fext.equalsIgnoreCase("mov") && !fext.equalsIgnoreCase("mp4") && !fext.equalsIgnoreCase("asf")) {
                                 cmsContentFile.setIs_streaming("N");
                              } else {
                                 cmsContentFile.setIs_streaming("Y");
                              }

                              cmsContentFile.setFile_id(fileID);
                              cmsContentFile.setHash_code(hashId);
                              cmsContentFile.setFile_type("MAIN");
                              cmsContentFile.setFile_name(fileName);
                              cmsContentFile.setFile_size(fileSize);
                              cmsContentFile.setCreator_id(userId);
                              cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + fileID);
                              cmsDao.addFile(cmsContentFile);
                              fileListToSave.add(cmsContentFile);
                           }

                           try {
                              long versionId = cmsDao.getMaxContentVersionId(contentId) + 1L;
                              long existGroupId = cmsDao.getGroupId(contentId);
                              Content content = new Content();
                              content.setMain_file_id(fileID);
                              content.setMain_file_Extension(fext.toUpperCase());
                              content.setContent_id(contentId);
                              content.setVersion_id(versionId);
                              content.setCreator_id(userId);
                              content.setMedia_type(mediaType);
                              content.setGroup_id(existGroupId);
                              content.setShare_flag(1);
                              content.setContent_meta_data("");
                              content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
                              content.setTotal_size(fileSize);
                              content.setIs_active("N");
                              content.setOrg_creator_id(String.valueOf(orgCreatorId));
                              filePath = null;
                              if (bExistFile) {
                                 filePath = cmsDao.getThumbIdByMainFileId(fileID);
                                 if (filePath != null) {
                                    ContentFile thumbFile = cmsDao.getFileInfo(filePath);
                                    content.setThumb_file_id(filePath);
                                    content.setThumb_file_name(thumbFile.getFile_name());
                                    createThumbnail = false;
                                 }
                              }

                              if (content.getMedia_type().equalsIgnoreCase("SOUND")) {
                                 content.setThumb_file_id("SOUND_THUMBNAIL");
                                 content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
                                 String[] fileMeta;
                                 if (fext.equals("MP3")) {
                                    try {
                                       fileMeta = fileManager.getFileMeta(file);
                                       if (fileMeta[0] != null) {
                                          image_url = fileMeta[0];
                                          if (image_url.length() > 8) {
                                             content.setPlay_time(image_url.substring(0, 8));
                                             content.setPlay_time_milli(image_url.substring(9, image_url.length()));
                                          } else {
                                             content.setPlay_time(image_url);
                                          }
                                       } else {
                                          image_url = fileManager.getMP3PlayTime(file);
                                          if (image_url.length() > 8) {
                                             content.setPlay_time(image_url.substring(0, 8));
                                             content.setPlay_time_milli(image_url.substring(9, image_url.length()));
                                          } else {
                                             content.setPlay_time(image_url);
                                          }
                                       }
                                    } catch (Exception var111) {
                                       content.setPlay_time("");
                                    }
                                 } else {
                                    fileMeta = fileManager.getFileMeta(file);
                                    if (fileMeta[0] != null) {
                                       image_url = fileMeta[0];
                                       if (image_url.length() > 8) {
                                          content.setPlay_time(image_url.substring(0, 8));
                                          content.setPlay_time_milli(image_url.substring(9, image_url.length()));
                                       } else {
                                          content.setPlay_time(image_url);
                                       }
                                    } else {
                                       content.setPlay_time("");
                                    }
                                 }
                              } else if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                                 if (content.getMedia_type().equalsIgnoreCase("STRM")) {
                                    if (strmUrl != null) {
                                       cmsDao.updateUrlSetting(content.getContent_id(), content.getContent_name(), strmUrl);
                                    }

                                    content.setThumb_file_id("STRM_THUMBNAIL");
                                    content.setThumb_file_name("STRM_THUMBNAIL.PNG");
                                 } else if (content.getMedia_type().equalsIgnoreCase("OFFICE")) {
                                    content.setThumb_file_id("OFFICE_THUMBNAIL");
                                    content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
                                    thumbnailFileId = "OFFICE_THUMBNAIL";
                                 } else if (content.getMedia_type().equalsIgnoreCase("FLASH")) {
                                    content.setThumb_file_id("FLASH_THUMBNAIL");
                                    content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
                                    thumbnailFileId = "FLASH_THUMBNAIL";
                                 } else if (content.getMedia_type().equalsIgnoreCase("PDF")) {
                                    content.setThumb_file_id("PDF_THUMBNAIL");
                                    content.setThumb_file_name("PDF_THUMBNAIL.PNG");
                                    thumbnailFileId = "PDF_THUMBNAIL";
                                 } else {
                                    content.setThumb_file_id("ETC_THUMBNAIL");
                                    content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                                    thumbnailFileId = "ETC_THUMBNAIL";
                                 }
                              } else {
                                 boolean addThumbFile = true;
                                 ContentFile cmsThumbFile = new ContentFile();
                                 thumbnailFileId = null;
                                 if (createThumbnail) {
                                    thumbnailFileId = UUID.randomUUID().toString().toUpperCase();
                                 } else {
                                    thumbnailFileId = filePath;
                                 }

                                 Map thumbnailMap = fileManager.createThumbnailFile(file, thumbnailFileId, content);
                                 if (thumbnailMap != null && thumbnailMap.get("status") != null && thumbnailMap.get("status").equals("error")) {
                                    throw new Exception("error create thumbnail");
                                 }

                                 if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                                    content.setResolution((String)thumbnailMap.get("resolution"));
                                 }

                                 if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                                    String play_time = (String)thumbnailMap.get("playTime");
                                    if (play_time.length() > 8) {
                                       content.setPlay_time(play_time.substring(0, 8));
                                       content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                    } else {
                                       content.setPlay_time(play_time);
                                    }
                                 }

                                 checkFile = null;
                                 if (thumbnailMap != null && thumbnailMap.get("file") != null) {
                                    checkFile = (File)thumbnailMap.get("file");
                                 }

                                 String thmubHash = FileUtils.getHash(checkFile);
                                 if (thmubHash == null) {
                                    content.setThumb_file_id("NOIMAGE_THUMBNAIL");
                                    content.setThumb_file_name("NOIMAGE_THUMBNAIL.PNG");
                                 } else {
                                    if (cmsDao.isExistFileByHash(fileName + ".png", checkFile.length(), thmubHash)) {
                                       thumbnailFileId = cmsDao.getFileIDByHash(fileName + ".png", checkFile.length(), thmubHash);
                                    }

                                    content.setThumb_file_id(thumbnailFileId);
                                    content.setThumb_file_name(fileName + ".png");
                                 }

                                 addThumbFile = true;
                                 File fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
                                 File thumbnailFileChk = new File(SecurityUtils.directoryTraversalChecker(THUMBNAIL_HOME + "/" + thumbnailFileId, (String)null));
                                 if (thumbnailFileChk.exists()) {
                                    addThumbFile = false;
                                 }

                                 if (addThumbFile) {
                                    cmsThumbFile.setFile_id(thumbnailFileId);
                                    cmsThumbFile.setHash_code(thmubHash);
                                    cmsThumbFile.setFile_type("THUMBNAIL");
                                    cmsThumbFile.setFile_name(fileName + ".png");
                                    cmsThumbFile.setFile_size(checkFile.length());
                                    cmsThumbFile.setCreator_id("SYSTEM");
                                    cmsThumbFile.setFile_path(CONTENTS_HOME + File.separator + thumbnailFileId);
                                    cmsDao.addFile(cmsThumbFile);
                                    if (cmsThumbFile != null) {
                                       fileListToSave.add(cmsThumbFile);
                                    }

                                    content.setArr_file_list(fileListToSave);
                                    if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                                       content.setResolution((String)thumbnailMap.get("resolution"));
                                    }

                                    String filePath;
                                    if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                                       filePath = (String)thumbnailMap.get("playTime");
                                       if (filePath.length() > 8) {
                                          content.setPlay_time(filePath.substring(0, 8));
                                          content.setPlay_time_milli(filePath.substring(9, filePath.length()));
                                       } else {
                                          content.setPlay_time(filePath);
                                       }
                                    }

                                    try {
                                       if (!fileCmsHome.exists()) {
                                          boolean fSuccess = fileCmsHome.mkdir();
                                          if (!fSuccess) {
                                             this.logger.error("mkdir Fail");
                                          }
                                       }

                                       filePath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id();
                                       File fileCmsFile = SecurityUtils.getSafeFile(filePath);
                                       if (!fileCmsFile.exists()) {
                                          boolean fSuccess = fileCmsFile.mkdir();
                                          if (!fSuccess) {
                                             this.logger.error("mkdir Fail");
                                          }
                                       }

                                       String image_url = CONTENTS_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                                       String thumb_url = THUMBNAIL_HOME + "/" + cmsThumbFile.getFile_id() + "/" + cmsThumbFile.getFile_name();
                                       File checkFile = SecurityUtils.getSafeFile(thumb_url);
                                       if (!checkFile.exists()) {
                                          thumb_File = SecurityUtils.getSafeFile(image_url);
                                          if (thumb_File.exists()) {
                                             BufferedImage bufferedImage = ImageIO.read(thumb_File);
                                             if (bufferedImage == null) {
                                                this.logger.error("[ContentFileUploadServlet] null thumbnail image : " + thumb_File.getPath());
                                                throw new NullPointerException();
                                             }

                                             int orgWidth = bufferedImage.getWidth();
                                             int orgHeight = bufferedImage.getHeight();
                                             int smallWidth = 50;
                                             int smallHeight = 38;
                                             int mediumWidth = 165;
                                             int mediumHeight = 109;
                                             if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                                                mediumHeight = orgHeight * mediumWidth / orgWidth;
                                                if (mediumHeight % 2 != 0) {
                                                   ++mediumHeight;
                                                }
                                             } else {
                                                mediumWidth = orgWidth * mediumHeight / orgHeight;
                                                if (mediumWidth % 2 != 0) {
                                                   ++mediumWidth;
                                                }
                                             }

                                             if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                                                smallHeight = orgHeight * smallWidth / orgWidth;
                                                if (smallHeight % 2 != 0) {
                                                   ++smallHeight;
                                                }
                                             } else {
                                                smallWidth = orgWidth * smallHeight / orgHeight;
                                                if (smallWidth % 2 != 0) {
                                                   ++smallWidth;
                                                }
                                             }

                                             if (mediumWidth < 1) {
                                                mediumWidth = 1;
                                             }

                                             if (mediumHeight < 1) {
                                                mediumHeight = 1;
                                             }

                                             if (smallWidth < 1) {
                                                smallWidth = 1;
                                             }

                                             if (smallHeight < 1) {
                                                smallHeight = 1;
                                             }

                                             File thumb_File = SecurityUtils.getSafeFile(thumb_url);
                                             File thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                                             File thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                                             ImageIO.write(fileManager.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                                             ImageIO.write(fileManager.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                                             ImageIO.write(fileManager.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                                             Map hdThumbnailMap = fileManager.createThumbnailFile(file, THUMBNAIL_HOME, thumbnailFileId, content, 1280, 720, "_HD.PNG");
                                             if (hdThumbnailMap != null && hdThumbnailMap.get("status") != null && hdThumbnailMap.get("status").equals("error")) {
                                                this.logger.error("HD Thumbnail create error.");
                                             }
                                          }
                                       }
                                    } catch (Exception var110) {
                                       this.logger.error("[ContentFileUploadServlet] error create thumbnail");
                                       throw new Exception("error create thumbnail!");
                                    }
                                 }
                              }

                              this.logger.info("[MagicInfo_ContentFileUpload] content update! contentId : " + contentId + " new version : " + versionId + " fileId : " + fileID);
                              cmsDao.addContentVersionInfo(content);
                              cmsDao.addMapContentFile(contentId, versionId, fileID);
                              if (thumbnailFileId == null) {
                                 thumbnailFileId = "ETC_THUMBNAIL";
                              }

                              cmsDao.addMapContentFile(contentId, versionId, thumbnailFileId);
                              cmsDao.setActiveVersionForUploader(contentId, versionId, true);
                              ContentFileResource resource = new ContentFileResource();
                              resource.setFileId(fileID);
                              resource.setContentId(contentId);
                              resources.add(resource);
                           } catch (Exception var120) {
                              this.logger.error("[MagicInfo_ContentFileUpload] fail update content error : " + var120.getMessage(), var120);
                           }
                        } else {
                           ContentFile cmsContentFile = new ContentFile();
                           if (!fext.equalsIgnoreCase("mpeg") && !fext.equalsIgnoreCase("mpg") && !fext.equalsIgnoreCase("wmv") && !fext.equalsIgnoreCase("avi") && !fext.equalsIgnoreCase("mov") && !fext.equalsIgnoreCase("mp4") && !fext.equalsIgnoreCase("asf")) {
                              cmsContentFile.setIs_streaming("N");
                           } else {
                              cmsContentFile.setIs_streaming("Y");
                           }

                           if (cmsDao.isExistFileByHash(fileName, fileSize, hashId)) {
                              bExistFile = true;
                           }

                           ContentFile cmsSFIContentFile;
                           if (bExistFile) {
                              cmsSFIContentFile = cmsDao.getMainFileInfo(contentId);
                              if (cmsSFIContentFile == null) {
                                 cmsSFIContentFile = cmsDao.getMainFileInfoOfTmpVer(contentId);
                              }

                              if (cmsSFIContentFile != null) {
                                 if (cmsSFIContentFile.getFile_name().equals(cmsContentFile.getFile_name()) && cmsSFIContentFile.getFile_size().equals(cmsContentFile.getFile_size()) && cmsSFIContentFile.getHash_code().equalsIgnoreCase(cmsContentFile.getHash_code())) {
                                    bMustAddContent = false;
                                 } else {
                                    bMustAddContent = true;
                                 }
                              }
                           } else {
                              bMustAddContent = true;
                           }

                           if (bExistFile) {
                              file.delete();
                              fileID = cmsDao.getFileIDByHash(fileName, fileSize, hashId);
                              file = new File(SecurityUtils.directoryTraversalChecker(CONTENTS_HOME + File.separatorChar + fileID + File.separatorChar + fileName, (String)null));
                           }

                           cmsSFIContentFile = new ContentFile();
                           if (mediaType.equalsIgnoreCase("OFFICE") || mediaType.equalsIgnoreCase("FLASH")) {
                              Map data = new HashMap();
                              data.put("startPage", startPage);
                              data.put("fileSize", fileSize);
                              cmsSFIContentFile = ContentUtils.createSfiFile(data, userId, contentId, hashId, fileID, fileName);
                           }

                           cmsContentFile.setFile_type("MAIN");
                           cmsContentFile.setFile_id(fileID);
                           cmsContentFile.setHash_code(hashId);
                           cmsContentFile.setFile_name(fileName);
                           cmsContentFile.setFile_size(fileSize);
                           cmsContentFile.setCreator_id(userId);
                           cmsContentFile.setFile_path(CONTENTS_HOME + File.separator + fileID);
                           codeDao.getFileTypeListByFileType(fext.toUpperCase(), mediaType);
                           Content content = new Content();
                           content.setVersion_id(1L);
                           content.setContent_id(contentId);
                           content.setGroup_id(Long.valueOf(groupId));
                           content.setShare_flag(1);
                           content.setContent_meta_data("");
                           content.setCreator_id(userId);
                           content.setMedia_type(mediaType);
                           content.setOrganization_id(uInfo.getRootGroupIdByUserId(userId));
                           content.setTotal_size(fileSize);
                           if (!cmsSFIContentFile.getFile_id().equals("")) {
                              content.setSfi_file_id(cmsSFIContentFile.getFile_id());
                           }

                           content.setIs_active("Y");
                           content.setOrg_creator_id(String.valueOf(orgCreatorId));
                           ContentFile cmsThumbFile = null;
                           this.logger.error("[ContentFileUploadServlet] content name : " + content.getContent_name() + " content Media Type : " + content.getMedia_type());
                           if (content.getMedia_type().equalsIgnoreCase("LFD") || content.getMedia_type().equalsIgnoreCase("LFT") || content.getMedia_type().equalsIgnoreCase("VWL") || content.getMedia_type().equalsIgnoreCase("PROM")) {
                              throw new IllegalArgumentException("Not supported file type.");
                           }

                           File fileCmsHome;
                           if (content.getMedia_type().equalsIgnoreCase("SOUND")) {
                              content.setThumb_file_id("SOUND_THUMBNAIL");
                              content.setThumb_file_name("SOUND_THUMBNAIL.PNG");
                              String[] fileMeta;
                              String play_time;
                              if (fext.equals("MP3")) {
                                 try {
                                    fileMeta = fileManager.getFileMeta(file);
                                    if (fileMeta[0] != null) {
                                       play_time = fileMeta[0];
                                       if (play_time.length() > 8) {
                                          content.setPlay_time(play_time.substring(0, 8));
                                          content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                       } else {
                                          content.setPlay_time(play_time);
                                       }
                                    } else {
                                       play_time = fileManager.getMP3PlayTime(file);
                                       if (play_time.length() > 8) {
                                          content.setPlay_time(play_time.substring(0, 8));
                                          content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                       } else {
                                          content.setPlay_time(play_time);
                                       }
                                    }
                                 } catch (Exception var109) {
                                    content.setPlay_time("");
                                 }
                              } else {
                                 fileMeta = fileManager.getFileMeta(file);
                                 if (fileMeta[0] != null) {
                                    play_time = fileMeta[0];
                                    if (play_time.length() > 8) {
                                       content.setPlay_time(play_time.substring(0, 8));
                                       content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                    } else {
                                       content.setPlay_time(play_time);
                                    }
                                 } else {
                                    content.setPlay_time("");
                                 }
                              }
                           } else if (!content.getMedia_type().equalsIgnoreCase("IMAGE") && !content.getMedia_type().equalsIgnoreCase("MOVIE")) {
                              if (content.getMedia_type().equalsIgnoreCase("OFFICE")) {
                                 content.setThumb_file_id("OFFICE_THUMBNAIL");
                                 content.setThumb_file_name("OFFICE_THUMBNAIL.PNG");
                              } else if (content.getMedia_type().equalsIgnoreCase("FLASH")) {
                                 content.setThumb_file_id("FLASH_THUMBNAIL");
                                 content.setThumb_file_name("FLASH_THUMBNAIL.PNG");
                              } else if (content.getMedia_type().equalsIgnoreCase("PDF")) {
                                 content.setThumb_file_id("PDF_THUMBNAIL");
                                 content.setThumb_file_name("PDF_THUMBNAIL.PNG");
                              } else {
                                 content.setThumb_file_id("ETC_THUMBNAIL");
                                 content.setThumb_file_name("ETC_THUMBNAIL.PNG");
                              }
                           } else {
                              cmsThumbFile = new ContentFile();
                              thumbnailFileId = UUID.randomUUID().toString().toUpperCase();
                              Map thumbnailMap = fileManager.createThumbnailFile(file, thumbnailFileId, content);
                              if (thumbnailMap != null && thumbnailMap.get("status") != null && thumbnailMap.get("status").equals("error")) {
                                 throw new Exception("error create thumbnail");
                              }

                              fileCmsHome = null;
                              if (thumbnailMap != null && thumbnailMap.get("file") != null) {
                                 fileCmsHome = (File)thumbnailMap.get("file");
                              }

                              filePath = FileUtils.getHash(fileCmsHome);
                              if (filePath == null) {
                                 content.setThumb_file_id("NOIMAGE_THUMBNAIL");
                                 content.setThumb_file_name("NOIMAGE_THUMBNAIL.PNG");
                              } else {
                                 if (cmsDao.isExistFileByHash(fileName + ".png", fileCmsHome.length(), filePath)) {
                                    thumbnailFileId = cmsDao.getFileIDByHash(fileName + ".png", fileCmsHome.length(), filePath);
                                 }

                                 content.setThumb_file_id(thumbnailFileId);
                                 content.setThumb_file_name(fileName + ".png");
                              }

                              cmsThumbFile.setFile_id(thumbnailFileId);
                              cmsThumbFile.setHash_code(filePath);
                              cmsThumbFile.setFile_type("THUMBNAIL");
                              cmsThumbFile.setFile_name(fileName + ".png");
                              cmsThumbFile.setFile_size(fileCmsHome.length());
                              cmsThumbFile.setCreator_id(userId);
                              cmsThumbFile.setFile_path(CONTENTS_HOME + File.separator + thumbnailFileId);
                              if (thumbnailMap != null && thumbnailMap.get("resolution") != null) {
                                 content.setResolution((String)thumbnailMap.get("resolution"));
                              }

                              if (thumbnailMap != null && thumbnailMap.get("playTime") != null) {
                                 String play_time = (String)thumbnailMap.get("playTime");
                                 if (play_time.length() > 8) {
                                    content.setPlay_time(play_time.substring(0, 8));
                                    content.setPlay_time_milli(play_time.substring(9, play_time.length()));
                                 } else {
                                    content.setPlay_time(play_time);
                                 }
                              }
                           }

                           if (contentName.length() > 100) {
                              contentName = contentName.substring(0, 100);
                           }

                           content.setContent_name(contentName);
                           content.setMain_file_id(fileID);
                           content.setMain_file_Extension(fext.toUpperCase());
                           if (contentsApprovalEnable) {
                              if (content.getMain_file_Extension().equalsIgnoreCase("LFT")) {
                                 content.setApproval_status("APPROVED");
                              } else {
                                 AbilityUtils abilityUtils = new AbilityUtils();
                                 if (abilityUtils.isContentApprovalAuthority(userId)) {
                                    content.setApproval_status("APPROVED");
                                 } else {
                                    content.setApproval_status("UNAPPROVED");
                                 }
                              }
                           } else {
                              content.setApproval_status("APPROVED");
                           }

                           List fileListToSave = new ArrayList();
                           fileListToSave.add(cmsContentFile);
                           if (cmsThumbFile != null) {
                              fileListToSave.add(cmsThumbFile);
                           }

                           if (mediaType.equalsIgnoreCase("OFFICE") || mediaType.equalsIgnoreCase("FLASH")) {
                              fileListToSave.add(cmsSFIContentFile);
                           }

                           content.setArr_file_list(fileListToSave);
                           cmsDao.addContent(content);

                           try {
                              fileCmsHome = SecurityUtils.getSafeFile(THUMBNAIL_HOME);
                              if (!fileCmsHome.exists()) {
                                 boolean fSuccess = fileCmsHome.mkdir();
                                 if (!fSuccess) {
                                    this.logger.error("mkdir Fail");
                                 }
                              }

                              filePath = THUMBNAIL_HOME + File.separator + content.getThumb_file_id();
                              File fileCmsFile = SecurityUtils.getSafeFile(filePath);
                              if (!fileCmsFile.exists()) {
                                 boolean fSuccess = fileCmsFile.mkdir();
                                 if (!fSuccess) {
                                    this.logger.error("mkdir Fail");
                                 }
                              }

                              image_url = CONTENTS_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                              String thumb_url = THUMBNAIL_HOME + "/" + content.getThumb_file_id() + "/" + content.getThumb_file_name();
                              checkFile = SecurityUtils.getSafeFile(thumb_url);
                              if (!checkFile.exists()) {
                                 File thumbFile = SecurityUtils.getSafeFile(image_url);
                                 if (thumbFile.exists()) {
                                    BufferedImage bufferedImage = ImageIO.read(thumbFile);
                                    if (bufferedImage == null) {
                                       this.logger.error("[ContentFileUploadServlet] null thumbnail image : " + thumbFile.getPath());
                                       throw new NullPointerException();
                                    }

                                    int orgWidth = bufferedImage.getWidth();
                                    int orgHeight = bufferedImage.getHeight();
                                    int smallWidth = 50;
                                    int smallHeight = 38;
                                    int mediumWidth = 165;
                                    int mediumHeight = 109;
                                    if ((long)(orgWidth / mediumWidth) > (long)(orgHeight / mediumHeight)) {
                                       mediumHeight = orgHeight * mediumWidth / orgWidth;
                                       if (mediumHeight % 2 != 0) {
                                          ++mediumHeight;
                                       }
                                    } else {
                                       mediumWidth = orgWidth * mediumHeight / orgHeight;
                                       if (mediumWidth % 2 != 0) {
                                          ++mediumWidth;
                                       }
                                    }

                                    if ((long)(orgWidth / smallWidth) > (long)(orgHeight / smallHeight)) {
                                       smallHeight = orgHeight * smallWidth / orgWidth;
                                       if (smallHeight % 2 != 0) {
                                          ++smallHeight;
                                       }
                                    } else {
                                       smallWidth = orgWidth * smallHeight / orgHeight;
                                       if (smallWidth % 2 != 0) {
                                          ++smallWidth;
                                       }
                                    }

                                    if (mediumWidth < 1) {
                                       mediumWidth = 1;
                                    }

                                    if (mediumHeight < 1) {
                                       mediumHeight = 1;
                                    }

                                    if (smallWidth < 1) {
                                       smallWidth = 1;
                                    }

                                    if (smallHeight < 1) {
                                       smallHeight = 1;
                                    }

                                    thumb_File = SecurityUtils.getSafeFile(thumb_url);
                                    File thumb_smallFile = SecurityUtils.getSafeFile(thumb_url + "_SMALL.PNG");
                                    File thumb_mediumFile = SecurityUtils.getSafeFile(thumb_url + "_MEDIUM.PNG");
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, orgWidth, orgHeight), "PNG", thumb_File);
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, smallWidth, smallHeight), "PNG", thumb_smallFile);
                                    ImageIO.write(fileManager.createResizedCopy(bufferedImage, mediumWidth, mediumHeight), "PNG", thumb_mediumFile);
                                    Map hdThumbnailMap = fileManager.createThumbnailFile(file, THUMBNAIL_HOME, thumbnailFileId, content, 1280, 720, "_HD.PNG");
                                    if (hdThumbnailMap != null && hdThumbnailMap.get("status") != null && hdThumbnailMap.get("status").equals("error")) {
                                       this.logger.error("HD Thumbnail create error.");
                                    }
                                 }
                              }
                           } catch (Exception var121) {
                              this.logger.error("[ContentFileUploadServlet] error create thumbnail");
                              throw new Exception("error create thumbnail!");
                           }

                           cmsDao.setActiveVersion(contentId, true);
                           cmsDao.setContentInfo(content.getContent_id(), content.getContent_name(), content.getContent_meta_data(), content.getShare_flag());
                           cmsDao.setContentGroup(content.getContent_id(), content.getGroup_id());
                           ContentFileResource resource = new ContentFileResource();
                           resource.setFileId(fileID);
                           resource.setContentId(contentId);
                           resources.add(resource);
                        }
                     }
                  }
               }
            }

            if (doNotSupportContentList != null && doNotSupportContentList.size() > 0) {
               throw new IllegalArgumentException("Not supported file type.");
            } else {
               return resources;
            }
         } catch (Exception var123) {
            this.logger.error("[MagicInfo_ContentUpload][" + userId + "] fail file upload! message : " + var123.getMessage(), var123);
            throw new IllegalArgumentException(var123.getMessage());
         }
      }
   }

   public String getLoginUserId() {
      return SecurityUtils.getUserContainer() == null ? "" : SecurityUtils.getUserContainer().getUser().getUser_id();
   }

   private boolean isAssignedTag(String contentId, long newTagId, String newTagConditionId) throws SQLException {
      TagInfo tagInfo = TagInfoImpl.getInstance();
      List assignedTagIds = tagInfo.getTagIdFromContentId(contentId);
      if (assignedTagIds != null && assignedTagIds.size() > 0) {
         Iterator var7 = assignedTagIds.iterator();

         while(true) {
            long existingTagId;
            do {
               if (!var7.hasNext()) {
                  return false;
               }

               Map tagIdMap = (Map)var7.next();
               existingTagId = (Long)tagIdMap.get("tag_id");
            } while(newTagId != existingTagId);

            List tagConditionIds = tagInfo.getContentTagConditionList(contentId, existingTagId);
            Iterator var12 = tagConditionIds.iterator();

            while(var12.hasNext()) {
               Map tagConditionIdMap = (Map)var12.next();
               long existingTagConditionId = (Long)tagConditionIdMap.get("tag_condition_id");
               if (String.valueOf(existingTagConditionId).equals(newTagConditionId)) {
                  return true;
               }
            }
         }
      } else {
         return false;
      }
   }

   private ContentResource getContentDetail(String contentId) {
      try {
         ContentInfo cInfo = ContentInfoImpl.getInstance();
         Content content = null;
         ContentResource resource = new ContentResource();
         content = cInfo.getContentAndFileActiveVerInfo(contentId);
         resource.setContentId(contentId);
         new LinkedHashMap();
         if (content.getContent_name() != null) {
            resource.setContentName(content.getContent_name());
         }

         if (content.getMedia_type() != null) {
            resource.setMediaType(content.getMedia_type());
         }

         if (content.getTotal_size() != null) {
            resource.setTotalSize(content.getTotal_size());
         }

         if (content.getResolution() != null) {
            resource.setResolution(content.getResolution());
         }

         if (content.getLast_modified_date() != null) {
            resource.setLastModifiedDate(TimeUtil.getGMTTime(content.getLast_modified_date()));
         }

         if (content.getVersion_id() != null) {
            resource.setVersionId(content.getVersion_id());
         }

         String[] deviceType;
         if (content.getPlay_time() != null) {
            deviceType = content.getPlay_time().split(":");
            if (deviceType.length == 1) {
               resource.setPlayTime(0L);
            } else {
               resource.setPlayTime(Long.valueOf(deviceType[1]) * 60L + Long.valueOf(deviceType[2]));
            }
         }

         if (content.getCreator_id() != null) {
            resource.setCreatorId(content.getCreator_id());
         }

         if (content.getGroup_name() != null) {
            resource.setGroupName(content.getGroup_name());
         }

         resource.setThumbFilePath("/servlet/ContentThumbnail?thumb_id=" + content.getThumb_file_id() + "&thumb_filename=" + URLEncoder.encode(content.getThumb_file_name(), "UTF-8") + "_MEDIUM.PNG");
         resource.setShareFlag(content.getShare_flag());
         List contentFile;
         if (content.getMedia_type() != null && content.getMedia_type().equalsIgnoreCase("STRM")) {
            contentFile = cInfo.getUrlContentSettingByContentId(contentId);
            if (contentFile != null && contentFile.size() > 0) {
               Map urlInfoValues = (Map)contentFile.get(0);
               resource.setStreamingUrl(urlInfoValues.get("url").toString());
            }
         }

         resource.setApprovalStatus(content.getApproval_status());
         resource.setApprovalOpinion(content.getApproval_opinion());
         if (content.getMedia_type() != null && (content.getMedia_type().equalsIgnoreCase("CIFS") || content.getMedia_type().equalsIgnoreCase("FTP"))) {
            contentFile = cInfo.getActiveFileList(contentId);
            resource.setArrFileList(contentFile);
         }

         deviceType = null;
         float deviceTypeVersion = 1.0F;
         boolean isSupportAPLAYER = false;
         String deviceType;
         if (content.getDevice_type() != null && !content.getDevice_type().equals("")) {
            deviceType = content.getDevice_type();
            deviceTypeVersion = content.getDevice_type_version();
         } else {
            Map typeMap = ContentUtils.getContentDeviceTypeAndVersion(content);
            deviceType = (String)typeMap.get("deviceType");
            deviceTypeVersion = Float.parseFloat(typeMap.get("deviceTypeVersion").toString());
         }

         resource.setDeviceType(deviceType);
         resource.setDeviceTypeVersion(deviceTypeVersion);
         TagInfo tagInfo = TagInfoImpl.getInstance();
         List tagList = tagInfo.getContentTagList(contentId);
         if (tagList != null && tagList.size() > 0) {
            List tagConditionList = new ArrayList();

            HashMap tagMap;
            for(Iterator var12 = tagList.iterator(); var12.hasNext(); tagConditionList.add(tagMap)) {
               TagEntity tag = (TagEntity)var12.next();
               tagMap = new HashMap();
               tagMap.put("tag_value", tag.getTag_value());
               tagMap.put("tag_id", tag.getTag_id());
               tagMap.put("tag_name", tag.getTag_name());
               tagMap.put("tag_type", tag.getTag_type());
               int cnt = tagInfo.getCntTagCondition(Long.valueOf((long)tag.getTag_id()));
               if (cnt > 0) {
                  List tagConditions = tagInfo.getContentTagConditionList(contentId, Long.valueOf((long)tag.getTag_id()));
                  if (tagConditions != null && tagConditions.size() > 0) {
                     tagMap.put("conditionList", tagConditions);
                  }
               }
            }

            resource.setTagList(tagConditionList);
         }

         resource.setIsDeleted(content.getIs_deleted());
         CategoryInfo categoryInfo = CategoryInfoImpl.getInstance();
         List categoryList = categoryInfo.getCategoryWithContentId(contentId);
         if (categoryList != null && categoryList.size() > 0) {
            resource.setCategoryList(categoryList);
         }

         resource.setMainFileId(content.getMain_file_id());
         resource.setMainFileName(content.getMain_file_name());
         String misUrl = CommonConfig.get("webauthor.web_url_public");
         String loadUrl = misUrl + "/servlet/GetFileLoader?paramPathConfName=CONTENTS_HOME&filepath=" + content.getMain_file_id() + "/" + content.getMain_file_name();
         resource.setMainFileUrl(loadUrl);
         return resource;
      } catch (Exception var17) {
         return null;
      }
   }
}
