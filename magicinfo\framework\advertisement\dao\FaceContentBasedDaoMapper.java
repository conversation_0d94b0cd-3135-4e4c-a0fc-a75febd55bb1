package com.samsung.magicinfo.framework.advertisement.dao;

import com.samsung.magicinfo.framework.advertisement.entity.FaceInfoEntity;
import com.samsung.magicinfo.framework.advertisement.entity.FaceKey;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FaceContentBasedDaoMapper {
   FaceInfoEntity getPopMonthInfo(@Param("device_id") String var1, @Param("faceKey") FaceKey var2, @Param("month") Timestamp var3) throws SQLException;

   FaceInfoEntity getPopYearInfo(@Param("device_id") String var1, @Param("faceKey") FaceKey var2, @Param("year") Timestamp var3) throws SQLException;

   void addPopDayInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("day") Timestamp var3, @Param("dow") int var4) throws SQLException;

   void addPopHourInfoList(@Param("deviceId") String var1, @Param("faceKey") FaceKey var2, @Param("entities") List var3) throws SQLException;

   long checkDuplicationByDB(@Param("date") Timestamp var1, @Param("device_id") String var2) throws SQLException;

   void setPopYearInfo(@Param("view_count") int var1, @Param("duration") int var2, @Param("year") Timestamp var3, @Param("faceKey") FaceKey var4, @Param("device_id") String var5) throws SQLException;

   void setPopMonthInfo(@Param("view_count") int var1, @Param("duration") int var2, @Param("month") Timestamp var3, @Param("faceKey") FaceKey var4, @Param("device_id") String var5) throws SQLException;

   void addPopYearInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("startTime") Timestamp var3) throws SQLException;

   void addPopMonthInfoList(@Param("deviceId") String var1, @Param("map") HashMap var2, @Param("month") Timestamp var3, @Param("quarter") int var4) throws SQLException;
}
