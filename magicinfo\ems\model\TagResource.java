package com.samsung.magicinfo.ems.model;

import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;

public class TagResource {
   @ApiModelProperty(
      example = "1"
   )
   private int startIndex = 1;
   @ApiModelProperty(
      example = "10"
   )
   private int pageSize = 10;
   private ArrayList tagList;

   public TagResource() {
      super();
   }

   public int getStartIndex() {
      return this.startIndex;
   }

   public void setStartIndex(int startIndex) {
      this.startIndex = startIndex;
   }

   public int getPageSize() {
      return this.pageSize;
   }

   public void setPageSize(int pageSize) {
      this.pageSize = pageSize;
   }

   public ArrayList getTagList() {
      return this.tagList;
   }

   public void setTagList(ArrayList tagEntityList) {
      this.tagList = tagEntityList;
   }
}
