package com.samsung.common.utils;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.net.InetAddress;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.StringTokenizer;
import java.util.Vector;
import org.apache.commons.net.ntp.NTPUDPClient;
import org.apache.commons.net.ntp.TimeInfo;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.Period;

public class DateUtils {
   static Logger logger = LoggingManagerV2.getLogger(DateUtils.class);
   public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
   private static String defaultDatePattern = null;
   private static final String DATE_PATTERN = (new SimpleDateFormat()).toLocalizedPattern();

   public DateUtils() {
      super();
   }

   public static String calendar2String(Calendar calendar) {
      int year = calendar.get(1);
      int month = calendar.get(2) + 1;
      int day = calendar.get(5);
      int hour = calendar.get(11);
      int minute = calendar.get(12);
      int second = calendar.get(13);
      return StrUtils.integer2string(year) + StrUtils.integer2string(month) + StrUtils.integer2string(day) + StrUtils.integer2string(hour) + StrUtils.integer2string(minute) + StrUtils.integer2string(second) + "000";
   }

   public static String changeSecToPlayTime(Long sec) {
      String result = "";
      Long oneDay = new Long(86400L);
      Long oneHour = new Long(3600L);
      Long oneMinute = new Long(60L);
      Long days = sec / oneDay;
      if (days >= 1L) {
         sec = sec % oneDay;
      }

      Long hours = sec / oneHour;
      if (hours >= 1L) {
         sec = sec % oneHour;
      }

      Long minutes = sec / oneMinute;
      if (minutes >= 1L) {
         sec = sec % oneMinute;
      }

      if (days >= 1L) {
         result = result + days + ", ";
      }

      if (hours >= 1L) {
         result = result + hours + ":";
      }

      if (minutes >= 1L) {
         result = result + minutes + ":";
      }

      result = result + sec;
      return result;
   }

   public static String date2String(Date date) {
      return date2String(date, DATE_PATTERN);
   }

   public static String date2String(Date date, String format) {
      SimpleDateFormat sdf = new SimpleDateFormat(format);
      return sdf.format(date);
   }

   public static String subtractStringTime(String time1, String time2) throws ParseException {
      SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
      Date date1 = null;
      Date date2 = null;

      try {
         date1 = format.parse(time1);
         date2 = format.parse(time2);
      } catch (ParseException var7) {
         logger.error("Incorrect Time Format" + var7.getMessage());
         throw var7;
      }

      long difference = date1.getTime() - date2.getTime();
      difference /= 1000L;
      return changeSecondToFormatTime((int)difference);
   }

   public static Timestamp dateTime2TimeStamp(Date date) throws ParseException {
      return new Timestamp(date.getTime());
   }

   public static boolean equals(Date date1, Date date2) {
      return date1.getTime() == date2.getTime();
   }

   public static boolean equals(Date date1, String date2) {
      return equals(date1, date2, DATE_PATTERN);
   }

   public static boolean equals(Date date1, String date2, String date2format) {
      Date date = string2Date(date2, date2format);
      return equals(date1, date);
   }

   private static Calendar getCalendar() {
      Calendar calendar = new GregorianCalendar();
      calendar.setTime(new Date());
      return calendar;
   }

   public static String getCurrentDay() {
      return getCurrentTime(DATE_PATTERN);
   }

   public static String getCurrentHour() {
      return getCurrentTime("HH:mm");
   }

   public static String getCurrentTime() {
      return getCurrentTime("yyyy-MM-dd HH:mm");
   }

   public static String getCurrentTime(String format) {
      Date d = new Date();
      SimpleDateFormat sdf = new SimpleDateFormat(format);
      return sdf.format(d);
   }

   public static String[] getDates(String startDay, String endDay) {
      return getDates(startDay, endDay, DATE_PATTERN);
   }

   public static String[] getDates(String startDay, String endDay, String format) {
      Vector v = new Vector();
      v.addElement(startDay);
      Calendar cal = getCalendar();
      cal.setTime(string2Date(startDay, format));
      String nextDay = date2String(cal.getTime(), format);

      while(!nextDay.equals(endDay)) {
         cal.add(5, 1);
         nextDay = date2String(cal.getTime(), format);
         v.addElement(nextDay);
      }

      String[] go = new String[v.size()];
      v.copyInto(go);
      return go;
   }

   public static String getAddDate(int nDates, String compDate, String strFormat) throws Exception {
      if (nDates < 0) {
         nDates = 0;
      }

      Date retDate = new Date();
      SimpleDateFormat sdf = new SimpleDateFormat(strFormat);
      retDate.setTime(sdf.parse(compDate).getTime() + 86400000L * (long)nDates);
      return sdf.format(retDate);
   }

   public static String getDayDiff(String startDate, String endDate, String fmat) throws Exception {
      SimpleDateFormat sdf = new SimpleDateFormat(fmat);
      Date sourceDate1 = sdf.parse(startDate);
      Date sourceDate2 = sdf.parse(endDate);
      long rtnVal = (sourceDate2.getTime() - sourceDate1.getTime()) / 86400000L;
      return Long.toString(rtnVal);
   }

   public static String getDayDistance(String startDate, String endDate) throws Exception {
      long lsDate = Long.parseLong(startDate.substring(8, 10)) * 3600L + Long.parseLong(startDate.substring(10, 12)) * 60L + Long.parseLong(startDate.substring(12, 14));
      long leDate = Long.parseLong(endDate.substring(8, 10)) * 3600L + Long.parseLong(endDate.substring(10, 12)) * 60L + Long.parseLong(endDate.substring(12, 14));
      long difftime = leDate - lsDate;
      long ch = 0L;
      long cm = 0L;
      long cs = 0L;
      String rtnVal = "";
      DecimalFormat df = new DecimalFormat("00");
      ch = difftime / 3600L;
      difftime %= 3600L;
      cm = difftime / 60L;
      difftime %= 60L;
      if (ch <= 23L && (ch != 23L || cm <= 59L)) {
         rtnVal = df.format(ch) + ":" + df.format(cm) + ":" + df.format(difftime);
      } else {
         rtnVal = "23:59:59";
      }

      return rtnVal;
   }

   public static int getDays(Calendar cal1, Calendar cal2) {
      long min = (long)getMinutes(cal1, cal2);
      return (int)(min / 1440L);
   }

   public static int getDays(String date1, String date2) {
      Calendar cal1 = string2Calender(date1);
      Calendar cal2 = string2Calender(date2);
      return getDays(cal1, cal2);
   }

   public static String getEndDate(String startDay, int intervalDays) {
      StringTokenizer st = new StringTokenizer(startDay, "-");
      int year = 0;
      int mon = 0;
      int day = 0;

      for(int i = 0; st.hasMoreTokens(); ++i) {
         if (i == 0) {
            year = Integer.parseInt(st.nextToken());
         }

         String sDay;
         if (i == 1) {
            sDay = st.nextToken();
            if (sDay.startsWith("0")) {
               sDay = sDay.substring(1);
            }

            mon = Integer.parseInt(sDay);
         }

         if (i == 2) {
            sDay = st.nextToken();
            if (sDay.startsWith("0")) {
               sDay = sDay.substring(1);
            }

            day = Integer.parseInt(sDay);
         }
      }

      DateTime start = new DateTime(year, mon, day, 0, 0, 0, 0);
      Period p1 = new Period(1728000000L);
      Period p2 = new Period((long)((intervalDays - 20) * 86400000));
      DateTime end = start.plus(p1);
      end = end.plus(p2);
      year = end.getYear();
      mon = end.getMonthOfYear();
      day = end.getDayOfMonth();
      String xMon = "";
      String xDay = "";
      if (mon < 10) {
         xMon = "0" + (new Integer(mon)).toString();
      } else {
         xMon = (new Integer(mon)).toString();
      }

      if (day < 10) {
         xDay = "0" + (new Integer(day)).toString();
      } else {
         xDay = (new Integer(day)).toString();
      }

      String endDay = (new Integer(year)).toString() + "-" + xMon + "-" + xDay;
      return endDay;
   }

   public static String getEngMonth(String month) {
      String[] monthName = new String[]{"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
      String monthValue = "";
      if (month == null) {
         return "";
      } else {
         if (month.length() > 2) {
            monthValue = month.substring(month.length() - 2);
         } else {
            monthValue = month;
         }

         if (monthValue.substring(0, 1).equals("0") && !monthValue.equals("0")) {
            monthValue = monthValue.substring(1);
         }

         return monthName[Integer.parseInt(monthValue) - 1];
      }
   }

   public static String getEngToKorMonth(int month) {
      String[] monthName = new String[]{"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
      return monthName[month];
   }

   public static int getMinutes(Calendar cal1, Calendar cal2) {
      long utc1 = cal1.getTimeInMillis();
      long utc2 = cal2.getTimeInMillis();
      long result = (utc2 - utc1) / 60000L;
      return (int)result;
   }

   public static int getMinutes(String date1, String date2) {
      Calendar cal1 = string2Calender(date1);
      Calendar cal2 = string2Calender(date2);
      return getMinutes(cal1, cal2);
   }

   public static long getMinutesDistance(String startDate, String endDate) throws ParseException {
      String fmat = "yyyy-MM-dd HH:mm:ss";
      SimpleDateFormat sdf = new SimpleDateFormat(fmat);
      long diffMinutes = 0L;
      Date sDate = sdf.parse(startDate);
      Date eDate = sdf.parse(endDate);
      diffMinutes = (long)(eDate.getMinutes() - sDate.getMinutes());
      return diffMinutes;
   }

   public static String[] getPlaylistDayDistance(String startDate, String endDate, int contentCnt) throws ParseException {
      String[] arrTime = new String[contentCnt];
      String fmat = "yyyyMMddHHmmss";
      SimpleDateFormat sdf = new SimpleDateFormat(fmat);
      double ch = 0.0D;
      double cm = 0.0D;
      double cs = 0.0D;
      String rtnVal = "";
      DecimalFormat df = new DecimalFormat("00");
      Date sDate = sdf.parse(startDate);
      Date eDate = sdf.parse(endDate);
      double difftime = (double)(eDate.getTime() - sDate.getTime());
      double dval = Math.floor(difftime / (double)contentCnt);
      double restval = difftime % (double)contentCnt;
      ch = Math.floor(difftime / 3600000.0D);
      cm = Math.floor((difftime / 3600000.0D - ch) * 60.0D + 0.5D);
      cs = difftime % 60.0D;
      rtnVal = df.format(ch) + ":" + df.format(cm) + ":" + df.format(cs);

      for(int i = 0; i < contentCnt; ++i) {
         if (i + 1 == contentCnt) {
            dval += restval;
         }

         ch = Math.floor(dval / 3600000.0D);
         cm = Math.floor((dval / 3600000.0D - ch) * 60.0D + 0.5D);
         cs = dval % 60.0D;
         rtnVal = df.format(ch) + ":" + df.format(cm) + ":" + df.format(cs);
         arrTime[i] = rtnVal;
      }

      return arrTime;
   }

   public static long getSecondsDistance(String startDate, String endDate) throws ParseException {
      String fmat = "yyyy-MM-dd HH:mm:ss";
      SimpleDateFormat sdf = new SimpleDateFormat(fmat);
      long diffMinutes = 0L;
      Date sDate = sdf.parse(startDate);
      Date eDate = sdf.parse(endDate);
      diffMinutes = (long)(eDate.getSeconds() - sDate.getSeconds());
      return diffMinutes;
   }

   public static String getSubtractDate(int nDates) {
      return getSubtractDate(nDates, "yyyyMMddHHmmss");
   }

   public static String getSubtractDate(int nDates, String strFormat) {
      if (nDates < 0) {
         nDates = 0;
      }

      Date retDate = new Date();
      SimpleDateFormat sdf = new SimpleDateFormat(strFormat);
      retDate.setTime((new Date()).getTime() - 86400000L * (long)nDates);
      return sdf.format(retDate);
   }

   public static String getSubtractDate(int nDates, String compDate, String strFormat) throws ParseException {
      if (nDates < 0) {
         nDates = 0;
      }

      Date retDate = new Date();
      SimpleDateFormat sdf = new SimpleDateFormat(strFormat);
      retDate.setTime(sdf.parse(compDate).getTime() - 86400000L * (long)nDates);
      return sdf.format(retDate);
   }

   public static String getSubtractMinute(int nMinutes) throws ParseException {
      if (nMinutes < 0) {
         nMinutes = 0;
      }

      Date retDate = new Date();
      String fmat = "yyyyMMddHHmmss";
      SimpleDateFormat sdf = new SimpleDateFormat(fmat);
      retDate.setTime((new Date()).getTime() - 60000L * (long)nMinutes);
      return sdf.format(retDate);
   }

   public static String getSubtractMinutes(int nMinutes, String compDate, String strFormat) throws ParseException {
      if (nMinutes < 0) {
         nMinutes = 0;
      }

      Date retDate = new Date();
      SimpleDateFormat sdf = new SimpleDateFormat(strFormat);
      retDate.setTime(sdf.parse(compDate).getTime() - 60000L * (long)nMinutes);
      return sdf.format(retDate);
   }

   public static String getThisMonth() {
      return getCurrentTime("yyyy-MM");
   }

   public static String getThisYear() {
      return getCurrentTime("yyyy");
   }

   public static String getYesterday() {
      return getYesterday(DATE_PATTERN);
   }

   public static String getYesterday(String format) {
      Calendar cal = getCalendar();
      cal.roll(5, -1);
      Date date = cal.getTime();
      return date2String(date, format);
   }

   public static boolean greaterThan(Date date1, Date date2) {
      return date1.getTime() > date2.getTime();
   }

   public static boolean greaterThan(Date date1, String date2) {
      return greaterThan(date1, date2, DATE_PATTERN);
   }

   public static boolean greaterThan(Date date1, String date2, String date2format) {
      Date date = string2Date(date2, date2format);
      return greaterThan(date1, date);
   }

   public static Calendar string2Calender(String str) {
      if (str != null && str.length() >= 14) {
         String year = str.substring(0, 4);
         String month = str.substring(4, 6);
         String day = str.substring(6, 8);
         String hour = str.substring(8, 10);
         String minute = str.substring(10, 12);
         String second = str.substring(12, 13);
         return new GregorianCalendar(StrUtils.string2integer(year), StrUtils.string2integer(month) - 1, StrUtils.string2integer(day), StrUtils.string2integer(hour), StrUtils.string2integer(minute), StrUtils.string2integer(second));
      } else {
         return null;
      }
   }

   public static Date string2Date(String str) {
      return string2Date(str, DATE_PATTERN);
   }

   public static Date string2Date(String str, String format) {
      Date d = null;
      SimpleDateFormat sdf = new SimpleDateFormat(format);
      d = sdf.parse(str, new ParsePosition(0));
      return d;
   }

   public static java.sql.Date string2SQLDate(String date) throws Exception {
      return string2SQLDate(date, DATE_PATTERN);
   }

   public static java.sql.Date string2SQLDate(String date, String format) throws Exception {
      DateFormat sdf = new SimpleDateFormat(format);
      Date dateFormat = null;
      dateFormat = sdf.parse(date);
      java.sql.Date sqlDate = new java.sql.Date(dateFormat.getTime());
      return sqlDate;
   }

   public static Timestamp string2Timestamp(String str) {
      return string2Timestamp(str, DATE_PATTERN);
   }

   public static Timestamp string2Timestamp(String str, String format) {
      SimpleDateFormat dateFormat = new SimpleDateFormat(format);
      Date strDate = dateFormat.parse(str, new ParsePosition(0));
      long strTime = strDate.getTime();
      Timestamp timestamp = new Timestamp(strTime);
      return timestamp;
   }

   public static String timestamp2String(Timestamp date) {
      return timestamp2String(date, DATE_PATTERN);
   }

   public static String timestamp2String(Timestamp date, String format) {
      return date == null ? "" : date2String(date, format);
   }

   public static String timestamp2StringDate(Timestamp date) {
      return timestamp2String(date, "yyyy-MM-dd");
   }

   public static String getEndTime(String startTime, int duration) {
      String result = null;
      int startSec = changeFormatTimeToSecond(startTime);
      int secSum = 0;
      int secSum = secSum + startSec;
      secSum += duration;
      result = changeSecondToFormatTime(secSum);
      return result;
   }

   public static int changeFormatTimeToSecond(String formatTime) {
      int resultSec = 0;
      String[] arrTime = formatTime.split(":");
      if (arrTime.length < 3) {
         return resultSec;
      } else {
         int hour = Integer.parseInt(arrTime[0]);
         int min = Integer.parseInt(arrTime[1]);
         int sec = Integer.parseInt(arrTime[2]);
         int resultSec = hour * 60 * 60 + min * 60 + sec;
         return resultSec;
      }
   }

   public static String changeSecondToFormatTime(int sec) {
      String result = null;
      int oneHour = 3600;
      int oneMinute = 60;
      int hours = sec / oneHour;
      if (hours >= 1) {
         sec %= oneHour;
      }

      int minutes = sec / oneMinute;
      if (minutes >= 1) {
         sec %= oneMinute;
      }

      if (hours < 10) {
         result = "0" + hours + ":";
      } else {
         result = hours + ":";
      }

      if (minutes < 10) {
         result = result + "0" + minutes + ":";
      } else {
         result = result + minutes + ":";
      }

      if (sec < 10) {
         result = result + "0" + sec;
      } else {
         result = result + sec;
      }

      return result;
   }

   public static String[] getThisWeek() {
      String[] datesOfThisweek = new String[2];
      Calendar cal = Calendar.getInstance();
      cal.setTime(new Date(System.currentTimeMillis()));
      int todayDOW = cal.get(7) - 1;
      String initDowStr = "0";

      try {
         initDowStr = StrUtils.nvl(CommonConfig.get("calendar.init_dow"));
      } catch (ConfigException var5) {
         logger.error("Cannot get value from config.properties = calendar.init_dow", var5);
      }

      if (initDowStr.equals("")) {
         initDowStr = "0";
      }

      int initDowInt = Integer.parseInt(initDowStr);
      if (todayDOW < initDowInt) {
         initDowInt -= 7;
      }

      datesOfThisweek[1] = (new SimpleDateFormat("yyyy-MM-dd")).format(cal.getTime());
      cal.add(5, -todayDOW + initDowInt);
      datesOfThisweek[0] = (new SimpleDateFormat("yyyy-MM-dd")).format(cal.getTime());
      return datesOfThisweek;
   }

   public static String[] getLastWeek() {
      String[] datesOfLastweek = new String[2];
      Calendar cal = Calendar.getInstance();
      cal.setTime(new Date(System.currentTimeMillis()));
      int todayDOW = cal.get(7) - 1;
      String initDowStr = "0";

      try {
         initDowStr = StrUtils.nvl(CommonConfig.get("calendar.init_dow"));
      } catch (ConfigException var5) {
         logger.error("Cannot get value from config.properties = calendar.init_dow", var5);
      }

      if (initDowStr.equals("")) {
         initDowStr = "0";
      }

      int initDowInt = Integer.parseInt(initDowStr);
      if (todayDOW < initDowInt) {
         initDowInt -= 7;
      }

      cal.add(5, -todayDOW + initDowInt);
      cal.add(5, -1);
      datesOfLastweek[1] = (new SimpleDateFormat("yyyy-MM-dd")).format(cal.getTime());
      cal.add(5, -6);
      datesOfLastweek[0] = (new SimpleDateFormat("yyyy-MM-dd")).format(cal.getTime());
      return datesOfLastweek;
   }

   public static String[] getStartAndEndDate(String period) {
      String[] dates = new String[2];
      Calendar cal = Calendar.getInstance();
      cal.setTime(new Date(System.currentTimeMillis()));
      Date start = new Date();
      Date end = new Date();
      new Date();
      Date current = cal.getTime();
      byte var7 = -1;
      switch(period.hashCode()) {
      case -2018226281:
         if (period.equals("last_month")) {
            var7 = 2;
         }
         break;
      case -1621979774:
         if (period.equals("yesterday")) {
            var7 = 0;
         }
         break;
      case -1349088399:
         if (period.equals("custom")) {
            var7 = 7;
         }
         break;
      case -560241346:
         if (period.equals("this_year")) {
            var7 = 5;
         }
         break;
      case -198384225:
         if (period.equals("this_month")) {
            var7 = 1;
         }
         break;
      case 1224533507:
         if (period.equals("last_quarter")) {
            var7 = 4;
         }
         break;
      case 2013453382:
         if (period.equals("last_year")) {
            var7 = 6;
         }
         break;
      case 2041059851:
         if (period.equals("this_quarter")) {
            var7 = 3;
         }
      }

      switch(var7) {
      case 0:
         cal.roll(5, -1);
         start = cal.getTime();
         end = cal.getTime();
         break;
      case 1:
         cal.set(5, cal.getActualMinimum(5));
         start = cal.getTime();
         cal.set(5, cal.getActualMaximum(5));
         end = cal.getTime();
         break;
      case 2:
         cal.roll(2, -1);
         cal.set(5, 1);
         start = cal.getTime();
         cal.set(5, cal.getActualMaximum(5));
         end = cal.getTime();
         break;
      case 3:
         int thisQuarter = current.getMonth() / 3 + 1;
         cal.set(2, 3 * thisQuarter - 3);
         cal.set(5, cal.getActualMinimum(5));
         start = cal.getTime();
         cal.set(2, 3 * thisQuarter - 1);
         cal.set(5, cal.getActualMaximum(5));
         end = cal.getTime();
         break;
      case 4:
         int lastQuarter = current.getMonth() / 3;
         Calendar startLastQCal = Calendar.getInstance();
         startLastQCal.setTime(new Date(System.currentTimeMillis()));
         startLastQCal.set(2, 3 * lastQuarter - 3);
         startLastQCal.set(5, cal.getActualMinimum(5));
         start = startLastQCal.getTime();
         startLastQCal.set(2, 3 * lastQuarter - 1);
         startLastQCal.set(5, startLastQCal.getActualMaximum(5));
         end = startLastQCal.getTime();
         break;
      case 5:
         cal.set(2, cal.getActualMinimum(2));
         cal.set(5, cal.getActualMinimum(5));
         start = cal.getTime();
         cal.set(2, cal.getActualMaximum(2));
         cal.set(5, cal.getActualMaximum(5));
         end = cal.getTime();
         break;
      case 6:
         cal.roll(1, -1);
         cal.set(2, cal.getActualMinimum(2));
         cal.set(5, cal.getActualMinimum(5));
         start = cal.getTime();
         cal.set(2, cal.getActualMaximum(2));
         cal.set(5, cal.getActualMaximum(5));
         end = cal.getTime();
      case 7:
      }

      dates[0] = (new SimpleDateFormat("yyyy-MM-dd")).format(start);
      dates[1] = (new SimpleDateFormat("yyyy-MM-dd")).format(end);
      System.out.println("start: " + dates[0] + " end: " + dates[1]);
      return dates;
   }

   public static boolean checkFormat(String format, String dateString) {
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
      simpleDateFormat.setLenient(false);

      try {
         simpleDateFormat.parse(dateString);
         return true;
      } catch (Exception var4) {
         return false;
      }
   }

   public static Date getCurrentDateFromNetwork() {
      NTPUDPClient timeClient = new NTPUDPClient();
      Date current = null;

      InetAddress inetAddress;
      TimeInfo timeInfo;
      long returnTime;
      try {
         inetAddress = InetAddress.getByName("pool.ntp.org");
         timeInfo = timeClient.getTime(inetAddress);
         returnTime = timeInfo.getMessage().getTransmitTimeStamp().getTime();
         current = new Date(returnTime);
      } catch (Exception var7) {
         logger.error("Can't get current time from pool.ntp.org");
      }

      if (current != null) {
         return current;
      } else {
         try {
            inetAddress = InetAddress.getByName("time.windows.com");
            timeInfo = timeClient.getTime(inetAddress);
            returnTime = timeInfo.getMessage().getTransmitTimeStamp().getTime();
            current = new Date(returnTime);
         } catch (Exception var6) {
            logger.error("Can't get current time from time.windows.com");
         }

         return current;
      }
   }

   public static String setDate(int day) {
      Date date = new Date();
      Calendar c = Calendar.getInstance();
      c.setTime(date);
      c.add(5, day);
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      return sdf.format(c.getTime());
   }
}
