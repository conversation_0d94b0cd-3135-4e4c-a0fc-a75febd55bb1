package com.samsung.common.config;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.utils.SecurityUtils;
import com.samsung.common.utils.StrUtils;
import com.samsung.magicinfo.framework.setup.manager.ConfigEncryptionManagement;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.BufferedInputStream;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Scanner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.logging.log4j.Logger;

public class CommonConfig {
   static Logger logger = LoggingManagerV2.getLogger(CommonConfig.class);
   private static String configFileName = null;
   private static boolean FILE_IN_USE_STATUS = false;
   private static volatile Properties config;
   private static final Pattern pattern = Pattern.compile("([^=]+)[=](.*)");
   static List newKeyList = new ArrayList() {
      {
         this.add("privacy_policy.enable");
         this.add("privacy_policy.location");
         this.add("privacy_policy.serverid");
         this.add("security.filter.src");
      }
   };

   private CommonConfig() {
      super();
   }

   private static void loadConfig() throws ConfigException {
      Locale.setDefault(Locale.ENGLISH);
      FileInputStream in = null;

      try {
         String confFilePath = getConfigFilePath();
         Properties props = new Properties();
         if (configFileName == null) {
            configFileName = getConfigFileName();
         }

         in = new FileInputStream(confFilePath + File.separator + configFileName);
         props.load(new BufferedInputStream(in));
         config = props;
         in.close();
         String securityKey = config.getProperty("encrypt.manager.key.v1");

         for(int i = 0; i < ConfigEncryptionManagement.ENCRYPTION_PROPERTY_KEYS.length; ++i) {
            String propertyKey = ConfigEncryptionManagement.ENCRYPTION_PROPERTY_KEYS[i];
            String value = config.getProperty(propertyKey);

            try {
               String desValue = ConfigEncryptionManagement.getDecryption(value, securityKey);
               config.remove(propertyKey);
               config.put(propertyKey, desValue);
            } catch (Exception var9) {
               logger.error("[decryption failure] Config Key : " + propertyKey + " Error Msg : " + var9.getMessage());
            }
         }

      } catch (Exception var10) {
         if (in != null) {
            try {
               in.close();
            } catch (IOException var8) {
               var8.printStackTrace();
            }
         }

         throw new ConfigException("Can't load config file");
      }
   }

   public static Properties getConfig() throws ConfigException {
      if (config == null) {
         Class var0 = CommonConfig.class;
         synchronized(CommonConfig.class) {
            if (config == null) {
               loadConfig();
            }
         }
      }

      return config;
   }

   public static String get(String key) throws ConfigException {
      if (config == null) {
         loadConfig();
      }

      if (key == null) {
         throw new ConfigException("Config Exception : key can not be null");
      } else {
         String value = config.getProperty(key);
         if (value != null) {
            value = value.trim();
         }

         return value;
      }
   }

   public static void set(String key, String value) throws ConfigException {
      if (config == null) {
         loadConfig();
      }

      if (key != null && value != null) {
         config.setProperty(key, value);
      } else {
         throw new ConfigException("Config Exception : key and value can not be null");
      }
   }

   public static String getConfigFilePath() {
      String configFilePath = System.getProperty("user.home");
      String magicInfoHome = System.getenv("MAGICINFO_PREMIUM_HOME");
      if (magicInfoHome != null && !magicInfoHome.equals("")) {
         configFilePath = magicInfoHome + File.separator + "conf";
      }

      return configFilePath;
   }

   public static String getConfigFileName() {
      String configFileName = "config.properties";
      String magicInfoConf = System.getenv("MAGICINFO_PREMIUM_CONF");
      if (magicInfoConf != null && !magicInfoConf.equals("")) {
         configFileName = magicInfoConf;
      }

      return configFileName;
   }

   public static String getKeyFilePath() {
      String keyFilePath = System.getProperty("user.home");
      String magicInfoHome = System.getenv("MAGICINFO_PREMIUM_HOME");
      if (magicInfoHome != null && !magicInfoHome.equals("")) {
         keyFilePath = magicInfoHome + File.separator + "runtime" + File.separator + "keystore";
      }

      return keyFilePath;
   }

   public static void setConfigFileOpenStatus(boolean is_in_use) {
      FILE_IN_USE_STATUS = is_in_use;
   }

   public static boolean getConfigFileOpenStatus() {
      return FILE_IN_USE_STATUS;
   }

   public static int saveModifiedConfigFile(Map attrib_map, String[] attrib_key) {
      int attrib_number = attrib_key.length;
      Scanner scanner = null;

      try {
         String confFilePath = getConfigFilePath();
         if (configFileName == null) {
            configFileName = getConfigFileName();
         }

         String confFileFullPath = confFilePath + File.separator + configFileName;
         File confFile = SecurityUtils.getSafeFile(confFileFullPath);
         scanner = new Scanner(confFile, "UTF-8");
         scanner.useDelimiter("\n|\r\n");
         List configAttrib = new ArrayList();
         int index = 0;

         label250:
         while(true) {
            while(true) {
               if (!scanner.hasNext()) {
                  scanner.close();
                  scanner = null;
                  int size = configAttrib.size();

                  for(int seq = 0; seq < size; ++seq) {
                     String[] temp = (String[])configAttrib.get(seq);
                     if (temp.length >= 1 && !temp[0].startsWith("#")) {
                        for(int attrib_index = 0; attrib_index < attrib_number; ++attrib_index) {
                           String key = attrib_key[attrib_index];
                           String comp_value = temp[0].trim();
                           if (comp_value.equalsIgnoreCase(key)) {
                              String key_value = ((String)attrib_map.get(key)).toString();
                              String[] temp_array = new String[]{key, key_value};
                              configAttrib.set(seq, temp_array);
                           }
                        }
                     }
                  }

                  StringBuffer config_content = new StringBuffer();

                  for(int seq = 0; seq < size; ++seq) {
                     String[] temp = (String[])configAttrib.get(seq);
                     if (temp[0].startsWith("#")) {
                        config_content.append(temp[0] + "\n");
                     } else if (temp.length >= 2) {
                        config_content.append(temp[0] + "=" + temp[1] + "\n");
                     }
                  }

                  if (!confFile.delete()) {
                     byte var38 = 1;
                     return var38;
                  }

                  FileWriter fstream = new FileWriter(SecurityUtils.directoryTraversalChecker(confFileFullPath, (String)null));
                  BufferedWriter out = new BufferedWriter(fstream);

                  try {
                     out.write(config_content.toString());
                  } catch (Exception var29) {
                     var29.printStackTrace();
                  }

                  out.close();
                  fstream.close();
                  break label250;
               }

               String temp_attrib_line = scanner.next();
               if (temp_attrib_line.startsWith("#")) {
                  String[] temp = new String[]{temp_attrib_line};
                  configAttrib.add(index, temp);
                  break;
               }

               if (!temp_attrib_line.equals("")) {
                  Matcher match = pattern.matcher(temp_attrib_line);
                  if (match.find()) {
                     configAttrib.add(index, new String[]{match.group(1), match.group(2)});
                  } else {
                     logger.warn("Can not parse property:" + temp_attrib_line + "\n from file: " + confFileFullPath);
                  }
                  break;
               }
            }

            ++index;
         }
      } catch (Exception var30) {
         var30.printStackTrace();
      } finally {
         try {
            if (scanner != null) {
               scanner.close();
               scanner = null;
            }
         } catch (Exception var27) {
            var27.printStackTrace();
         }

      }

      if (attrib_map.containsKey("logger.MAIN_LOGGER.level") || attrib_map.containsKey("appender.FILE.policies.size.size") || attrib_map.containsKey("appender.FILE.strategy.max")) {
         try {
            LoggingManagerV2.updateConfig();
         } catch (ConfigException var28) {
            var28.printStackTrace();
         }
      }

      return 0;
   }

   private static List addExtraItem(Map attrib_map, List attrib_key, List configAttrib, List checkList) {
      if (attrib_key.size() != checkList.size()) {
         Iterator var4 = attrib_key.iterator();

         while(var4.hasNext()) {
            String key = (String)var4.next();
            if (!checkList.contains(key) && newKeyList.contains(key)) {
               String key_value = ((String)attrib_map.get(key)).toString();
               String[] temp_array = new String[]{key, key_value};
               configAttrib.add(temp_array);
            }
         }
      }

      return configAttrib;
   }

   public static int saveOnlyModifiedConfigFile(Map attrib_map, List attrib_key) {
      int attrib_number = attrib_key.size();
      Scanner scanner = null;

      label287: {
         byte var39;
         try {
            String confFilePath = getConfigFilePath();
            if (configFileName == null) {
               configFileName = getConfigFileName();
            }

            String confFileFullPath = confFilePath + File.separator + configFileName;
            File confFile = SecurityUtils.getSafeFile(confFileFullPath);
            scanner = new Scanner(confFile, "UTF-8");
            scanner.useDelimiter("\n|\r\n");
            List configAttrib = new ArrayList();
            int index = 0;

            while(scanner.hasNext()) {
               String temp_attrib_line = scanner.next();
               if (temp_attrib_line.startsWith("#")) {
                  String[] temp = new String[]{temp_attrib_line};
                  configAttrib.add(index, temp);
                  ++index;
               } else if (!temp_attrib_line.trim().equals("")) {
                  Matcher match = pattern.matcher(temp_attrib_line);
                  if (match.find()) {
                     configAttrib.add(index, new String[]{match.group(1), match.group(2)});
                     ++index;
                  } else {
                     logger.warn("Can not parse property:" + temp_attrib_line + "\n from file: " + confFileFullPath);
                  }
               }
            }

            scanner.close();
            scanner = null;
            List checkList = new ArrayList();
            int size = configAttrib.size();

            for(int seq = 0; seq < size; ++seq) {
               String[] temp = (String[])configAttrib.get(seq);
               if (temp.length >= 1 && !temp[0].startsWith("#")) {
                  for(int attrib_index = 0; attrib_index < attrib_number; ++attrib_index) {
                     String key = StrUtils.nvl((String)attrib_key.get(attrib_index));
                     String comp_value = temp[0].trim();
                     if (comp_value.equalsIgnoreCase(key)) {
                        String key_value = ((String)attrib_map.get(key)).toString();
                        String[] temp_array = new String[]{key, key_value};
                        configAttrib.set(seq, temp_array);
                        checkList.add(key);
                        break;
                     }
                  }
               }
            }

            List configAttrib = addExtraItem(attrib_map, attrib_key, configAttrib, checkList);
            size = configAttrib.size();
            StringBuffer config_content = new StringBuffer();

            for(int seq = 0; seq < size; ++seq) {
               String[] temp = (String[])configAttrib.get(seq);
               if (temp[0].startsWith("#")) {
                  config_content.append(temp[0] + "\n");
               } else if (temp.length >= 2) {
                  config_content.append(temp[0] + "=" + temp[1] + "\n");
               }
            }

            if (confFile.delete()) {
               FileWriter fstream = new FileWriter(confFileFullPath);
               BufferedWriter out = new BufferedWriter(fstream);

               try {
                  out.write(config_content.toString());
               } catch (Exception var30) {
                  var30.printStackTrace();
               }

               out.close();
               fstream.close();
               break label287;
            }

            var39 = 1;
         } catch (Exception var31) {
            var31.printStackTrace();
            break label287;
         } finally {
            try {
               if (scanner != null) {
                  scanner.close();
               }
            } catch (Exception var28) {
               var28.printStackTrace();
            }

         }

         return var39;
      }

      if (attrib_map.containsKey("logger.MAIN_LOGGER.level") || attrib_map.containsKey("appender.FILE.policies.size.size") || attrib_map.containsKey("appender.FILE.strategy.max")) {
         try {
            LoggingManagerV2.updateConfig();
         } catch (ConfigException var29) {
            var29.printStackTrace();
         }
      }

      if (attrib_map.containsKey("device.log4j.on") || attrib_map.containsKey("device.log4j.devices")) {
         LoggingManagerV2.updateDeviceLoggerConfig();
      }

      return 0;
   }
}
