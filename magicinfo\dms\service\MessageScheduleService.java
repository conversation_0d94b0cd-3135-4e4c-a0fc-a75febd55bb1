package com.samsung.magicinfo.dms.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.dms.model.MessageScheduleFilter;
import com.samsung.magicinfo.dms.model.MessageScheduleResource;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.sql.SQLException;

public interface MessageScheduleService {
   ResponseBody deleteMessageSchedule(String var1);

   ResponseBody permanentlyDeleteMessageSchedule(String var1);

   ResponseBody listAllMessageSchedule(int var1, int var2);

   ResponseBody listMessage(MessageScheduleFilter var1);

   ResponseBody getMessageInfo(String var1) throws ConfigException, SQLException;

   ResponseBody createMessageSchedule(MessageScheduleResource var1) throws ConfigException, SQLException;

   ResponseBody editMessageSchedule(String var1, MessageScheduleResource var2) throws ConfigException, SQLException;

   ResponseBody deployMessageSchedule(String var1, MessageScheduleResource var2) throws ConfigException, SQLException;

   ResponseBody copyMessageSchedule(String var1, MessageScheduleResource var2) throws ConfigException, SQLException;
}
