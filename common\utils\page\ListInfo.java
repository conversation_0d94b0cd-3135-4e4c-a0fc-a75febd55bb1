package com.samsung.common.utils.page;

import com.samsung.common.logger.LoggingManagerV2;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.ResourceBundle;
import org.apache.logging.log4j.Logger;

public class ListInfo {
   static Logger logger = LoggingManagerV2.getLogger(ListInfo.class);
   private int pageSize;
   private int listSize;
   private String lastSymbol;
   private String firstSymbol;
   private String prevSymbol;
   private String nextSymbol;
   private String newSymbol;
   private String replySymbol;
   private String currentPageClass;
   private static Hashtable listInfos = new Hashtable();

   public ListInfo() {
      super();
   }

   public ListInfo(int pageSize, int listSize, String lastSymbol, String firstSymbol, String prevSymbol, String nextSymbol, String newSymbol, String replySymbol, String currentPageClass) {
      super();
      this.setPageSize(pageSize);
      this.setListSize(listSize);
      this.lastSymbol = lastSymbol;
      this.firstSymbol = firstSymbol;
      this.prevSymbol = prevSymbol;
      this.nextSymbol = nextSymbol;
      this.newSymbol = newSymbol;
      this.replySymbol = replySymbol;
      this.currentPageClass = currentPageClass;
   }

   public String getReplySymbol() {
      return this.replySymbol;
   }

   public String getPrevSymbol() {
      return this.prevSymbol;
   }

   public int getPageSize() {
      return this.pageSize;
   }

   public Hashtable getPageInfo() {
      return listInfos;
   }

   public String getNextSymbol() {
      return this.nextSymbol;
   }

   public String getNewSymbol() {
      return this.newSymbol;
   }

   public int getListSize() {
      return this.listSize;
   }

   public String getLastSymbol() {
      return this.lastSymbol;
   }

   public String getFirstSymbol() {
      return this.firstSymbol;
   }

   public String getCurrentPageClass() {
      return this.currentPageClass;
   }

   public void setFirstSymbol(String firstSymbol) {
      this.firstSymbol = firstSymbol;
   }

   public void setLastSymbol(String lastSymbol) {
      this.lastSymbol = lastSymbol;
   }

   public void setNewSymbol(String newSymbol) {
      this.newSymbol = newSymbol;
   }

   public void setNextSymbol(String nextSymbol) {
      this.nextSymbol = nextSymbol;
   }

   public void setPageInfo(Hashtable pageInfo) {
      listInfos = pageInfo;
   }

   public void setPageSize(int pageSize) {
      if (pageSize == 0) {
         this.pageSize = 1;
      } else {
         this.pageSize = pageSize;
      }

   }

   public void setPrevSymbol(String prevSymbol) {
      this.prevSymbol = prevSymbol;
   }

   public void setReplySymbol(String replySymbol) {
      this.replySymbol = replySymbol;
   }

   public void setListSize(int listSize) {
      if (listSize == 0) {
         this.listSize = 1;
      } else {
         this.listSize = listSize;
      }

   }

   public void setCurrentPageClass(String currentPageClass) {
      this.currentPageClass = currentPageClass;
   }

   public String toString() {
      return "pageSize    = " + this.pageSize + ",listSize    = " + this.listSize + ",lastSymbol  = " + this.lastSymbol + ",firstSymbol = " + this.firstSymbol + ",prevSymbol  = " + this.prevSymbol + ",nextSymbol  = " + this.nextSymbol + ",newSymbol   = " + this.newSymbol + ",replySymbol = " + this.replySymbol + ",currentPageClass=" + this.currentPageClass;
   }

   private static void loadProperties() {
      ResourceBundle prop = ResourceBundle.getBundle("properties/list_types");
      ResourceBundle values = ResourceBundle.getBundle("properties/list_values");
      Enumeration e = prop.getKeys();

      while(e.hasMoreElements()) {
         try {
            String key = e.nextElement().toString().trim();
            listInfos.put(key, new ListInfo(Integer.parseInt(values.getString(key + ".pageSize").trim()), Integer.parseInt(values.getString(key + ".listSize").trim()), values.getString(key + ".lastSymbol").trim(), values.getString(key + ".firstSymbol").trim(), values.getString(key + ".prevSymbol").trim(), values.getString(key + ".nextSymbol").trim(), values.getString(key + ".newSymbol").trim(), values.getString(key + ".replySymbol").trim(), values.getString(key + ".currentPageClass").trim()));
         } catch (Exception var4) {
            logger.error("", var4);
         }
      }

   }

   public static ListInfo getListInfo(String type) {
      return !listInfos.containsKey(type) ? (ListInfo)listInfos.get("default") : (ListInfo)listInfos.get(type);
   }

   public static ListInfo getListInfo(String type, int lstSize) {
      new ListInfo();
      if (!listInfos.containsKey(type)) {
         return (ListInfo)listInfos.get("default");
      } else {
         ListInfo listInfo = (ListInfo)listInfos.get(type);
         if (type != null) {
            if (!type.equals("photolist") && !type.equals("list") && !type.equals("commonlist") && !type.equals("photos") && !type.equals("smallPhotolist")) {
               if (type.equals("monitoringlist")) {
                  listInfo.setPageSize(lstSize);
               }
            } else {
               listInfo.setListSize(lstSize);
               listInfo.setPageSize(lstSize);
            }
         }

         return (ListInfo)listInfos.get(type);
      }
   }

   public static ListInfo getCmsListInfo(String type, int lstSize) {
      new ListInfo();
      ListInfo listInfo;
      if (!listInfos.containsKey(type)) {
         listInfo = (ListInfo)listInfos.get("default");
         return listInfo;
      } else {
         listInfo = (ListInfo)listInfos.get(type);
         if (type != null && (type.equals("photolist") || type.equals("list") || type.equals("photos") || type.equals("smallPhotolist"))) {
            listInfo.setListSize(lstSize);
            listInfo.setPageSize(lstSize);
         }

         return listInfo;
      }
   }

   static {
      loadProperties();
   }
}
