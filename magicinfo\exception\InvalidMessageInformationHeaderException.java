package com.samsung.magicinfo.exception;

import com.samsung.common.exception.BasicException;

public class InvalidMessageInformationHeaderException extends BasicException {
   private static final long serialVersionUID = 891261002140593429L;

   public InvalidMessageInformationHeaderException(String reason) {
      super(reason);
   }

   public InvalidMessageInformationHeaderException(String code, String reason) {
      super(code, reason);
   }

   public InvalidMessageInformationHeaderException(String code, String subCode, String reason) {
      super(code, subCode, reason);
   }
}
