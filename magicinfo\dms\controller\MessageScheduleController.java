package com.samsung.magicinfo.dms.controller;

import com.samsung.common.logger.LoggingManagerV2;
import com.samsung.common.model.ResponseBody;
import com.samsung.common.utils.CommonUtils;
import com.samsung.magicinfo.dms.model.MessageScheduleFilter;
import com.samsung.magicinfo.dms.model.MessageScheduleResource;
import com.samsung.magicinfo.dms.service.MessageScheduleService;
import com.samsung.magicinfo.framework.kpi.annotation.KPI;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import java.sql.SQLException;
import javax.validation.Valid;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(
   value = "Basic Schedules Management System Rest api",
   description = "Operations pertaining to schedules in Basic Schedules Management System",
   tags = {"Schedule API Group"}
)
@RestController
@RequestMapping({"/restapi/v1.0/dms/schedule/messages"})
public class MessageScheduleController {
   private final Logger logger = LoggingManagerV2.getLogger(this.getClass());
   @Autowired
   private MessageScheduleService messageScheduleService;

   public MessageScheduleController() {
      super();
   }

   @ApiOperation(
      value = "get all message schedule list",
      notes = "get all message schedule list.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity listAllMessageSchedule(@RequestParam(value = "startIndex",required = true) int startIndex, @RequestParam(value = "pageSize",required = true) int pageSize) throws Exception {
      this.logger.info("[REST][MESSAGE SCHEDULE][listAllMessageSchedule] get all message schedule list");
      ResponseBody responsebody = new ResponseBody();
      if (!CommonUtils.checkAvailable("schedule")) {
         return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
      } else {
         try {
            MessageScheduleFilter filter = new MessageScheduleFilter();
            filter.setStartIndex(startIndex);
            filter.setPageSize(pageSize);
            responsebody = this.messageScheduleService.listMessage(filter);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][MESSAGE SCHEDULE][listAllMessageSchedule] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][MESSAGE SCHEDULE][listAllMessageSchedule] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][listAllMessageSchedule] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][listAllMessageSchedule] Exception is occured. " + var6.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @KPI
   @ApiOperation(
      value = "get message by filter",
      notes = "get message by filter.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/filter"},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity listMessage(@Valid @RequestBody MessageScheduleFilter filter, BindingResult result) throws Exception {
      this.logger.info("[REST][MESSAGE SCHEDULE][listMessage] get message schedule list by filter");
      ResponseBody responsebody = new ResponseBody();
      if (!CommonUtils.checkAvailable("schedule")) {
         return new ResponseEntity(responsebody, HttpStatus.METHOD_NOT_ALLOWED);
      } else if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.messageScheduleService.listMessage(filter);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][MESSAGE SCHEDULE][listMessage] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][MESSAGE SCHEDULE][listMessage] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][listMessage] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][listContentScheduleByGroup] Exception is occured. " + var6.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "get message information",
      notes = "get message information.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{messageId}"},
      method = {RequestMethod.GET},
      produces = {"application/json"}
   )
   public ResponseEntity getMessageInfo(@PathVariable String messageId) throws SQLException, ConfigException {
      this.logger.info("[REST][MESSAGE SCHEDULE][getProgramInfo][" + messageId + "] get message schedule detail information by messageId");
      ResponseBody responsebody = new ResponseBody();

      try {
         responsebody = this.messageScheduleService.getMessageInfo(messageId);
         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][MESSAGE SCHEDULE][getProgramInfo][" + messageId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][MESSAGE SCHEDULE][getProgramInfo][" + messageId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var4) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var4.getMessage());
         this.logger.error("[REST][MESSAGE SCHEDULE][getProgramInfo][" + messageId + "] access denied.");
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var5) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         this.logger.error("[REST][MESSAGE SCHEDULE][getProgramInfo][" + messageId + "] Exception is occured. " + var5.toString());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "create new message schedule",
      notes = "create new message schedule.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {""},
      method = {RequestMethod.POST},
      produces = {"application/json"}
   )
   public ResponseEntity createMessageSchedule(@Valid @RequestBody MessageScheduleResource params, BindingResult result) throws SQLException, ConfigException {
      this.logger.info("[REST][MESSAGE SCHEDULE][createMessageSchedule] create the message schedule");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.messageScheduleService.createMessageSchedule(params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][MESSAGE SCHEDULE][createMessageSchedule] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][MESSAGE SCHEDULE][createMessageSchedule] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var5) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var5.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][createMessageSchedule] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var6) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][createMessageSchedule] Exception is occured. " + var6.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "edit message schedule",
      notes = "edit message schedule.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{messageId}"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   public ResponseEntity editMessageSchedule(@PathVariable String messageId, @Valid @RequestBody MessageScheduleResource params, BindingResult result) throws SQLException, ConfigException {
      this.logger.info("[REST][MESSAGE SCHEDULE][editMessageSchedule][" + messageId + "] edit the message schedule by messageId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.messageScheduleService.editMessageSchedule(messageId, params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][MESSAGE SCHEDULE][editMessageSchedule][" + messageId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][MESSAGE SCHEDULE][editMessageSchedule][" + messageId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][editMessageSchedule][" + messageId + "] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var7.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][editMessageSchedule][" + messageId + "] Exception is occured. " + var7.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "deploy message schedule",
      notes = "deploy message schedule.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{messageId}/deploy"},
      method = {RequestMethod.PUT},
      produces = {"application/json"}
   )
   public ResponseEntity deployMessageSchedule(@PathVariable String messageId, @Valid @RequestBody MessageScheduleResource params, BindingResult result) throws SQLException, ConfigException {
      this.logger.info("[REST][MESSAGE SCHEDULE][deployMessageSchedule][" + messageId + "] deploy the message schedule by messageId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      } else {
         try {
            responsebody = this.messageScheduleService.deployMessageSchedule(messageId, params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][MESSAGE SCHEDULE][deployMessageSchedule][" + messageId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.OK);
            } else {
               this.logger.error("[REST][MESSAGE SCHEDULE][deployMessageSchedule][" + messageId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][deployMessageSchedule][" + messageId + "] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            responsebody.setStatus("Fail");
            responsebody.setErrorMessage(var7.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][deployMessageSchedule][" + messageId + "] Exception is occured. " + var7.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }

   @ApiOperation(
      value = "move message schedule to recyclebin",
      notes = "move message schedule to recyclebin.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{messageId}"},
      method = {RequestMethod.DELETE},
      produces = {"application/json"}
   )
   public ResponseEntity deleteMessageSchedule(@PathVariable String messageId, @RequestParam(value = "permanently",required = false) Boolean permanently) throws SQLException {
      this.logger.info("[REST][MESSAGE SCHEDULE][deleteMessageSchedule][" + messageId + "] delete message schedule by messageId");
      ResponseBody responsebody = new ResponseBody();

      try {
         if (permanently != null && permanently) {
            responsebody = this.messageScheduleService.permanentlyDeleteMessageSchedule(messageId);
         } else {
            responsebody = this.messageScheduleService.deleteMessageSchedule(messageId);
         }

         responsebody.setApiVersion("1.0");
         if (responsebody.getStatus().equalsIgnoreCase("Success")) {
            this.logger.info("[REST][MESSAGE SCHEDULE][deleteMessageSchedule][" + messageId + "] finish successfully.");
            return new ResponseEntity(responsebody, HttpStatus.OK);
         } else {
            this.logger.error("[REST][MESSAGE SCHEDULE][deleteMessageSchedule][" + messageId + "] Internal Error is occured. " + responsebody.getErrorMessage());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      } catch (AccessDeniedException var5) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var5.getMessage());
         this.logger.error("[REST][MESSAGE SCHEDULE][deleteMessageSchedule][" + messageId + "] access denied.");
         return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
      } catch (Exception var6) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(var6.getMessage());
         this.logger.error("[REST][MESSAGE SCHEDULE][deleteMessageSchedule][" + messageId + "] Exception is occured. " + var6.toString());
         return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
      }
   }

   @ApiOperation(
      value = "copy the message schedule by messageId",
      notes = "copy the message schedule by messageId.",
      authorizations = {@Authorization("api_key")}
   )
   @RequestMapping(
      value = {"/{messageId}/copy"},
      method = {RequestMethod.PUT},
      produces = {"application/json"},
      consumes = {"application/json"}
   )
   public ResponseEntity copyMessageSchedule(@PathVariable String messageId, @Valid @RequestBody MessageScheduleResource params, BindingResult result) throws SQLException, ConfigException {
      this.logger.info("[REST][MESSAGE SCHEDULE][copyMessageSchedule][" + messageId + "] copy the message schedule by messageId");
      ResponseBody responsebody = new ResponseBody();
      if (result.hasErrors()) {
         responsebody.setStatus("Fail");
         responsebody.setErrorMessage(result.getFieldError().getDefaultMessage());
         return new ResponseEntity(responsebody, HttpStatus.UNPROCESSABLE_ENTITY);
      } else {
         try {
            responsebody = this.messageScheduleService.copyMessageSchedule(messageId, params);
            responsebody.setApiVersion("1.0");
            if (responsebody.getStatus().equalsIgnoreCase("Success")) {
               this.logger.info("[REST][MESSAGE SCHEDULE][copyMessageSchedule][" + messageId + "] finish successfully.");
               return new ResponseEntity(responsebody, HttpStatus.CREATED);
            } else {
               this.logger.error("[REST][MESSAGE SCHEDULE][copyMessageSchedule][" + messageId + "] Internal Error is occured. " + responsebody.getErrorMessage());
               return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
            }
         } catch (AccessDeniedException var6) {
            responsebody.setErrorMessage(var6.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][copyMessageSchedule][" + messageId + "] access denied.");
            return new ResponseEntity(responsebody, HttpStatus.UNAUTHORIZED);
         } catch (Exception var7) {
            responsebody.setErrorMessage(var7.getMessage());
            this.logger.error("[REST][MESSAGE SCHEDULE][copyMessageSchedule][" + messageId + "] Exception is occured. " + var7.toString());
            return new ResponseEntity(responsebody, HttpStatus.INTERNAL_SERVER_ERROR);
         }
      }
   }
}
