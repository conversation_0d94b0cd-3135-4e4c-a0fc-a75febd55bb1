package com.samsung.magicinfo.cms.model;

import io.swagger.annotations.ApiModel;

@ApiModel
public class DashboardContentResource {
   public int totalCount;
   public int usedCount;
   public int unapprovedCount;
   public int rejectCount;

   public DashboardContentResource() {
      super();
   }

   public int getTotalCount() {
      return this.totalCount;
   }

   public void setTotalCount(int totalCount) {
      this.totalCount = totalCount;
   }

   public int getUsedCount() {
      return this.usedCount;
   }

   public void setUsedCount(int usedCount) {
      this.usedCount = usedCount;
   }

   public int getUnapprovedCount() {
      return this.unapprovedCount;
   }

   public void setUnapprovedCount(int unapprovedCount) {
      this.unapprovedCount = unapprovedCount;
   }

   public int getRejectCount() {
      return this.rejectCount;
   }

   public void setRejectCount(int rejectCount) {
      this.rejectCount = rejectCount;
   }
}
