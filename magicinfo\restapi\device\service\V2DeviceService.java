package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import com.samsung.magicinfo.protocol.exception.ParsingException;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonStatusResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonUpdateResult;
import com.samsung.magicinfo.restapi.device.model.V2DeletedDevice;
import com.samsung.magicinfo.restapi.device.model.V2DeviceCabinetSendCmdWrapper;
import com.samsung.magicinfo.restapi.device.model.V2DeviceConversion;
import com.samsung.magicinfo.restapi.device.model.V2DeviceDeleteResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceExternalPowerResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFilter;
import com.samsung.magicinfo.restapi.device.model.V2DeviceFlipConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLedCabinetResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLicenseConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLogProcess;
import com.samsung.magicinfo.restapi.device.model.V2DevicePreconfigData;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetCopyParam;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetDeleteResource;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetFilter;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetResource;
import com.samsung.magicinfo.restapi.device.model.V2DevicePresetResultResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceReqServiceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSaveChannelConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceServerStatus;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSetSboxLayoutResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceStatusResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceSystemUsageResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceTagAssignment;
import com.samsung.magicinfo.restapi.device.model.V2DeviceVncConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningRule;
import com.samsung.magicinfo.restapi.device.model.V2RemoteControlIds;
import com.samsung.magicinfo.restapi.device.model.V2ServerConfig;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import com.samsung.magicinfo.rms.model.DeviceFilter;
import com.samsung.magicinfo.rms.model.DeviceLedCabinetResource;
import com.samsung.magicinfo.rms.model.GeneralInfoResource;
import java.sql.SQLException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

public interface V2DeviceService {
   V2PageResource getAllDeviceList(V2DeviceFilter var1, HttpServletRequest var2, HttpServletResponse var3) throws Exception;

   V2DeviceResource getDevice(String var1, HttpServletRequest var2) throws Exception;

   V2PageResource getDevicesScheduleInfo(int var1, int var2, String var3) throws Exception;

   List getTagList(String var1, String var2) throws Exception;

   V2CommonBulkResultResource assignTag(V2DeviceTagAssignment var1) throws Exception;

   V2PageResource getUnapprovedDeviceList(V2DeviceFilter var1) throws Exception;

   ResponseBody getCabinetList(String var1, DeviceFilter var2);

   ResponseBody updateCabinetInfo(String var1, DeviceLedCabinetResource var2);

   GeneralInfoResource getRmServerVnc(String var1, String var2, HttpServletRequest var3) throws Exception;

   Long uploadCustomizeFile(String var1, String var2, MultipartFile var3) throws Exception;

   void publishCustomizeFile(String var1, String var2) throws Exception;

   String saveRmRuleFile(MultipartFile var1) throws Exception;

   V2PageResource checkUpcomingExpiryDateOnPlaylist(int var1, int var2, String var3, String var4, String var5) throws Exception;

   V2PageResource checkUpcomingExpiryDateDevice(int var1, int var2, String var3, String var4, String var5) throws Exception;

   V2PageResource checkUpcomingExpiryDateOnSchedule(int var1, int var2, String var3, String var4, String var5) throws Exception;

   V2CommonUpdateResult networkMode(V2CommonIds var1) throws Exception;

   V2DeviceSystemUsageResource deviceUsage(String var1, HttpServletRequest var2, HttpServletResponse var3) throws Exception;

   V2CommonIds deploySchedule(String var1) throws Exception;

   V2CommonUpdateResult isConnected(V2CommonIds var1) throws Exception;

   V2DeviceSetSboxLayoutResource setSboxLayout(String var1) throws Exception;

   void deviceVnc(String var1, V2DeviceVncConf var2, HttpServletRequest var3, HttpServletResponse var4) throws Exception;

   V2DeviceReqServiceResource reqCabinetMdc(String var1, V2CommonIds var2) throws Exception;

   V2CommonBulkResultResource getCabinetInfo(String var1, String var2) throws Exception;

   V2CommonBulkResultResource getExternalPowerInfo(String var1, String var2) throws Exception;

   V2CommonUpdateResult saveExpiration(V2DeviceLicenseConf var1) throws Exception;

   V2CommonBulkResultResource saveChannel(V2DeviceSaveChannelConf var1, HttpServletRequest var2) throws Exception;

   V2CommonStatusResource logProcess(String var1, V2DeviceLogProcess var2, HttpServletRequest var3) throws Exception;

   V2DeletedDevice clearCollected(String var1, String var2) throws Exception;

   List getLogCollected(String var1) throws Exception;

   V2DeviceWarningRule getWarningRuleInfo(String var1) throws Exception;

   V2CommonUpdateResult getDeviceInfo(V2CommonIds var1) throws Exception;

   V2DeviceReqServiceResource requestDeviceConversion(V2DeviceConversion var1) throws Exception;

   List getDeviceConversionResult(V2DeviceReqServiceConf var1) throws Exception;

   V2DeviceReqServiceResource quickControl(List var1, String var2, String var3, String var4) throws Exception;

   ResponseBody getContentsDownloadStatus(String var1, String var2) throws Exception;

   V2DeviceDeleteResource deleteDevice(List var1, HttpServletRequest var2, HttpServletResponse var3) throws Exception;

   V2PageResource checkContent(int var1, int var2, String var3, String var4, String var5, HttpServletResponse var6) throws Exception;

   V2PageResource checkSchedule(int var1, int var2, String var3, String var4, String var5, HttpServletResponse var6) throws Exception;

   V2PageResource checkStorage(int var1, int var2, String var3, String var4, String var5, HttpServletResponse var6) throws Exception;

   V2PageResource checkTimezone(int var1, int var2, String var3, String var4, String var5, HttpServletResponse var6) throws Exception;

   V2PageResource getPresetList(V2DevicePresetFilter var1) throws Exception;

   V2DevicePresetResource preconfigitems(String var1) throws Exception;

   V2PageResource getDeviceLedCabinets(String var1, Long var2) throws Exception;

   V2DeviceExternalPowerResource getDeviceExternalPower(String var1) throws Exception;

   V2DeviceReqServiceResource reqExternalPowerMdc(String var1) throws Exception;

   List getDeviceLedCabinetsDetails(String var1, List var2, Long var3) throws Exception;

   List updateDeviceCaninetInfo(String var1, List var2, V2DeviceLedCabinetResource var3, String var4) throws Exception;

   void scanDeviceCabinets(String var1, List var2) throws Exception;

   void sendCommandOnCabinets(String var1, V2DeviceCabinetSendCmdWrapper var2) throws Exception;

   void sendCommandToSbox(String var1, String var2, String var3) throws Exception;

   V2DeviceStatusResource getDeviceListByUserId() throws Exception;

   V2DevicePresetResource savePreconfig(@Valid V2DevicePreconfigData var1) throws Exception;

   V2DevicePresetResource editPreconfig(@Valid V2DevicePreconfigData var1, String var2) throws Exception;

   V2DevicePresetDeleteResource deletePreconfig(@Valid V2CommonIds var1) throws SQLException;

   V2DevicePresetResource copyPreconfig(String var1, @Valid V2DevicePresetCopyParam var2) throws Exception;

   ModelAndView noticeExport(String var1, String var2, String var3, String var4, String var5, HttpServletResponse var6, String var7);

   ModelAndView expiryDatePlaylistExport(String var1, String var2, String var3, String var4, HttpServletResponse var5, String var6);

   ModelAndView expiryDateExport(String var1, String var2, String var3, String var4, HttpServletResponse var5, String var6);

   ModelAndView expiryDateDeviceExport(String var1, String var2, String var3, String var4, HttpServletResponse var5, String var6);

   ModelAndView deviceExport(V2DeviceFilter var1, String var2, String var3, HttpServletResponse var4, String var5) throws Exception;

   void customizeDownload(String var1, HttpServletRequest var2, HttpServletResponse var3) throws Exception;

   ModelAndView ledCabinetsExport(String var1, HttpServletResponse var2, String var3) throws Exception;

   V2CommonBulkResultResource getFlipInfo(V2CommonIds var1) throws Exception;

   V2DeviceReqServiceResource reqSetCommonToDevice(V2DeviceFlipConf var1) throws Exception;

   V2CommonBulkResultResource reqGetCommonCurrentStatus(V2CommonIds var1) throws Exception;

   V2DeviceReqServiceResource reqGetCommonAllStatus(V2CommonIds var1) throws Exception;

   List preconfigDeployStatus(String var1);

   String sendPostboot(String var1) throws Exception;

   V2DevicePresetResultResource getDevicePreconfigResult(String var1, int var2) throws Exception;

   void logDownload(String var1, String var2, String var3, HttpServletRequest var4, HttpServletResponse var5) throws Exception;

   V2ServerConfig serverSettings() throws ConfigException;

   V2DeviceServerStatus checkServerStatus(String var1) throws Exception;

   V2DeviceReqServiceResource getCommonGetResult(V2DeviceReqServiceConf var1) throws Exception;

   V2CommonBulkResultResource getRCServerVnc(V2RemoteControlIds var1, String var2, HttpServletRequest var3) throws Exception;

   V2DeviceReqServiceResource processCommand(V2CommonIds var1, String var2, String var3) throws Exception;

   Integer getSessionExpiry() throws ConfigException;

   String remoteLoggerSettings() throws ConfigException;

   List fetchRemoteLogScriptsList() throws ConfigException;

   String parseLogScriptFile(String var1) throws ParsingException, ConfigException;
}
