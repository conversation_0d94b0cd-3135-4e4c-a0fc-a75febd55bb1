package com.samsung.common.utils;

import com.samsung.common.logger.LoggingManagerV2;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.Set;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

public class DBCacheEntityUtils {
   static Logger logger = LoggingManagerV2.getLogger(DBCacheEntityUtils.class);

   public DBCacheEntityUtils() {
      super();
   }

   public static void copyNonNullProperties(Object dest, Object orig) {
      BeanUtils.copyProperties(orig, dest, getNullPropertyNames(orig));
   }

   public static void copyPropertiesToOtherObjects(Object[] objectList, Object orig) {
      Object[] var2 = objectList;
      int var3 = objectList.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         Object dest = var2[var4];
         if (dest != orig) {
            try {
               BeanUtils.copyProperties(orig, dest, getNullPropertyNames(orig));
            } catch (Exception var7) {
               logger.error("", var7);
            }
         }
      }

   }

   public static String[] getNullPropertyNames(Object source) {
      BeanWrapper src = new BeanWrapperImpl(source);
      PropertyDescriptor[] pds = src.getPropertyDescriptors();
      Set emptyNames = new HashSet();
      PropertyDescriptor[] var4 = pds;
      int var5 = pds.length;

      for(int var6 = 0; var6 < var5; ++var6) {
         PropertyDescriptor pd = var4[var6];

         try {
            String propertyName = pd.getName();
            Field field = source.getClass().getDeclaredField(propertyName);
            CopyNotNull copyEmpty = (CopyNotNull)field.getAnnotation(CopyNotNull.class);
            Object srcValue;
            if (copyEmpty == null) {
               srcValue = src.getPropertyValue(propertyName);
               if (srcValue == null) {
                  emptyNames.add(pd.getName());
               }
            } else {
               srcValue = src.getPropertyValue(propertyName);
               if (srcValue == null) {
                  emptyNames.add(pd.getName());
               }
            }
         } catch (Exception var12) {
         }
      }

      String[] result = new String[emptyNames.size()];
      return (String[])emptyNames.toArray(result);
   }
}
