package com.samsung.common.logger;

import com.samsung.common.config.CommonConfig;
import com.samsung.common.utils.DeviceLogger;
import com.samsung.magicinfo.protocol.compiler.ConfigException;
import java.io.File;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.Layout;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.appender.RollingFileAppender;
import org.apache.logging.log4j.core.appender.RollingFileAppender.Builder;
import org.apache.logging.log4j.core.appender.rolling.DefaultRolloverStrategy;
import org.apache.logging.log4j.core.appender.rolling.RollingFileManager;
import org.apache.logging.log4j.core.appender.rolling.SizeBasedTriggeringPolicy;
import org.apache.logging.log4j.core.appender.rolling.TimeBasedTriggeringPolicy;
import org.apache.logging.log4j.core.config.AppenderRef;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.Configurator;
import org.apache.logging.log4j.core.config.LoggerConfig;
import org.apache.logging.log4j.core.config.Property;
import org.apache.logging.log4j.core.layout.PatternLayout;

public class LoggingManagerV2 {
   private static File file;
   private static final String configFilePath = CommonConfig.getConfigFilePath();
   private static final String configFileName = CommonConfig.getConfigFileName();

   public LoggingManagerV2() {
      super();
   }

   private static void initConfiguration() {
      if (file == null) {
         file = new File(configFilePath + File.separator + configFileName);
      }

      if (file != null) {
         LoggerContext activeContext = (LoggerContext)LogManager.getContext(false);
         activeContext.setConfigLocation(file.toURI());
      }

   }

   public static Logger getLogger(String name) {
      return LogManager.getLogger(name);
   }

   public static Logger getLogger(Class cls) {
      return getLogger(cls.getName());
   }

   public static void updateConfig() throws ConfigException {
      String loggerName = CommonConfig.get("logger.MAIN_LOGGER.name");
      String appenderName = "FILE";
      String logLevel = CommonConfig.get("logger.MAIN_LOGGER.level");
      String maxFileSize = CommonConfig.get("appender.FILE.policies.size.size");
      if (!maxFileSize.contains("MB")) {
         maxFileSize = maxFileSize + "MB";
      }

      String maxFileCount = CommonConfig.get("appender.FILE.strategy.max");
      LoggerContext activeContext = (LoggerContext)LogManager.getContext(false);
      Configurator.setLevel(loggerName, Level.valueOf(logLevel));
      RollingFileAppender appFile = (RollingFileAppender)activeContext.getConfiguration().getAppender(appenderName);
      ((RollingFileManager)appFile.getManager()).setTriggeringPolicy(SizeBasedTriggeringPolicy.createPolicy(maxFileSize));
      ((RollingFileManager)appFile.getManager()).setRolloverStrategy(DefaultRolloverStrategy.newBuilder().withMax(maxFileCount).build());
   }

   public static void updateDeviceLoggerConfig() {
      DeviceLogger.updateDeviceLogger();
   }

   public static void initDeviceLoggerConfig(String loggerName, String path, String deviceLogLevel) {
      String filePattern = path.replace(".log", ".log.%d{yyyy-MM-dd}");
      LoggerContext ctx = (LoggerContext)LogManager.getContext(false);
      Configuration config = ctx.getConfiguration();
      Layout layout = PatternLayout.newBuilder().withConfiguration(config).withPattern("[%d{yyyy-MM-dd HH:mm:ss}] %-5p [%l] - %m%n").build();
      TimeBasedTriggeringPolicy tbtp = TimeBasedTriggeringPolicy.newBuilder().withInterval(1).withModulate(true).build();
      RollingFileAppender appender = ((Builder)((Builder)((Builder)RollingFileAppender.newBuilder().withAppend(true).withFileName(path).withFilePattern(filePattern).withPolicy(tbtp).setLayout(layout)).setName(loggerName))).build();
      appender.start();
      config.addAppender(appender);
      AppenderRef ref = AppenderRef.createAppenderRef(loggerName, (Level)null, (Filter)null);
      AppenderRef[] refs = new AppenderRef[]{ref};
      LoggerConfig loggerConfig = LoggerConfig.createLogger(false, Level.valueOf(deviceLogLevel), loggerName, "true", refs, (Property[])null, config, (Filter)null);
      loggerConfig.addAppender(appender, (Level)null, (Filter)null);
      config.addLogger(loggerName, loggerConfig);
      ctx.updateLoggers();
   }

   static {
      initConfiguration();
   }
}
