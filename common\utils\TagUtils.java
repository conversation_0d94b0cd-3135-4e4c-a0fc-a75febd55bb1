package com.samsung.common.utils;

import com.samsung.magicinfo.framework.content.entity.Content;
import com.samsung.magicinfo.framework.content.entity.Playlist;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfo;
import com.samsung.magicinfo.framework.content.manager.PlaylistInfoImpl;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.Device;
import com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup;
import com.samsung.magicinfo.framework.setup.entity.TagEntity;
import com.samsung.magicinfo.framework.setup.manager.TagInfo;
import com.samsung.magicinfo.framework.setup.manager.TagInfoImpl;
import com.samsung.magicinfo.framework.user.manager.UserInfo;
import com.samsung.magicinfo.framework.user.manager.UserInfoImpl;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.springframework.context.support.ResourceBundleMessageSource;

public class TagUtils {
   public TagUtils() {
      super();
   }

   public static ArrayList getTagMappingInfoByTagId(int tagId, String userId, String contextPath) throws SQLException, UnsupportedEncodingException {
      ArrayList tagMappingResult = new ArrayList();
      TagInfo tagDao = TagInfoImpl.getInstance();
      UserInfo uInfo = UserInfoImpl.getInstance();
      String orgName = uInfo.getOrganNameByRootGroupId(uInfo.getRootGroupIdByUserId(userId));
      Map rootTagNodeInfo = new HashMap();
      TagEntity tag = tagDao.getTag(tagId);
      rootTagNodeInfo.put("id", tagId);
      rootTagNodeInfo.put("isroot", true);
      rootTagNodeInfo.put("topic", tag.getTag_name());
      rootTagNodeInfo.put("background-color", "#3196ff");
      tagMappingResult.add(rootTagNodeInfo);
      Map contentNode = new HashMap();
      contentNode.put("id", "contentNode");
      contentNode.put("parentid", tagId);
      contentNode.put("topic", "DID_MAIN_CONTENT");
      contentNode.put("depth", "1");
      tagMappingResult.add(contentNode);
      Map playlistNode = new HashMap();
      playlistNode.put("id", "playlistNode");
      playlistNode.put("parentid", tagId);
      playlistNode.put("topic", "TEXT_TITLE_PLAYLIST_P");
      playlistNode.put("depth", "1");
      tagMappingResult.add(playlistNode);
      Map deviceNode = new HashMap();
      deviceNode.put("id", "deviceNode");
      deviceNode.put("parentid", tagId);
      deviceNode.put("topic", "TEXT_DEVICE_P");
      deviceNode.put("depth", "1");
      tagMappingResult.add(deviceNode);
      List contentList = tagDao.getTagContentListByTagIdAndOrgId(tagId, uInfo.getRootGroupIdByUserId(userId));
      Content content = null;

      for(int i = 0; i < contentList.size(); ++i) {
         Map contentMapInfo = new HashMap();
         content = null;
         content = (Content)contentList.get(i);
         contentMapInfo.put("id", content.getContent_id());
         contentMapInfo.put("parentid", "contentNode");
         contentMapInfo.put("topic", content.getContent_name());
         contentMapInfo.put("contentType", content.getMedia_type());
         contentMapInfo.put("background-image", "thumb_id=" + content.getThumb_file_id());
         contentMapInfo.put("width", "100");
         contentMapInfo.put("height", "56");
         tagMappingResult.add(contentMapInfo);
      }

      List playlistList = null;
      playlistList = tagDao.getPlaylistInfoByTagIdAndOrgId(tagId, uInfo.getRootGroupIdByUserId(userId));

      HashMap playlistMapInfo;
      int j;
      for(j = 0; j < playlistList.size(); ++j) {
         playlistMapInfo = new HashMap();
         playlistMapInfo.put("id", ((Playlist)playlistList.get(j)).getPlaylist_id());
         playlistMapInfo.put("parentid", "playlistNode");
         playlistMapInfo.put("topic", ((Playlist)playlistList.get(j)).getPlaylist_name());
         playlistMapInfo.put("playlistType", getPlaylistTypeStr(((Playlist)playlistList.get(j)).getPlaylist_type()));
         tagMappingResult.add(playlistMapInfo);
      }

      playlistList = tagDao.getTagPlaylistInfoByTagIdAndOrgId(tagId, uInfo.getRootGroupIdByUserId(userId));

      for(j = 0; j < playlistList.size(); ++j) {
         playlistMapInfo = new HashMap();
         playlistMapInfo.put("id", ((Playlist)playlistList.get(j)).getPlaylist_id());
         playlistMapInfo.put("parentid", "playlistNode");
         playlistMapInfo.put("topic", ((Playlist)playlistList.get(j)).getPlaylist_name());
         playlistMapInfo.put("playlistType", getPlaylistTypeStr(((Playlist)playlistList.get(j)).getPlaylist_type()));
         tagMappingResult.add(playlistMapInfo);
      }

      List deviceList = tagDao.getDeviceListByTagIdAndOrgName(tagId, orgName);

      for(int j = 0; j < deviceList.size(); ++j) {
         Map deviceMapInfo = new HashMap();
         deviceMapInfo.put("id", ((Device)deviceList.get(j)).getDevice_id());
         deviceMapInfo.put("parentid", "deviceNode");
         deviceMapInfo.put("topic", ((Device)deviceList.get(j)).getDevice_name());
         deviceMapInfo.put("deviceType", ((Device)deviceList.get(j)).getDevice_type());
         deviceMapInfo.put("deviceTypeVersion", ((Device)deviceList.get(j)).getDevice_type_version());
         List groups = DeviceUtils.getGroupNamePath(((Device)deviceList.get(j)).getGroup_id().intValue());
         new ArrayList();
         String groupPath = "";

         for(int k = 0; k < groups.size(); ++k) {
            if (k == 0) {
               groupPath = groupPath + ((DeviceGroup)groups.get(k)).getGroup_name();
            } else {
               groupPath = groupPath + ">" + ((DeviceGroup)groups.get(k)).getGroup_name();
            }
         }

         deviceMapInfo.put("groupPath", groupPath);
         tagMappingResult.add(deviceMapInfo);
      }

      return tagMappingResult;
   }

   public static ArrayList getTagValueMappingInfoByTagId(int tagId, String userId, String contextPath) throws SQLException, UnsupportedEncodingException {
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      Locale locale = SecurityUtils.getLocale();
      ArrayList tagMappingResult = new ArrayList();
      TagInfo tagDao = TagInfoImpl.getInstance();
      UserInfo uInfo = UserInfoImpl.getInstance();
      PlaylistInfo pInfo = PlaylistInfoImpl.getInstance();
      List tagMap = tagDao.getTagConditionFromTagId(Long.valueOf((long)tagId));
      HashMap notAssignCondition = new HashMap();
      notAssignCondition.put("tag_id", tagId);
      notAssignCondition.put("tag_condition_id", -1);
      notAssignCondition.put("tag_condition", rms.getMessage("COM_DID_VWL_NOT_ASSIGNED", (Object[])null, locale));
      tagMap.add(notAssignCondition);
      Map rootTagNodeInfo = new HashMap();
      TagEntity tag = tagDao.getTag(tagId);
      rootTagNodeInfo.put("id", "root_" + tagId);
      rootTagNodeInfo.put("isroot", true);
      rootTagNodeInfo.put("topic", tag.getTag_name());
      rootTagNodeInfo.put("background-color", "#3196ff");
      tagMappingResult.add(rootTagNodeInfo);

      for(int i = 0; i < tagMap.size(); ++i) {
         String tagConditionId = String.valueOf(((Map)tagMap.get(i)).get("tag_condition_id"));
         Map valueMapInfo = new HashMap();
         valueMapInfo.put("id", tagConditionId);
         valueMapInfo.put("parentid", "root_" + tagId);
         valueMapInfo.put("topic", (String)((Map)tagMap.get(i)).get("tag_condition"));
         valueMapInfo.put("showExpander", false);
         tagMappingResult.add(valueMapInfo);
         List thumb = pInfo.getThumbContentAtTagPlaylistAll((long)tagId, String.valueOf(((Map)tagMap.get(i)).get("tag_condition_id")));

         for(int j = 0; j < thumb.size(); ++j) {
            Map thumbMapInfo = new HashMap();
            thumbMapInfo.put("id", ((Map)thumb.get(j)).get("file_id") + "_" + tagConditionId);
            if (j == 0) {
               thumbMapInfo.put("parentid", tagConditionId);
            } else {
               thumbMapInfo.put("parentid", ((Map)thumb.get(j - 1)).get("file_id") + "_" + tagConditionId);
            }

            thumbMapInfo.put("topic", ((Map)thumb.get(j)).get("content_name"));
            thumbMapInfo.put("contentType", ((Map)thumb.get(j)).get("media_type"));
            thumbMapInfo.put("background-image", contextPath + "/servlet/ContentThumbnail?width=100&height=56&thumb_id=" + ((Map)thumb.get(j)).get("file_id") + "&thumb_filename=" + URLEncoder.encode((String)((Map)thumb.get(j)).get("file_name"), "UTF-8"));
            thumbMapInfo.put("width", "100");
            thumbMapInfo.put("height", "56");
            thumbMapInfo.put("showEdge", false);
            thumbMapInfo.put("showExpander", false);
            tagMappingResult.add(thumbMapInfo);
         }
      }

      return tagMappingResult;
   }

   private static String getPlaylistTypeStr(String playlistTypeNum) {
      ResourceBundleMessageSource rms = new ResourceBundleMessageSource();
      rms.setBasename("resource/messages");
      Locale locale = SecurityUtils.getLocale();
      String playlistTypeStr = rms.getMessage("COM_TEXT_GENERAL_P", (Object[])null, locale);
      if (playlistTypeNum.equals("0")) {
         rms.getMessage("COM_TEXT_GENERAL_P", (Object[])null, locale);
      } else if (playlistTypeNum.equals("1")) {
         playlistTypeStr = rms.getMessage("MIS_SID_AUDIENCE-TARGETED", (Object[])null, locale);
      } else if (playlistTypeNum.equals("2")) {
         playlistTypeStr = rms.getMessage("COM_DID_RMS_DEVICE_VIDEO_WALL", (Object[])null, locale);
      } else if (playlistTypeNum.equals("3")) {
         playlistTypeStr = rms.getMessage("MIS_SID_SYNC_PLAY", (Object[])null, locale);
      } else if (playlistTypeNum.equals("4")) {
         playlistTypeStr = rms.getMessage("COM_DID_LFD_ADVERTISEMENT", (Object[])null, locale);
      } else if (playlistTypeNum.equals("5")) {
         playlistTypeStr = rms.getMessage("MIS_TEXT_TAG_P", (Object[])null, locale);
      } else if (playlistTypeNum.equals("6")) {
         playlistTypeStr = rms.getMessage("MIS_SID_NESTED", (Object[])null, locale);
      }

      return playlistTypeStr;
   }
}
