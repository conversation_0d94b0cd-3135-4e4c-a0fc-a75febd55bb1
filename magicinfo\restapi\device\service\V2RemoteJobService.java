package com.samsung.magicinfo.restapi.device.service;

import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.common.model.V2CommonUpdateResult;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface V2RemoteJobService {
   V2PageResource getRemoteJobList(int var1, int var2, String var3, String var4, String var5) throws Exception;

   V2CommonUpdateResult cancelJobs(V2CommonIds var1) throws Exception;

   void downloadToAddRemoteControl(HttpServletRequest var1, HttpServletResponse var2) throws Exception;

   void downloadToEditRemoteControlSettings(String var1, HttpServletRequest var2, HttpServletResponse var3) throws Exception;

   void downloadToReuseRemoteControl(String var1, HttpServletRequest var2, HttpServletResponse var3) throws Exception;
}
