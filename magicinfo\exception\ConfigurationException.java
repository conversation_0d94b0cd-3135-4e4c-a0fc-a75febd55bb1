package com.samsung.magicinfo.exception;

import com.samsung.common.exception.BasicException;

public class ConfigurationException extends BasicException {
   private static final long serialVersionUID = 5154988794951936392L;

   public ConfigurationException() {
      super();
   }

   public ConfigurationException(String message) {
      super(message);
   }

   public ConfigurationException(Throwable cause) {
      super(cause);
   }

   public ConfigurationException(String message, Throwable cause) {
      super(message, cause);
   }

   public ConfigurationException(String code, String subcode, String reason) {
      super(code, subcode, reason);
   }
}
