package com.samsung.common.excel;

import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.servlet.ModelAndView;

@Scope("sigleton")
public class ExcelDownloadService {
   private String returnUrl;
   private ExcelBuilder excelBuilder;

   public ExcelDownloadService() {
      super();
   }

   public void setReturnUrl(String returnUrl) {
      this.returnUrl = returnUrl;
   }

   public ModelAndView downloadPage() {
      ModelAndView mvc = new ModelAndView(this.returnUrl);
      return mvc;
   }

   public void downloadExcelFile(Map dataMap, HttpServletResponse response) {
      String fileName = (String)dataMap.get("fileName");
      String sheetName = (String)dataMap.get("sheetName");
      String[] columnNames = (String[])((String[])dataMap.get("columnNames"));
      String[] fieldNames = (String[])((String[])dataMap.get("fieldNames"));
      Object[] dataList = (Object[])((Object[])dataMap.get("dataList"));
      this.excelBuilder = new ExcelBuilder(sheetName);
      this.excelBuilder.setAlign((short)2);
      this.excelBuilder.setBold(true);
      this.excelBuilder.setItalic(true);
      this.excelBuilder.setFontColor((short)9);
      this.excelBuilder.setFontName("굴림체");
      this.excelBuilder.setFontSize((short)11);
      this.excelBuilder.setBgColor((short)11);
      this.excelBuilder.setBorder(true);
      this.excelBuilder.addTitleRow(0, fieldNames);
      this.excelBuilder.setDefaultStyle();
      this.excelBuilder.setAlign(new short[]{2, 3});
      this.excelBuilder.setVAlign((short)1);
      this.excelBuilder.setFontColor(new short[]{0, 8, 0});
      this.excelBuilder.setFontSize(new short[]{8, 0, 10});
      this.excelBuilder.setBorder(new boolean[]{true});
      this.excelBuilder.addRows(0, columnNames, dataList);
      this.excelBuilder.setDefaultStyle();
      this.excelBuilder.addRow(0, new String[]{"Notice List Data", ExcelBuilder.MERGE_LEFT, ExcelBuilder.MERGE_LEFT, ExcelBuilder.MERGE_LEFT}, 1, true);
      this.excelBuilder.download(fileName, response);
   }
}
