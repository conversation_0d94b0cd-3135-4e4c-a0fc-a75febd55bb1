package com.samsung.magicinfo.restapi.device.service;

import com.samsung.magicinfo.framework.device.deviceInfo.entity.LedCabinet;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLedCabinetModuleResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLedCabinetResource;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLedCabinetVoltageStatus;
import com.samsung.magicinfo.restapi.device.model.V2DeviceLedCabinetWithGroupResource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class V2DeviceLedCabinetFactory {
   public V2DeviceLedCabinetFactory() {
      super();
   }

   private static List v2DeviceLedCabinetModuleResource(LedCabinet cabinet) {
      String productNumberString = cabinet.getModule_product_number();
      String openDetectionString = cabinet.getOpen_detection();
      String moduleDateString = cabinet.getModule_date();
      List products = Arrays.asList(((String)Optional.ofNullable(productNumberString).orElse("")).split(":"));
      List openDetection = Arrays.asList(((String)Optional.ofNullable(openDetectionString).orElse("")).split(":"));
      List moduleDate = Arrays.asList(((String)Optional.ofNullable(moduleDateString).orElse("")).split(":"));
      List res = new ArrayList();
      int idx = Integer.max(products.size(), openDetection.size());
      idx = Integer.max(idx, moduleDate.size());
      IntStream.range(0, idx).forEach((inx) -> {
         V2DeviceLedCabinetModuleResource vr = V2DeviceLedCabinetModuleResource.V2DeviceLedCabinetModuleResourceBuilder.aV2DeviceLedCabinetModuleResource().moduelDate(moduleDate.size() > inx ? (String)moduleDate.get(inx) : "").productNumber(products.size() > inx ? (String)products.get(inx) : "").openDetection(openDetection.size() > inx ? (String)openDetection.get(inx) : "").build();
         res.add(vr);
      });
      return res;
   }

   private static List v2DeviceLedCabinetVoltageStatuses(LedCabinet cabinet) {
      List volList = Arrays.asList(((String)Optional.ofNullable(cabinet.getVoltage_status()).orElse("")).split(","));
      return (List)volList.stream().filter((v) -> {
         return v.contains(":");
      }).map((v) -> {
         return new V2DeviceLedCabinetVoltageStatus(v.split(":")[0], v.split(":")[1]);
      }).collect(Collectors.toList());
   }

   public static V2DeviceLedCabinetResource v2DeviceLedCabinetResourceBuilder(LedCabinet cabinet) {
      return V2DeviceLedCabinetResource.V2DeviceLedCabinetResourceBuilder.aV2DeviceLedCabinetResource().abl(cabinet.getAbl()).aspectRatio(cabinet.getAspect_ratio()).autoSource(cabinet.getAuto_source()).backlight(cabinet.getBacklight()).maxBacklight(cabinet.getMax_backlight()).cabinetGroupId(cabinet.getCabinet_group_id()).cabinetId(cabinet.getCabinet_id()).blockId(cabinet.getBlock_id()).signalPath(cabinet.getSignal_path()).currentTemperature(cabinet.getCurrent_temperature()).edgeCorrection(cabinet.getEdge_correction()).fpgaVersion(cabinet.getFpga_version()).gamut(cabinet.getGamut()).hdbt(cabinet.getHdbt()).ic(Arrays.asList(((String)Optional.ofNullable(cabinet.getIc()).orElse("")).split(","))).inputSource(cabinet.getInput_source()).lastScannedTime(cabinet.getLast_scanned_time_timestamp()).luminance(cabinet.getLuminance()).moduleRgbCc(cabinet.getModule_rgb_cc()).modules(cabinet.getModules()).parentDeviceId(cabinet.getParent_device_id()).phySize(cabinet.getPhy_size()).pitch(cabinet.getPitch()).pixelRgbCc(cabinet.getPixel_rgb_cc()).power(cabinet.getPower()).resolution(cabinet.getResolution()).positionX(cabinet.getPosition_X()).positionY(cabinet.getPosition_Y()).fwVersion(cabinet.getFw_version()).onScreenDisplay(cabinet.getOn_screen_display()).ledCabinetModules(v2DeviceLedCabinetModuleResource(cabinet)).voltageStatus(v2DeviceLedCabinetVoltageStatuses(cabinet)).cabinetGroupId(cabinet.getCabinet_group_id()).isICError(cabinet.getIsICError()).isLODError(cabinet.getIsLODError()).isTemperatureError(cabinet.getIsTemperatureError()).isVoltageError(cabinet.getIsVoltageError()).build();
   }

   public static List v2DeviceLedCabinetWithGroupResource(List cabinets) {
      List res = new ArrayList();
      Map resultMap = (Map)((List)Optional.ofNullable(cabinets).orElse(new ArrayList())).stream().map(V2DeviceLedCabinetFactory::v2DeviceLedCabinetResourceBuilder).collect(Collectors.groupingBy(V2DeviceLedCabinetResource::getCabinetGroupId));
      resultMap.entrySet().forEach((e) -> {
         List list = (List)e.getValue();
         Long groupId = (Long)e.getKey();
         res.add(new V2DeviceLedCabinetWithGroupResource(groupId, list));
      });
      return res;
   }

   public static LedCabinet getLedCabinet4UpdateCabinetInfo(V2DeviceLedCabinetResource resource) {
      LedCabinet cabinet = new LedCabinet();
      cabinet.setAbl(resource.getAbl());
      cabinet.setInput_source(resource.getInputSource());
      cabinet.setBacklight(resource.getBacklight());
      cabinet.setGamut(resource.getGamut());
      cabinet.setOn_screen_display(resource.getOnScreenDisplay());
      cabinet.setPixel_rgb_cc(resource.getPixelRgbCc());
      cabinet.setModule_rgb_cc(resource.getModuleRgbCc());
      cabinet.setEdge_correction(resource.getEdgeCorrection());
      return cabinet;
   }
}
