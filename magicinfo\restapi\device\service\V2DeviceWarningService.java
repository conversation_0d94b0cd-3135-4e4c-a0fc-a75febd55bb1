package com.samsung.magicinfo.restapi.device.service;

import com.samsung.common.model.ResponseBody;
import com.samsung.magicinfo.restapi.common.model.V2CommonBulkResultResource;
import com.samsung.magicinfo.restapi.common.model.V2CommonIds;
import com.samsung.magicinfo.restapi.device.model.V2DeviceGetWarningResources;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningGroupConf;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningRule;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningRuleElement;
import com.samsung.magicinfo.restapi.device.model.V2DeviceWarningRuleElementResoruce;
import com.samsung.magicinfo.restapi.utils.V2PageResource;
import java.sql.SQLException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.ModelAndView;

public interface V2DeviceWarningService {
   V2DeviceGetWarningResources getWarningRuleElement(String var1) throws Exception;

   ResponseBody warningRule(String var1, String var2) throws SQLException;

   V2PageResource getWarningRules(int var1, int var2, String var3, String var4, String var5) throws Exception;

   V2DeviceWarningRuleElementResoruce createWarningRuleElement(V2DeviceWarningRuleElement var1) throws Exception;

   V2DeviceWarningRuleElementResoruce updateWarningRuleElement(String var1, V2DeviceWarningRuleElement var2) throws Exception;

   V2CommonBulkResultResource deleteWarningRuleElement(V2CommonIds var1) throws Exception;

   String updateDevGroup(String var1, String var2, String var3) throws Exception;

   V2PageResource errorWarningList(int var1, int var2, String var3, String var4, String var5, String var6, String var7, List var8) throws Exception;

   V2DeviceWarningRule getWarningRuleElements() throws Exception;

   ModelAndView errorWarningExport(String var1, String var2, HttpServletResponse var3, String var4, String var5);

   V2PageResource errorWarningDetail(String var1, int var2, int var3, String var4, String var5, Long var6) throws Exception;

   V2PageResource errorWarningDetailHistory(String var1, String var2, String var3, String var4, int var5, int var6, String var7, String var8) throws Exception;

   V2CommonBulkResultResource waringGroupUpdate(V2DeviceWarningGroupConf var1) throws Exception;
}
