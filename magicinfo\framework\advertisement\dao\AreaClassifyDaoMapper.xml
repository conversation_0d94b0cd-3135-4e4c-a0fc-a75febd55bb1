<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.samsung.magicinfo.framework.advertisement.dao.AreaClassifyDaoMapper">

    <select id="getVendingDeviceGroupList"
            resultType="com.samsung.magicinfo.framework.device.deviceInfo.entity.DeviceGroup">
        SELECT * FROM MI_DMS_INFO_GROUP WHERE GROUP_TYPE = 'VENDING' AND GROUP_DEPTH = '3' AND IS_ROOT = <include refid="utils.false" />
    </select>

</mapper>